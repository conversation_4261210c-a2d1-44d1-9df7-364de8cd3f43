<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.dahe.dao.ChkUpdateSiteColumnResultDao">
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO chk_update_site_column_result (
        site_id, site_name, group_id, group_name, column_id, column_name, process_type,
        check_status, check_status_desc, continuous_not_update_days, continuous_not_update_days_detail,
        update_days, update_days_detail, last_update_time, update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.siteId}, #{item.siteName}, #{item.groupId}, #{item.groupName}, #{item.columnId}, #{item.columnName}, #{item.processType},
            #{item.checkStatus}, #{item.checkStatusDesc}, #{item.continuousNotUpdateDays}, #{item.continuousNotUpdateDaysDetail},
            #{item.updateDays}, #{item.updateDaysDetail}, #{item.lastUpdateTime}, #{item.updateTime}
            )
        </foreach>
    </insert>
</mapper>