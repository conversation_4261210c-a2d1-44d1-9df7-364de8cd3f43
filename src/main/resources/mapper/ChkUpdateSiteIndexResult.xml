<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dahe.dao.ChkUpdateSiteIndexResultDao">
    <!-- 批量插入 -->
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO chk_update_site_index_result (
        group_id, group_name, site_id, site_name, site_url,process_type,
        update_days_desc, update_days, continuous_not_update_days_desc,
        continuous_not_update_days_num, last_update_time, update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.groupId}, #{item.groupName},#{item.siteId}, #{item.siteName}, #{item.siteUrl}, #{item.processType},
            #{item.updateDaysDesc},#{item.updateDays}, #{item.continuousNotUpdateDaysDesc},
            #{item.continuousNotUpdateDaysNum}, #{item.lastUpdateTime},#{item.updateTime}
            )
        </foreach>
    </insert>

</mapper>