<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dahe.dao.UserTenantDao">
    <resultMap id="BaseResultMap" type="cn.dahe.entity.UserTenant">
        <!--@Table t_user_tenant-->
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <insert id="insertList">
        insert ignore t_user_tenant(user_id, tenant_id)value
        <foreach collection="list" item="element" index="index" separator=",">
            (#{element.userId}, #{element.tenantId})
        </foreach>
    </insert>
</mapper>
