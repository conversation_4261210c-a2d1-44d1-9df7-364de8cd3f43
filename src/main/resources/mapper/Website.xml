<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.dahe.dao.WebsiteDao">
    <sql id="websiteCondition">
        <if test="query.websiteIds != null and query.websiteIds.size() > 0">
            AND tw.id IN
            <foreach collection="query.websiteIds" item="websiteId" open="(" separator="," close=")">
                #{websiteId}
            </foreach>
        </if>
    </sql>

    <sql id="searchTypeCondition">
        <choose>
            <when test="query.searchType != null and query.searchType == 1">
                and tw.process_type != 0
            </when>
            <otherwise>
                and tw.process_type = 0
            </otherwise>
        </choose>
    </sql>

    <sql id="processTypesCondition">
        <choose>
            <when test="query.processTypes != null and query.processTypes.size > 0">
                and tw.process_type in
                <foreach collection="query.processTypes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                and false
            </otherwise>
        </choose>
    </sql>
    <sql id="processTypeCondition">
        <if test="query.processType != null">
            and tw.process_type = #{query.processType}
        </if>
    </sql>

    <sql id="websiteVOColumn">
        tw.id,
        tw.id as websiteId,
        tw.site_id,
        tw.channel_id,
        tw.web_name as name,
        tw.web_url as url,
        ifnull(tw.group_id,0) as groupId,
        tw.process_type,
        tw.check_strategy,
        tw.status,
        ifnull(twg.group_name, '未分组') as groupName,
        tw.create_time
    </sql>


    <select id="pageList" resultType="cn.dahe.model.vo.WebsiteVO">
        select
        <include refid="websiteVOColumn"/>
        from t_website tw
        left join t_website_group twg on tw.group_id = twg.id
        <where>
            tw.is_del = 0
            <include refid="cn.dahe.dao.WebsiteGroupDao.groupNameCondition"/>
            <include refid="cn.dahe.dao.WebsiteGroupDao.groupIdCondition"/>
            <if test="query.status != null">
                AND tw.status = #{query.status}
            </if>
            <if test="query.name != null and query.name != ''">
                AND tw.web_name like concat('%', #{query.name}, '%')
            </if>
            <if test="query.url != null and query.url != ''">
                AND tw.web_url like concat('%', #{query.url}, '%')
            </if>
            <include refid="searchTypeCondition"/>
            <include refid="processTypeCondition"/>
        </where>
        order by tw.create_time desc,id asc
    </select>
    <select id="listSelectByQuery" resultType="cn.dahe.model.vo.WebsiteVO">
        select
        <include refid="websiteVOColumn"/>
        from t_website tw
        left join t_website_group twg on tw.group_id = twg.id
        <where>
            tw.is_del = 0 and tw.status = 1 and tw.process_type = 0
            <include refid="processTypeCondition"/>
            <include refid="websiteCondition"/>
        </where>
    </select>
    <select id="selectBySiteIds" resultType="cn.dahe.entity.Website">
        select * from t_website where site_id in
        <foreach collection="siteIds" item="siteId" open="(" separator="," close=")">
            #{siteId}
        </foreach>
        and is_del = 0 and process_type = 0 and status = 1
    </select>

    <select id="selectEnableByTenantIdAndUserAndProcessTypeIn" resultType="cn.dahe.entity.Website">
        select w.* from t_website w
        <if test="defaultRole">
            join t_user_website uw on w.id = uw.website_id
        </if>
        where w.is_del = 0 and w.status = 1
        and w.tenant_id = #{tenantId}
        and w.process_type in
        <foreach collection="platformSet" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="defaultRole">
            and uw.user_id = #{userId}
        </if>
    </select>

    <select id="selectNotDeleteByTenantIdAndUserAndProcessTypeIn" resultType="cn.dahe.entity.Website">
        select w.* from t_website w
        <if test="defaultRole">
            join t_user_website uw on w.id = uw.website_id
        </if>
        where w.is_del = 0
        and w.tenant_id = #{tenantId}
        and w.process_type in
        <foreach collection="platformSet" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="defaultRole">
            and uw.user_id = #{userId}
        </if>
    </select>
</mapper>