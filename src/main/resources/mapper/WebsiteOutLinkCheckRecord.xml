<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.dahe.dao.WebsiteOutLinkCheckRecordDao">

    <select id="countByFilters" resultType="java.lang.Integer">
        SELECT IFNULL(COUNT(*), 0)
        FROM `t_website_out_link_check_record`
        WHERE 1 = 1 and web_id > 0 and web_id in(select site_id from t_website where channel_id is null)
        <include refid="commonWhereClause"/>
    </select>


    <sql id="commonWhereClause">
        <if test="webId != null and webId != ''">
            AND web_id = #{webId}
        </if>
        <if test="sourcePage != null and sourcePage != ''">
            AND source_page = #{sourcePage}
        </if>
        <if test="linkUrl != null and linkUrl != ''">
            AND link_url = #{linkUrl}
        </if>
        <if test="httpCode != null and httpCode != ''">
            AND http_code = #{httpCode}
        </if>
        <if test="beginTime != null and beginTime != ''">
            AND check_time &gt;= #{beginTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND check_time &lt;= #{endTime}
        </if>
    </sql>

</mapper>