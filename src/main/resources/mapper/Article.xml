<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dahe.dao.ArticleDao">


    <sql id="articleAuditCondition">
        <if test="query.articleAuditStatuses != null and query.articleAuditStatuses.size() > 0">
            AND ta.audit_status IN
            <foreach collection="query.articleAuditStatuses" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
    </sql>
    <sql id="disposalStatusCondition">
        <if test="query.disposalStatuses != null and query.disposalStatuses.size() > 0">
            AND ta.disposal_status IN
            <foreach collection="query.disposalStatuses" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
    </sql>
    <sql id="rectifyStatusCondition">
        <if test="query.rectifyStatuses != null and query.rectifyStatuses.size() > 0">
            AND ta.rectify_status IN
            <foreach collection="query.rectifyStatuses" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
    </sql>
    <sql id="pubTimeCondition">
        <if test="query.pubBeginTime != null">
            AND ta.pub_time >= #{query.pubBeginTime}
        </if>
        <if test="query.pubEndTime != null">
            AND ta.pub_time &lt;= #{query.pubEndTime}
        </if>
    </sql>
    <sql id="pubDateCondition">
        <if test="query.pubBeginDate != null">
            AND ta.pub_time >= DATE_FORMAT(#{query.pubBeginDate}, '%Y-%m-%d 00:00:00')
        </if>
        <if test="query.pubEndDate != null">
            AND ta.pub_time &lt;= DATE_FORMAT(#{query.pubEndDate}, '%Y-%m-%d 23:59:59')
        </if>
    </sql>
    <sql id="articleIdsCondition">
        <if test="query.articleIds != null and query.articleIds.size() > 0">
            AND ta.id IN
            <foreach collection="query.articleIds" item="articleId" open="(" separator="," close=")">
                #{articleId}
            </foreach>
        </if>
    </sql>


    <sql id="articleCheckVOColumn">
        tct.id as taskId,
        ta.id as articleId,
        ta.site_id,
        ta.author,
        ta.summary,
        ta.focus_img_url,
        ta.pub_time,
        tw.group_id,
        tw.id as websiteId,
        tw.web_name as websiteName,
        tc.id as columnId,
        tc.name as columnName,
        tc.process_type,
        ta.article_url as url,
        tcc.cleaned_title,
        tcc.cleaned_content,
        tcc.compressed_title as htmlTitle,
        tcc.compressed_content as htmlContent,
        tcc.compressed_code as htmlCode,
        ta.audit_status,
        tct.check_time,
        tct.check_strategy,
        trs.reprint_source
    </sql>

    <sql id="processTypesCondition">
        <choose>
            <when test="query.processTypes != null and query.processTypes.size > 0">
                and ta.process_type in
                <foreach collection="query.processTypes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                and false
            </otherwise>
        </choose>
    </sql>

    <!-- 分页查询文章检查结果 -->
    <select id="listArticleInfo" resultType="cn.dahe.model.vo.ArticleCheckVO">
        SELECT
        <include refid="articleCheckVOColumn"/>
        FROM t_article ta
        left join t_channel tc on ta.channel_id = tc.id
        left join t_website tw on ta.website_id = tw.id
        left join t_check_task tct on ta.check_id = tct.id
        LEFT JOIN t_check_content tcc ON tct.id = tcc.id
        left join t_reprint_source trs on trs.id = ta.reprint_source_id
        <where>
            tct.check_status = 1 and tct.result_status = 1
            and tw.status = 1 and tw.is_del = 0
            <include refid="processTypesCondition"/>
            and ((ta.reprint_source_id is null and ta.reprint_source is null) or trs.filter_status = 0)
            <include refid="cn.dahe.dao.ReprintSourceDao.reprintSourceCondition"/>
            and
            exists (
            SELECT 1
            FROM t_check_result tcr
            left join t_check_word tcw on tcr.word_id = tcw.id
            WHERE ta.check_id = tcr.check_id and tcr.article_location in (0,1)
            <include refid="cn.dahe.dao.CheckWordDao.errorTypeAndLevelCondition"/>
            <include refid="cn.dahe.dao.CheckResultDao.resultAuditCondition"/>
            <include refid="cn.dahe.dao.CheckWordDao.filterStatusCondition"/>
            <include refid="cn.dahe.dao.CheckWordDao.wordCondition"/>
            <include refid="cn.dahe.dao.CheckResultDao.wordIdCondition"/>
            )
            <include refid="cn.dahe.dao.CheckTaskDao.checkStrategyCondition"/>
            <include refid="articleAuditCondition"/>
            <include refid="articleIdsCondition"/>
            <include refid="cn.dahe.dao.WebsiteDao.websiteCondition"/>
            <if test="query.keyword != null and query.keyword != ''">
                AND (
                tcc.cleaned_title LIKE CONCAT('%', #{query.keyword}, '%')
                or tcc.cleaned_content LIKE CONCAT('%',#{query.keyword}, '%')
                )
            </if>
            <include refid="pubTimeCondition"/>
            <include refid="cn.dahe.dao.CheckTaskDao.checkTimeCondition"/>
        </where>
        ORDER BY ${query.sortTypeEnum.field} ${query.sortTypeEnum.direction}
    </select>

    <select id="getWarnArticleInfo" resultType="cn.dahe.model.dto.WarnCheckDetailDto">
        SELECT tw.web_name    as websiteName,
               twg.group_name,
               ta.pub_time,
               ta.article_url as url,
               ta.check_id
        FROM t_article ta
                 left join t_website tw on ta.website_id = tw.id
                 left join t_website_group twg on tw.group_id = twg.id
        where ta.id = #{articleId}
#           and tw.status = 1 and tw.is_del = 0
    </select>

    <select id="getArticleInfo" resultType="cn.dahe.model.vo.ArticleCheckVO">
        SELECT
        <include refid="articleCheckVOColumn"/>
        FROM t_article ta
        left join t_channel tc on ta.channel_id = tc.id
        left join t_website tw on ta.website_id = tw.id
        left join t_check_task tct on ta.check_id = tct.id
        LEFT JOIN t_check_content tcc ON tct.id = tcc.id
        left join t_reprint_source trs on trs.id = ta.reprint_source_id
        where ta.id = #{articleId}
    </select>

    <!-- 全站搜索检查结果 -->
    <select id="pageAllSiteSearch" resultType="cn.dahe.model.vo.ArticleAllSiteSearchVO">
        SELECT
        ta.id,
        tac.title as html_title,
        tac.content as html_content,
        ta.pub_time,
        ta.collect_time as check_time,
        ta.reprint_source,
        ta.article_url as url,
        ta.website_id,
        tw.web_name as websiteName
        FROM t_article ta
        INNER JOIN t_article_content tac ON ta.id = tac.id
        <if test="(query.siteId != null and query.siteId != '') or (query.groupId != null and query.groupId != '' and query.type != null and query.type == 1)">
            INNER JOIN t_website tw ON ta.website_id = tw.id
        </if>
        <where>
            ta.website_id in(select id from t_website where is_del = 0 and status = 1)
            <!-- 优化：将最有选择性的条件放在前面 -->
            <if test="query.beginTime != null and query.beginTime != ''">
                AND ta.update_time >= #{query.beginTime}
            </if>
            <if test="query.endTime != null and query.endTime != ''">
                AND ta.update_time &lt;= #{query.endTime}
            </if>

            <!-- 优化：使用更高效的条件判断 -->
            <if test="query.searchType != null and query.searchType == 1">
                AND ta.process_type > 0
            </if>
            <if test="query.searchType == null or query.searchType == 0">
                AND (ta.process_type = 0 OR ta.process_type IS NULL)
            </if>

            <!-- 网站相关条件 -->
            <if test="query.siteId != null and query.siteId != ''">
                AND FIND_IN_SET(tw.id, #{query.siteId})
            </if>
            <if test="query.groupId != null and query.groupId != '' and query.type != null and query.type == 1">
                AND FIND_IN_SET(tw.group_id, #{query.groupId})
            </if>

            <!-- 关键词搜索条件放在最后，因为通常选择性较低 -->
            <if test="query.keywordType != null and query.keywordType == 0 and query.keyword != null and query.keyword != ''">
                AND tac.title LIKE CONCAT('%', #{query.keyword}, '%')
            </if>
            <if test="query.keywordType != null and query.keywordType == 1 and query.keyword != null and query.keyword != ''">
                AND tac.content LIKE CONCAT('%', #{query.keyword}, '%')
            </if>
        </where>

        <!-- 优化排序逻辑 -->
        <choose>
            <when test="query.sortField != null and query.sortField == 0">
                ORDER BY ta.pub_time
                <choose>
                    <when test="query.sortDirection == 1">ASC</when>
                    <otherwise>DESC</otherwise>
                </choose>
            </when>
            <when test="query.sortField != null and query.sortField == 1">
                ORDER BY ta.create_time
                <choose>
                    <when test="query.sortDirection == 1">ASC</when>
                    <otherwise>DESC</otherwise>
                </choose>
            </when>
            <otherwise>
                ORDER BY ta.id DESC
            </otherwise>
        </choose>
    </select>
    <select id="allSiteSearchExport" resultType="map">
        SELECT
        ta.id,
        tac.title as html_title,
        tac.content as html_content,
        DATE_FORMAT(ta.pub_time, '%Y-%m-%d %H:%i:%s') as pub_time,
        DATE_FORMAT(ta.collect_time, '%Y-%m-%d %H:%i:%s') as check_time,
        ta.reprint_source,
        ta.article_url as url
        FROM t_article ta
        INNER JOIN t_article_content tac ON ta.id = tac.id
        <if test="(query.siteId != null and query.siteId != '') or (query.groupId != null and query.groupId != '' and query.type != null and query.type == 1)">
            INNER JOIN t_website tw ON ta.website_id = tw.id
        </if>
        <where>
            ta.website_id in(select id from t_website where is_del = 0 and status = 1)
            <!-- 优化：将最有选择性的条件放在前面 -->
            <if test="query.beginTime != null and query.beginTime != ''">
                AND ta.update_time >= #{query.beginTime}
            </if>
            <if test="query.endTime != null and query.endTime != ''">
                AND ta.update_time &lt;= #{query.endTime}
            </if>

            <!-- 优化：使用更高效的条件判断 -->
            <if test="query.searchType != null and query.searchType == 1">
                AND ta.process_type > 0
            </if>
            <if test="query.searchType == null or query.searchType == 0">
                AND (ta.process_type = 0 OR ta.process_type IS NULL)
            </if>

            <!-- 网站相关条件 -->
            <if test="query.siteId != null and query.siteId != ''">
                AND FIND_IN_SET(tw.id, #{query.siteId})
            </if>
            <if test="query.groupId != null and query.groupId != '' and query.type != null and query.type == 1">
                AND FIND_IN_SET(tw.group_id, #{query.groupId})
            </if>

            <!-- 关键词搜索条件放在最后，因为通常选择性较低 -->
            <if test="query.keywordType != null and query.keywordType == 0 and query.keyword != null and query.keyword != ''">
                AND tac.title LIKE CONCAT('%', #{query.keyword}, '%')
            </if>
            <if test="query.keywordType != null and query.keywordType == 1 and query.keyword != null and query.keyword != ''">
                AND tac.content LIKE CONCAT('%', #{query.keyword}, '%')
            </if>
            <if test="query.ids != null and query.ids != ''">
                AND FIND_IN_SET(ta.id, #{query.ids})
            </if>
        </where>

        <!-- 优化排序逻辑 -->
        <choose>
            <when test="query.sortField != null and query.sortField == 0">
                ORDER BY ta.pub_time
                <choose>
                    <when test="query.sortDirection == 1">ASC</when>
                    <otherwise>DESC</otherwise>
                </choose>
            </when>
            <when test="query.sortField != null and query.sortField == 1">
                ORDER BY ta.create_time
                <choose>
                    <when test="query.sortDirection == 1">ASC</when>
                    <otherwise>DESC</otherwise>
                </choose>
            </when>
            <otherwise>
                ORDER BY ta.id DESC
            </otherwise>
        </choose>
    </select>
    <select id="listUncheckedArticle" resultType="cn.dahe.entity.Article">
        select tat.*
        from t_article tat
                 left join t_check_task tct on tat.check_id = tct.id
        where tct.id is null;
    </select>

    <!-- 栏目文章分页（只从Article表查询） -->
    <select id="pageChannelArticles" resultType="cn.dahe.model.vo.ArticleVO">
        SELECT
        ta.id AS articleId,
        ta.title AS title,
        ta.article_url AS url,
        ta.pub_time AS pubTime
        FROM t_article ta
        <where>
            1=1
            <if test="query.channelId != null">
                AND ta.channel_id = #{query.channelId}
            </if>
            <if test="query.keyword != null and query.keyword != ''">
                AND ta.title LIKE CONCAT('%', #{query.keyword}, '%')
            </if>
            <if test="query.beginTime != null">
                AND ta.pub_time &gt;= #{query.beginTime}
            </if>
            <if test="query.endTime != null">
                AND ta.pub_time &lt;= #{query.endTime}
            </if>
        </where>
        ORDER BY ta.pub_time DESC
    </select>
    <select id="listUnrelatedReprintSourceArticles" resultType="cn.dahe.entity.Article">
        select ta.*
        from t_article ta
        where ta.website_id is not null
          and ta.reprint_source_id is null
          and ta.reprint_source is not null
    </select>
    <select id="listByWebsiteId" resultType="cn.dahe.entity.Article">
        select *
        from t_article
        where website_id = #{websiteId}
    </select>

    <select id="listByIsIndex" resultType="cn.dahe.entity.Article">
        select *
        from t_article
        where is_index = 1
          and website_id = #{webSiteId}
          and pub_time &lt; CURRENT_DATE()
        order by pub_time desc
    </select>
    <select id="listByIsIndexAndPubTime" resultType="cn.dahe.entity.Article">
        select *
        from t_article
        where is_index = 1
          and website_id = #{webSiteId}
          and pub_time >= DATE_SUB(CURRENT_DATE(), INTERVAL 1 DAY)
          and pub_time &lt; CURRENT_DATE()
        order by pub_time desc
    </select>
    <select id="listNoContentArticle" resultType="cn.dahe.entity.Article">
        select ta.*
        from t_article ta
                 left join t_article_content tac on ta.id = tac.id
        where tac.id is null
           or (tac.content is null or tac.content = '')
    </select>
    <select id="getSnapshotInfo" resultType="cn.dahe.model.vo.CheckSnapshotVO">
        select ta.website_id,
               ta.process_type,
               tw.web_name    as websiteName,
               twg.group_name as groupName,
               ta.pub_time,
               ta.article_url as url,
               ta.title
        from t_article ta
                 left join t_website tw on ta.website_id = tw.id
                 left join t_website_group twg on tw.group_id = twg.id
        where ta.id = #{articleId}
    </select>


</mapper>