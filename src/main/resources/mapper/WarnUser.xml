<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dahe.dao.WarnUserDao">

    <select id="list" resultType="cn.dahe.entity.WarnUser">
        SELECT *
        FROM t_warn_user
        <where>
            <if test="query.userName != null and query.userName != ''">
                AND user_name LIKE CONCAT('%', #{query.userName}, '%')
            </if>
            AND is_subscribe = 1
        </where>
        ORDER BY id DESC
    </select>
    <select id="getByOpenId" resultType="cn.dahe.entity.WarnUser">
        SELECT *
        FROM t_warn_user
        WHERE user_open_id = #{openId}
    </select>
    <select id="getByUserPhoneWarnUser" resultType="cn.dahe.entity.WarnUser">
        SELECT *
        FROM t_warn_user
        WHERE user_phone = #{userPhone}
    </select>

</mapper>


