<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.dahe.dao.AttachmentDao">

    <sql id="attachmentCheckVOColumn">
        tat.id,tat.process_type,
        tw.web_name as website_name,
        tw.web_url as website_url,
        ta.title as source_name,
        ta.article_url as source_url,
        ta.pub_time,
        tct.check_time,
        tat.attachment_name,
        tat.attachment_url,
        tat.attachment_type,
        tat.attachment_size,
        tct.id as taskId
    </sql>
    <sql id="attachmentIdsCondition">
        <if test="query.attachmentIds != null and query.attachmentIds.size() > 0">
            and tat.id in
            <foreach item="item" collection="query.attachmentIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
    </sql>

    <!-- ==================== 附件检查记录 ==================== -->

    <select id="listAttachmentInfo" resultType="cn.dahe.model.vo.AttachmentCheckVO">
        SELECT
        <include refid="attachmentCheckVOColumn"/>
        FROM t_attachment tat
        left join t_check_task tct on tat.check_id = tct.id
        left join t_article ta on tat.parent_article_id = ta.id
        LEFT JOIN t_website tw ON tat.website_id = tw.id
        <where>
            tct.result_status = 1
            and tw.status = 1 and tw.is_del = 0
            <include refid="attachmentIdsCondition"/>
            <include refid="cn.dahe.dao.WebsiteDao.websiteCondition"/>
            <include refid="cn.dahe.dao.CheckTaskDao.checkStrategyCondition"/>
            <include refid="cn.dahe.dao.ArticleDao.pubDateCondition"/>
        </where>
        <!-- 排序 -->
        ORDER BY ${query.sortTypeEnum.field} ${query.sortTypeEnum.direction}
    </select>

    <select id="getAttachmentInfo" resultType="cn.dahe.model.vo.AttachmentCheckVO">
        SELECT
        <include refid="attachmentCheckVOColumn"/>,
        tcc.compressed_content as htmlContent
        FROM t_attachment tat
        left join t_check_task tct on tat.check_id = tct.id
        left join t_article ta on tat.parent_article_id = ta.id
        LEFT JOIN t_website tw ON tat.website_id = tw.id
        left join t_check_content tcc on tct.id = tcc.id
        WHERE tat.id = #{id}
    </select>

    <select id="listNoContentAttachment" resultType="cn.dahe.entity.Attachment">
        select tat.*
        from t_attachment tat
                 left join t_check_task tct on tat.check_id = tct.id
        where tct.content_status in (0, 2)
    </select>

    <select id="listNoTaskAttachment" resultType="cn.dahe.entity.Attachment">
        select tat.*
        from t_attachment tat
                 left join t_check_task tct on tat.check_id = tct.id
        where tct.id is null
    </select>

    <select id="getAttachmentContentInfo" resultType="cn.dahe.model.vo.AttachmentCheckVO">
        SELECT
        <include refid="attachmentCheckVOColumn"/>,
        tcc.compressed_code as htmlCode
        FROM t_attachment tat
        left join t_check_task tct on tat.check_id = tct.id
        left join t_check_content tcc on tct.id = tcc.id
        left join t_article ta on tat.parent_article_id = ta.id
        WHERE tat.id = #{id}
    </select>
    <select id="getWarnAttachmentInfo" resultType="cn.dahe.model.dto.WarnCheckDetailDto">
        select
        tat.check_id,
        tw.web_name as websiteName,
        twg.group_name as groupName,
        ta.pub_time,
        tat.attachment_url as url,
        ta.article_url as parentUrl,
        ta.title as parentTitle
        from t_attachment tat
        left join t_website tw on tat.website_id = tw.id
        left join t_website_group twg on tw.group_id = twg.id
        left join t_article ta on ta.id = tat.parent_article_id
        where tat.id = #{attachmentId}
    </select>
    <select id="getSnapshotInfo" resultType="cn.dahe.model.vo.CheckSnapshotVO">
        select tat.website_id,
               tat.process_type,
               tw.web_name         as websiteName,
               twg.group_name      as groupName,
               ta.pub_time,
               tat.attachment_url  as url,
               tat.attachment_name as title,
               ta.article_url      as parentUrl,
               ta.title            as parentTitle
        from t_attachment tat
                 left join t_website tw on tat.website_id = tw.id
                 left join t_website_group twg on tw.group_id = twg.id
                 left join t_article ta on ta.id = tat.parent_article_id
        where tat.id = #{attachmentId}
    </select>


</mapper>
