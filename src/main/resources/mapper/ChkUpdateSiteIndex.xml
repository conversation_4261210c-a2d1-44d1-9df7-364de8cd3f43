<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dahe.dao.ChkUpdateSiteIndexDao">
    <!-- 已更新站点数 - 统计状态为updated的不重复站点数量 -->
    <select id="getUpdatedCount" parameterType="cn.dahe.model.query.ChkUpdateSiteIndexQuery" resultType="int">
        SELECT
        count(cusir.id)
        FROM chk_update_site_index_result cusir
        WHERE cusir.site_id > 0 AND cusir.process_type = 0 AND cusir.site_id in(select id from t_website where is_del = 0 and status = 1 and process_type=0)
        AND cusir.continuous_not_update_days_num = 0 and cusir.update_days > 0
        <if test="query.groupId != null and query.groupId != ''">
            AND cusir.group_id IN (${query.groupId})
        </if>
        <if test="query.websiteId != null and query.websiteId != ''">
            AND cusir.site_id IN (${query.websiteId})
        </if>
        <if test="query.beginTime != null and query.beginTime != ''">
            AND cusir.update_time >= #{query.beginTime}
        </if>
        <if test="query.endTime != null and query.endTime != ''">
            AND cusir.update_time = DATE(#{query.endTime})
        </if>
        <if test="query.endTime == null or query.endTime == ''">
            AND cusir.update_time = CURRENT_DATE()
        </if>
    </select>

    <!-- 分页查询首页更新结果列表 - 基于article表 -->
    <select id="selectPageWithExtInfo" resultType="map">
        SELECT
        cusir.site_id as id,
        cusir.site_name as websiteName,  -- 使用聚合函数
        cusir.site_url as websiteIndexUrl,
        cusir.group_id as groupId,
        cusir.last_update_time as lastParseTime,
        CASE
        WHEN cusir.continuous_not_update_days_num > 0 THEN 0
        ELSE 1
        END as status,
        cusir.update_days_desc as updateDaysStr,
        cusir.continuous_not_update_days_desc as continuousNotUpdateDaysStr
        FROM chk_update_site_index_result cusir
        WHERE cusir.site_id > 0 AND cusir.process_type = 0 AND cusir.site_id in(select id from t_website where is_del = 0 and status = 1 and process_type=0)
        <if test="groupId != null and groupId != ''">
            AND cusir.group_id IN (${groupId})
        </if>
        <if test="siteId != null and siteId != ''">
            AND cusir.site_id IN (${siteId})
        </if>
        <if test="beginTime != null and beginTime != ''">
            AND cusir.update_time >= #{beginTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND cusir.update_time = DATE(#{endTime})
        </if>
        <if test="endTime == null or endTime == ''">
            AND cusir.update_time = CURRENT_DATE()
        </if>
        ORDER BY cusir.last_update_time DESC
    </select>

    <select id="selectPageWithExtInfo2" resultType="map">
        SELECT
        cusir.site_id as id,
        MAX(cusir.site_name) as websiteName,  -- 使用聚合函数
        MAX(cusir.site_url) as websiteIndexUrl,
        MAX(cusir.group_id) as groupId,
        MAX(cusir.group_name) as groupName,
        MAX(cusir.update_time),
        CASE
        WHEN MAX(cusir.continuous_not_update_days_num) > 0 THEN 0
        ELSE 1
        END as status,
        MAX(cusir.last_update_time) as lastParseTime,
        MAX(cusir.update_days_desc) as updateDaysStr,
        MAX(cusir.continuous_not_update_days_desc) as continuousNotUpdateDaysStr
        FROM chk_update_site_index_result cusir
        WHERE cusir.site_id = 0
        AND process_type > 0
        <where>
            <if test="groupId != null and groupId != ''">
                AND w.group_id IN (${groupId})
            </if>
            <if test="siteId != null and siteId != ''">
                AND tc.site_id IN (${siteId})
            </if>
            <if test="beginTime != null and beginTime != ''">
                AND ta.pub_time >= #{beginTime}
            </if>
            <if test="endTime != null and endTime != ''">
                AND ta.pub_time &lt; DATE_ADD(#{endTime}, INTERVAL 1 DAY)
            </if>
            <if test="processTypeList != null and processTypeList.size > 0">
                AND ta.process_type IN
                <foreach collection="processTypeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY cusir.site_id, cusir.site_name, cusir.site_url, cusir.group_id, cusir.group_name
        ORDER BY cusir.last_update_time DESC
    </select>

    <select id="selectExportData" resultType="map">
        SELECT
        cusir.site_id as id,
        cusir.site_name as websiteName,  -- 使用聚合函数
        cusir.site_url as websiteIndexUrl,
        cusir.group_id as groupId,
        DATE_FORMAT(cusir.last_update_time, '%Y-%m-%d %H:%i:%s') as lastUpdateTime,
        CASE
        WHEN cusir.continuous_not_update_days_num > 0 THEN '否'
        ELSE '是'
        END as status,
        cusir.update_days as updateDays,
        cusir.continuous_not_update_days_num as continuousNotUpdateDays
        FROM chk_update_site_index_result cusir
        WHERE cusir.site_id > 0 AND cusir.process_type = 0 AND cusir.site_id in(select id from t_website where is_del = 0 and status = 1 and process_type=0)
        <if test="query.groupId != null and query.groupId != ''">
            AND cusir.group_id IN (${query.groupId})
        </if>
        <if test="query.websiteId != null and query.websiteId != ''">
            AND cusir.site_id IN (${query.websiteId})
        </if>
        <if test="query.beginTime != null and query.beginTime != ''">
            AND cusir.update_time >= #{query.beginTime}
        </if>
        <if test="query.endTime != null and query.endTime != ''">
            AND cusir.update_time = DATE(#{query.endTime})
        </if>
        <if test="query.endTime == null or query.endTime == ''">
            AND cusir.update_time = CURRENT_DATE()
        </if>
        ORDER BY cusir.last_update_time DESC
    </select>


    <!-- 步骤1：删除当天已存在的数据 -->
    <delete id="deleteSiteIndexTaskDeleteTodayData">
        DELETE FROM chk_update_site_index_result
        WHERE update_time = CURRENT_DATE()  AND process_type = 0 AND id BETWEEN #{beginId} AND #{endId}
    </delete>

    <select id="selectSiteIndexTaskInsertTodayData" resultType="map">
        SELECT
        w.id AS site_id,
        w.web_name AS site_name,
        w.web_url AS site_url,
        w.group_id AS group_id,

        -- 从创建站点到昨天更新了几天
        (SELECT COUNT(DISTINCT DATE(a.pub_time))
        FROM t_article a
        WHERE a.website_id = w.id
        AND a.is_index = 1
        AND DATE(a.pub_time) >= DATE(w.create_time)
        AND DATE(a.pub_time) &lt; CURDATE()
        ) AS update_days,

        -- 从创建站点到昨天更新日期列表（逗号拼接）
        (SELECT GROUP_CONCAT(DISTINCT DATE(a.pub_time) ORDER BY DATE(a.pub_time) SEPARATOR ',')
        FROM t_article a
        WHERE a.website_id = w.id
        AND a.is_index = 1
        AND DATE(a.pub_time) >= DATE(w.create_time)
        AND DATE(a.pub_time) &lt; CURDATE()
        ) AS update_days_desc,

        -- 连续未更新天数（最后一次更新时间到昨天的天数）
        CASE
        WHEN (SELECT MAX(DATE(a.pub_time)) FROM t_article a WHERE a.website_id = w.id AND a.is_index = 1 AND a.pub_time &lt; CURDATE()) IS NULL
        THEN DATEDIFF(CURDATE(), DATE(w.create_time))  -- 从未更新过，从创建到昨天的天数
        ELSE GREATEST(0, DATEDIFF(CURDATE(), (SELECT MAX(DATE(a.pub_time)) FROM t_article a WHERE a.website_id = w.id AND a.is_index = 1 AND a.pub_time &lt; CURDATE())) - 1)
        -- 关键修改：减去1，因为最后更新日不算未更新日
        END AS continuous_not_update_days_num,

        -- 连续未更新日期列表（从最后更新日期的下一天到昨天）
        CASE
        WHEN (SELECT MAX(DATE(a.pub_time)) FROM t_article a WHERE a.website_id = w.id AND a.is_index = 1) IS NULL THEN
        -- 如果从未更新过，则从创建时间开始到昨天
        (SELECT GROUP_CONCAT(
        DATE_ADD(DATE(w.create_time), INTERVAL seq.n DAY)
        ORDER BY seq.n SEPARATOR ','
        )
        FROM (
        SELECT a.n + b.n * 10 + c.n * 100 + d.n * 1000 AS n
        FROM (SELECT 0 AS n UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) a
        CROSS JOIN (SELECT 0 AS n UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) b
        CROSS JOIN (SELECT 0 AS n UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) c
        CROSS JOIN (SELECT 0 AS n UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) d
        ) seq
        WHERE DATE_ADD(DATE(w.create_time), INTERVAL seq.n DAY) &lt; CURDATE()
        )
        WHEN DATEDIFF(CURDATE(), (SELECT MAX(DATE(a.pub_time)) FROM t_article a WHERE a.website_id = w.id AND a.is_index = 1)) &lt;= 0 THEN
        -- 如果最后更新时间就是昨天或更晚，则没有连续未更新的日期
        NULL
        ELSE
        -- 从最后更新日期的下一天到昨天
        (SELECT GROUP_CONCAT(
        DATE_ADD((SELECT MAX(DATE(a2.pub_time)) FROM t_article a2 WHERE a2.website_id = w.id AND a2.is_index = 1), INTERVAL seq.n + 1 DAY)
        ORDER BY seq.n SEPARATOR ','
        )
        FROM (
        SELECT a.n + b.n * 10 + c.n * 100 + d.n * 1000 AS n
        FROM (SELECT 0 AS n UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) a
        CROSS JOIN (SELECT 0 AS n UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) b
        CROSS JOIN (SELECT 0 AS n UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) c
        CROSS JOIN (SELECT 0 AS n UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) d
        ) seq
        WHERE DATE_ADD((SELECT MAX(DATE(a3.pub_time)) FROM t_article a3 WHERE a3.website_id = w.id AND a3.is_index = 1), INTERVAL seq.n + 1 DAY) &lt; CURDATE()
        )
        END AS continuous_not_update_days_desc,

        -- 最后更新时间（便于验证）
        (SELECT MAX(a.pub_time) FROM t_article a WHERE a.website_id = w.id AND a.is_index = 1 AND a.pub_time &lt; CURDATE()) AS last_update_time,

        CURRENT_DATE() AS update_time,
        0 as process_type

        FROM t_website w
        WHERE w.status = 1 AND w.is_del = 0 AND process_type=0
        ORDER BY w.id DESC
    </select>


</mapper>