<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dahe.dao.ChannelDao">

    <sql id="processTypesCondition">
        <choose>
            <when test="query.processTypes != null and query.processTypes.size > 0">
                and tc.process_type in
                <foreach collection="query.processTypes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                and false
            </otherwise>
        </choose>
    </sql>
    <sql id="channelCondition">
        <if test="query.channelIds != null and query.channelIds.size() > 0">
            AND tc.id IN
            <foreach collection="query.channelIds" item="channelId" open="(" separator="," close=")">
                #{channelId}
            </foreach>
        </if>
    </sql>
    <resultMap id="BaseResultMap" type="cn.dahe.entity.Channel">
        <!--@Table t_channel-->
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="url" property="url"/>
        <result column="enable" property="enable"/>
        <result column="async" property="async"/>
        <result column="site_id" property="siteId"/>
        <result column="site_name" property="siteName"/>
        <result column="create_time" property="createTime"/>
        <result column="last_modify_time" property="lastModifyTime"/>
        <result column="process_type" property="processType"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="create_user_name" property="createUserName"/>
    </resultMap>
    <select id="getCountChannelsByWebsiteAndProcessType" resultType="int">
        SELECT count(*)
        FROM t_channel tc
        LEFT JOIN t_website w ON tc.site_id = w.site_id
        <where>
            w.is_del = 0
            AND w.status = 1
            AND w.process_type = 0
            AND tc.process_type = 0
            AND tc.enable = 1
            <if test="websiteIds != null and websiteIds.size() > 0">
                AND tc.site_id IN
                <foreach collection="websiteIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="channelName != null and channelName != ''">
                AND tc.name LIKE CONCAT('%', #{channelName}, '%')
            </if>
            <if test="channelTypeId != null">
                AND tc.channel_type_id = #{channelTypeId}
            </if>
        </where>
    </select>
    <select id="getChannelsByWebsiteAndProcessType" resultType="cn.dahe.entity.Channel">
        SELECT tc.*
        FROM t_channel tc
        LEFT JOIN t_website w ON tc.site_id = w.site_id
        <where>
            w.is_del = 0
            AND w.status = 1
            AND w.process_type = 0
            AND tc.process_type = 0
            AND tc.enable = 1
            <if test="websiteIds != null and websiteIds.size() > 0">
                AND tc.site_id IN
                <foreach collection="websiteIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="channelName != null and channelName != ''">
                AND tc.name LIKE CONCAT('%', #{channelName}, '%')
            </if>
            <if test="channelTypeId != null">
                AND tc.channel_type_id = #{channelTypeId}
            </if>
            <!-- 分页 -->
            <if test="limit != null and page != null">
                LIMIT #{page}, #{limit}
            </if>
        </where>
    </select>

    <!-- 根据栏目类型ID查询栏目列表 -->
    <select id="selectChannelsByChannelTypeId" resultType="cn.dahe.entity.Channel">
        SELECT id, name, channel_type_id, update_period, last_modify_time
        FROM t_channel
        WHERE channel_type_id = #{channelTypeId}
    </select>

    <!-- 批量更新栏目的更新期限 -->
    <update id="updateChannelUpdatePeriodByTypeId">
        UPDATE t_channel
        SET update_period = #{updatePeriod},
            last_modify_time = NOW()
        WHERE channel_type_id = #{channelTypeId}
    </update>

    <!-- 批量重置栏目的栏目类型ID和更新期限 -->
    <update id="resetChannelTypeAndUpdatePeriodByTypeId">
        UPDATE t_channel
        SET channel_type_id = 0,
            update_period = 0,
            last_modify_time = NOW()
        WHERE channel_type_id = #{channelTypeId}
    </update>

    <insert id="insertById">
        INSERT INTO t_channel (id, name, url, site_id, site_name, process_type,create_time, enable)
        VALUES (#{channel.id}, #{channel.name}, #{channel.url}, #{channel.siteId}, #{channel.siteName}, #{channel.processType}, #{channel.createTime}, 1)
    </insert>


</mapper>
