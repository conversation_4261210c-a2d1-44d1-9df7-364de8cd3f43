<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dahe.dao.WebSiteChannelCheckDao">

    <!-- 根据栏目ID和检查日期查询记录 -->
    <select id="selectByColumnIdAndDate" resultType="cn.dahe.entity.WebSiteChannelCheck">
        SELECT id, website_id, column_id, column_name, update_count, last_update_time, check_time, is_update
        FROM web_site_channel_check
        WHERE column_id = #{columnId}
          AND DATE(check_time) = #{checkDate}
        ORDER BY check_time DESC
        LIMIT 1
    </select>

    <!-- 根据栏目ID和日期范围查询检查记录 -->
    <select id="selectByColumnIdAndDateRange" resultType="cn.dahe.entity.WebSiteChannelCheck">
        SELECT id, website_id, column_id, column_name, update_count, last_update_time, check_time, is_update
        FROM web_site_channel_check
        WHERE column_id = #{columnId}
          AND DATE(check_time) &gt;= #{startDate}
          AND DATE(check_time) &lt;= #{endDate}
        ORDER BY check_time DESC
    </select>
    
    <!-- 批量根据栏目ID列表和日期范围查询检查记录 -->
    <select id="selectByColumnIdsAndDateRange" resultType="cn.dahe.entity.WebSiteChannelCheck">
        SELECT id, website_id, column_id, column_name, update_count, last_update_time, check_time, is_update
        FROM web_site_channel_check
        WHERE column_id IN
        <foreach collection="columnIds" item="columnId" open="(" separator="," close=")">
            #{columnId}
        </foreach>
          AND DATE(check_time) &gt;= #{startDate}
          AND DATE(check_time) &lt;= #{endDate}
        ORDER BY column_id, check_time DESC
    </select>

</mapper>