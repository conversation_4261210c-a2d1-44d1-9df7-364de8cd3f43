<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dahe.dao.CheckWordDao">

    <sql id="filterStatusCondition">
        <if test="query.filterStatuses != null and query.filterStatuses.size() > 0">
            AND tcw.filter_status IN
            <foreach collection="query.filterStatuses" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
    </sql>
    <sql id="errorLevelCondition">
        <if test="query.errorLevels != null and query.errorLevels.size() > 0">
            AND tcw.error_level IN
            <foreach collection="query.errorLevels" item="level" open="(" separator="," close=")">
                #{level}
            </foreach>
        </if>
    </sql>
    <sql id="errorTypeCondition">
        <if test="query.firstErrorTypes != null and query.firstErrorTypes.size() > 0">
            and tcw.first_error_type IN
            <foreach collection="query.firstErrorTypes" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="query.secondErrorTypes != null and query.secondErrorTypes.size() > 0">
            and tcw.second_error_type IN
            <foreach collection="query.secondErrorTypes" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="query.thirdErrorTypes != null and query.thirdErrorTypes.size() > 0">
            and tcw.third_error_type IN
            <foreach collection="query.thirdErrorTypes" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </sql>
    <sql id="errorTypeAndLevelCondition">
        <include refid="cn.dahe.dao.CheckWordDao.errorLevelCondition"/>
        <include refid="cn.dahe.dao.CheckWordDao.errorTypeCondition"/>
    </sql>
    <sql id="wordCondition">
        <if test="query.errorWord != null and query.errorWord != ''">
            and tcw.error_word LIKE CONCAT('%',#{query.errorWord},'%')
        </if>
        <if test="query.suggestWord != null and query.suggestWord != ''">
            and tcw.suggest_word LIKE CONCAT('%',#{query.suggestWord},'%')
        </if>
    </sql>
    <sql id="keywordCondition">
        <if test="query.keyword != null and query.keyword != '' ">
            AND (
            tcw.error_word like concat('%',#{query.keyword},'%') or
            tcw.suggest_word like concat('%',#{query.keyword},'%')
            )
        </if>
    </sql>

    <!-- 根据错误词和建议词查询记录 -->
    <select id="selectDistinctWord" resultType="cn.dahe.entity.CheckWord">
        SELECT *
        FROM t_check_word tcw
        WHERE error_word = #{errorWord}
    </select>

    <!-- 批量根据错误词查询记录 -->
    <select id="selectBatchDistinctWord" resultType="cn.dahe.entity.CheckWord">
        SELECT *
        FROM t_check_word
        <where>
            <choose>
                <when test="errorWords != null and errorWords.size() > 0">
                    AND error_word IN
                    <foreach collection="errorWords" item="errorWord" open="(" separator="," close=")">
                        #{errorWord}
                    </foreach>
                </when>
                <otherwise>
                    AND false
                </otherwise>
            </choose>
        </where>
    </select>

</mapper> 