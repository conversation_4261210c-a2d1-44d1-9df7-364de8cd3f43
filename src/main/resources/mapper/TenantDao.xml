<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dahe.dao.TenantDao">
    <resultMap id="BaseResultMap" type="cn.dahe.entity.Tenant">
        <!--@Table t_tenant-->
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="status" property="status"/>
        <result column="user_count" property="userCount"/>
        <result column="site_count" property="siteCount"/>
        <result column="new_media_count" property="newMediaCount"/>
        <result column="group_count" property="groupCount"/>
        <result column="team_count" property="teamCount"/>
        <result column="warn_plan_count" property="warnPlanCount"/>
        <result column="remark" property="remark"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="last_update_user_id" property="lastUpdateUserId"/>
        <result column="delete_user_id" property="deleteUserId"/>
        <result column="expire_date" property="expireDate"/>
        <result column="create_time" property="createTime"/>
        <result column="last_update_time" property="lastUpdateTime"/>
        <result column="delete_time" property="deleteTime"/>
        <result column="create_user_name" property="createUserName"/>
        <result column="last_update_user_name" property="lastUpdateUserName"/>
    </resultMap>

    <select id="selectNotDeletePage" resultMap="BaseResultMap">
        select t.*, u1.user_name create_user_name, u2.user_name last_update_user_name
        from t_tenant t
        left join t_user u1
        on u1.user_id = t.create_user_id
        left join t_user u2
        on u2.user_id = t.last_update_user_id
        where t.status != 4
        <if test="status != null">
            and t.status=#{status}
        </if>
        <if test="likeName != null">
            and t.name like concat('%',#{likeName},'%')
        </if>
        order by t.id desc
    </select>

    <select id="selectEnableByUserId" resultMap="BaseResultMap">
        select t.*
        from t_tenant t
                 join t_user_tenant ut on t.id = ut.tenant_id
        where ut.user_id = #{userId}
          and t.status = 1
    </select>

    <select id="selectNotDeleteByUserId" resultMap="BaseResultMap">
        select t.*
        from t_tenant t
                 join t_user_tenant ut on t.id = ut.tenant_id
        where ut.user_id = #{userId}
          and t.status != 4
    </select>
</mapper>
