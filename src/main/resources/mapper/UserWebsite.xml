<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.dahe.dao.UserWebsiteDao">
    <select id="listWebsiteIdsByUserId" resultType="java.lang.Long">
        select website_id
        from t_user_website tuw
        where tuw.user_id = #{userId}
    </select>

    <select id="selectWebsiteIdByUserIdAndProcessTypeIn" resultType="long">
        select uw.website_id
        from t_user_website uw
        join t_website w on uw.website_id = w.id
        where uw.user_id = #{userId}
        and w.process_type in
        <foreach collection="platformSet" item="platform" open="(" separator="," close=")">
            #{platform}
        </foreach>
        and w.status = 1
    </select>

    <delete id="deleteByUserIdAndProcessTypeIn">
        delete uw
        from t_user_website uw
        join t_website w on uw.website_id = w.id
        where uw.user_id = #{userId}
        and w.process_type in
        <foreach collection="platformSet" item="platform" open="(" separator="," close=")">
            #{platform}
        </foreach>
    </delete>
</mapper>