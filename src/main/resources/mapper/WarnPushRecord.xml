<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dahe.dao.WarnPushRecordDao">

    <select id="listByWarnPlanId" resultType="cn.dahe.entity.WarnPushRecord">
        SELECT * FROM t_warn_push_record WHERE warn_plan_id = #{warnPlanId}
    </select>

    <select id="list" resultType="cn.dahe.model.vo.WarnPushRecordVO">
        SELECT 
            wpr.id,
            wpr.actual_push_time,
            wpr.source_platform,
            wpr.warn_source_name,
            wpr.website_id,
            wpr.warn_plan_id,
            wpr.receiver_user_id,
            wpr.warn_type,
            wpr.warn_push_type,
            wpr.warn_link,
            wpr.is_deleted,
            wpr.data,
            wpr.msg_id,
            wp.scheme_name
        FROM t_warn_push_record wpr
        LEFT JOIN t_warn_plan wp ON wpr.warn_plan_id = wp.id
        <where>
            wpr.is_deleted = 0
            <if test="query.pushTimeStart != null">
                AND wpr.actual_push_time &gt;= #{query.pushTimeStart}
            </if>
            <if test="query.pushTimeEnd != null">
                AND wpr.actual_push_time &lt;= #{query.pushTimeEnd}
            </if>
            <if test="query.sourcePlatforms != null and query.sourcePlatforms.size() &gt; 0">
                AND wpr.source_platform IN
                <foreach collection="query.sourcePlatforms" item="sp" open="(" separator="," close=")">
                    #{sp}
                </foreach>
            </if>
            <if test="query.warnSourceName != null and query.warnSourceName != ''">
                AND wpr.warn_source_name LIKE CONCAT(#{query.warnSourceName}, '%')
            </if>
            <if test="query.warnPlanIds != null and query.warnPlanIds.size() &gt; 0">
                AND wpr.warn_plan_id IN
                <foreach collection="query.warnPlanIds" item="pid" open="(" separator="," close=")">
                    #{pid}
                </foreach>
            </if>
            <if test="query.receiverUserIds != null and query.receiverUserIds.size() &gt; 0">
                AND wpr.receiver_user_id IN
                <foreach collection="query.receiverUserIds" item="uid" open="(" separator="," close=")">
                    #{uid}
                </foreach>
            </if>
            <if test="query.warnType != null">
                AND wpr.warn_type = #{query.warnType}
            </if>
            <if test="query.warnPushType != null">
                AND wpr.warn_push_type = #{query.warnPushType}
            </if>
        </where>
        ORDER BY wpr.actual_push_time DESC
    </select>

</mapper>


