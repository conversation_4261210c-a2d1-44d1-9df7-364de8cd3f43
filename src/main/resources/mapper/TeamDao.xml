<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dahe.dao.TeamDao">
    <resultMap id="BaseResultMap" type="cn.dahe.entity.Team">
        <!--@Table t_team-->
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="remark" property="remark"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="last_update_user_id" property="lastUpdateUserId"/>
        <result column="delete_user_id" property="deleteUserId"/>
        <result column="create_time" property="createTime"/>
        <result column="last_update_time" property="lastUpdateTime"/>
        <result column="delete_time" property="deleteTime"/>
        <result column="create_user_name" property="createUserName"/>
        <result column="last_update_user_name" property="lastUpdateUserName"/>
        <result column="tenant_name" property="tenantName"/>
    </resultMap>

    <select id="selectPage" resultMap="BaseResultMap">
        select t.*, u1.user_name create_user_name, u2.user_name last_update_user_name, t1.name tenant_name
        from t_team t
        left join t_user u1
        on u1.user_id = t.create_user_id
        left join t_user u2
        on u2.user_id = t.last_update_user_id
        left join t_tenant t1
        on t1.id = t.tenant_id
        where t.delete_time = 0
        <if test="likeName != null">
            and t.name like concat('%',#{likeName},'%')
        </if>
        <if test="tenantId != null">
            and t.tenant_id=#{tenantId}
        </if>
        order by t.id desc
    </select>
</mapper>
