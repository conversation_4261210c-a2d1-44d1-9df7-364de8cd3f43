<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.dahe.dao.WebsiteGroupDao">


    <sql id="groupNameCondition">
        <if test="query.groupName != null and query.groupName != ''">
            and twg.group_name like concat('%',#{query.groupName},'%')
        </if>
    </sql>

    <sql id="groupIdCondition">
        <if test="query.groupId != null">
            <choose>
                <when test="query.groupId == 0">
                    and twg.id is null
                </when>
                <otherwise>
                    and twg.id= #{query.groupId}
                </otherwise>
            </choose>
        </if>
    </sql>

    <resultMap id="WebsiteGroupVOMap" type="cn.dahe.model.vo.WebsiteGroupVO">
        <id property="id" column="group_id"/>
        <result property="groupName" column="group_name"/>
        <collection property="websites" ofType="cn.dahe.model.vo.WebsiteVO">
            <id property="id" column="website_id"/>
            <result property="websiteId" column="website_id"/>
            <result property="siteId" column="site_id"/>
            <result property="channelId" column="channel_id"/>
            <result property="status" column="status"/>
            <result property="name" column="name"/>
            <result property="url" column="url"/>
            <result property="processType" column="process_type"/>
            <result property="groupId" column="website_group_id"/>
            <result property="checkStrategy" column="check_strategy"/>
        </collection>
    </resultMap>

    <sql id="twColumn">
        tw.id as website_id,
        tw.site_id,
        tw.channel_id,
        tw.status,
        tw.web_name as name,
        tw.web_url as url,
        tw.group_id as website_group_id,
        tw.check_strategy,
        tw.process_type
    </sql>

    <select id="listSelectByQuery" resultMap="WebsiteGroupVOMap">
        select * from(
        SELECT
        twg.id as group_id,
        twg.group_name,
        <include refid="twColumn"/>
        FROM t_website_group twg
        LEFT JOIN t_website tw ON twg.id = tw.group_id AND tw.is_del = 0
        <if test="query.showDisable == null or !query.showDisable">
            AND tw.status = 1
        </if>
        <include refid="cn.dahe.dao.WebsiteDao.searchTypeCondition"/>
        <include refid="cn.dahe.dao.WebsiteDao.processTypeCondition"/>
        WHERE twg.is_del = 0
        ) as twg
        <where>
            <include refid="groupNameCondition"/>
        </where>
        ORDER BY group_id, process_type, website_id
    </select>
    <select id="listAvailableGroup" resultType="cn.dahe.model.vo.WebsiteGroupVO">
        SELECT 0        as id,
               '未分组' as group_name
        UNION ALL
        select id,
               group_name
        from t_website_group twg
        WHERE twg.is_del = 0
    </select>

</mapper> 