<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dahe.dao.CheckErrorTypeDao">
    
    <resultMap id="BaseResultMap" type="cn.dahe.entity.CheckErrorType">
        <id column="id" property="id"/>
        <result column="type_name" property="typeName"/>
        <result column="parent_id" property="parentId"/>
        <result column="level" property="level"/>
        <result column="error_level" property="errorLevel"/>
        <result column="sort_order" property="sortOrder"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 错误类型树形结构的结果映射 -->
    <resultMap id="TypeTreeMap" type="cn.dahe.model.vo.check.FirstLevelErrorTypeVO">
        <id column="l1_id" property="id"/>
        <result column="l1_name" property="name"/>
        <result column="l1_level" property="level"/>
        <collection property="children" ofType="cn.dahe.model.vo.check.SecondLevelErrorTypeVO">
            <id column="l2_id" property="id"/>
            <result column="l2_name" property="name"/>
            <result column="l2_parent_id" property="parentId"/>
            <result column="l2_error_level" property="errorLevel"/>
            <result column="l2_level" property="level"/>
            <collection property="children" ofType="cn.dahe.model.vo.check.ThirdLevelErrorTypeVO">
                <id column="l3_id" property="id"/>
                <result column="l3_name" property="name"/>
                <result column="l3_parent_id" property="parentId"/>
                <result column="l3_level" property="level"/>
            </collection>
        </collection>
    </resultMap>

    <!-- 获取错误类型树 -->
    <select id="getTypeTree" resultMap="TypeTreeMap">
        SELECT 
            t1.id as l1_id,
            t1.type_name as l1_name,
            t1.level as l1_level,
            t2.id as l2_id,
            t2.type_name as l2_name,
            t2.parent_id as l2_parent_id,
            t2.error_level as l2_error_level,
            t2.level as l2_level,
            t3.id as l3_id,
            t3.type_name as l3_name,
            t3.parent_id as l3_parent_id,
            t3.level as l3_level
        FROM t_check_error_type t1
        LEFT JOIN t_check_error_type t2 ON t2.parent_id = t1.id AND t2.level = 2
        LEFT JOIN t_check_error_type t3 ON t3.parent_id = t2.id AND t3.level = 3
        WHERE t1.level = 1
        ORDER BY t1.sort_order, t2.sort_order, t3.sort_order
    </select>

    <select id="listTypeName" resultType="cn.dahe.model.vo.check.BaseErrorTypeVO">
        SELECT t1.id                                                                        as id,
               IF(t2.id IS NOT NULL, concat(t2.type_name, '-', t1.type_name), t1.type_name) as name
        FROM t_check_error_type t1
                 LEFT JOIN t_check_error_type t2 ON t1.parent_id = t2.id AND t2.level = 2
        WHERE t1.level in (2, 3)
    </select>

</mapper> 