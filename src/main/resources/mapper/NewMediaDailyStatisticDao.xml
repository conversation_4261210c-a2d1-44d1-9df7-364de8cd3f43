<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dahe.dao.NewMediaDailyStatisticDao">
    <resultMap id="BaseResultMap" type="cn.dahe.entity.NewMediaDailyStatistic">
        <!--@Table t_new_media_daily_statistic-->
        <id column="id" property="id"/>
        <result column="new_media_id" property="newMediaId"/>
        <result column="article_total" property="articleTotal"/>
        <result column="new_media_last_update_time" property="newMediaLastUpdateTime"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <sql id="statisticBaseSelect">
        select new_media_id, article_total, new_media_last_update_time, create_time
        from t_new_media_daily_statistic
        <where>
            <if test="startTime != null">
                and create_time >= #{startTime}
            </if>
            <if test="endTime != null">
                and create_time &lt; #{endTime}
            </if>
        </where>
    </sql>

    <sql id="sequenceSelect">
        select jq.*, @serial := if(@prev = jq.new_media_id, @serial + 1, 1) serial, @prev := jq.new_media_id
        from (<include refid="statisticBaseSelect"/>) jq join (select @serial := 0, @prev := 0) cs
        order by jq.new_media_id, jq.create_time desc
    </sql>

    <sql id="updateSelect">
        select new_media_id, coalesce(min(case when article_total > 0 then serial end), max(serial)) serial
        from (<include refid="sequenceSelect"/>) xl
        group by new_media_id
    </sql>

    <sql id="noUpdateSelect">
        select xl.new_media_id, sum(if(xl.serial &lt;= gx.serial and xl.article_total = 0, 1, 0)) day
        from (<include refid="sequenceSelect"/>) xl join (<include refid="updateSelect"/>) gx on gx.new_media_id = xl.new_media_id
        group by xl.new_media_id
    </sql>

    <select id="selectStatisticPage" resultType="map">
        select w.id, w.web_name name, w.web_url link, wg.group_name groupName, tg.day noUpdateDay,
        case w.process_type
        when 1 then '公众号'
        when 2 then '微博'
        when 3 then '头条号'
        when 6335 then '抖音号'
        when 6429 then '快手号'
        when 6452 then '视频号'
        when 6460 then '小红书'
        else cast(w.process_type as char)
        end platform,
        if(sum(if(jq.article_total > 0, 1, 0)) > 0, 1, 0) updated,
        sum(if(jq.article_total > 0, 1, 0)) updateDay,
        coalesce(date_format(max(case when jq.article_total > 0 then jq.new_media_last_update_time end), '%Y-%m-%d %H:%i:%s'), '') lastUpdateTime,
        sum(jq.article_total) total
        from t_website w
        left join t_website_group wg on wg.id = w.group_id
        join (<include refid="statisticBaseSelect"/>) jq on jq.new_media_id = w.id
        join (<include refid="noUpdateSelect"/>) tg on tg.new_media_id = w.id
        <if test="defaultRole">
            join t_user_website uw on w.id = uw.website_id
        </if>
        where w.is_del = 0 and w.status = 1
        <if test="tenantId != null">
            and w.tenant_id = #{tenantId}
        </if>
        <if test="defaultRole">
            and uw.user_id = #{user.id}
        </if>
        <if test="platformSet != null and platformSet.size() > 0">
            and w.process_type in
            <foreach collection="platformSet" item="platform" open="(" separator="," close=")">
                #{platform}
            </foreach>
        </if>
        <if test="groupIdSet != null and groupIdSet.size() > 0">
            and w.group_id in
            <foreach collection="groupIdSet" item="groupId" open="(" separator="," close=")">
                #{groupId}
            </foreach>
        </if>
        <if test="newMediaIdSet != null and newMediaIdSet.size() > 0">
            and w.id in
            <foreach collection="newMediaIdSet" item="newMediaId" open="(" separator="," close=")">
                #{newMediaId}
            </foreach>
        </if>
        <if test="noUpdateDay != null">
            and tg.day >= #{noUpdateDay}
        </if>
        group by w.id, w.web_name, wg.group_name
        <choose>
            <when test="updated">
                having sum(if(jq.article_total > 0, 1, 0)) > 0
            </when>
            <when test="!updated">
                having sum(if(jq.article_total > 0, 1, 0)) = 0
            </when>
        </choose>
        order by
        <choose>
            <when test="sort == 1">
                updateDay desc
            </when>
            <when test="sort == 2">
                total desc
            </when>
            <otherwise>
                noUpdateDay desc
            </otherwise>
        </choose>
    </select>

    <select id="selectStatistic" resultType="map">
        select count(distinct w.id) newMediaTotal,
        sum(nmds.article_total) articleTotal,
        count(distinct case when nmds.article_total > 0 then w.id end) updateNewMediaTotal
        from t_website w
        join t_new_media_daily_statistic nmds on nmds.new_media_id = w.id
        <if test="defaultRole">
            join t_user_website uw on w.id = uw.website_id
        </if>
        where w.is_del = 0 and w.status = 1
        and w.tenant_id = #{tenantId}
        <if test="defaultRole">
            and uw.user_id = #{userId}
        </if>
        <if test="platformSet != null and platformSet.size() > 0">
            and w.process_type in
            <foreach collection="platformSet" item="platform" open="(" separator="," close=")">
                #{platform}
            </foreach>
        </if>
        <if test="groupIdSet != null and groupIdSet.size() > 0">
            and w.group_id in
            <foreach collection="groupIdSet" item="groupId" open="(" separator="," close=")">
                #{groupId}
            </foreach>
        </if>
        <if test="newMediaIdSet != null and newMediaIdSet.size() > 0">
            and w.id in
            <foreach collection="newMediaIdSet" item="newMediaId" open="(" separator="," close=")">
                #{newMediaId}
            </foreach>
        </if>
        <if test="startTime != null">
            and nmds.create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            and nmds.create_time &lt; #{endTime}
        </if>
    </select>

    <select id="selectStatisticTotalPage" resultMap="BaseResultMap">
        select nmds.*
        from t_new_media_daily_statistic nmds
        join t_website w on nmds.new_media_id = w.id
        where w.is_del = 0 and w.status = 1
        and nmds.new_media_id = #{newMediaId}
        <if test="startTime != null">
            and nmds.create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            and nmds.create_time &lt; #{endTime}
        </if>
        order by nmds.id desc
    </select>

    <select id="selectArticlePage" resultType="cn.dahe.entity.Article">
        select a.*,
        case w.process_type
        when 1 then '公众号'
        when 2 then '微博'
        when 3 then '头条号'
        when 6335 then '抖音号'
        when 6429 then '快手号'
        when 6452 then '视频号'
        when 6460 then '小红书'
        else cast(w.process_type as char)
        end platform
        from t_article a
        join t_website w on a.channel_id = w.channel_id
        left join t_website_group wg on w.group_id = wg.id
        <if test="defaultRole">
            join t_user_website uw on w.id = uw.website_id
        </if>
        where w.is_del = 0 and w.status = 1
        and w.tenant_id = #{tenantId}
        <if test="defaultRole">
            and uw.user_id = #{userId}
        </if>
        <if test="platformSet != null and platformSet.size() > 0">
            and w.process_type in
            <foreach collection="platformSet" item="platform" open="(" separator="," close=")">
                #{platform}
            </foreach>
        </if>
        <if test="groupIdSet != null and groupIdSet.size() > 0">
            and w.group_id in
            <foreach collection="groupIdSet" item="groupId" open="(" separator="," close=")">
                #{groupId}
            </foreach>
        </if>
        <if test="newMediaIdSet != null and newMediaIdSet.size() > 0">
            and w.id in
            <foreach collection="newMediaIdSet" item="newMediaId" open="(" separator="," close=")">
                #{newMediaId}
            </foreach>
        </if>
        <if test="startTime != null">
            and a.pub_time >= #{startTime}
        </if>
        <if test="endTime != null">
            and a.pub_time &lt; #{endTime}
        </if>
        <if test="keyword != null and keyword != ''">
            and a.title like concat('%', #{keyword}, '%')
        </if>
        order by
        <choose>
            <when test="sort == 1">
                a.pub_time
            </when>
            <otherwise>
                a.pub_time desc
            </otherwise>
        </choose>
    </select>

    <select id="selectPage" resultType="map">
        select w.id, w.web_name, w.web_url, w.status, wg.group_name,
        date_format(w.create_time, '%Y-%m-%d %H:%i:%s') create_time,
        case w.process_type
        when 1 then '公众号'
        when 2 then '微博'
        when 3 then '头条号'
        when 6335 then '抖音号'
        when 6429 then '快手号'
        when 6452 then '视频号'
        when 6460 then '小红书'
        else cast(w.process_type as char)
        end platform
        from t_website w
        left join t_website_group wg on wg.id = w.group_id
        <if test="defaultRole">
            join t_user_website uw on w.id = uw.website_id
        </if>
        where w.is_del = 0
        and w.tenant_id = #{tenantId}
        <if test="defaultRole">
            and uw.user_id = #{userId}
        </if>
        <if test="status != null">
            and w.status = #{status}
        </if>
        <if test="name != null and name != ''">
            and w.web_name like concat('%', #{name}, '%')
        </if>
        <if test="link != null and link != ''">
            and w.web_url like concat('%', #{link}, '%')
        </if>
        <if test="platformSet != null and platformSet.size() > 0">
            and w.process_type in
            <foreach collection="platformSet" item="platform" open="(" separator="," close=")">
                #{platform}
            </foreach>
        </if>
        <if test="groupIdSet != null and groupIdSet.size() > 0">
            and w.group_id in
            <foreach collection="groupIdSet" item="groupId" open="(" separator="," close=")">
                #{groupId}
            </foreach>
        </if>
        <if test="newMediaIdSet != null and newMediaIdSet.size() > 0">
            and w.id in
            <foreach collection="newMediaIdSet" item="newMediaId" open="(" separator="," close=")">
                #{newMediaId}
            </foreach>
        </if>
        <!-- 排序：先按创建时间倒序，再按新媒体名称，最后按平台类型 -->
        order by w.create_time desc, w.web_name, platform
    </select>

    <insert id="insertByWebsite">
        insert ignore t_new_media_daily_statistic(new_media_id, article_total, create_time)
        select id, 0, curdate()
        from t_website
        where is_del = 0
          and process_type > 0
    </insert>

    <update id="updateByArticle">
        update t_new_media_daily_statistic
        set article_total              = article_total + 1,
            new_media_last_update_time = greatest(ifnull(new_media_last_update_time, '1970-01-01 00:00:00'), #{pubTime})
        where new_media_id = #{websiteId}
          and create_time = date(#{pubTime})
    </update>
</mapper>