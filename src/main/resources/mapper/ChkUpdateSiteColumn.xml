<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.dahe.dao.ChkUpdateSiteColumnDao">

    <resultMap id="ChkUpdateSiteColumnVOMap" type="cn.dahe.model.vo.ChkUpdateSiteColumnVO">
        <id property="id" column="id"/>
        <result property="serialNumber" column="serial_number"/>

        <!-- 基础字段 -->
        <result property="siteId" column="site_id"/>
        <result property="siteName" column="site_name"/>
        <result property="groupId" column="group_id"/>
        <result property="groupName" column="group_name"/>
        <result property="columnId" column="column_id"/>
        <result property="columnName" column="column_name"/>
        <!-- 扩展字段 -->
        <result property="updateDays" column="update_days"/>
        <result property="updateDaysDetail" column="update_days_detail"/>
        <result property="continuousNotUpdateDaysDetail" column="continuous_not_update_days_detail"/>
        <result property="continuousNotUpdateDays" column="continuous_not_update_days"/>
        <result property="lastUpdateTime" column="last_update_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <resultMap id="ChkUpdateSiteColumnDetailVOMap" type="cn.dahe.model.vo.ChkUpdateSiteColumnDetailVO">
        <id property="id" column="id"/>
        <result property="serialNumber" column="serial_number"/>

        <!-- 基础字段 -->
        <result property="articleId" column="article_id"/>
        <result property="articleUrl" column="article_url"/>
        <result property="articleTitle" column="article_title"/>
        <result property="focusImgUrl" column="focus_img_url"/>
        <result property="summary" column="summary"/>
        <result property="content" column="content"/>
        <result property="source" column="source"/>
        <result property="province" column="province"/>
        <result property="classify" column="classify"/>
        <result property="siteClassify" column="site_classify"/>
        <result property="boost" column="boost"/>
        <result property="sens" column="sens"/>
        <result property="pubTime" column="pub_time"/>
        <result property="siteId" column="site_id"/>
        <result property="siteName" column="site_name"/>
        <result property="columnId" column="column_id"/>
        <result property="updateTime" column="update_time"/>
        <result property="checkStatus" column="check_status"/>
        <result property="snapshotUrl" column="snapshot_url"/>
        <result property="snapshotSignature" column="snapshot_signature"/>
        <result property="parseTime" column="parse_time"/>
        <result property="createTime" column="create_time"/>
        <result property="isDel" column="is_del"/>

        <!-- 扩展字段 -->
        <result property="checkStatusDesc" column="check_status_desc"/>
        <result property="continuousNotUpdateDays" column="continuous_not_update_days"/>
        <result property="operation" column="operation"/>
        <result property="groupName" column="group_name"/>
        <result property="columnName" column="column_name"/>
        <result property="columnCategory" column="column_category"/>
        <result property="columnUrl" column="column_url"/>
    </resultMap>

    <!-- ==================== 栏目更新检查概览 ==================== -->

    <select id="getOverviewStatistics" resultType="cn.dahe.model.vo.ChkUpdateSiteColumnCountVO">
        SELECT
        -- 检测状态统计
        COALESCE(
        (select count(*) from t_channel ch where ch.process_type=0 <include refid="commonWhereCountClause"/>),
        0
        ) as totalColumnCount,
        COALESCE(
        0,0
        ) as normalColumnCount,
        COALESCE(
        0,0
        ) as collectAbnormalCount,
        COALESCE(
        0,0
        ) as noCheckUpdateCount,

        -- 检测结果统计
        COALESCE(
        0,0
        ) AS totalCheckResultCount,

        COALESCE(
        0,0
        ) AS normalCheckCount,

        COALESCE(
        0,0
        ) AS seriousOverdueCount,

        COALESCE(
        0,0
        ) AS aboutToOverdueCount

    </select>

    <!-- ==================== 栏目更新检查记录 ==================== -->

    <select id="selectPageWithExtInfo" resultMap="ChkUpdateSiteColumnVOMap">
        SELECT
        t.id,
        t.site_id,
        t.site_name,
        t.group_id,
        t.group_name,
        t.column_id,
        t.column_name,
        t.continuous_not_update_days,
        t.continuous_not_update_days_detail,
        t.update_days,
        t.update_days_detail,
        t.update_time,
        t.last_update_time
        FROM chk_update_site_column_result t
        WHERE t.process_type=0 AND t.update_time = CURRENT_DATE()
        <include refid="commonWherePageClause"/>
        ORDER BY t.id DESC
    </select>

    <select id="selectDetailById" resultMap="ChkUpdateSiteColumnDetailVOMap">
        SELECT
        ta.*
        FROM t_article ta
        LEFT JOIN t_website w ON ta.site_id = w.id
        LEFT JOIN t_website_group wg ON w.group_id = wg.id
        LEFT JOIN t_channel ch ON ta.channel_id = ch.id
        WHERE ta.channel_id = #{id}
        AND ta.site_id > 0
        AND ta.channel_id is not null
        AND ta.update_time &lt; CURRENT_DATE()
    </select>

    <!-- ==================== 筛选条件 ==================== -->
    <select id="selectExportData" resultType="map">
        SELECT
        t.id,
        t.site_id,
        t.site_name,
        t.group_id,
        t.group_name,
        t.column_id,
        t.column_name,
        t.continuous_not_update_days,
        t.continuous_not_update_days_detail,
        t.update_days,
        t.update_days_detail,
        t.update_time,
        t.last_update_time
        FROM chk_update_site_column_result t
        WHERE t.process_type=0 AND t.update_time = CURRENT_DATE()
        <include refid="commonWherePageClause"/>
        ORDER BY t.id DESC
    </select>

    <sql id="commonWhereCountClause">
        <if test="query.siteId != null and query.siteId != ''">
            AND FIND_IN_SET(site_id, #{query.siteId})
        </if>
        <if test="query.groupId != null and query.groupId != '' and query.type != null and query.type == 1">
            AND FIND_IN_SET(group_id, #{query.groupId})
        </if>
    </sql>

    <sql id="commonWherePageClause">
        <if test="query.processType == null or query.processType == '' or query.processType == 0">
            AND (t.process_type is null or t.process_type = 0)
        </if>
        <if test="query.ids != null and query.ids != ''">
            AND FIND_IN_SET(t.id, #{query.ids})
        </if>
        <if test="query.siteId != null and query.siteId != ''">
            AND FIND_IN_SET(t.site_id, #{query.siteId})
        </if>
        <if test="query.groupId != null and query.groupId != '' and query.type != null and query.type == 1">
            AND FIND_IN_SET(t.group_id, #{query.groupId})
        </if>
        <if test="query.keyword != null and query.keyword != ''">
            AND t.column_name LIKE CONCAT('%', #{query.keyword}, '%')
        </if>
        <if test="query.xpathPubTime != null and query.xpathPubTime != ''">
            AND t.update_time = #{query.xpathPubTime}
        </if>
        <if test="query.pubBeginTime != null and query.pubBeginTime != ''">
            AND t.update_time >= #{query.pubBeginTime}
        </if>
        <if test="query.pubEndTime != null and query.pubEndTime != ''">
            AND t.update_time &lt;= #{query.pubEndTime}
        </if>
        <!-- 未更新天数过滤条件 -->
        <if test="query.notUpdateDays != null and query.notUpdateDays != ''">
            AND t.continuous_not_update_days >= #{query.notUpdateDays}
        </if>
    </sql>

    <!-- 步骤1：删除当天已存在的数据 -->
    <delete id="deleteSiteColumnTaskDeleteTodayData">
        DELETE FROM chk_update_site_column_result
        WHERE update_time = CURRENT_DATE() AND process_type = 0 AND id BETWEEN #{beginId} AND #{endId}
    </delete>

    <select id="selectSiteColumnTaskInsertTodayData" resultType="map">
        SELECT
        c.id AS channel_id,
        c.name AS channel_name,
        c.url AS channel_url,
        c.site_id AS site_id,
        c.site_name AS site_name,

        -- 从创建栏目到昨天更新了几天
        (SELECT COUNT(DISTINCT DATE(a.pub_time))
        FROM t_article a
        WHERE a.channel_id = c.id
        AND a.process_type = 0
        AND DATE(a.pub_time) >= DATE(c.create_time)
        AND DATE(a.pub_time) &lt; CURDATE()
        ) AS update_days,

        -- 从创建栏目到昨天更新日期列表（逗号拼接）
        (SELECT GROUP_CONCAT(DISTINCT DATE(a.pub_time) ORDER BY DATE(a.pub_time) SEPARATOR ',')
        FROM t_article a
        WHERE a.channel_id = c.id
        AND a.process_type = 0
        AND DATE(a.pub_time) >= DATE(c.create_time)
        AND DATE(a.pub_time) &lt; CURDATE()
        ) AS update_days_desc,

        -- 连续未更新天数（最后一次更新时间到昨天的天数）
        CASE
        WHEN (SELECT MAX(DATE(a.pub_time)) FROM t_article a WHERE a.channel_id = c.id AND a.process_type = 0 AND a.pub_time &lt; CURDATE()) IS NULL
        THEN DATEDIFF(CURDATE(), DATE(c.create_time))  -- 从未更新过，从创建到昨天的天数
        ELSE GREATEST(0, DATEDIFF(CURDATE(), (SELECT MAX(DATE(a.pub_time)) FROM t_article a WHERE a.channel_id = c.id AND a.process_type = 0 AND a.pub_time &lt; CURDATE())) - 1)
        -- 关键修改：减去1，因为最后更新日不算未更新日
        END AS continuous_not_update_days_num,

        -- 连续未更新日期列表（从最后更新日期的下一天到昨天）
        CASE
        WHEN (SELECT MAX(DATE(a.pub_time)) FROM t_article a WHERE a.channel_id = c.id AND a.process_type = 0) IS NULL THEN
        -- 如果从未更新过，则从创建时间开始到昨天
        (SELECT GROUP_CONCAT(
        DATE_ADD(DATE(c.create_time), INTERVAL seq.n DAY)
        ORDER BY seq.n SEPARATOR ','
        )
        FROM (
        SELECT a.n + b.n * 10 + c.n * 100 + d.n * 1000 AS n
        FROM (SELECT 0 AS n UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) a
        CROSS JOIN (SELECT 0 AS n UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) b
        CROSS JOIN (SELECT 0 AS n UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) c
        CROSS JOIN (SELECT 0 AS n UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) d
        ) seq
        WHERE DATE_ADD(DATE(c.create_time), INTERVAL seq.n DAY) &lt; CURDATE()
        )
        WHEN DATEDIFF(CURDATE(), (SELECT MAX(DATE(a.pub_time)) FROM t_article a WHERE a.channel_id = c.id AND a.process_type = 0)) &lt;= 0 THEN
        -- 如果最后更新时间就是昨天或更晚，则没有连续未更新的日期
        NULL
        ELSE
        -- 从最后更新日期的下一天到昨天
        (SELECT GROUP_CONCAT(
        DATE_ADD((SELECT MAX(DATE(a2.pub_time)) FROM t_article a2 WHERE a2.channel_id = c.id AND a2.process_type = 0), INTERVAL seq.n + 1 DAY)
        ORDER BY seq.n SEPARATOR ','
        )
        FROM (
        SELECT a.n + b.n * 10 + c.n * 100 + d.n * 1000 AS n
        FROM (SELECT 0 AS n UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) a
        CROSS JOIN (SELECT 0 AS n UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) b
        CROSS JOIN (SELECT 0 AS n UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) c
        CROSS JOIN (SELECT 0 AS n UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) d
        ) seq
        WHERE DATE_ADD((SELECT MAX(DATE(a3.pub_time)) FROM t_article a3 WHERE a3.channel_id = c.id AND a3.process_type = 0), INTERVAL seq.n + 1 DAY) &lt; CURDATE()
        )
        END AS continuous_not_update_days_desc,

        -- 最后更新时间（便于验证）
        (SELECT MAX(a.pub_time) FROM t_article a WHERE a.channel_id = c.id AND a.process_type = 0 AND a.pub_time &lt; CURDATE()) AS last_update_time,

        CURRENT_DATE() AS update_time,
        0 as process_type

        FROM t_channel c
        WHERE c.enable = 1 and c.process_type = 0
        ORDER BY c.id DESC
    </select>
</mapper>