<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dahe.dao.ChkAutoRecheckTaskDao">

    <!-- 分页查询自动复查任务 -->
    <select id="selectPage" resultType="cn.dahe.model.vo.ChkAutoRecheckTaskVO">
        SELECT
        cart.id,
        cart.task_name,
        cart.site_ids,
        cart.site_names,
        cart.article_titles,
        cart.article_ids,
        cart.article_urls,
        cart.check_error_type,
        cart.check_begin_time,
        cart.check_end_time,
        cart.rectify_problem_count,
        cart.not_rectify_problem_count,
        cart.increase_problem_count,
        cart.maybe_del_problem_count,
        cart.status,
        cart.filter_title,
        cart.create_time,
        cart.create_user_id,
        cart.create_user_name,
        cart.create_dep_id,
        cart.create_dep_name
        FROM chk_auto_recheck_task cart
        LEFT JOIN t_website tw ON FIND_IN_SET(cart.site_ids,tw.id)
        <where>
            tw.status=1 and tw.is_del=0 and cart.is_del = 0
            <if test="query.taskName != null and query.taskName != ''">
                AND cart.task_name LIKE CONCAT('%', #{query.taskName}, '%')
            </if>
            <if test="query.siteIds != null and query.siteIds != ''">
                AND FIND_IN_SET(cart.site_id, #{query.siteIds})
            </if>
            <if test="query.articleTitle != null and query.articleTitle != ''">
                AND cart.article_title LIKE CONCAT('%', #{query.articleTitle}, '%')
            </if>
            <if test="query.checkErrorType != null and query.checkErrorType != ''">
                AND cart.check_error_type LIKE CONCAT('%', #{query.checkErrorType}, '%')
            </if>
            <if test="query.checkBeginTime != null">
                AND cart.check_begin_time &gt;= #{query.checkBeginTime}
            </if>
            <if test="query.checkEndTime != null">
                AND cart.check_end_time &lt;= #{query.checkEndTime}
            </if>
            <if test="query.status != null and query.status != 0">
                AND cart.status = #{query.status}
            </if>
            <if test="query.filterTitle != null and query.filterTitle != 0">
                AND cart.filter_title = #{query.filterTitle}
            </if>
            <if test="query.beginCreateTime != null">
                AND cart.create_time &gt;= #{query.beginCreateTime}
            </if>
            <if test="query.endCreateTime != null">
                AND cart.create_time &lt;= #{query.endCreateTime}
            </if>
        </where>
        ORDER BY cart.create_time DESC
    </select>

    <!-- 获取任务统计信息 -->
    <select id="getTaskStatistics" resultType="java.util.HashMap">
        SELECT
        COUNT(*) as totalTasks,
        IFNULL(SUM(cart.rectify_problem_count), 0) as totalRectifyProblemCount,
        IFNULL(SUM(cart.not_rectify_problem_count), 0) as totalNotRectifyProblemCount,
        IFNULL(SUM(cart.increase_problem_count), 0) as totalIncreaseProblemCount,
        IFNULL(SUM(cart.maybe_del_problem_count), 0) as totalMaybeDelProblemCount,
        IFNULL(SUM(cart.rectify_problem_count + cart.not_rectify_problem_count + cart.increase_problem_count + cart.maybe_del_problem_count), 0) as totalProblemCount
        FROM chk_auto_recheck_task cart
        LEFT JOIN t_website tw ON FIND_IN_SET(cart.site_ids,tw.id)
        <where>
            tw.status=1 and tw.is_del=0 and cart.is_del = 0
            <if test="taskName != null and taskName != ''">
                AND cart.task_name LIKE CONCAT('%', #{taskName}, '%')
            </if>
            <if test="siteIds != null and siteIds != ''">
                AND FIND_IN_SET(cart.site_id, #{siteIds})
            </if>
            <if test="articleTitle != null and articleTitle != ''">
                AND cart.article_title LIKE CONCAT('%', #{articleTitle}, '%')
            </if>
            <if test="checkErrorType != null and checkErrorType != ''">
                AND cart.check_error_type LIKE CONCAT('%', #{checkErrorType}, '%')
            </if>
            <if test="checkBeginTime != null">
                AND cart.check_begin_time &gt;= #{checkBeginTime}
            </if>
            <if test="checkEndTime != null">
                AND cart.check_end_time &lt;= #{checkEndTime}
            </if>
            <if test="status != null and status != 0">
                AND cart.status = #{status}
            </if>
            <if test="filterTitle != null and filterTitle != 0">
                AND cart.filter_title = #{filterTitle}
            </if>
            <if test="beginCreateTime != null">
                AND cart.create_time &gt;= #{beginCreateTime}
            </if>
            <if test="endCreateTime != null">
                AND cart.create_time &lt;= #{endCreateTime}
            </if>
        </where>
    </select>

</mapper>