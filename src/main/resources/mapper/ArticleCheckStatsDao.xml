<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dahe.dao.ArticleCheckStatsDao">
    <sql id="errorLevelExistCondition">
        <if test="query.errorLevels != null and query.errorLevels.size() > 0">
            and exists(
            SELECT 1
            FROM t_check_result tcr
            left join t_check_word tcw on tcw.id = tcr.word_id
            WHERE tct.id = tcr.check_id and tcr.article_location in (0,1)
            <include refid="cn.dahe.dao.CheckWordDao.errorLevelCondition"/>
            )
        </if>
    </sql>

    <sql id="selectArticleCountAndLength">
        select
        tw.id as website_id,
        tw.site_id as site_id,
        tw.channel_id as channel_id,
        count(ta.id) as article_count,
        IFNULL(sum(tct.content_length),0) as article_length,
        MAX(ta.pub_time) as latest_pub_time
        from t_article ta
        left join t_website tw on ta.website_id = tw.id
        left join t_check_task tct on ta.check_id = tct.id
        <where>
            tct.check_status = 1
            and tw.status = 1 and tw.is_del = 0
            <include refid="cn.dahe.dao.WebsiteDao.processTypesCondition"/>
            <include refid="cn.dahe.dao.WebsiteDao.websiteCondition"/>
            <include refid="cn.dahe.dao.ArticleDao.pubDateCondition"/>
            <include refid="errorLevelExistCondition"/>
        </where>
    </sql>


    <select id="pageStats" resultType="cn.dahe.model.vo.WebsiteArticleCheckStatsVO">
        SELECT
        tw.id as website_id,
        tw.web_name as website_name,
        tw.web_url as website_url,
        tw.channel_id AS channel_id,
        tw.web_name AS channel_name,
        tw.web_url AS channel_url,
        tw.process_type,
        tw.group_id,
        ta_stats.article_count,
        ta_stats.article_length,
        ta_stats.latest_pub_time,
        count(tcr.id) as check_result_count,
        count(distinct IF(tcw.error_level = 1, ta.id, null)) as lv1_article_count,
        count(distinct IF(tcw.error_level = 2, ta.id, null)) as lv2_article_count,
        count(distinct IF(tcw.error_level = 3, ta.id, null)) as lv3_article_count,
        count(distinct IF(tcw.error_level = 4, ta.id, null)) as lv4_article_count,
        count(distinct IF(tcw.error_level = 5, ta.id, null)) as lv5_article_count
        FROM t_website tw
        left join
        (
        <include refid="selectArticleCountAndLength"/>
        GROUP BY tw.id
        ) as ta_stats on ta_stats.website_id = tw.id
        left join t_article ta on ta.website_id = tw.id
        left join t_check_task tct on tct.id = ta.check_id
        left join t_check_result tcr on tcr.check_id = tct.id and tcr.article_location in (0,1)
        left join t_check_word tcw on tcw.id = tcr.word_id
        <where>
            tct.check_status = 1
            and tw.status = 1 and tw.is_del = 0
            <include refid="cn.dahe.dao.WebsiteDao.processTypesCondition"/>
            <include refid="cn.dahe.dao.WebsiteDao.websiteCondition"/>
            <include refid="cn.dahe.dao.ArticleDao.pubDateCondition"/>
            <include refid="errorLevelExistCondition"/>
        </where>
        group by tw.id
    </select>


    <select id="queryTotalStats" resultType="cn.dahe.model.vo.WebsiteArticleCheckTotalStatsVO">
        SELECT
        count(distinct tw.id) as count,
        ta_stats.article_count,
        ta_stats.article_length,
        count(distinct tcr.id) as check_result_count,
        count(distinct tcw.id) as check_word_count,
        count(distinct IF(tcw.error_level = 1, ta.website_id, null)) as lv1_count,
        count(distinct IF(tcw.error_level = 2, ta.website_id, null)) as lv2_count,
        count(distinct IF(tcw.error_level = 3, ta.website_id, null)) as lv3_count,
        count(distinct IF(tcw.error_level = 4, ta.website_id, null)) as lv4_count,
        count(distinct IF(tcw.error_level = 5, ta.website_id, null)) as lv5_count
        FROM t_website tw
        left join t_article ta on ta.website_id = tw.id
        left join t_check_task tct on tct.id = ta.check_id
        left join t_check_result tcr on tcr.check_id = tct.id and tcr.article_location in (0,1)
        left join t_check_word tcw on tcw.id = tcr.word_id
        left join
        (<include refid="selectArticleCountAndLength"/>)
        as ta_stats on true
        <where>
            tct.check_status = 1
            and tw.status = 1 and tw.is_del = 0
            <include refid="cn.dahe.dao.WebsiteDao.processTypesCondition"/>
            <include refid="cn.dahe.dao.WebsiteDao.websiteCondition"/>
            <include refid="cn.dahe.dao.ArticleDao.pubDateCondition"/>
            <include refid="errorLevelExistCondition"/>
        </where>
    </select>


    <select id="queryWordTotalStats" resultType="cn.dahe.model.vo.WebsiteArticleCheckWordTotalStatsVO">
        select
        count(distinct tcw.id) as check_word_count,
        count(distinct IF(tcw.error_level = 1, tcw.id, null)) as lv1_check_word_count,
        count(distinct IF(tcw.error_level = 2, tcw.id, null)) as lv2_check_word_count,
        count(distinct IF(tcw.error_level = 3, tcw.id, null)) as lv3_check_word_count,
        count(distinct IF(tcw.error_level = 4, tcw.id, null)) as lv4_check_word_count,
        count(distinct IF(tcw.error_level = 5, tcw.id, null)) as lv5_check_word_count
        from t_check_word tcw
        left join t_check_result tcr on tcr.word_id = tcw.id
        left join t_check_task tct on tcr.check_id = tct.id
        left join t_article ta on ta.check_id = tct.id
        left join t_website tw on ta.website_id = tw.id
        <where>
            tct.check_status = 1 and ta.id is not null
            and tcr.article_location in (0,1)
            and tw.status = 1 and tw.is_del = 0
            <include refid="cn.dahe.dao.WebsiteDao.websiteCondition"/>
            <include refid="cn.dahe.dao.WebsiteDao.processTypesCondition"/>
            <include refid="cn.dahe.dao.ArticleDao.pubDateCondition"/>
            <include refid="cn.dahe.dao.CheckWordDao.errorLevelCondition"/>
            <include refid="cn.dahe.dao.CheckWordDao.filterStatusCondition"/>
            <include refid="cn.dahe.dao.CheckWordDao.keywordCondition"/>
        </where>
    </select>

    <resultMap id="WebsiteArticleCheckWordStatsVOMap" type="cn.dahe.model.vo.WebsiteArticleCheckWordStatsVO">
        <id column="word_id" property="wordId"/>
        <result column="error_word" property="errorWord"/>
        <result column="suggest_word" property="suggestWord"/>
        <result column="filter_status" property="filterStatus"/>
        <result column="error_level" property="errorLevel"/>
        <result column="error_type" property="errorType"/>
        <result column="check_result_count" property="checkResultCount"/>
        <result column="count" property="count"/>
        <collection property="relates"
                    ofType="cn.dahe.model.vo.WebsiteArticleCheckWordStatsVO$Relate"
                    select="selectDistinctByRelateIds"
                    column="{relateIds=relateIds, searchProcessType=searchProcessType}"/>
    </resultMap>

    <select id="selectDistinctByRelateIds" resultType="cn.dahe.model.vo.WebsiteArticleCheckWordStatsVO$Relate">
        select
            tw.id as relateId,
            tw.web_name as relateName
        from t_website tw
        where FIND_IN_SET(tw.id, #{relateIds})
          and tw.status = 1 and tw.is_del = 0
    </select>
    <select id="pageWordStats" resultMap="WebsiteArticleCheckWordStatsVOMap">
        select
        tcw.id as word_id,
        tcw.error_word,
        tcw.suggest_word,
        tcw.filter_status,
        tcw.error_level,
        ifNULL(tcw.third_error_type,tcw.second_error_type) as error_type,
        count(distinct tcr.id) as check_result_count,
        #{query.searchProcessType} as searchProcessType,
        group_concat(distinct tw.id) as relateIds,
        count(distinct tw.id) as count
        from t_check_word tcw
        left join t_check_result tcr on tcr.word_id = tcw.id
        left join t_check_task tct on tcr.check_id = tct.id
        left join t_article ta on ta.check_id = tct.id
        left join t_website tw on ta.website_id = tw.id
        <where>
            tct.check_status = 1 and ta.id is not null
            and tcr.article_location in (0,1)
            and tw.status = 1 and tw.is_del = 0
            <include refid="cn.dahe.dao.WebsiteDao.processTypesCondition"/>
            <include refid="cn.dahe.dao.WebsiteDao.websiteCondition"/>
            <include refid="cn.dahe.dao.ChannelDao.channelCondition"/>
            <include refid="cn.dahe.dao.CheckWordDao.filterStatusCondition"/>
            <include refid="cn.dahe.dao.CheckWordDao.errorTypeAndLevelCondition"/>
            <include refid="cn.dahe.dao.ArticleDao.pubDateCondition"/>
            <include refid="cn.dahe.dao.CheckWordDao.keywordCondition"/>
        </where>
        group by tcw.id
        ORDER BY check_result_count desc, `count` DESC
    </select>

</mapper>