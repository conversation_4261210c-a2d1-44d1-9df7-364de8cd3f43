<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.dahe.dao.ChkTransferTaskDao">

    <!-- 结果映射 -->
    <resultMap id="ChkTransferTaskVOMap" type="cn.dahe.model.vo.ChkTransferTaskVO">
        <id column="id" property="id"/>
        <result column="dep_id" property="depId"/>
        <result column="dep_name" property="depName"/>
        <result column="transfer_user_id" property="transferUserId"/>
        <result column="transfer_user_name" property="transferUserName"/>
        <result column="site_id" property="siteId"/>
        <result column="site_name" property="siteName"/>
        <result column="site_url" property="siteUrl"/>
        <result column="article_title" property="articleTitle"/>
        <result column="article_id" property="articleId"/>
        <result column="article_url" property="articleUrl"/>
        <!-- 使用IFNULL处理null值为0 -->
        <result column="serious_error_count" property="seriousErrorCount" jdbcType="INTEGER" javaType="java.lang.Integer"/>
        <result column="common_error_count" property="commonErrorCount" jdbcType="INTEGER" javaType="java.lang.Integer"/>
        <result column="maybe_error_count" property="maybeErrorCount" jdbcType="INTEGER" javaType="java.lang.Integer"/>
        <result column="diy_error_count" property="diyErrorCount" jdbcType="INTEGER" javaType="java.lang.Integer"/>
        <result column="risk_tip_count" property="riskTipCount" jdbcType="INTEGER" javaType="java.lang.Integer"/>
        <result column="send_time" property="sendTime"/>
        <result column="select_result_ids" property="selectResultIds"/>
        <result column="create_time" property="createTime"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="create_user_name" property="createUserName"/>
        <result column="rectify_status" property="rectifyStatus"/>
        <result column="disposal_status" property="disposalStatus"/>
        <result column="audit_status" property="auditStatus"/>
        <result column="pub_time" property="pubTime"/>
        <result column="collect_time" property="collectTime"/>
    </resultMap>

    <!-- 简化版统计 - 根据时间范围统计各状态任务数 -->
    <select id="getTaskStatisticsByTimeRange" resultType="java.util.Map">
        SELECT
        COUNT(*) AS total_count,  -- 无数据时自动返回 0，无需处理
        -- 将 SUM 的 NULL 结果转为 0
        COALESCE(SUM(CASE WHEN ta.rectify_status = 1 THEN 1 ELSE 0 END), 0) AS pending_count,
        COALESCE(SUM(CASE WHEN ta.rectify_status = 2 THEN 1 ELSE 0 END), 0) AS completed_count,
        COALESCE(SUM(CASE WHEN ta.rectify_status = 3 THEN 1 ELSE 0 END), 0) AS no_need_count
        FROM chk_transfer_task ctt
        LEFT JOIN t_article ta ON ctt.article_id = ta.id
        LEFT JOIN t_website tw ON ctt.site_id = tw.id
        <where>
            tw.status=1 and tw.is_del=0 and ctt.is_del = 0
            <if test="beginTime != null">
                AND ctt.send_time &gt;= #{beginTime}
            </if>
            <if test="endTime != null">
                AND ctt.send_time &lt; #{endTime}
            </if>
            <if test="query != null">
                <if test="query.keyword != null and query.keyword != ''">
                    <choose>
                        <when test="query.type == 0">
                            AND ctt.site_name LIKE CONCAT('%', #{query.keyword}, '%')
                        </when>
                        <when test="query.type == 1">
                            AND ctt.article_title LIKE CONCAT('%', #{query.keyword}, '%')
                        </when>
                        <otherwise>
                            AND (ctt.site_name LIKE CONCAT('%', #{query.keyword}, '%')
                            OR ctt.article_title LIKE CONCAT('%', #{query.keyword}, '%'))
                        </otherwise>
                    </choose>
                </if>
                <if test="query.rectifyStatus != null">
                    AND ta.rectify_status = #{query.rectifyStatus}
                </if>
                <if test="query.beginSendTime != null">
                    AND ctt.send_time &gt;= #{query.beginSendTime}
                </if>
                <if test="query.endSendTime != null">
                    AND ctt.send_time &lt;= #{query.endSendTime}
                </if>
            </if>
        </where>
    </select>

    <!-- 分页查询转办督办任务 -->
    <select id="selectPage" resultMap="ChkTransferTaskVOMap">
        SELECT
        ctt.id,
        ctt.dep_id,
        ctt.dep_name,
        ctt.transfer_user_id,
        ctt.transfer_user_name,
        ctt.site_id,
        ctt.site_name,
        ctt.site_url,
        ctt.article_title,
        ctt.article_id,
        ctt.article_url,
        -- 使用IFNULL处理null值为0
        IFNULL(ctt.serious_error_count, 0) as serious_error_count,
        IFNULL(ctt.common_error_count, 0) as common_error_count,
        IFNULL(ctt.maybe_error_count, 0) as maybe_error_count,
        IFNULL(ctt.diy_error_count, 0) as diy_error_count,
        IFNULL(ctt.risk_tip_count, 0) as risk_tip_count,
        ctt.select_result_ids,
        ctt.send_time,
        ctt.create_time,
        ctt.create_user_id,
        ctt.create_user_name,
        ta.rectify_status,
        ta.disposal_status,
        ta.audit_status,
        ta.pub_time,
        ta.collect_time
        FROM chk_transfer_task ctt
        LEFT JOIN t_article ta ON ctt.article_id = ta.id
        LEFT JOIN t_website tw ON ctt.site_id = tw.id
        <where>
            tw.status=1 and tw.is_del=0 and ctt.is_del = 0
            <if test="query.keyword != null and query.keyword != ''">
                <choose>
                    <when test="query.type == 0">
                        AND ctt.site_name LIKE CONCAT('%', #{query.keyword}, '%')
                    </when>
                    <when test="query.type == 1">
                        AND ctt.article_title LIKE CONCAT('%', #{query.keyword}, '%')
                    </when>
                    <otherwise>
                        AND (ctt.site_name LIKE CONCAT('%', #{query.keyword}, '%')
                        OR ctt.article_title LIKE CONCAT('%', #{query.keyword}, '%'))
                    </otherwise>
                </choose>
            </if>
            <if test="query.rectifyStatus != null">
                AND ta.rectify_status = #{query.rectifyStatus}
            </if>
            <if test="query.beginSendTime != null">
                AND ctt.send_time &gt;= #{query.beginSendTime}
            </if>
            <if test="query.endSendTime != null">
                AND ctt.send_time &lt;= #{query.endSendTime}
            </if>
        </where>
        ORDER BY ctt.send_time DESC, ctt.id DESC
    </select>

    <!-- 批量插入转办任务 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO chk_transfer_task (
        dep_id, dep_name, transfer_user_id, transfer_user_name,
        site_id, site_name, site_url, article_title, article_id, article_url,
        serious_error_count, common_error_count, maybe_error_count, diy_error_count, risk_tip_count, select_result_ids,
        send_time, create_time, create_user_id, create_user_name, is_del
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.depId}, #{item.depName}, #{item.transferUserId}, #{item.transferUserName},
            #{item.siteId}, #{item.siteName}, #{item.siteUrl}, #{item.articleTitle},
            #{item.articleId}, #{item.articleUrl},
            IFNULL(#{item.seriousErrorCount}, 0),
            IFNULL(#{item.commonErrorCount}, 0),
            IFNULL(#{item.maybeErrorCount}, 0),
            IFNULL(#{item.diyErrorCount}, 0),
            IFNULL(#{item.riskTipCount}, 0),
            #{item.selectResultIds},
            #{item.sendTime}, #{item.createTime}, #{item.createUserId}, #{item.createUserName}, 0
            )
        </foreach>
    </insert>

</mapper>