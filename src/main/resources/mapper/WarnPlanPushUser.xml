<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dahe.dao.WarnPlanPushUserDao">

    <select id="listByWarnPlanId" resultType="cn.dahe.entity.WarnPlanPushUser">
        SELECT * FROM t_warn_plan_push_user WHERE warn_plan_id = #{warnPlanId}
    </select>

    <delete id="deleteByWarnPlanId">
        DELETE FROM t_warn_plan_push_user WHERE warn_plan_id = #{warnPlanId}
    </delete>

</mapper>


