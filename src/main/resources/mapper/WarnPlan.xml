<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dahe.dao.WarnPlanDao">

    <resultMap id="WarnPlanVOMap" type="cn.dahe.model.vo.WarnPlanVO">
        <id property="id" column="id" />
        <result property="schemeName" column="scheme_name" />
        <result property="articleErrorWarn" column="article_error_warn" />
        <result property="attachmentErrorWarn" column="attachment_error_warn" />
        <!-- <result property="patrolWarn" column="patrol_warn" /> -->
        <!-- <result property="historyDataPatrolWarn" column="history_data_patrol_warn" /> -->
        <result property="updateWarn" column="update_warn" />
        <result property="wechatUnupdateDays" column="wechat_unupdate_days" />
        <result property="weiboUnupdateDays" column="weibo_unupdate_days" />
        <result property="toutiaoContinuousDays" column="toutiao_continuous_days" />
        <result property="douyinContinuousDays" column="douyin_continuous_days" />
        <result property="kuaishouContinuousDays" column="kuaishou_continuous_days" />
        <result property="wxvideonumberContinuousDays" column="wxvideonumber_continuous_days" />
        <result property="xiaohongshuContinuousDays" column="xiaohongshu_continuous_days" />
        <result property="homePageUpdateDays" column="home_page_update_days" />
        <result property="columnCategory" column="column_category" />
        <result property="columnInfoDays" column="column_info_days" />
        <result property="linkWarn" column="link_warn" />
        <result property="connectivityWarn" column="connectivity_warn" />
        <result property="abnormalAccessCount" column="abnormal_access_count" />
        <result property="deadLinkWarn" column="dead_link_warn" />
        <result property="deadLinkCount" column="dead_link_count" />
        <result property="deadLinkTypes" column="dead_link_types" />
        <result property="externalLinkWarn" column="external_link_warn" />
        <result property="externalLinkCount" column="external_link_count" />
        <!-- <result property="isDeleted" column="is_deleted" /> -->
        <result property="wechatPushEnable" column="wechat_push_enable" />
        <result property="smsPushEnable" column="sms_push_enable" />
        <!-- <result property="createTime" column="create_time" /> -->
        <!-- <result property="updateTime" column="update_time" /> -->
        <!-- <result property="warnPlanId" column="warn_plan_id" /> -->

        <collection property="platforms" ofType="cn.dahe.entity.WarnPlanPlatform" select="cn.dahe.dao.WarnPlanPlatformDao.listByWarnPlanId" column="id" />
        <collection property="pushUsers" ofType="cn.dahe.entity.WarnPlanPushUser" select="cn.dahe.dao.WarnPlanPushUserDao.listByWarnPlanId" column="id" />
    </resultMap>

    <select id="list" resultMap="WarnPlanVOMap">
        SELECT * FROM t_warn_plan
        <where>
            <if test="query.schemeName != null and query.schemeName != ''">
                AND scheme_name LIKE CONCAT('%', #{query.schemeName}, '%')
            </if>
            <if test="query.updateWarn != null">
                AND update_warn = #{query.updateWarn}
            </if>
            AND is_deleted = 0
        </where>
        ORDER BY id DESC
    </select>

    <select id="getById" resultMap="WarnPlanVOMap">
        SELECT * FROM t_warn_plan WHERE id = #{id} AND is_deleted = 0
    </select>

    <select id="listAvailablePlanWithCondition" resultType="java.lang.Long">
        select
        distinct twp.id
        from t_warn_plan twp
        left join t_warn_platform twpm on twpm.warn_plan_id = twp.id
        where
        twp.is_deleted = 0
        <choose>
            <when test="warnType!= null and warnType == 0">
                and twpm.website_id = #{websiteId} and twp.article_error_warn = 1
            </when>
            <when test="warnType!= null and warnType == 1">
                and twpm.website_id = #{websiteId} and twp.attachment_error_warn = 1
            </when>
            <otherwise>
                and false
            </otherwise>
        </choose>
    </select>

</mapper>


