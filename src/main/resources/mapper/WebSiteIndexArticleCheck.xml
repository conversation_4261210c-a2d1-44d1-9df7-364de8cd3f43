<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.dahe.dao.WebSiteIndexArticleCheckDao">

    <!-- 根据站点ID和检查日期查询记录 -->
    <select id="selectByWebsiteIdAndDate" resultType="cn.dahe.entity.WebSiteIndexArticleCheck">
        SELECT *
        FROM web_site_index_article_check
        WHERE website_id = #{websiteId}
          AND DATE(check_time) = #{checkDate}
        LIMIT 1
    </select>

    <!-- 根据站点ID分页查询检查记录 -->
    <select id="selectByWebsiteIdAndDateRange" resultType="cn.dahe.entity.WebSiteIndexArticleCheck">
        SELECT *
        FROM web_site_index_article_check
        WHERE website_id = #{websiteId}
          AND DATE(check_time) BETWEEN #{startDate} AND #{endDate}
        ORDER BY check_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 根据站点ID和日期范围统计记录总数 -->
    <select id="countByWebsiteIdAndDateRange" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM web_site_index_article_check
        WHERE website_id = #{websiteId}
          AND DATE(check_time) BETWEEN #{startDate} AND #{endDate}
    </select>

</mapper>
