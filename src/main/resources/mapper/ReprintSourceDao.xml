<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dahe.dao.ReprintSourceDao">

    <sql id="filterStatusCondition">
        <if test="query.filterStatuses != null and query.filterStatuses.size() > 0">
            AND trs.filter_status IN
            <foreach collection="query.filterStatuses" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
    </sql>
    <sql id="filterDateCondition">
        <if test="query.filterBeginDate != null">
            and trs.filter_status = 1 AND trs.filter_time >= DATE_FORMAT(#{query.filterBeginDate}, '%Y-%m-%d 00:00:00')
        </if>
        <if test="query.filterEndDate != null">
            and trs.filter_status = 1 AND trs.filter_time &lt;= DATE_FORMAT(#{query.filterEndDate}, '%Y-%m-%d 23:59:59')
        </if>
    </sql>
    <sql id="reprintSourceCondition">
        <if test="query.reprintSource != null">
            and trs.reprint_source LIKE CONCAT('%', #{query.reprintSource}, '%')
        </if>
    </sql>

    <!-- 分页查询转载信源列表 -->
    <select id="pageList" resultType="cn.dahe.model.vo.ReprintSourceVO">
        SELECT
        trs.id,
        tw.id as website_id,
        tw.web_name as website_name,
        trs.reprint_source,
        trs.filter_status,
        if(trs.filter_status = 1, trs.filter_time, null) as filter_time
        FROM t_reprint_source trs
        LEFT JOIN t_website tw ON tw.id = trs.website_id
        <where>
            tw.process_type = 0
            <include refid="cn.dahe.dao.WebsiteDao.websiteCondition"/>
            <include refid="filterStatusCondition"/>
            <include refid="reprintSourceCondition"/>
            <include refid="filterDateCondition"/>
            <if test="query.ids != null and query.ids.size > 0">
                and trs.id in
                <foreach item="id" collection="query.ids" index="index" separator="," close=")" open="(">
                    #{id}
                </foreach>
            </if>
        </where>
        ORDER BY trs.filter_status desc, trs.filter_time DESC
    </select>
    <select id="findNonExistent" resultType="cn.dahe.entity.ReprintSource">
        SELECT
            temp.website_id,
            temp.reprint_source,
            0 as filter_status
        FROM (
        <!-- 构造临时表，包含所有待插入的组合 -->
        <foreach collection="list" item="item" index="index" separator="UNION ALL">
            SELECT
            #{item.websiteId} AS website_id,
            #{item.reprintSource} AS reprint_source
        </foreach>
        ) temp
        LEFT JOIN t_reprint_source trs
        ON temp.website_id = trs.website_id
        AND temp.reprint_source = trs.reprint_source
        WHERE trs.id IS NULL
    </select>

</mapper>
