<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dahe.dao.ChannelTypeDao">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="cn.dahe.entity.ChannelType">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="parent_id" property="parentId" jdbcType="BIGINT"/>
        <result column="update_period" property="updatePeriod" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
<!--        <result column="del_status" property="delStatus" jdbcType="INTEGER"/>-->
        <result column="sort_num" property="sortNum" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, name, parent_id, update_period, create_time, update_time, del_status, sort_num
    </sql>

    <!-- 新增栏目类型 -->
    <insert id="insertChannelType" parameterType="cn.dahe.entity.ChannelType" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_channel_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="updatePeriod != null">update_period,</if>
            <if test="sortNum != null">sort_num,</if>
            create_time,
            update_time,
            del_status
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="updatePeriod != null">#{updatePeriod},</if>
            <if test="sortNum != null">#{sortNum},</if>
            NOW(),
            NOW(),
            0
        </trim>
    </insert>

    <!-- 修改栏目类型 -->
    <update id="updateChannelType" parameterType="cn.dahe.entity.ChannelType">
        UPDATE t_channel_type
        <set>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="updatePeriod != null">update_period = #{updatePeriod},</if>
            <if test="sortNum != null">sort_num = #{sortNum},</if>
            update_time = NOW()
        </set>
        WHERE id = #{id} AND del_status = 0
    </update>

    <!-- 逻辑删除栏目类型 -->
    <update id="deleteChannelTypeById" parameterType="java.lang.Long">
        UPDATE t_channel_type 
        SET del_status = 1, update_time = NOW()
        WHERE id = #{id} AND del_status = 0
    </update>

    <!-- 根据ID查询栏目类型 -->
    <select id="selectChannelTypeById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_channel_type
        WHERE id = #{id} AND del_status = 0
    </select>

    <!-- 校验名称在同一层级下是否重复 -->
    <select id="checkNameDuplicate" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM t_channel_type
        WHERE name = #{name}
        AND del_status = 0
        <if test="parentId != null">
            AND parent_id = #{parentId}
        </if>
        <if test="parentId == null">
            AND parent_id IS NULL
        </if>
        <if test="id != null">
            AND id != #{id}
        </if>
    </select>

    <!-- 校验父级ID是否存在 -->
    <select id="checkParentIdExists" parameterType="java.lang.Long" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM t_channel_type
        WHERE id = #{parentId} AND del_status = 0
    </select>

    <!-- 分页查询栏目类型列表 -->
    <select id="listChannelTypesByPage" parameterType="cn.dahe.model.dto.ChannelTypeQuery" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_channel_type
        WHERE del_status = 0
        <if test="query.name != null and query.name != ''">
            AND name LIKE CONCAT('%', #{query.name}, '%')
        </if>
        <if test="query.status != null">
            AND del_status = #{query.status}
        </if>
        <if test="query.parentId != null">
            AND parent_id = #{query.parentId}
        </if>
        ORDER BY create_time DESC
        <if test="query.getPage() != null and query.getLimit() != null">
            LIMIT #{query.page}, #{query.limit}
        </if>
    </select>

    <!-- 分页查询总数 -->
    <select id="countChannelTypesByPage" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM t_channel_type
        WHERE del_status = 0
        <if test="query.name != null and query.name != ''">
            AND name LIKE CONCAT('%', #{query.name}, '%')
        </if>
        <if test="query.status != null">
            AND del_status = #{query.status}
        </if>
        <if test="query.parentId != null">
            AND parent_id = #{query.parentId}
        </if>
    </select>

    <!-- 查询所有栏目类型（用于构建树结构） -->
    <select id="listChannelTypeTree" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_channel_type
        WHERE del_status = 0
        ORDER BY sort_num ASC, create_time ASC
    </select>

    <!-- 根据父级ID查询子级栏目类型 -->
    <select id="listChannelTypesByParentId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_channel_type
        WHERE del_status = 0
        <if test="parentId != null">
            AND parent_id = #{parentId}
        </if>
        <if test="parentId == null">
            AND parent_id IS NULL
        </if>
        ORDER BY sort_num ASC, create_time ASC
    </select>

    <!-- 查询根节点栏目类型 -->
    <select id="listRootChannelTypes" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_channel_type
        WHERE del_status = 0 AND parent_id IS NULL
        ORDER BY sort_num ASC, create_time ASC
    </select>

    <!-- 批量查询栏目类型 -->
    <select id="listChannelTypesByIds" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_channel_type
        WHERE del_status = 0
        AND id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        ORDER BY sort_num ASC, create_time ASC
    </select>

    <!-- 查询某个节点的所有子节点ID -->
    <select id="listChildrenIds" parameterType="java.lang.Long" resultType="java.lang.Long">
        WITH RECURSIVE channel_tree AS (
            SELECT id, parent_id, name
            FROM t_channel_type
            WHERE id = #{parentId} AND del_status = 0
            
            UNION ALL
            
            SELECT ct.id, ct.parent_id, ct.name
            FROM t_channel_type ct
            INNER JOIN channel_tree pt ON ct.parent_id = pt.id
            WHERE ct.del_status = 0
        )
        SELECT id FROM channel_tree WHERE id != #{parentId}
    </select>

</mapper>