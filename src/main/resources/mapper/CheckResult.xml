<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dahe.dao.CheckResultDao">


    <sql id="resultAuditCondition">
        <if test="query.resultAuditStatuses != null and query.resultAuditStatuses.size() > 0">
            AND tcr.audit_status IN
            <foreach collection="query.resultAuditStatuses" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
    </sql>
    <sql id="wordIdCondition">
        <if test="query.wordId != null">
            AND tcr.word_id = #{query.wordId}
        </if>
    </sql>

    <update id="updateUnrelatedCheckWordResult">
        update
            t_check_result tcr
                left join t_check_word tcw on tcr.error_word = tcw.error_word
        set tcr.word_id = tcw.id
        where tcr.word_id is null
    </update>

    <!-- 获取文章的错误信息 -->
    <select id="listCheckResultByTaskId" resultType="cn.dahe.model.vo.CheckResultVO">
        SELECT
        tcr.id as result_id,
        tcw.error_word,
        tcr.html_error_word,
        tcw.suggest_word,
        tcr.position,
        tcr.html_position,
        ifNULl(tcw.third_error_type,tcw.second_error_type) as error_type,
        tcr.article_location,
        tcw.error_level,
        tcr.audit_status,
        tcr.context,
        tcr.context_position,
        tcw.filter_status
        FROM t_check_result tcr
        left join t_check_word tcw on tcr.word_id = tcw.id
        left join t_check_task tct on tcr.check_id = tct.id
        WHERE tct.id = #{taskId}
        <choose>
            <when test="query.resultDisplayType != null and query.resultDisplayType == 1">
                and tcr.article_location = 2
            </when>
            <otherwise>
                and tcr.article_location in (0,1)
            </otherwise>
        </choose>
        <include refid="resultAuditCondition"/>
        <include refid="cn.dahe.dao.CheckWordDao.filterStatusCondition"/>
        <include refid="cn.dahe.dao.CheckWordDao.wordCondition"/>
        <if test="query.onlyShowSpecifiedErrors != null and query.onlyShowSpecifiedErrors">
            <include refid="cn.dahe.dao.CheckWordDao.errorTypeAndLevelCondition"/>
            <include refid="cn.dahe.dao.CheckResultDao.wordIdCondition"/>
        </if>
        order by tcr.article_location desc,tcr.position
    </select>
    <select id="listResultIdsFromArticle" resultType="java.lang.Long">
        select tcr.id
        from t_check_result tcr
        left join t_check_task tct on tcr.check_id = tct.id
        left join t_article ta on tct.relation_id = ta.id and tct.relation_type in (0, 2)
        where ta.id = #{articleId}
        and tcr.id in
        <foreach item="resultId" collection="resultIds" separator="," open="(" close=")">
            #{resultId}
        </foreach>
    </select>
    <select id="listUnrelatedWordResult" resultType="cn.dahe.entity.CheckResult">
        select tcr.error_word,
               tcr.suggest_word,
               tcr.error_type,
               tcr.error_level
        from t_check_result tcr
        where tcr.word_id is null
          and tcr.error_word is not null
        group by tcr.error_word
    </select>


</mapper>