/*
 * @Author: xrf
 * @Date: 2020-01-03 17:59:03
 * @LastEditors  : xrf
 * @LastEditTime : 2020-01-22 10:53:07
 */
@charset "utf-8";

table,
th,
td,
input {
    padding: 0;
    margin: 0;
    border: 0;
    border-spacing: 0;
    font-weight: normal;
    font-size: 14px;
}

input[type="number"] {
    -moz-appearance: textfield;
}

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
}

table {
    border-top: 1px solid #000;
    border-left: 1px solid #000;
}

tr {
    height: 40px;
    line-height: 1.8;
}

th,
td {
    border-right: 1px solid #000;
    border-bottom: 1px solid #000;
    text-align: center;
}
.fl {
    float: left;
}
.fr {
    float: right;
}
.clear {
    zoom: 1;
}
.clear::after {
    display: block;
    content: "";
    clear: both;
}
.app {
    width: 1070px;
    margin: auto;
    padding: 20px 0 100px;
}
.bg-blue {
    background-color: #c9d9ef;
}

.input-cell {
    box-sizing: border-box;
    width: 100%;
    height: 51px;
    line-height: 51px;
    padding: 0 10px;
    text-align: center;
}

.input-cell.wrong, .input-cell.illegal {
    outline: 1px solid #f00;
}

.second-table td {
    width: 60px;
}

.section-title {
    font-size: 16px;
    font-weight: bold;
    margin: 15px 0;
}
.text-info {
    display: block;
    box-sizing: border-box;
    margin: auto;
    outline: 0;
    padding: 10px;
    font-size: 14px;
    resize: vertical;
}
.content-area {
    width: 1000px;
    margin: auto;
}
.file-item {
    height: 36px;
    line-height: 36px;
    background-color: #efefef;
    margin: 10px 0;
    padding: 0 10px;
}
.file-item .file-name {
    max-width: calc(100% - 40px);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.file-item .file-name:hover {
    text-decoration: underline;
    color: #1e9fff;
}
.file-item .del-file {
    color: #ff5722;
    cursor: pointer;
}
.file-item .del-file:hover {
    color: #f00;
}
.page-title {
    text-align: center;
    margin-bottom: 15px;
}
.btn-area {
    width: 100%;
    padding: 20px 0;
    position: fixed;
    bottom: 0;
    left: 0;
    background-color: #fff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    z-index: 80;
}
.save-btn {
    width: 150px;
    height: 40px;
    outline: 0;
    border: 0;
    background: #1e9fff;
    cursor: pointer;
    color: #fff;
    font-size: 16px;
    border-radius: 5px;
    display: block;
    margin: 0 auto;
}
.save-btn:hover {
    opacity: 0.9;
}

.expandingArea {
    position: relative;
    border: 1px solid #e6e6e6;
    background: #fff;
    min-height: 100px;
    width: 1000px;
    margin: auto;
}
.expandingArea textarea,
.expandingArea pre {
    padding: 5px;
    background: transparent;
    font-size: 16px;
    /* Make the text soft-wrap */
    white-space: pre-wrap;
    word-wrap: break-word;
}
.expandingArea textarea {
    /* The border-box box model is used to allow
         * padding whilst still keeping the overall width
         * at exactly that of the containing element.
         */
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    -ms-box-sizing: border-box;
    box-sizing: border-box;
    width: 100%;
    /* This height is used when JS is disabled */
    height: 100px;
}
.expandingArea.active textarea {
    /* Hide any scrollbars */
    overflow: hidden;
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    /* Remove WebKit user-resize widget */
    resize: none;
}
.expandingArea pre {
    display: none;
}
.expandingArea.active pre {
    display: block;
    /* Hide the text; just using it for sizing */
    visibility: hidden;
}
.year-select {
    margin-right: 35px;
}
.editor-area {
    width: 1000px;
    margin: auto;
}
.edui-default {
    border-color: #ccc;
}
.print-btn {
    margin-right: 35px;
    outline: 0;
    width: 80px;
    height: 36px;
    border: 0;
    background: #1e9fff;
    color: #fff;
    border-radius: 5px;
    /* box-sizing: ; */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
    cursor: pointer;
}
.print-btn:hover {
    box-shadow: none;
}
@media print {
    .print-btn {
        display: none;
    }
}

.app {
    max-width: 100%;
    padding: 20px;
    background-color: white;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    margin-top: 50px;
}

.content-area {
    table-layout: fixed;
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.content-area td {
    border: 1px solid black;
    padding: 8px;
    word-wrap: break-word;
}

/* 左侧窄列样式 */
.narrow-column {
    width: 90px;
}

.input-inline td {
    width: 150px;
}