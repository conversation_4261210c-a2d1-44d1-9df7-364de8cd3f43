spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: *************************************************************************************************
      username: axxm
      password: Sd2of8aJ2KFUkd84lxa
      #      filters: stat,wall
      filters: stat
      max-active: 100
      initial-size: 10
      max-wait: 60000
      min-idle: 1
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: select '1'
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-open-prepared-statements: 50
      max-pool-prepared-statement-per-connection-size: 20

  redis:
    host: ************
    port: 6418
    password: Dlsielxiec82lfslfulssxx

anxun:
  base-url: https://dhax-beta.dahe.cn
  content-check:
    api:
      token-url: https://ysxt-beta.dahe.cn/ysxt/app/accessToken/
      refresh-token-url: https://ysxt-beta.dahe.cn/ysxt/app/refreshAccessToken/
      check-url: https://ysxt-beta.dahe.cn/ysxt/realtime/apicheck/
      app-key: 85cd716e3f2449af
      app-secret: c7ef962e2ad43ecbaa450bb3f4d1957a
      # 使用正式线扫描 403
#      token-url: https://ysxt.dahe.cn/ysxt/app/accessToken/
#      refresh-token-url: https://ysxt.dahe.cn/ysxt/app/refreshAccessToken/
#      check-url: https://ysxt.dahe.cn/ysxt/realtime/apicheck/
#      app-key: ba9a7a1319af2724
#      app-secret: 09b8013733493bebb2506097ac77b2da
  attachment:
    download-path: /data/wwwroot/anxunfile/temp
  schedule:
    xxl-job:
      admin-addresses: http://127.0.0.1:8084/xxl-job-admin
      appname: anxun-executor
      port: 9999
      log-path: /data/applogs/xxl-job/jobhandler
      log-retention-days: 30
      access-token: eyJhbGciOiJIUzI1NiJ9
#      enable: false

jodconverter:
  remote:
    enabled: true
    url: http://************:8100
    ssl:
      enabled: false


