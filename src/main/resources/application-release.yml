spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: **************************************************************************************************
      username: axxm
      password: rGjKn8yM3d84lxa
      filters: stat,wall
#      filters: stat
      max-active: 100
      initial-size: 10
      max-wait: 60000
      min-idle: 1
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: select '1'
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-open-prepared-statements: 50
      max-pool-prepared-statement-per-connection-size: 20

  redis:
    host: *************
    port: 6398
    password: Sd#mkhPM3d84lxa

anxun:
  base-url: https://anxun.dahe.cn
  content-check:
    api:
      # TODO 配置正式线
      token-url: https://ysxt.dahe.cn/ysxt/app/accessToken/
      refresh-token-url: https://ysxt.dahe.cn/ysxt/app/refreshAccessToken/
      check-url: https://ysxt.dahe.cn/ysxt/realtime/apicheck/
      app-key: ba9a7a1319af2724
      app-secret: 09b8013733493bebb2506097ac77b2da
  attachment:
    download-path: /data/wwwroot/anxunfile/temp
  schedule:
    xxl-job:
      admin-addresses: http://127.0.0.1:8084/xxl-job-admin
      appname: anxun-executor
      port: 9999
      logpath: /data/applogs/xxl-job/jobhandler
      log-retention-days: 30
      access-token: eyJhbGciOiJIUzI1NiJ9

jodconverter:
  remote:
    enabled: true
    # TODO 监控
    url: http://************:8100
    ssl:
      enabled: false

#mybatis-plus:
#  configuration:
#    ### 开启打印sql配置
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
