<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="off" monitorInterval="7200">


    <Appenders>
        <Console name="STDOUT" target="SYSTEM_OUT" follow="true">
            <PatternLayout pattern="%date{yyyy-MM-dd HH:mm:ss.SSS} %level [%thread][%file:%line] - %msg%n"/>
        </Console>
    </Appenders>

    <Loggers>
        <Logger name="cn.dahe" level="info" additivity="false">
            <AppenderRef ref="STDOUT"/>
        </Logger>
        <Logger name="org.springframework" level="info" additivity="false">
            <AppenderRef ref="STDOUT"/>
        </Logger>
        <Root level="info" includeLocation="true">
            <AppenderRef ref="STDOUT"/>
        </Root>
    </Loggers>

</Configuration>