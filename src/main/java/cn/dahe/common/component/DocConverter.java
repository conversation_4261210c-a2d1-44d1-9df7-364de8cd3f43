// package cn.dahe.common.component;
//
// import cn.hutool.core.codec.Base64;
// import cn.hutool.core.util.StrUtil;
// import lombok.extern.slf4j.Slf4j;
// import org.apache.poi.hwpf.HWPFDocument;
// import org.apache.poi.hwpf.converter.WordToHtmlConverter;
// import org.apache.poi.hwpf.usermodel.Picture;
// import org.springframework.stereotype.Component;
// import org.w3c.dom.Document;
//
// import javax.xml.parsers.DocumentBuilderFactory;
// import javax.xml.transform.OutputKeys;
// import javax.xml.transform.Transformer;
// import javax.xml.transform.TransformerFactory;
// import javax.xml.transform.dom.DOMSource;
// import javax.xml.transform.stream.StreamResult;
// import java.io.ByteArrayOutputStream;
// import java.io.File;
// import java.io.FileInputStream;
// import java.util.ArrayList;
// import java.util.List;
// import java.util.regex.Matcher;
// import java.util.regex.Pattern;
// import java.util.stream.Collectors;
//
// /**
//  * Word文档转换组件
//  */
// @Slf4j
// @Component
// public class DocConverter {
//
//     /**
//      * 将DOC文件转换为HTML
//      *
//      * @param filePath 文件路径
//      * @return HTML内容
//      */
//     public String parse(String filePath) {
//         File file = new File(filePath);
//         try (FileInputStream inputStream = new FileInputStream(file)) {
//             HWPFDocument hwpfDocument = new HWPFDocument(inputStream);
//
//             // 提取图像数据并转换为BASE64编码字符串
//             List<Picture> pictures = hwpfDocument.getPicturesTable().getAllPictures();
//             List<String> base64ImageStrings = pictures.stream()
//                     .map(picture -> StrUtil.format("data:image/{};base64,{}",
//                             picture.suggestFileExtension(),
//                             Base64.encode(picture.getContent())))
//                     .collect(Collectors.toList());
//
//             // 转换为HTML文本
//             Document document = convertDocToHtml(hwpfDocument);
//             String html = convertDocumentToString(document);
//
//             // 处理图片和标签
//             List<String> imageMatches = findImageComments(html);
//             html = cleanupHtmlTags(html);
//
//             // 替换图片链接为base64编码
//             for (int i = 0; i < base64ImageStrings.size() && i < imageMatches.size(); i++) {
//                 html = html.replace(imageMatches.get(i),
//                         "<img src=\"" + base64ImageStrings.get(i) + "\">");
//             }
//
//             return html;
//         } catch (Exception e) {
//             log.error("转换DOC文件到HTML失败：{}", filePath, e);
//             throw new RuntimeException("文档转换失败：" + e.getMessage());
//         }
//     }
//
//     /**
//      * 将Word文档转换为HTML Document
//      */
//     private Document convertDocToHtml(HWPFDocument hwpfDocument) throws Exception {
//         WordToHtmlConverter converter = new WordToHtmlConverter(
//                 DocumentBuilderFactory.newInstance().newDocumentBuilder().newDocument());
//         converter.processDocument(hwpfDocument);
//         return converter.getDocument();
//     }
//
//     /**
//      * 将Document转换为字符串
//      */
//     private String convertDocumentToString(Document document) throws Exception {
//         ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
//         Transformer transformer = TransformerFactory.newInstance().newTransformer();
//         transformer.setOutputProperty(OutputKeys.ENCODING, "UTF-8");
//         transformer.setOutputProperty(OutputKeys.INDENT, "yes");
//         transformer.setOutputProperty(OutputKeys.METHOD, "html");
//         transformer.transform(new DOMSource(document), new StreamResult(outputStream));
//         return outputStream.toString("UTF-8");
//     }
//
//     /**
//      * 查找HTML中的图片注释
//      */
//     private List<String> findImageComments(String html) {
//         List<String> matches = new ArrayList<>();
//         Pattern pattern = Pattern.compile("<!--.*?-->");
//         Matcher matcher = pattern.matcher(html);
//         while (matcher.find()) {
//             matches.add(matcher.group());
//         }
//         return matches;
//     }
//
//     /**
//      * 清理HTML标签
//      */
//     private String cleanupHtmlTags(String html) {
//         // 移除META标签
//         html = removeTag(html, "<META.*?>", Pattern.DOTALL);
//         html = removeTag(html, "<meta.*?>", Pattern.DOTALL);
//
//         // 移除body标签
//         html = removeTag(html, "<body.*?>", Pattern.DOTALL);
//
//         // 移除style标签及内容
//         html = removeTag(html, "<style.*?</style>", Pattern.DOTALL);
//
//         // 移除基本HTML标签
//         return html.replace("<html>", "")
//                 .replace("<head>", "")
//                 .replace("</head>", "")
//                 .replace("</body>", "")
//                 .replace("</html>", "");
//     }
//
//     /**
//      * 移除指定的HTML标签
//      */
//     private String removeTag(String html, String regex, int flags) {
//         Pattern pattern = Pattern.compile(regex, flags);
//         Matcher matcher = pattern.matcher(html);
//         if (matcher.find()) {
//             html = html.replace(matcher.group(), "");
//         }
//         return html;
//     }
// }
