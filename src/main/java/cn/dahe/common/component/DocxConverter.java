// package cn.dahe.common.component;
//
// import cn.hutool.core.io.FileUtil;
// import cn.hutool.core.map.MapUtil;
// import cn.hutool.core.util.StrUtil;
// import lombok.extern.slf4j.Slf4j;
// import org.springframework.stereotype.Component;
// import org.zwobble.mammoth.DocumentConverter;
// import org.zwobble.mammoth.Result;
//
// import javax.annotation.PostConstruct;
// import java.io.File;
// import java.io.IOException;
// import java.util.Map;
//
// import static org.zwobble.mammoth.internal.util.Base64Encoding.streamToBase64;
//
// /**
//  * DOCX文档转换组件
//  */
// @Slf4j
// @Component
// public class DocxConverter {
//
//     private DocumentConverter documentConverter;
//
//     @PostConstruct
//     public void init() {
//         documentConverter = new DocumentConverter()
//                 .imageConverter(image -> {
//                     try {
//                         String base64 = streamToBase64(image::getInputStream);
//                         Map<String, String> attributes = MapUtil.newHashMap();
//                         attributes.put("src", StrUtil.format("data:{};base64,{}",
//                                 image.getContentType(),
//                                 base64));
//                         return attributes;
//                     } catch (Exception e) {
//                         log.error("转换图片失败", e);
//                         return MapUtil.newHashMap();
//                     }
//                 });
//     }
//
//     /**
//      * 将DOCX文件转换为HTML
//      *
//      * @param filePath DOCX文件路径
//      * @return HTML内容
//      */
//     public String parse(String filePath) {
//         try {
//             File file = FileUtil.file(filePath);
//
//             Result<String> result = documentConverter.convertToHtml(file);
//
//             // 记录警告信息
//             if (!result.getWarnings().isEmpty()) {
//                 log.warn("转换文件 {} 时有警告：{}", filePath, result.getWarnings());
//             }
//
//             return result.getValue();
//         } catch (IOException e) {
//             log.error("转换DOCX文件到HTML失败：{}", filePath, e);
//             throw new RuntimeException("文档转换失败：" + e.getMessage());
//         }
//     }
// }
