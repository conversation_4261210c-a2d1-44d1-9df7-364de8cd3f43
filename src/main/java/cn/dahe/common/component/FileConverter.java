package cn.dahe.common.component;

import cn.dahe.check.exception.ContentGetException;
import cn.hutool.core.exceptions.ExceptionUtil;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.fit.pdfdom.PDFDomTree;
import org.jodconverter.core.DocumentConverter;
import org.jodconverter.core.document.DefaultDocumentFormatRegistry;
import org.jodconverter.core.document.DocumentFormat;
import org.jodconverter.core.office.OfficeException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.*;
import java.nio.charset.StandardCharsets;

@Slf4j
@Component
public class FileConverter {
    @Resource
    private DocumentConverter documentConverter;

    /**
     * 文件类型枚举
     */
    @Getter
    public enum FileType {
        DOC(DefaultDocumentFormatRegistry.DOC),
        DOCX(DefaultDocumentFormatRegistry.DOCX),
        // PDF(DefaultDocumentFormatRegistry.PDF),
        PPT(DefaultDocumentFormatRegistry.PPT),
        PPTX(DefaultDocumentFormatRegistry.PPTX),
        XLS(DefaultDocumentFormatRegistry.XLS),
        XLSX(DefaultDocumentFormatRegistry.XLSX),
        TXT(DefaultDocumentFormatRegistry.TXT),
        WPS(DefaultDocumentFormatRegistry.DOCX);

        private final DocumentFormat format;

        FileType(DocumentFormat format) {
            this.format = format;
        }

        /**
         * 根据文件扩展名获取文件类型
         */
        public static FileType fromExtension(String fileName) {
            String ext = fileName.substring(fileName.lastIndexOf(".") + 1).toUpperCase();
            try {
                return FileType.valueOf(ext);
            } catch (IllegalArgumentException e) {
                throw new RuntimeException("不支持的文件类型：" + ext);
            }
        }
    }

    /**
     * 通用的文件转HTML方法
     *
     * @param filePath 文件路径
     * @param fileType 文件类型
     * @return HTML字符串
     */
    public String convertToHtml(String filePath, FileType fileType) {
        log.info("开始转换文件：{}", filePath);
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            documentConverter.convert(new File(filePath))
                    .as(fileType.getFormat())
                    .to(outputStream)
                    .as(DefaultDocumentFormatRegistry.HTML)
                    .execute();
            return outputStream.toString(StandardCharsets.UTF_8.name());
        } catch (OfficeException | IOException e) {
            throw ContentGetException.parseFail(e.getMessage(), ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 自动检测文件类型并转换为HTML
     *
     * @param filePath 文件路径
     * @return HTML字符串
     */
    public String convertToHtml(String filePath) {
        FileType fileType = FileType.fromExtension(filePath);
        return convertToHtml(filePath, fileType);
    }

    public String convertPdfToHtml(String filePath) {
        try (PDDocument pdf = PDDocument.load(new File(filePath));
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
             Writer writer = new PrintWriter(outputStream)) {
            new PDFDomTree().writeText(pdf, writer);
            writer.flush();
            return outputStream.toString(StandardCharsets.UTF_8.name());
        } catch (Exception e) {
            throw ContentGetException.parseFail(e.getMessage(), ExceptionUtil.stacktraceToString(e));
        }
    }

}
