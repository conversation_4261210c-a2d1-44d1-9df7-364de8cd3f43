package cn.dahe.common.config;

import com.xxl.job.core.executor.impl.XxlJobSpringExecutor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * XXL-JOB配置
 * 只有当enable-xxl-job=true时才启用
 *
 * <AUTHOR>
 * @date 2025-09-09
 */
@Data
@Slf4j
@Configuration
@ConfigurationProperties(prefix = "anxun.schedule.xxl-job")
@ConditionalOnProperty(name = "anxun.schedule.xxl-job.enable", havingValue = "true")
public class XxlJobConfig {

    private boolean enable;

    private String adminAddresses;

    private String accessToken;

    private String appname;

    private String address;

    private String ip;

    private int port;

    private String logPath;

    private int logRetentionDays;

    @Bean
    public XxlJobSpringExecutor xxlJobExecutor() {
        log.info(">>>>>>>>>>> XXL-JOB 已启用，配置初始化");
        log.info("xxl配置参数如下：{} {} {} {}:{} {}", adminAddresses, appname, address, ip, port, logPath);

        // 确保日志目录存在
        java.io.File logDir = new java.io.File(logPath);
        if (!logDir.exists()) {
            boolean created = logDir.mkdirs();
            if (created) {
                log.info("XXL-JOB 日志目录创建成功: {}", logPath);
            } else {
                log.warn("XXL-JOB 日志目录创建失败: {}", logPath);
            }
        } else {
            log.info("XXL-JOB 日志目录: {}", logPath);
        }

        XxlJobSpringExecutor xxlJobSpringExecutor = new XxlJobSpringExecutor();
        xxlJobSpringExecutor.setAdminAddresses(adminAddresses);
        xxlJobSpringExecutor.setAppname(appname);
        xxlJobSpringExecutor.setAddress(address);
        xxlJobSpringExecutor.setIp(ip);
        xxlJobSpringExecutor.setPort(port);
        xxlJobSpringExecutor.setAccessToken(accessToken);
        xxlJobSpringExecutor.setLogPath(logPath);
        xxlJobSpringExecutor.setLogRetentionDays(logRetentionDays);

        return xxlJobSpringExecutor;
    }
}