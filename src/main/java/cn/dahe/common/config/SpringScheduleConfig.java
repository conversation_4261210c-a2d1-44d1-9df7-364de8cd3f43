package cn.dahe.common.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * Spring定时任务配置
 * 只有当XXL-JOB未启用时，才启用Spring自带的定时任务
 *
 * <AUTHOR>
 * @date 2025-09-09
 */
@Slf4j
@Configuration
@Data
@EnableScheduling
@ConfigurationProperties(prefix = "anxun.schedule.spring-schedule")
@ConditionalOnProperty(name = "anxun.schedule.spring-schedule.enable", havingValue = "true", matchIfMissing = false)
public class SpringScheduleConfig {

    private boolean enable;

    public SpringScheduleConfig() {
        log.info(">>>>>>>>>>> Spring Schedule 已启用");
    }
}