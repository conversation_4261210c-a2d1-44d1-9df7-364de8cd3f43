package cn.dahe.common.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import org.springdoc.core.customizers.GlobalOpenApiCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpHeaders;
import org.springframework.util.AntPathMatcher;

import static cn.dahe.common.config.cas.FilterConfig.authorization_url_pattern_array;

@Configuration
public class OpenApiConfig {

    @Bean
    public OpenAPI customOpenAPI() {


        return new OpenAPI()
                .info(new Info()
                        .title("安巡系统API文档")
                        .description("安巡系统后端的所有接口文档")
                        .version("1.0")
                        .contact(new Contact().name("大河网").url("http://www.dahe.cn")))
                .components(new Components()
                        .addSecuritySchemes(HttpHeaders.AUTHORIZATION,
                                new SecurityScheme().name(HttpHeaders.AUTHORIZATION).type(SecurityScheme.Type.HTTP)))
                .addSecurityItem(new SecurityRequirement().addList(HttpHeaders.AUTHORIZATION));
    }

    public static final AntPathMatcher MATCHER = new AntPathMatcher();

    @Bean
    public GlobalOpenApiCustomizer orderGlobalOpenApiCustomizer() {
        return openApi -> {
            // 全局添加鉴权参数
            if (openApi.getPaths() != null) {
                openApi.getPaths().forEach((path, pathItem) -> {
                    // 检查当前路径是否需要鉴权
                    boolean needAuth = false;
                    for (String pattern : authorization_url_pattern_array) {
                        if (MATCHER.match(pattern, path)) {
                            needAuth = true;
                            break;
                        }
                    }

                    // 只为需要鉴权的路径添加鉴权
                    if (needAuth) {
                        pathItem.readOperations().forEach(operation -> {
                            operation.addSecurityItem(new SecurityRequirement().addList(HttpHeaders.AUTHORIZATION));
                        });
                    }
                });
            }

        };
    }
} 