package cn.dahe.common.config.cas;


import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;

@Configuration
public class FilterConfig {


    /**
     * 跨域过滤器
     *
     * @return
     */
    @Bean
    public FilterRegistrationBean<CorsFilter> corsFilter() {
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        CorsConfiguration config = new CorsConfiguration();
        config.setAllowCredentials(true);
        // 允许的源，*表示允许所有源
        // config.addAllowedOrigin("*");
        config.addAllowedOriginPattern("*");
        // 允许的请求头
        config.addAllowedHeader("*");
        // 允许的请求方法
        config.addAllowedMethod("*");
        // 预检请求的有效期
        // config.setMaxAge(3600L);

        source.registerCorsConfiguration("/**", config);
        FilterRegistrationBean<CorsFilter> bean = new FilterRegistrationBean<>(new CorsFilter(source));
        // 设置过滤器的优先级
        bean.setOrder(Ordered.HIGHEST_PRECEDENCE);
        return bean;
    }
    // @Bean
    // public FilterRegistrationBean<cn.dahe.common.config.cas.CorsFilter> myFilterRegistration() {
    //     FilterRegistrationBean<cn.dahe.common.config.cas.CorsFilter> registration = new FilterRegistrationBean<>();
    //     registration.setFilter(new cn.dahe.common.config.cas.CorsFilter());
    //     registration.addUrlPatterns("/*");
    //     registration.setName("CorsFilter");
    //     registration.setOrder(Ordered.HIGHEST_PRECEDENCE); // 设置执行顺序，较小的 order 值优先执行
    //     return registration;
    // }


    // knife4j 资源
    String[] IGNORE_URL_ARRAY = {"/swagger-resources", "/v2/api-docs", "/doc.html",
            "/webjars/**", "/favicon.ico"};
    public static String[] authorization_url_array = {"/pro/*"};
    public static String[] authorization_url_pattern_array = {"/pro/**"};

    /**
     * 获取当前登录用户信息
     */
    @Bean
    public FilterRegistrationBean<LoginFilter> getLoginUserInfoFilter() {
        FilterRegistrationBean<LoginFilter> registrationBean = new FilterRegistrationBean<>(new LoginFilter());
        registrationBean.addUrlPatterns(authorization_url_array);
        registrationBean.setName("loginFilter");
        registrationBean.setOrder(1);
        return registrationBean;
    }


}

