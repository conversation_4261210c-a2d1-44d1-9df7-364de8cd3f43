package cn.dahe.common.config.cas;

import cn.dahe.common.constants.RoleConstants;

import java.util.*;


public class CASUtil {

    public static boolean isAdmin(Collection<String> userRoleSns) {
        boolean isAdmin = false;
        Set<String> adminRoles = RoleConstants.adminRoles;
        for (String role : userRoleSns) {
            if (adminRoles.contains(role)) {
                isAdmin = true;
                break;
            }
        }
        return isAdmin;
    }


}


