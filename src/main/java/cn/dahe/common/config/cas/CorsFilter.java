package cn.dahe.common.config.cas;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2023-06-29
 */
public class CorsFilter implements Filter {

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        // 设置允许跨域的域名，这里使用通配符表示允许任意域名跨域
        httpResponse.setHeader("Access-Control-Allow-Origin", "*");
        // 设置允许携带的请求头信息，包括token
        httpResponse.setHeader("Access-Control-Allow-Headers", "Origin, X-Requested-With, Content-Type, Accept, Authorization");
        // 设置允许的请求方法，如GET、POST等
        httpResponse.setHeader("Access-Control-Allow-Methods", "*");
        // 设置是否允许携带凭证，如cookie
        httpResponse.setHeader("Access-Control-Allow-Credentials", "true");
        // 处理options请求，直接返回
        if (request instanceof HttpServletRequest) {
            HttpServletRequest httpRequest = (HttpServletRequest) request;
            if (httpRequest.getMethod().equalsIgnoreCase("OPTIONS")) {
                httpResponse.setStatus(HttpServletResponse.SC_OK);
                return;
            }
        }
        // 继续执行请求链
        chain.doFilter(request, response);
    }

}
