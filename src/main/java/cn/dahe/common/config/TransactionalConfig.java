//package cn.dahe.common.config;
//
//import org.aspectj.lang.annotation.Aspect;
//import org.springframework.aop.Advisor;
//import org.springframework.aop.aspectj.AspectJExpressionPointcut;
//import org.springframework.aop.support.DefaultPointcutAdvisor;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.transaction.TransactionDefinition;
//import org.springframework.transaction.TransactionManager;
//import org.springframework.transaction.interceptor.*;
//
//import javax.annotation.Resource;
//import java.util.Collections;
//import java.util.HashMap;
//import java.util.Map;
//
///**
// * <AUTHOR>
// * on 2021/08/03
// * description：配置事务
// */
//@Aspect
//@Configuration
//public class TransactionalConfig {
//
//    /**
//     * 配置切入点表达式
//     */
//    private static final String POINTCUT_EXPRESSION = "execution(* cn.dahe.service.*.*(..))";
//
//    @Resource
//    private TransactionManager transactionManager;
//
//
//    @Bean
//    public TransactionInterceptor txAdvice() {
//        //事务管理规则，声明具备事务管理的方法名
//        NameMatchTransactionAttributeSource source = new NameMatchTransactionAttributeSource();
//        //只读事务，不做更新操作
//        RuleBasedTransactionAttribute readOnly = new RuleBasedTransactionAttribute();
//        readOnly.setReadOnly(true);
//        readOnly.setPropagationBehavior(TransactionDefinition.PROPAGATION_NOT_SUPPORTED);
//        //当前存在事务就使用当前事务，当前不存在事务就创建一个新的事务
//        RuleBasedTransactionAttribute required = new RuleBasedTransactionAttribute();
//        //抛出异常后执行切点回滚,这边你可以更换异常的类型
//        required.setRollbackRules(Collections.singletonList(new RollbackRuleAttribute(Exception.class)));
//        //PROPAGATION_REQUIRED:事务隔离性为1，若当前存在事务，则加入该事务；如果当前没有事务，则创建一个新的事务。这是默认值
//        required.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
//        Map<String, TransactionAttribute> attributesMap = new HashMap<>(30);
//        //设置增删改上传等使用事务
//        attributesMap.put("save*", required);
//        attributesMap.put("remove*", required);
//        attributesMap.put("update*", required);
//        attributesMap.put("add*", required);
//        attributesMap.put("modify*", required);
//        attributesMap.put("edit*", required);
//        attributesMap.put("insert*", required);
//        attributesMap.put("delete*", required);
//        attributesMap.put("del*", required);
//        attributesMap.put("*", readOnly);
//        source.setNameMap(attributesMap);
//        return new TransactionInterceptor(transactionManager, source);
//    }
//
//    /**
//     * 设置切面=切点pointcut+通知TxAdvice
//     */
//    @Bean
//    public Advisor txAdviceAdvisor() {
//        //声明切点的面：切面就是通知和切入点的结合。通知和切入点共同定义了关于切面的全部内容——它的功能、在何时和何地完成其功能
//        AspectJExpressionPointcut pointcut = new AspectJExpressionPointcut();
//        //声明和设置需要拦截的方法,用切点语言描写
//        pointcut.setExpression(POINTCUT_EXPRESSION);
//        //设置切面=切点pointcut+通知TxAdvice
//        return new DefaultPointcutAdvisor(pointcut, txAdvice());
//    }
//
//}
