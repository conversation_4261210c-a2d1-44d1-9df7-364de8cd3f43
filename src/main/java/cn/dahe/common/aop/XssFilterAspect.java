package cn.dahe.common.aop;

import cn.dahe.common.annotation.XssFilter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringEscapeUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * XSS过滤切面
 * 通过AOP自动处理标记了@XssFilter注解的方法
 *
 * <AUTHOR>
 */
@Aspect
@Component
@Slf4j
public class XssFilterAspect {

    /**
     * 拦截所有标记了@XssFilter注解的方法
     */
    @Around("@annotation(xssFilter)")
    public Object filterXss(ProceedingJoinPoint joinPoint, XssFilter xssFilter) throws Throwable {
        try {
            Object[] args = joinPoint.getArgs();
            if (args != null && args.length > 0) {
                // 获取需要过滤的字段名集合
                Set<String> targetFields = new HashSet<>();
                if (xssFilter.fields().length > 0) {
                    targetFields.addAll(Arrays.asList(xssFilter.fields()));
                }

                // 处理每个参数
                for (Object arg : args) {
                    if (arg != null) {
                        filterObject(arg, targetFields, xssFilter.filterAllStrings(), xssFilter.type());
                    }
                }
            }
            
            // 执行原方法
            return joinPoint.proceed();
        } catch (Exception e) {
            log.error("XSS过滤处理异常", e);
            return joinPoint.proceed();
        }
    }

    /**
     * 递归过滤对象中的String字段
     */
    private void filterObject(Object obj, Set<String> targetFields, boolean filterAllStrings, XssFilter.FilterType filterType) {
        if (obj == null) {
            return;
        }

        Class<?> clazz = obj.getClass();
        
        // 跳过基本类型
        if (clazz.isPrimitive()) {
            return;
        }

        // 获取所有字段（包括私有字段）
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            try {
                field.setAccessible(true);
                Object value = field.get(obj);
                
                if (value instanceof String) {
                    String stringValue = (String) value;
                    if (stringValue != null && shouldFilterField(field.getName(), targetFields, filterAllStrings)) {
                        String filteredValue = StringEscapeUtils.escapeHtml4(stringValue);
                        field.set(obj, filteredValue);
                        log.debug("XSS过滤字段: {}.{} = {}", clazz.getSimpleName(), field.getName(), filteredValue);
                    }
                } else if (value != null && !value.getClass().isPrimitive()) {
                    // 递归处理嵌套对象
                    filterObject(value, targetFields, filterAllStrings, filterType);
                }
            } catch (Exception e) {
                log.warn("处理字段 {} 时发生异常: {}", field.getName(), e.getMessage());
            }
        }
    }

    /**
     * 判断是否需要过滤该字段
     */
    private boolean shouldFilterField(String fieldName, Set<String> targetFields, boolean filterAllStrings) {
        if (targetFields.isEmpty()) {
            return filterAllStrings;
        }
        return targetFields.contains(fieldName);
    }
}
