package cn.dahe.common.model;

import cn.hutool.http.HttpStatus;

import java.util.Arrays;
import java.util.Optional;

public enum ResultCode {
    /**
     * 系统错误
     */
    Success(HttpStatus.HTTP_OK, "操作成功"),
    Fail(HttpStatus.HTTP_INTERNAL_ERROR, "操作失败"),
    INTERNAL_SERVER_ERROR(HttpStatus.HTTP_INTERNAL_ERROR, "服务器异常，请稍后再试"),
    MissingError(400, "缺少路径变量"),
    NotSupportError(400, "HttpRequest方法不支持"),
    METHOD_NOT_ALLOWED(405, "HttpRequest方法不支持。请确认使用的请求方法是否正确"),
    ILLEGAL_HEADER(4001, "非法请求头,请添加合适的签名"),
    REPLAY_ERROR(4002, "访问已过期,请重新访问!"),
    ARGUMENT_ERROR(4003, "您正在尝试恶意访问，已记录IP备案！"),
    NotFindError(10001, "未查询到信息"),
    SaveError(10002, "保存信息失败"),
    UpdateError(10003, "更新信息失败"),
    ValidateError(10004, "数据检验失败"),
    StatusHasValid(10005, "状态已经被启用"),
    StatusHasInvalid(10006, "状态已经被禁用"),
    SystemError(10007, "系统异常"),
    BusinessError(10008, "业务异常"),
    ParamSetIllegal(10009, "参数设置非法"),
    TransferStatusError(10010, "当前状态不正确，请勿重复提交"),
    NotGrant(401, "没有操作该功能的权限，请联系管理员"),
    UploadError(10012, "上传失败，请联系管理员"),
    NoLoginError(10014, "当前用户未登录，请刷新重新登录");
    /**
     * code的取值规则，xx代表模块，xxx代表功能异常 例如：基础模块（10）的查询异常（001）
     */
    private Integer code;
    /**
     * 异常信息
     */
    private String msg;


    ResultCode(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getMsg() {
        return this.msg;
    }

    public static Optional<ResultCode> of(Integer code) {
        return Arrays.stream(ResultCode.values()).filter(resultCode -> resultCode.getCode().equals(code)).findFirst();
    }
}
