package cn.dahe.common.advice;


import cn.dahe.common.model.ResultCode;
import cn.dahe.model.dto.Result;
import cn.hutool.core.util.StrUtil;
import com.github.houbb.resubmit.api.exception.ResubmitException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.InvalidPropertyException;
import org.springframework.beans.TypeMismatchException;
import org.springframework.http.HttpStatus;
import org.springframework.jdbc.BadSqlGrammarException;
import org.springframework.validation.BindException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletResponse;
import java.util.stream.Collectors;


/**
 * 捕获全局异常
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(BusinessException.class)
    public Result<String> methodArgumentNotValidHandler(BusinessException ex) {
        return Result.error(ResultCode.Fail, ex.getMessage());
    }

    @ExceptionHandler(ResubmitException.class)
    public Result<String> resubmitException(ResubmitException ex) {
        //  目前有bug 暂时先别用 TODO
        log.warn("重复提交：【{}】", ex.getMessage());
        return Result.error(ResultCode.Fail, "请勿连续提交");
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Result<String> methodArgumentNotValidHandler(MethodArgumentNotValidException ex) {
        String message = ex.getBindingResult().getFieldErrors().stream()
                .map(fieldError -> StrUtil.format("{}：{}：{}", fieldError.getField(), fieldError.getRejectedValue(), fieldError.getDefaultMessage()))
                .collect(Collectors.joining(";"));
        return Result.error(ResultCode.ParamSetIllegal, message);
    }

    /**
     * 处理参数校验异常
     */
    @ExceptionHandler(BindException.class)
    @ResponseBody
    public Result<String> handleValidationException(BindException ex) {
        String message = ex.getBindingResult().getFieldErrors().stream()
                .map(fieldError -> StrUtil.format("{}：{}：{}", fieldError.getField(), fieldError.getRejectedValue(), fieldError.getDefaultMessage()))
                .collect(Collectors.joining(";"));
        return Result.error(ResultCode.ValidateError, message);
    }

    @ExceptionHandler(InvalidPropertyException.class)
    @ResponseBody
    public Result<String> handleValidationException(InvalidPropertyException ex) {
        String message = StrUtil.format("{}：{}", ex.getPropertyName(), ex.getMessage());
        return Result.error(ResultCode.ValidateError, message);
    }

    /**
     * 请求参数不全
     *
     * @param ex 异常
     * @return 返回值
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    public Result<String> missingServletRequestParameterException(MissingServletRequestParameterException ex) {
        log.warn("请求参数不全：【{}】", ex.getMessage());
        return Result.error(ResultCode.ParamSetIllegal);
    }

    @ExceptionHandler(TypeMismatchException.class)
    public Result<String> typeMismatchException(TypeMismatchException ex) {
        log.warn("请求参数类型不正确：【{}】", ex.getMessage());
        return Result.error(ResultCode.ParamSetIllegal);
    }

    /**
     * 未知异常捕获
     *
     * @param e 异常
     * @return 结果
     */
    @ExceptionHandler(Exception.class)
    @ResponseBody
    public Result<String> handleException(Exception e, HttpServletResponse response) {
        log.info("GlobalException-->Exception【{}】", e.getMessage());
        e.printStackTrace();
        //  用于处理部分接口（尤其是导出）对相应contentType的修改
        response.reset();
        return Result.error(ResultCode.INTERNAL_SERVER_ERROR);
    }


    @ExceptionHandler(BadSqlGrammarException.class)
    public Result<String> handleBadSqlGrammarException(BadSqlGrammarException e) {
        log.info("GlobalException-->BadSqlGrammarException报错信息\n{}", e.getMessage());
        return Result.error(ResultCode.SystemError);
    }

    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    @ExceptionHandler(NotPermissionException.class)
    @ResponseBody
    public Result<String> handleNotPermissionException(NotPermissionException e) {
        log.info("GlobalException-->NotPermissionException【{}】", e.getMessage());
        return Result.error(ResultCode.NotGrant, e.getMessage());
    }

    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    @ExceptionHandler(NotRoleException.class)
    @ResponseBody
    public Result<String> handleNotRoleException(NotRoleException e) {
        log.info("GlobalException-->NotRoleException【{}】", e.getMessage());
        return Result.error(ResultCode.NotGrant, e.getMessage());
    }

    @ResponseStatus(HttpStatus.METHOD_NOT_ALLOWED)
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    @ResponseBody
    public Result<String> handleMethodNotSupportedException(HttpRequestMethodNotSupportedException e) {
        log.info("GlobalException-->handleMethodNotSupportedException【{}】", e.getMessage());
        e.printStackTrace();
        return Result.error(ResultCode.METHOD_NOT_ALLOWED);
    }

    @ExceptionHandler(NotLoginException.class)
    @ResponseBody
    public Result<Object> handleNotLoginException(NotLoginException e) {
        log.info("GlobalException-->NotLoginException【{}】", e.getMessage());
        return Result.error(ResultCode.NoLoginError, e.getMessage(), null);
    }

    @ExceptionHandler(IllegalArgumentException.class)
    @ResponseBody
    public Result<Object> commonExceptionResolve(IllegalArgumentException e) {
        log.info("GlobalException-->IllegalArgumentException【{}】", e.getMessage());
        return Result.error(ResultCode.Fail, e.getMessage(), "");
    }

}
