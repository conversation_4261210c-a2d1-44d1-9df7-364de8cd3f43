package cn.dahe.common.constants;

/**
 * * <AUTHOR>
 * * @date 2023-06-29
 */
public interface CacheConstants {

    /**
     * 缓存有效期，默认720（分钟）
     */
    public final static long EXPIRATION = 720;

    public final static long EXPIRATION_MOBILE = 30;

    /**
     * 缓存刷新时间，默认120（分钟）
     */
    public final static long REFRESH_TIME = 120;

    /**
     * 密码最大错误次数
     */
    public final static int PASSWORD_MAX_RETRY_COUNT = 5;

    /**
     * 密码锁定时间，默认10（分钟）
     */
    public final static long PASSWORD_LOCK_TIME = 10;


    public final static String LOGIN_TOKEN_KEY = "hlht:login_tokens:";

    public final static String LOGIN_USER_KEY = "hlht:login_user:";

//**************************SSO 相关****************************

    public final static String SMS_CODE_BIND_PHONE = "dhax:sms_code:bind:phone_";


    public final static String SMS_CODE_LOGIN_PHONE = "dhax:sms_code:bind:phone_";
    /**
     * 用户验证码
     */
    public static final String KEY_USER_CHECK_CODE="user:check_code:";

}
