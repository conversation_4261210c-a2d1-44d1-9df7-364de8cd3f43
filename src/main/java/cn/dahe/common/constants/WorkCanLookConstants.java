package cn.dahe.common.constants;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024-03-01
 */
public class WorkCanLookConstants {


//   142 报纸专业委员会（筹备组）：HNXWJbz
//   143 省广电协会：HNXWJgbds
//   144 行业报刊专业委员会（筹备组）：HNXWJhybk
//   145 省报业协会：HNXWJbzzx
//   146 中央驻豫（境外）媒体专业委员会(筹备组)：HNXWJzyzy

    /**
     * 管理员账号
     * 规则 depid--->(depType--->dictIdProjects)
     */
    public static final HashMap<Integer, Map<Integer, List<String>>> HNXWJ_BZ = new HashMap<>();
    public static final HashMap<Integer, Map<Integer, List<String>>> HNXWJ_GBDS = new HashMap<>();
    public static final HashMap<Integer, Map<Integer, List<String>>> HNXWJ_HYBK = new HashMap<>();
    public static final HashMap<Integer, Map<Integer, List<String>>> HNXWJ_BZZX = new HashMap<>();
    public static final HashMap<Integer, Map<Integer, List<String>>> HNXWJ_ZYZY = new HashMap<>();


    //    /**
//     * 部门类型/类别
//     * 1.报纸A
//     * 2.广播电视B
//     * 3.行业报、高校报、省直有关旗杆C
//     * 4.省直有关期刊
//     * 5.高校报
//     * 6.新媒体D
//     * 7.县级融媒体E
//     * 8.中央、香港 F
//     */

    static {

        // list的0代表看不到 -1代表全部
        //   142 报纸专业委员会（筹备组）：HNXWJbz
        HashMap<Integer, List<String>> map1 = new HashMap<>();
        map1.put(1, Arrays.asList("12", "13", "14", "15", "16", "17", "21", "22", "25", "7", "8", "9", "10", "11", "64"));
        map1.put(2, Arrays.asList("25"));
        map1.put(3, Arrays.asList("25"));
        map1.put(4, Arrays.asList("25"));
        map1.put(5, Arrays.asList("25"));
        map1.put(6, Arrays.asList("25"));
        map1.put(7, Arrays.asList("25"));
        map1.put(8, Arrays.asList("0"));
        HNXWJ_BZ.put(142, map1);

        //   143 省广电协会：HNXWJgbds
        HashMap<Integer, List<String>> map2 = new HashMap<>();
        map2.put(1, Arrays.asList("0"));
        map2.put(2, Arrays.asList("-1"));
        map2.put(3, Arrays.asList("0"));
        map2.put(4, Arrays.asList("0"));
        map2.put(5, Arrays.asList("0"));
        map2.put(6, Arrays.asList("0"));
        map2.put(7, Arrays.asList("0"));
        map2.put(8, Arrays.asList("0"));
        HNXWJ_GBDS.put(143, map2);

        //   144 行业报刊专业委员会（筹备组）：HNXWJhybk
        HashMap<Integer, List<String>> map3 = new HashMap<>();
        map3.put(1, Arrays.asList("0"));
        map3.put(2, Arrays.asList("0"));
        map3.put(3, Arrays.asList("12", "13", "14", "15", "16", "17", "21", "22", "7", "8", "9", "10", "11", "64"));
        map3.put(4, Arrays.asList("12", "13", "14", "15", "16", "17", "21", "22", "7", "8", "9", "10", "11", "64"));
        map3.put(5, Arrays.asList("12", "13", "14", "15", "16", "17", "21", "22", "7", "8", "9", "10", "11", "64"));
        map3.put(6, Arrays.asList("0"));
        map3.put(7, Arrays.asList("0"));
        map3.put(8, Arrays.asList("0"));
        HNXWJ_HYBK.put(144, map3);


        //   145 省报业协会：HNXWJbzzx
        HashMap<Integer, List<String>> map4 = new HashMap<>();
        map4.put(1, Arrays.asList("18", "19", "20", "23", "24"));
        map4.put(2, Arrays.asList("18", "19", "20", "23", "24"));
        map4.put(3, Arrays.asList("18", "19", "20", "23", "24"));
        map4.put(4, Arrays.asList("18", "19", "20", "23", "24"));
        map4.put(5, Arrays.asList("18", "19", "20", "23", "24"));
        map4.put(6, Arrays.asList("18", "19", "20", "23", "24"));
        map4.put(7, Arrays.asList("18", "19", "20", "23", "24"));
        map4.put(8, Arrays.asList("0"));
        HNXWJ_BZZX.put(145, map4);


        //   146 中央驻豫（境外）媒体专业委员会(筹备组)：HNXWJzyzy
        HashMap<Integer, List<String>> map5 = new HashMap<>();
        map4.put(1, Arrays.asList("0"));
        map4.put(2, Arrays.asList("0"));
        map4.put(3, Arrays.asList("0"));
        map4.put(4, Arrays.asList("0"));
        map4.put(5, Arrays.asList("0"));
        map4.put(6, Arrays.asList("0"));
        map4.put(7, Arrays.asList("0"));
        map4.put(8, Arrays.asList("-1"));
        HNXWJ_ZYZY.put(146, map5);
    }


    public static Map<Integer, Map<Integer, List<String>>> getMap(Integer depID) {
        switch (depID) {
            case 142:
                return HNXWJ_BZ;
            case 143:
                return HNXWJ_GBDS;
            case 144:
                return HNXWJ_HYBK;
            case 145:
                return HNXWJ_BZZX;
            case 146:
                return HNXWJ_ZYZY;
            default:
                return null;
        }
    }


}
