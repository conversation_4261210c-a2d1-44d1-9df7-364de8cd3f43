package cn.dahe.common.constants;

/**
 * 定时任务时间常量类
 * 用于统一管理所有定时任务的执行时间
 * 优化时间安排，避免多个任务同时执行导致资源竞争
 */
public class ScheduledTaskConstants {
    
    /**
     * 每日凌晨1点10分执行
     */
    public static final String DAILY_1_AM = "0 10 1 * * ?";
    
    /**
     * 每5分钟执行一次，偏移15秒避免与其他任务冲突
     */
    public static final String EVERY_5_MINUTES = "15 0/5 * * * ?";
    
    /**
     * 每10分钟执行一次，偏移30秒避免与其他任务冲突
     */
    public static final String EVERY_10_MINUTES = "30 0/10 * * * ?";
    
    /**
     * 每30分钟执行一次，偏移45秒避免与其他任务冲突
     */
    public static final String EVERY_30_MINUTES = "45 */30 * * * ?";
    
    /**
     * 每59秒执行一次
     */
    public static final String EVERY_59_SECONDS = "0/59 * * * * ?";
    
    /**
     * 每天凌晨3点15分执行，避免与其他凌晨任务冲突
     */
    public static final String DAILY_3_AM = "0 15 3 * * ?";
    
    /**
     * 每天凌晨3点45分执行，避免与其他凌晨任务冲突
     */
    public static final String DAILY_3_30_AM = "0 45 3 * * ?";
    
    /**
     * 每天凌晨2点30分执行，避免与其他凌晨任务冲突
     */
    public static final String DAILY_2_AM = "0 30 2 * * ?";
}