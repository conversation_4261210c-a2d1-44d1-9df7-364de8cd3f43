package cn.dahe.common.constants;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 角色相关
 *
 * <AUTHOR>
 * @date 2023-07-05
 */
public class RoleConstants {
    /**
     * 超级管理员
     */
    public static final String ADMIN_SUPER = "CJGLY";

    /**
     * 小管理员
     */
    public static final String ADMIN_SMALL = "XGLY";

    /**
     * 默认
     */
    public static final String defaultRole = "MR";

    /**
     * 管理员的角色
     */
    public static Set<String> adminRoles = new HashSet<>(Arrays.asList(ADMIN_SUPER, ADMIN_SMALL));

    public static Set<String> adminRoles_and_expert = new HashSet<>(Arrays.asList(ADMIN_SUPER, ADMIN_SMALL));
}
