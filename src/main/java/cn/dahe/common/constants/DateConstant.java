package cn.dahe.common.constants;

/**
 * Created by lzd
 * 2018/11/28
 * 日期常量
 *
 * <AUTHOR>
 */
public class DateConstant {
    /**
     * 年月日时分秒
     */
    public static final String DATE_YMD_HSM = "yyyy-MM-dd HH:mm:ss";

    /**
     * 年月
     */
    public static final String DATE_YM = "yyyy-MM";

    /**
     * 年月日
     */
    public static final String DATE_YMD = "yyyy-MM-dd";
    /**
     * 年月日（不带间隔符）
     */
    public static final String DATE_YMD_WITHOUT_PATTERN = "yyyyMMdd";
    /**
     * 年月日（不带间隔符）
     */
    public static final String DATE_YM_WITHOUT_PATTERN = "yyyyMM";
    /**
     * 年月日时分
     */
    public static final String DATE_YMD_HS = "yyyy-MM-dd HH:mm";
    /**
     * 年月日时分
     */
    public static final String DATE_YMD2_HS = "yyyy/MM/dd HH:mm";
    /**
     * 年月日时分秒（不带间隔符）
     */
    public static final String DATE_YMDHMS = "yyyyMMddHHmmss";
    /**
     * 月日（不带间隔符）
     */
    public static final String DATE_MD_WITHOUT_PATTERN = "MMdd";
    /**
     * 月日
     */
    public static final String DATE_MD = "MM-dd";
    /**
     * 时分
     */
    public static final String DATE_HM = "HH:mm";
    /**
     * 时分秒
     */
    public static final String DATE_HMS = "HH:mm:ss";
    /**
     * 年
     */
    public static final String DATE_Y = "yyyy";
    /**
     * 月
     */
    public static final String DATE_M = "MM";
    /**
     * 日
     */
    public static final String DATE_D = "dd";
    /**
     * 时
     */
    public static final String DATE_H = "HH";
    /**
     * 分
     */
    public static final String DATE_MINUTE = "mm";
    /**
     * 秒
     */
    public static final String DATE_S = "ss";
    /**
     * 月日 时分
     */
    public static final String DATE_MD_HMINUTE = "MM-dd HH:mm";
}
