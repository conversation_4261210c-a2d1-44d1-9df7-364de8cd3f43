package cn.dahe.common.auth;


import cn.dahe.common.context.SecurityContextHolder;
import cn.dahe.common.constants.SecurityConstants;
import cn.dahe.common.constants.TokenConstants;
import cn.dahe.utils.StringUtils;
import cn.dahe.model.vo.LoginUserVO;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;
import java.util.Set;

public class SecurityUtils {


    public static String getToken() {
        return getToken(Objects.requireNonNull(ServletUtils.getRequest()));
    }



    /**
     * 获取登录用户信息
     */
    public static LoginUserVO getLoginUser() {
        return SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUserVO.class);
    }

    /**
     * 判断用户是否含有某个角色
     *
     * @param role 角色tag
     * @return 结果
     */
    public static boolean hasRole(String role) {
        LoginUserVO loginUser = getLoginUser();
        Set<String> roles = loginUser.getRoles();
        return roles.contains(role);
    }

    /**
     * 判断用户是否含有权限
     *
     * @param authority 权限
     * @return
     */
    public static boolean hasAuthority(String authority) {
        LoginUserVO loginUser = getLoginUser();
        Set<String> permissions = loginUser.getPermissions();
        return permissions.contains(authority);
    }


    /**
     * 根据request获取请求token
     */
    public static String getToken(HttpServletRequest request) {
        // 从header获取token标识
        String token = request.getHeader(TokenConstants.AUTHENTICATION);
        return replaceTokenPrefix(token);
    }

    /**
     * 裁剪token前缀
     */
    public static String replaceTokenPrefix(String token) {
        // 如果前端设置了令牌前缀，则裁剪掉前缀
        if (StringUtils.isNotEmpty(token) && token.startsWith(TokenConstants.PREFIX)) {
            return token.replaceFirst(TokenConstants.PREFIX, "");
        }
        return token;
    }


}
