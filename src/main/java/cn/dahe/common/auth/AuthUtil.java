package cn.dahe.common.auth;


import cn.dahe.common.aop.role.RequiresPermissions;
import cn.dahe.common.aop.role.RequiresRoles;

/**
 * <AUTHOR>
 */
public class AuthUtil {
    public static AuthLogic authLogic = new AuthLogic();


    public static void checkLogin() {
        authLogic.checkLogin();
    }


    /**
     * 根据注解传入参数鉴权, 如果验证未通过，则抛出异常: NotRoleException
     *
     * @param requiresRoles 角色权限注解
     */
    public static void checkRole(RequiresRoles requiresRoles) {
        authLogic.checkRole(requiresRoles);
    }

    /**
     * 根据注解传入参数鉴权, 如果验证未通过，则抛出异常: NotPermissionException
     *
     * @param requiresPermissions 权限注解
     */
    public static void checkPermi(RequiresPermissions requiresPermissions) {
        authLogic.checkPermi(requiresPermissions);
    }
}
