package cn.dahe.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * XSS过滤注解
 * 用于标记需要进行XSS过滤的方法
 *
 * <AUTHOR>
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface XssFilter {

    /**
     * 需要过滤的字段名，支持多个字段
     * 如果不指定，则过滤所有String类型字段
     */
    String[] fields() default {};

    /**
     * 是否过滤所有String字段
     * 默认true，如果指定了fields则此配置无效
     */
    boolean filterAllStrings() default true;

    /**
     * 过滤类型
     * HTML4: 使用StringEscapeUtils.escapeHtml4
     * HTML: 使用StringEscapeUtils.escapeHtml
     * JAVASCRIPT: 使用StringEscapeUtils.escapeJavaScript
     */
    FilterType type() default FilterType.HTML4;

    enum FilterType {
        HTML4,
        HTML,
        JAVASCRIPT
    }
}
