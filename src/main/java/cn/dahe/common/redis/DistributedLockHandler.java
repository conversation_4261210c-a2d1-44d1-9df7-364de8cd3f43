package cn.dahe.common.redis;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.TimeUnit;

/**
 * Redis分布式锁处理器
 */
@Slf4j
@Component
public class DistributedLockHandler {

    @Resource
    private RedisService redisService;

    private static final long LOCK_TIMEOUT = 5;
    private static final long RENEWAL_INTERVAL = 2;
    private static final String LOCK_VALUE_PREFIX = "LOCK";

    /**
     * 获取分布式锁并执行任务
     *
     * @param lockKey 锁的key
     * @param task    要执行的任务
     */
    public void executeWithLock(String lockKey, Runnable task) {
        String lockValue = StrUtil.format("LOCK:{}:{}", StrUtil.uuid(), System.currentTimeMillis());
        Timer renewalTimer = null;
        try {
            // 尝试获取锁
            boolean locked = redisService.setIfAbsent(lockKey, lockValue, LOCK_TIMEOUT, TimeUnit.MINUTES);
            if (!locked) {
                log.info("获取锁失败，任务已被其他进程执行: {}", lockKey);
                return;
            }

            // 创建一个定时器用于续期
            renewalTimer = new Timer(true);
            renewalTimer.scheduleAtFixedRate(new TimerTask() {
                @Override
                public void run() {
                    try {
                        // 检查锁是否仍然存在且属于当前进程
                        String currentValue = redisService.getCacheObject(lockKey);
                        if (lockValue.equals(currentValue)) {
                            // 续期
                            redisService.setCacheObject(lockKey, lockValue, LOCK_TIMEOUT, TimeUnit.MINUTES);
                            log.debug("续期成功: {}", lockKey);
                        } else {
                            // 锁已经不属于当前进程，取消续期
                            this.cancel();
                            log.warn("锁已经不属于当前进程，取消续期: {}", lockKey);
                        }
                    } catch (Exception e) {
                        log.error("续期异常: {}", lockKey, e);
                        this.cancel();
                    }
                }
            }, TimeUnit.MINUTES.toMillis(RENEWAL_INTERVAL), TimeUnit.MINUTES.toMillis(RENEWAL_INTERVAL));

            // 执行任务
            task.run();

        } finally {
            // 停止续期定时器
            if (renewalTimer != null) {
                renewalTimer.cancel();
            }

            // 检查锁是否仍然属于当前进程，如果是则删除锁
            String currentValue = redisService.getCacheObject(lockKey);
            if (lockValue.equals(currentValue)) {
                redisService.deleteObject(lockKey);
                log.debug("释放锁成功: {}", lockKey);
            }
        }
    }
}
