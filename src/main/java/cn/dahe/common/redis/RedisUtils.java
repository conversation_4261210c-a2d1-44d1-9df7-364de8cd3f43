package cn.dahe.common.redis;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class RedisUtils {
    
    private static RedisTemplate<String, Object> staticRedisTemplate;
    
    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @PostConstruct
    public void init() {
        staticRedisTemplate = redisTemplate;
    }

    /**
     * 设置缓存
     */
    public static void set(String key, Object value) {
        staticRedisTemplate.opsForValue().set(key, value);
    }

    /**
     * 设置缓存并设置过期时间
     */
    public static void set(String key, Object value, long timeout, TimeUnit unit) {
        staticRedisTemplate.opsForValue().set(key, value, timeout, unit);
    }

    /**
     * 获取缓存
     */
    public static Object get(String key) {
        return staticRedisTemplate.opsForValue().get(key);
    }

    /**
     * 删除缓存
     */
    public static Boolean delete(String key) {
        return staticRedisTemplate.delete(key);
    }

    /**
     * 批量删除缓存
     */
    public static Long delete(Collection<String> keys) {
        return staticRedisTemplate.delete(keys);
    }

    /**
     * 设置过期时间
     */
    public static Boolean expire(String key, long timeout, TimeUnit unit) {
        return staticRedisTemplate.expire(key, timeout, unit);
    }

    /**
     * 判断key是否存在
     */
    public static Boolean hasKey(String key) {
        return staticRedisTemplate.hasKey(key);
    }

    /**
     * 获取过期时间
     */
    public static Long getExpire(String key, TimeUnit unit) {
        return staticRedisTemplate.getExpire(key, unit);
    }

    /**
     * Hash操作 - 设置字段值
     */
    public static void hSet(String key, String field, Object value) {
        staticRedisTemplate.opsForHash().put(key, field, value);
    }

    /**
     * Hash操作 - 获取字段值
     */
    public static Object hGet(String key, String field) {
        return staticRedisTemplate.opsForHash().get(key, field);
    }

    /**
     * Hash操作 - 设置多个字段值
     */
    public static void hSetAll(String key, Map<String, Object> map) {
        staticRedisTemplate.opsForHash().putAll(key, map);
    }

    /**
     * Hash操作 - 获取所有字段值
     */
    public static Map<Object, Object> hGetAll(String key) {
        return staticRedisTemplate.opsForHash().entries(key);
    }

    /**
     * List操作 - 从左侧添加
     */
    public static Long lLeftPush(String key, Object value) {
        return staticRedisTemplate.opsForList().leftPush(key, value);
    }

    /**
     * List操作 - 从右侧添加
     */
    public static Long lRightPush(String key, Object value) {
        return staticRedisTemplate.opsForList().rightPush(key, value);
    }

    /**
     * List操作 - 获取指定范围的元素
     */
    public static List<Object> lRange(String key, long start, long end) {
        return staticRedisTemplate.opsForList().range(key, start, end);
    }

    /**
     * Set操作 - 添加元素
     */
    public static Long sAdd(String key, Object... values) {
        return staticRedisTemplate.opsForSet().add(key, values);
    }

    /**
     * Set操作 - 获取所有元素
     */
    public static Set<Object> sMembers(String key) {
        return staticRedisTemplate.opsForSet().members(key);
    }

    /**
     * 增加值
     */
    public static Long increment(String key, long delta) {
        return staticRedisTemplate.opsForValue().increment(key, delta);
    }

    /**
     * 减少值
     */
    public static Long decrement(String key, long delta) {
        return staticRedisTemplate.opsForValue().decrement(key, delta);
    }
}
