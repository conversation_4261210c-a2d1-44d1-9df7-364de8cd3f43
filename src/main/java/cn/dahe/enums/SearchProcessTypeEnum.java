package cn.dahe.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Getter
@AllArgsConstructor
public enum SearchProcessTypeEnum implements BaseEnum {
    WEBSITE(0, "网站", Collections.singletonList(ProcessTypeEnum.WEBSITE.value)),
    WEIXIN(1, "微信公众号", Collections.singletonList(ProcessTypeEnum.WeChat_Official.value)),
    WEIBO(2, "微博", Collections.singletonList(ProcessTypeEnum.Weibo.value)),
    TOUTIAO(3, "今日头条", Collections.singletonList(ProcessTypeEnum.Toutiao.value)),
    OTHER(4, "其他平台", Arrays.asList(ProcessTypeEnum.Douyin.value, ProcessTypeEnum.KuaiShou.value, ProcessTypeEnum.WeChat_Channel.value, ProcessTypeEnum.Xiaohongshu.value));

    public final Integer value;
    public final String desc;

    private final List<Integer> processTypeValues;

    public static SearchProcessTypeEnum getByValue(Integer value) {
        return PackageEnumUtil.getEnumByValue(SearchProcessTypeEnum.class, value, WEBSITE);
    }
}
