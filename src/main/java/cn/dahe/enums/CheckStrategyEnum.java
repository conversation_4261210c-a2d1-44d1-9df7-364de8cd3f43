package cn.dahe.enums;

import cn.dahe.utils.PackageEnumUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 检查策略枚举
 */
@Getter
@AllArgsConstructor
public enum CheckStrategyEnum implements BaseEnum {

    DEFAULT(0, "默认策略"),
    /**
     * 策略1
     */
    FIRST(1, "策略1"),

    SECOND(2, "策略2"),
    ;

    private final Integer value;
    private final String desc;

    public static CheckStrategyEnum getByValue(Integer value) {
        return PackageEnumUtil.getEnumByValue(CheckStrategyEnum.class, value, DEFAULT);
    }
} 