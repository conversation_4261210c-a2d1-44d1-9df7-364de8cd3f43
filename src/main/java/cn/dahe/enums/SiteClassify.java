package cn.dahe.enums;

public enum SiteClassify {
    we<PERSON><PERSON>(12, "微信"),
    <PERSON><PERSON><PERSON>(13, "微博"),
    <PERSON><PERSON><PERSON>(19, "头条"),
    <PERSON><PERSON><PERSON>(27, "抖音"),
    <PERSON><PERSON><PERSON><PERSON>(28, "快手"),
    shipin(29, "视频号"),
    <PERSON><PERSON><PERSON>(30, "小红书");

    public final int value;
    public final String info;

    SiteClassify(int value, String info) {
        this.value = value;
        this.info = info;
    }

    public static String get(int value) {
        for (SiteClassify type : SiteClassify.values()) if (type.value == value) return type.info;
        return null;
    }
}
