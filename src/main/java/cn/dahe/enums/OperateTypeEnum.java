package cn.dahe.enums;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Getter;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
@Getter
@AllArgsConstructor
public enum OperateTypeEnum {
    /**
     * 查询  查询可用于查看记录
     */
    GET(1, "查询"),
    /**
     * 新增
     */
    CREATE(2, "新增"),
    /**
     * 修改
     */
    UPDATE(3, "修改"),
    /**
     * 删除
     */
    DELETE(4, "删除"),
    /**
     * 导出
     */
    EXPORT(5, "导出"),
    /**
     * 导入
     */
    IMPORT(6, "导入"),

    LOGIN(7, "登陆"),

    LOGOUT(8, "登出"),

    /**
     * 验证信息
     */
    AUTHENTICATION(9, "验证信息"),
    /**
     * 其 它
     * <p>
     * 在无法归类时，可以选择使用其它。因为还有操作名可以进一步标识123
     */
    OTHER(0, "其它");

    /**
     * 类型
     */
    private final Integer type;
    private final String desc;


    public static OperateTypeEnum getEnumByType(int type) {
        OperateTypeEnum[] values = OperateTypeEnum.values();
        for (OperateTypeEnum value : values) {
            if (value.getType() == type) {
                return value;
            }
        }
        return null;
    }
}
