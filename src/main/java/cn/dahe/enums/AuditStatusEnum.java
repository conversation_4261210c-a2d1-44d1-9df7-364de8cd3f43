package cn.dahe.enums;

import cn.dahe.utils.PackageEnumUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AuditStatusEnum implements BaseEnum {

    WAITING_FOR_REVIEW(0, "待审核"),
    PASS(1, "审核通过"),
    REJECT(2, "审核拒绝");

    private final Integer value;

    private final String desc;

    public static AuditStatusEnum getByValue(Integer value) {
        return PackageEnumUtil.getEnumByValue(AuditStatusEnum.class, value);
    }

}
