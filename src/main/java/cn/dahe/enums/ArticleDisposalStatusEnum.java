package cn.dahe.enums;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.annotation.DefaultValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025-07-16
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
@Getter
@AllArgsConstructor
public enum ArticleDisposalStatusEnum {

    WEI_CHU_ZHI(0, "未处置"),
    YUE_TAN_ZHENG_GAI(1, "已约谈整改"),
    SHANG_BAO_XIAN_SUO(2, "已上报线索"),
    GUAN_TING_ZHAN_DIAN(3, "已关停站点");

    public static ArticleDisposalStatusEnum getEnumByType(Integer type) {
        for (ArticleDisposalStatusEnum value : ArticleDisposalStatusEnum.values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }

    private final Integer type;

    private final String desc;
}
