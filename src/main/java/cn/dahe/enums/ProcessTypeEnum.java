package cn.dahe.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ProcessTypeEnum implements BaseEnum {
    WEBSITE(0, "网站", true, "/icon/website.svg"),
    WeChat_Official(1, "公众号", true, "/icon/weixin.svg"),
    <PERSON><PERSON>(2, "微博", true, "/icon/weibo.svg"),
    <PERSON><PERSON><PERSON>(3, "头条号", true, "/icon/toutiao.svg"),
    <PERSON><PERSON><PERSON>(6335, "抖音", false, "/icon/douyin.svg"),
    <PERSON><PERSON><PERSON><PERSON>(6429, "快手", false, "/icon/kuaishou.svg"),
    WeChat_Channel(6452, "视频号", false, "/icon/wechat-channel.svg"),
    <PERSON><PERSON><PERSON>(6460, "小红书", false, "/icon/xiaohongshu.svg");

    public final Integer value;
    public final String info;
    public final boolean enabled;
    public final String logoPath;

    public static ProcessTypeEnum getByValue(Integer value) {
        return PackageEnumUtil.getEnumByValue(ProcessTypeEnum.class, value, WEBSITE);
    }
}
