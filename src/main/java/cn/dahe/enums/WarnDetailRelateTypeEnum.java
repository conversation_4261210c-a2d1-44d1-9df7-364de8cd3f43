package cn.dahe.enums;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 内容获取失败类型枚举
 *
 * <AUTHOR>
 * @date 2024-03-21
 */
@Getter
@AllArgsConstructor
@Schema(description = "预警消息关联枚举")
public enum WarnDetailRelateTypeEnum implements BaseEnum {

    /**
     * 未失败
     */
    ARTICLE(0, "内容检查预警"),

    /**
     * 内容获取失败
     */
    ATTACHMENT(1, "附件检查预警");

    private final Integer value;
    private final String desc;

    public WarnDetailRelateTypeEnum getByValue(Integer value) {
        return PackageEnumUtil.getEnumByValue(WarnDetailRelateTypeEnum.class, value);
    }


}
