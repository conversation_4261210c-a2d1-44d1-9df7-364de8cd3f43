package cn.dahe.enums;

import cn.dahe.utils.PackageEnumUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 检查错误等级枚举
 *
 * <AUTHOR>
 * @date 2024-03-21
 */
@Getter
@AllArgsConstructor
@Schema(description = "检查错误等级枚举")
public enum CheckErrorLevelEnum implements BaseEnum{

    /**
     * 严重错误
     */
    SERIOUS(1, "严重错误"),

    /**
     * 一般错误
     */
    NORMAL(2, "一般错误"),

    /**
     * 可疑错误
     */
    SUSPICIOUS(3, "可疑错误"),

    /**
     * 自定义词
     */
    CUSTOM(4, "自定义词"),

    /**
     * 风险提示
     */
    RISK_TIPS(5, "风险提示");

    private final Integer value;
    private final String desc;

    public static CheckErrorLevelEnum getByValue(Integer value) {
        return PackageEnumUtil.getEnumByValue(CheckErrorLevelEnum.class, value);
    }


}
