package cn.dahe.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 文章排序类型枚举
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Getter
@AllArgsConstructor
public enum ArticleSortTypeEnum {
    /**
     * 按发布时间降序（默认）
     */
    PUB_TIME_DESC("ta.pub_time", 0, "desc", 0, "发布时间降序"),

    /**
     * 按发布时间升序
     */
    PUB_TIME_ASC("ta.pub_time", 0, "asc", 1, "发布时间升序"),

    /**
     * 按检测时间降序
     */
    CHECK_TIME_DESC("tct.check_time", 1, "desc", 0, "检测时间降序"),

    /**
     * 按检测时间升序
     */
    CHECK_TIME_ASC("tct.check_time", 1, "asc", 1, "检测时间升序");

    /**
     * 排序字段
     */
    private final String field;

    public final Integer fieldNum;

    /**
     * 排序方向
     */
    private final String direction;

    private final Integer directionNum;

    /**
     * 描述
     */
    private final String description;

    /**
     * 获取默认排序类型
     */
    public static ArticleSortTypeEnum getDefault() {
        return PUB_TIME_DESC;
    }

    /**
     * 根据code获取枚举
     */
    public static ArticleSortTypeEnum getByFieldAndDirection(Integer field, Integer direction) {
        if (field == null || direction == null) {
            return getDefault();
        }
        for (ArticleSortTypeEnum type : values()) {
            if (field.equals(type.fieldNum) && direction.equals(type.directionNum)) {
                return type;
            }
        }
        return getDefault();
    }
} 