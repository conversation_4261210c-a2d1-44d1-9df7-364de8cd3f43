package cn.dahe.enums;

public enum TenantStatus {
    enable(1, "正常"),
    disable(2, "禁用"),
    expire(3, "过期"),
    delete(4, "删除");

    public final int value;
    public final String info;

    TenantStatus(int value, String info) {
        this.value = value;
        this.info = info;
    }

    public static TenantStatus get(int value) {
        for (TenantStatus status : TenantStatus.values()) if (status.value == value) return status;
        return null;
    }
}
