package cn.dahe.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 内容获取失败类型枚举
 *
 * <AUTHOR>
 * @date 2024-03-21
 */
@Getter
@AllArgsConstructor
@Schema(description = "内容获取失败类型枚举")
public enum ContentFailTypeEnum implements BaseEnum {

    /**
     * 未失败
     */
    NONE(0, "未失败"),

    /**
     * 内容获取失败
     */
    CONTENT_GET_FAILED(1, "内容获取失败"),

    /**
     * 内容解析失败
     */
    CONTENT_PARSE_FAILED(2, "内容解析失败"),

    /**
     * 内容保存失败
     */
    CONTENT_SAVE_FAILED(3, "内容保存失败");

    @JsonValue
    private final Integer value;
    private final String desc;
    

}
