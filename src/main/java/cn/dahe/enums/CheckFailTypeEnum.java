package cn.dahe.enums;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 检查任务失败类型枚举
 *
 * <AUTHOR>
 * @date 2024-03-21
 */
@Getter
@AllArgsConstructor
@Schema(description = "检查任务失败类型枚举")
public enum CheckFailTypeEnum implements BaseEnum {

    /**
     * 未失败
     */
    NONE(0, "未失败"),

    /**
     * 本地异常
     */
    LOCAL_EXCEPTION(1, "本地异常"),

    /**
     * 接口调用失败
     */
    API_CALL_FAILED(2, "接口调用失败"),

    /**
     * 接口响应失败
     */
    API_RESPONSE_FAILED(3, "接口响应失败"),

    /**
     * 任务未启动
     */
    TASK_NOT_STARTED(4, "任务未启动");

    private final Integer value;
    private final String desc;


}
