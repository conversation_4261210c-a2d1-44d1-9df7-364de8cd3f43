package cn.dahe.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 检查结果显示类型枚举
 */
@Getter
@AllArgsConstructor
public enum CheckResultDisplayTypeEnum implements BaseEnum {

    /**
     * 普通显示 指显示正文和标题扫描结果
     */
    NORMAL(0, "普通显示"),

    /**
     * 快照显示 指显示源码扫描结果
     */
    SNAPSHOT(1, "快照显示");

    /**
     * 显示类型值
     */
    private final Integer value;

    /**
     * 显示类型描述
     */
    private final String desc;


}