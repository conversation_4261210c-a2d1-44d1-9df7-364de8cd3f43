package cn.dahe.enums;

import cn.dahe.utils.PackageEnumUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 文章错误位置枚举
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Getter
@AllArgsConstructor
public enum ArticleLocationEnum implements BaseEnum{

    /**
     * 正文
     */
    CONTENT(0, "正文"),

    /**
     * 标题
     */
    TITLE(1, "标题"),
    WEB_CODE(2, "网页源码");

    /**
     * 位置值
     */
    private final Integer value;

    /**
     * 位置描述
     */
    private final String desc;

    /**
     * 根据值获取枚举
     *
     * @param value 位置值
     * @return 对应的枚举值，如果没找到返回null
     */
    public static ArticleLocationEnum getByValue(Integer value) {
        return PackageEnumUtil.getEnumByValue(ArticleLocationEnum.class, value);
    }
}