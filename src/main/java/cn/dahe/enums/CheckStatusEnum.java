package cn.dahe.enums;

import cn.dahe.utils.PackageEnumUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 文章检查状态枚举
 *
 * <AUTHOR>
 * @date 2024-01-24
 */
@Getter
@AllArgsConstructor
@Schema(description = "文章检查状态枚举")
public enum CheckStatusEnum implements BaseEnum {

    /**
     * 未检查
     */
    UNCHECKED(0, "未检查", "lock:check:unchecked"),

    /**
     * 已检查
     */
    CHECKED(1, "已检查", null),

    /**
     * 检查失败待重试
     */
    FAILED_RETRY(2, "检查失败待重试", "lock:check:failed_retry"),

    /**
     * 由于某些原因不再进行检查 比如文件过大 或者其他原因
     */
    FAILED(3, "检查中止", null);

    private final Integer value;
    private final String desc;
    private final String lockPrefix;


    public static CheckStatusEnum getByValue(Integer value) {
        return PackageEnumUtil.getEnumByValue(CheckStatusEnum.class, value, UNCHECKED);
    }
}