package cn.dahe.enums;

import cn.dahe.utils.PackageEnumUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 文章检查状态枚举
 *
 * <AUTHOR>
 * @date 2024-01-24
 */
@Getter
@AllArgsConstructor
@Schema(description = "文章内容保存状态枚举")
public enum CheckContentStatusEnum implements BaseEnum {

    /**
     * 未检查
     */
    UNKNOWN(0, "未保存"),

    /**
     * 已保存 但不代表一定有数据
     */
    SAVE_SUCCESS(1, "已保存"),

    /**
     * 保存失败 重试
     */
    SAVE_FAIL_RETRY(2, "保存失败"),

    SAVE_FAIL_END(3, "保存失败并终止");

    private final Integer value;
    private final String desc;

    /**
     * 根据code获取枚举
     *
     * @param value 状态码
     * @return 对应的枚举值，如果没找到返回null
     */
    public static CheckContentStatusEnum getByValue(Integer value) {
        return PackageEnumUtil.getEnumByValue(CheckContentStatusEnum.class, value, UNKNOWN);
    }
}