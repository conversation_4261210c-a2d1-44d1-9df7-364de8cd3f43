package cn.dahe.enums;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 文章检查状态枚举
 *
 * <AUTHOR>
 * @date 2024-01-24
 */
@Getter
@AllArgsConstructor
@Schema(description = "文章检查结果状态枚举")
public enum CheckResultStatusEnum implements BaseEnum {

    /**
     * 未检查
     */
    UNKNOWN(0, "未检查"),

    /**
     * 已检查
     */
    HAS_RESULT(1, "有结果"),

    /**
     * 检查失败待重试
     */
    NOT_HAS_RESULT(2, "无结果");


    private final Integer value;
    private final String desc;


}