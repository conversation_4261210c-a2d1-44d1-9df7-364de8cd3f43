package cn.dahe.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum WordFilterStatusEnum implements BaseEnum {

    NOT_REJECT(0, "未过滤"),
    REJECT(1, "已过滤");

    private final Integer value;

    private final String desc;

    public static WordFilterStatusEnum getByValue(Integer value) {
        return PackageEnumUtil.getEnumByValue(WordFilterStatusEnum.class, value, NOT_REJECT);
    }

}
