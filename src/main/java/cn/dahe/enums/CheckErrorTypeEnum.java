package cn.dahe.enums;

import cn.dahe.utils.PackageEnumUtil;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 检查错误类型枚举
 *
 * <AUTHOR>
 * @date 2024-03-21
 */
@Getter
@AllArgsConstructor
@Schema(description = "检查错误类型枚举")
public enum CheckErrorTypeEnum implements BaseEnum{

    /**
     * 错词
     */
    WRONG_WORD(101, "错词"),

    /**
     * 敏感词
     */
    SENSITIVE_WORD(102, "敏感词");

    @JsonValue
    private final Integer value;
    private final String desc;

    public static CheckErrorTypeEnum getByValue(Integer value) {
        return PackageEnumUtil.getEnumByValue(CheckErrorTypeEnum.class, value);
    }
}
