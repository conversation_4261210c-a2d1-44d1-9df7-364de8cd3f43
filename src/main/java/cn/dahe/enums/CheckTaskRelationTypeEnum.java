package cn.dahe.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 检查任务关联类型枚举
 *
 * <AUTHOR> Assistant
 * @date 2024-03-21
 */
@Getter
@AllArgsConstructor
public enum CheckTaskRelationTypeEnum implements BaseEnum {

    /**
     * 文章检查
     */
    ARTICLE(0, "文章初查"),

    /**
     * 附件检查
     */
    ATTACHMENT(1, "附件"),

    ARTICLE_REVIEW(2, "文章复查"),
    ;

    /**
     * 类型编码
     */
    private final Integer value;

    /**
     * 类型描述
     */
    private final String desc;

    public static CheckTaskRelationTypeEnum getByValue(Integer value) {
        return PackageEnumUtil.getEnumByValue(CheckTaskRelationTypeEnum.class, value);
    }

}
