package cn.dahe.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 栏目操作类型枚举
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
@Getter
@AllArgsConstructor
public enum ChannelOperationTypeEnum {
    /**
     * 新增栏目
     */
    ADD(1, "新增栏目"),
    
    /**
     * 修改栏目
     */
    UPDATE(2, "修改栏目"),
    
    /**
     * 删除栏目
     */
    DELETE(3, "删除栏目"),
    
    /**
     * 下发预警
     */
    WARNING(4, "下发预警");

    // 标记数据库存储的字段值
    @EnumValue
    private final Integer type;
    private final String desc;

    /**
     * 根据类型获取枚举
     */
    public static ChannelOperationTypeEnum getEnumByType(Integer type) {
        for (ChannelOperationTypeEnum operationType : ChannelOperationTypeEnum.values()) {
            if (operationType.getType().equals(type)) {
                return operationType;
            }
        }
        return null;
    }
}