package cn.dahe.controller.check;

import cn.dahe.check.service.CheckService;
import cn.dahe.common.model.ResultCode;
import cn.dahe.entity.Article;
import cn.dahe.entity.Attachment;
import cn.dahe.entity.CheckTask;
import cn.dahe.enums.CheckContentStatusEnum;
import cn.dahe.model.dto.Result;
import cn.dahe.model.vo.CheckSnapshotVO;
import cn.dahe.service.ArticleService;
import cn.dahe.service.AttachmentService;
import cn.dahe.service.CheckTaskService;
import cn.dahe.task.ArticleCheckInitTask;
import cn.dahe.task.CheckDataRetryTask;
import cn.dahe.task.CheckDataTask;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 内容检查调试接口
 */
@RestController
@RequestMapping("/pro/check/")
public class CheckController {

    @Resource
    private CheckTaskService checkTaskService;
    @Resource
    private CheckService checkService;
    @Resource
    private ArticleService articleService;

    @PostMapping("/snapshot/info/{taskId}")
    public Result<CheckSnapshotVO> snapshotInfo(@PathVariable Long taskId) {
        CheckSnapshotVO vo = checkService.snapshotResult(taskId);
        return vo != null ? Result.ok(vo) : Result.error(ResultCode.NotFindError);
    }

    /**
     * 获取快照内容  有些过大所以和信息分开
     */
    @GetMapping(value = "/snapshot/content/{taskId}", produces = "text/html;charset=UTF-8")
    public String snapshotContent(@PathVariable Long taskId) {
        return checkService.snapshotContent(taskId);
    }


    @PostMapping("/tryCheck")
    public void tryCheck(@RequestParam Long checkId) {
        CheckTask checkTask = checkTaskService.getById(checkId);
        checkTaskService.updateById(checkTask.setCheckStatus(0).setResultStatus(0));
        checkService.doCheck(checkTask);
    }

    @Resource
    private CheckDataTask checkDataTask;
    @Resource
    private ArticleCheckInitTask articleCheckInit;

    @PostMapping("/articleCheckInit")
    public void articleCheckInit() {
        articleCheckInit.articleCheckInit();
    }

    @PostMapping("/checkDataTask")
    public void checkDataTask() {
        checkDataTask.checkDataTask();
    }

    @Resource
    private CheckDataRetryTask checkDataRetryTask;

    @PostMapping("/checkDataRetryTask")
    public void checkDataRetryTask() {
        checkDataRetryTask.checkDataRetryTask();
    }

    @PostMapping("/initCheck")
    public void initCheck(@RequestParam Long articleId) {
        Article article = articleService.getById(articleId);
        checkService.initCheckTaskForArticle(article);
    }

    @Resource
    private AttachmentService attachmentService;

    @PostMapping("/getContentForAttachment")
    public Result<Long> getContentForAttachment(@RequestParam Long attachmentId) {
        Attachment attachment = attachmentService.getById(attachmentId);
        if (attachment == null) {
            return Result.error("附件不存在");
        }
        if (attachment.getCheckId() == null) {
            checkService.initCheckTaskForAttachment(attachment);
            attachment = attachmentService.getById(attachmentId);
        } else {
            //  重置获取内容状态
            checkTaskService.lambdaUpdate()
                    .eq(CheckTask::getId, attachment.getCheckId())
                    .set(CheckTask::getContentStatus, CheckContentStatusEnum.UNKNOWN.getValue())
                    .update();
        }
        CheckTask checkTask = checkTaskService.getById(attachment.getCheckId());
        checkService.getContentForAttachment(attachment);
        return Result.ok(checkTask.getId());
    }
}
