package cn.dahe.controller.check;

import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.Result;
import cn.dahe.model.query.ReprintSourceQuery;
import cn.dahe.model.vo.ReprintSourceVO;
import cn.dahe.service.ReprintSourceService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 网站转载信源管理接口 目前只对网站有效
 */
@Tag(name = "网站转载信源管理")
@RestController
@RequestMapping("/pro/reprint-source")
@RequiredArgsConstructor
@Validated
public class ReprintSourceController {

    private final ReprintSourceService reprintSourceService;

    @Operation(summary = "分页查询转载信源列表")
    @PostMapping("/page")
    public Result<PageResult<ReprintSourceVO>> pageList(ReprintSourceQuery query) {
        Page<ReprintSourceVO> page = reprintSourceService.pageList(query);
        return Result.ok(PageResult.page(page));
    }

    @Operation(summary = "导出")
    @GetMapping("/export")
    public void export(ReprintSourceQuery query) {
        reprintSourceService.exportByQuery(query);
    }

    @Operation(summary = "批量添加转载信源过滤")
    @Parameters({
            @Parameter(name = "websiteIds", description = "站点ID集合", required = true),
            @Parameter(name = "reprintSources", description = "转载信源名称集合", required = true)
    })
    @PostMapping("/batch-add")
    public Result<Void> batchAdd(@RequestParam List<Long> websiteIds, @RequestParam List<String> reprintSources) {
        reprintSourceService.batchCreate(websiteIds, reprintSources);
        return Result.ok();
    }

    @Operation(summary = "批量更新过滤状态")
    @Parameters({
            @Parameter(name = "ids", description = "信源ID集合", required = true),
            @Parameter(name = "filterStatus", description = "过滤状态 1过滤 0 取消过滤", required = true)
    })
    @PostMapping("/filter")
    public Result<Void> batchUpdateFilterStatus(@RequestParam List<Long> ids, @RequestParam Integer filterStatus) {
        reprintSourceService.batchUpdateFilterStatus(ids, filterStatus != 0);
        return Result.ok();
    }
}
