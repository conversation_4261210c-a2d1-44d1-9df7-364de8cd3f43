package cn.dahe.controller.check;

import cn.dahe.model.dto.Result;
import cn.dahe.service.CheckWordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

/**
 * 文章检查词库管理
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Tag(name = "文章检查词库管理")
@Validated
@RestController
@RequestMapping("/pro/check/word")
public class CheckWordController {

    @Resource
    private CheckWordService checkWordService;

    @Operation(summary = "更新检查词过滤状态")
    @PostMapping("/filter/{wordId}")
    public Result<Boolean> updateFilterStatus(
            @Parameter(description = "检查词ID", required = true)
            @PathVariable("wordId") @NotNull(message = "检查词ID不能为空") Long wordId,
            @Parameter(description = "是否过滤（1-过滤，0-取消过滤）", required = true)
            @RequestParam @NotNull(message = "过滤状态不能为空") Integer filterStatus
    ) {
        //  TODO 检测词是否存在
        boolean success = checkWordService.updateFilterStatus(wordId, filterStatus != 0);
        return success ? Result.ok() : Result.error();
    }
}