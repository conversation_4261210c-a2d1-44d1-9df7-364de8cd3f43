package cn.dahe.controller.check;

import cn.dahe.model.dto.Result;
import cn.dahe.entity.CheckSource;
import cn.dahe.service.CheckSourceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 校对服务来源Controller
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@RestController
@RequestMapping("/pro/check/source")
public class CheckSourceController {

    @Autowired
    private CheckSourceService checkSourceService;

    /**
     * 获取所有来源列表
     */
    @GetMapping("/list")
    public Result<List<CheckSource>> list() {
        return Result.ok(checkSourceService.list());
    }

    /**
     * 根据来源编码获取来源信息
     */
    @GetMapping("/code/{sourceCode}")
    public Result<CheckSource> getBySourceCode(@PathVariable String sourceCode) {
        return Result.ok(checkSourceService.getBySourceCode(sourceCode));
    }

    /**
     * 新增来源
     */
    @PostMapping
    public Result<CheckSource> save(@RequestBody CheckSource source) {
        checkSourceService.save(source);
        return Result.ok(source);
    }

    /**
     * 更新来源
     */
    @PutMapping("/{id}")
    public Result<Boolean> update(@PathVariable Long id, CheckSource source) {
        source.setId(id);
        return Result.ok(checkSourceService.updateById(source));
    }

    /**
     * 删除来源
     */
    @DeleteMapping("/{id}")
    public Result<Boolean> delete(@PathVariable Long id) {
        return Result.ok(checkSourceService.removeById(id));
    }
} 