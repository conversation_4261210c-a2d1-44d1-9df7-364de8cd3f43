package cn.dahe.controller.check;

import cn.dahe.model.dto.Result;
import cn.dahe.entity.CheckErrorLevel;
import cn.dahe.entity.CheckErrorType;
import cn.dahe.service.CheckErrorLevelService;
import cn.dahe.service.CheckErrorTypeService;
import cn.dahe.model.vo.check.FirstLevelErrorTypeVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 内容检查错误类型Controller
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Tag(name = "错误类型errorType管理")
@RestController
@RequestMapping("/pro/check/error-type")
public class CheckErrorTypeController {

    @Autowired
    private CheckErrorTypeService checkErrorTypeService;

    @Autowired
    private CheckErrorLevelService checkErrorLevelService;

    @Operation(
        summary = "获取内容检查错误类型树"
    )
    @GetMapping("/tree")
    public Result<List<FirstLevelErrorTypeVO>> getTypeTree() {
        return Result.ok(checkErrorTypeService.getTypeTree());
    }

    @Operation(
        summary = "获取指定层级的错误类型列表",
        description = "层级：1-一级分类 2-二级原因 3-三级细分"
    )
    @GetMapping("/level/{level}")
    public Result<List<CheckErrorType>> getTypesByLevel(
            @Parameter(description = "层级", required = true, example = "1") 
            @PathVariable Integer level) {
        return Result.ok(checkErrorTypeService.getTypesByLevel(level));
    }

    @Operation(
        summary = "获取指定父级的子类型列表",
        description = "获取某个错误类型的直接子类型"
    )
    @GetMapping("/children/{parentId}")
    public Result<List<CheckErrorType>> getChildTypes(
            @Parameter(description = "父级ID", required = true, example = "1") 
            @PathVariable Long parentId) {
        return Result.ok(checkErrorTypeService.getChildTypes(parentId));
    }

    @Operation(
        summary = "新增错误类型",
        description = "创建新的错误类型，支持指定错误等级"
    )
    @PostMapping
    public Result<CheckErrorType> save(
            @Parameter(description = "错误类型信息", required = true, schema = @Schema(implementation = CheckErrorType.class)) 
            @RequestBody CheckErrorType errorType) {
        // 验证错误等级是否存在
        if (errorType.getErrorLevel() != null) {
            CheckErrorLevel errorLevel = checkErrorLevelService.getById(errorType.getErrorLevel());
            if (errorLevel == null) {
                return Result.error("指定的错误等级不存在");
            }
        }
        checkErrorTypeService.save(errorType);
        return Result.ok(errorType);
    }

    @Operation(
        summary = "更新错误类型",
        description = "更新指定ID的错误类型信息"
    )
    @PutMapping("/{id}")
    public Result<Boolean> update(
            @Parameter(description = "错误类型ID", required = true, example = "1") 
            @PathVariable Long id, 
            @Parameter(description = "错误类型信息", required = true, schema = @Schema(implementation = CheckErrorType.class)) 
            @RequestBody CheckErrorType errorType) {
        // 验证错误等级是否存在
        if (errorType.getErrorLevel() != null) {
            CheckErrorLevel errorLevel = checkErrorLevelService.getById(errorType.getErrorLevel());
            if (errorLevel == null) {
                return Result.error("指定的错误等级不存在");
            }
        }
        errorType.setId(id);
        return Result.ok(checkErrorTypeService.updateById(errorType));
    }

    @Operation(
        summary = "删除错误类型",
        description = "删除指定ID的错误类型"
    )
    @DeleteMapping("/{id}")
    public Result<Boolean> delete(
            @Parameter(description = "错误类型ID", required = true, example = "1") 
            @PathVariable Long id) {
        return Result.ok(checkErrorTypeService.removeById(id));
    }
} 