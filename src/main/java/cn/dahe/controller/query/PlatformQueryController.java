package cn.dahe.controller.query;

import cn.dahe.model.dto.Result;
import cn.dahe.service.PlatformQueryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/pro/platform-query")
@Tag(name = "平台与网站整合查询")
@RequiredArgsConstructor
public class PlatformQueryController {

    private final PlatformQueryService platformQueryService;

    @Operation(summary = "平台与网站信息整合查询")
    @PostMapping("/platformsAndWebsites")
    public Result<Map<String, Object>> platformsAndWebsites() {
        return Result.ok(platformQueryService.queryPlatformsAndWebsites());
    }
}


