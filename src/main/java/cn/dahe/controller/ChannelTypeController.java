package cn.dahe.controller;

import cn.dahe.entity.ChannelType;
import cn.dahe.model.dto.ChannelTypeQuery;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.Result;
import cn.dahe.service.ChannelTypeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 栏目类型控制器
 * 
 * <AUTHOR>
 * @date 2025-09-16
 */
@Slf4j
@RestController
@RequestMapping("/pro/channel-type")
@Tag(name = "栏目类型管理", description = "栏目类型相关接口")
public class ChannelTypeController {

    @Autowired
    private ChannelTypeService channelTypeService;

    @PostMapping("/insert")
    @Operation(summary = "新增栏目类型", description = "新增栏目类型")
    public Result<String> insertChannelType( ChannelType channelType) {
        return channelTypeService.insertChannelType(channelType);
    }

    @PostMapping("/update")
    @Operation(summary = "修改栏目类型", description = "修改栏目类型")
    public Result<String> updateChannelType(ChannelType channelType) {
        return channelTypeService.updateChannelType(channelType);
    }

    @GetMapping("/delete/{id}")
    @Operation(summary = "删除栏目类型", description = "删除栏目类型")
    public Result<String> deleteChannelTypeById(@Parameter(description = "栏目类型ID") @PathVariable Long id) {
        return channelTypeService.deleteChannelTypeById(id);
    }

    @GetMapping("/get/{id}")
    @Operation(summary = "根据ID查询栏目类型", description = "根据ID查询栏目类型")
    public Result<ChannelType> getChannelTypeById(@Parameter(description = "栏目类型ID") @PathVariable Long id) {
        return channelTypeService.getChannelTypeById(id);
    }

    @PostMapping("/page")
    @Operation(
        summary = "分页查询栏目类型", 
        description = "根据查询条件分页查询栏目类型列表，支持按名称、状态、父级ID等条件筛选"
    )
    public Result<PageResult<ChannelType>> listChannelTypesByPage(ChannelTypeQuery query) {
        // 参数校验
        if (query == null) {
            query = new ChannelTypeQuery();
        }
        
        // 设置默认分页参数
        if (query.getPage() == null || query.getPage() < 1) {
            query.setPage(1);
        }
        if (query.getLimit() == null || query.getLimit() < 1) {
            query.setLimit(10);
        }
        
        // 限制单页最大查询数量，防止性能问题
        if (query.getLimit() > 100) {
            query.setLimit(100);
        }
        
        return channelTypeService.listChannelTypesByPage(query);
    }

    @GetMapping("/tree")
    @Operation(summary = "查询栏目类型树结构", description = "查询栏目类型树结构")
    public Result<List<ChannelType>> listChannelTypeTree() {
        return channelTypeService.listChannelTypeTree();
    }
}