package cn.dahe.controller.system;

import cn.dahe.common.annotation.CurrentUser;
import cn.dahe.common.aop.log.OperateLog;
import cn.dahe.common.aop.role.Logical;
import cn.dahe.common.aop.role.RequiresRoles;
import cn.dahe.common.constants.RoleConstants;
import cn.dahe.model.dto.Result;
import cn.dahe.entity.SystemNotice;
import cn.dahe.enums.OperateTypeEnum;
import cn.dahe.model.query.SystemNoticeQuery;
import cn.dahe.service.SystemNoticeService;
import cn.dahe.model.vo.LoginUserVO;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023-08-10
 */
@RestController
@RequestMapping("/pro/system-notice")
@AllArgsConstructor
@Tag(name= "公告管理模块")
public class SystemNoticeController {

    @Resource
    private SystemNoticeService systemNoticeService;

    //分页
    @PostMapping("list")
    public Result page(SystemNoticeQuery query) {
        return Result.ok(systemNoticeService.page(query));
    }

    //列表-不分页
    @PostMapping("list-all")
    public Result listAll(SystemNoticeQuery query) {
        return Result.ok(systemNoticeService.listAll(query));
    }

    //添加
    @PostMapping("save")
    @RequiresRoles(value = {RoleConstants.ADMIN_SUPER}, logical = Logical.OR)
    @OperateLog(name = "添加", type = OperateTypeEnum.UPDATE)
    public Result save(SystemNotice systemNotice, @CurrentUser LoginUserVO user) {
        return systemNoticeService.save(systemNotice, user);
    }

    //修改
    @PostMapping("update")
    @RequiresRoles(value = {RoleConstants.ADMIN_SUPER}, logical = Logical.OR)
    @OperateLog(name = "修改", type = OperateTypeEnum.UPDATE)
    public Result update(SystemNotice systemNotice, @CurrentUser LoginUserVO user) {
        return systemNoticeService.update(systemNotice, user);
    }


    //修改状态
    @PostMapping("update-status")
    @RequiresRoles(value = {RoleConstants.ADMIN_SUPER}, logical = Logical.OR)
    @OperateLog(name = "修改状态", type = OperateTypeEnum.UPDATE)
    public Result updateStatus(@RequestParam(defaultValue = "") String id, @CurrentUser LoginUserVO user) {
        return systemNoticeService.updateStatus(id, user);
    }
}
