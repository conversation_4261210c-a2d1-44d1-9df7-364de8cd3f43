package cn.dahe.controller.system;

import cn.dahe.common.aop.role.Logical;
import cn.dahe.common.aop.role.RequiresRoles;
import cn.dahe.common.constants.RoleConstants;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.Result;
import cn.dahe.entity.LoginLog;
import cn.dahe.model.query.LoginLogQuery;
import cn.dahe.service.LoginLogService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
/**
 * 初始化
 */
@RestController
@RequestMapping("/pro/login-log/")
@AllArgsConstructor
public class LoginLogController {
    @Resource
    private LoginLogService loginLogService;

    @PostMapping("list")
    @RequiresRoles(value = {RoleConstants.ADMIN_SUPER}, logical = Logical.OR)
    public Result<PageResult<LoginLog>> page(LoginLogQuery query) {
        PageResult<LoginLog> page = loginLogService.page(query);
        return Result.ok(page);
    }

}