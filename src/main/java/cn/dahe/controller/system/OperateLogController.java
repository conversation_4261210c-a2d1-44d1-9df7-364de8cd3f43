package cn.dahe.controller.system;


import cn.dahe.common.aop.role.Logical;
import cn.dahe.common.aop.role.RequiresRoles;
import cn.dahe.common.constants.RoleConstants;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.Result;
import cn.dahe.entity.OperateLog;
import cn.dahe.model.query.OperateLogQuery;
import cn.dahe.service.OperateLogService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/pro/operate-log")
@AllArgsConstructor
public class OperateLogController {
    private final OperateLogService operateLogService;

    @PostMapping("list")
    @RequiresRoles(value = {RoleConstants.ADMIN_SUPER}, logical = Logical.OR)
    public Result<PageResult<OperateLog>> page(OperateLogQuery query) {
        PageResult<OperateLog> page = operateLogService.page(query);
        return Result.ok(page);
    }


}