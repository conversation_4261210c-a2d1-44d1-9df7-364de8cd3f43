package cn.dahe.controller.system;

import cn.dahe.common.annotation.CurrentUser;
import cn.dahe.common.aop.log.OperateLog;
import cn.dahe.common.aop.role.Logical;
import cn.dahe.common.aop.role.RequiresRoles;
import cn.dahe.common.constants.RoleConstants;
import cn.dahe.common.constants.UserConstants;
import cn.dahe.entity.User;
import cn.dahe.enums.OperateTypeEnum;
import cn.dahe.model.dto.Result;
import cn.dahe.model.query.UserQuery;
import cn.dahe.model.vo.LoginUserVO;
import cn.dahe.service.RoleService;
import cn.dahe.service.UserService;
import cn.dahe.service.WebsiteService;
import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023-07-05
 */
@RestController
@RequestMapping("/pro/user")
@AllArgsConstructor
@Tag(name = "用户管理模块")
public class UserController {

    @Resource
    private UserService userService;

    @Resource
    private RoleService roleService;

    @Resource
    private WebsiteService websiteService;

    @Operation(summary = "获取当前登录用户信息")
    @PostMapping("current")
    public Result getCurrentUser(@Parameter(hidden = true) @CurrentUser LoginUserVO loginUser) {
        return Result.ok(loginUser);
    }

    @Operation(summary = "获取用户列表")
    @PostMapping("list")
    public Result list(@Parameter(description = "用户查询条件") UserQuery userQuery, @CurrentUser LoginUserVO loginUser) throws IOException {
        return Result.ok(userService.page(userQuery, loginUser));
    }

    @Operation(summary = "获取指定城市的用户列表")
    @PostMapping("list-city")
    public Result listByCityId(@Parameter(description = "城市ID") @RequestParam("") String cityId) {
        return Result.ok(userService.listByCityIds(cityId));
    }

    @Operation(summary = "获取指定角色的用户列表")
    @PostMapping("list-role")
    public Result listRole(
            @Parameter(description = "用户查询条件") UserQuery userQuery,
            @Parameter(description = "角色ID") @RequestParam(defaultValue = "0") String roleId) {
        return Result.ok(userService.pageRole(userQuery, roleId));
    }

    @Operation(summary = "删除指定角色下的用户")
    @RequiresRoles(value = {RoleConstants.ADMIN_SUPER}, logical = Logical.OR)
    @PostMapping("delete-role")
    @OperateLog(name = "删除-某角色下的某用户", type = OperateTypeEnum.UPDATE)
    public Result listRole(
            @Parameter(description = "角色ID") String roleId,
            @Parameter(description = "用户ID") String userId) {
        return roleService.removeByUserId(userId, roleId);
    }

    @Operation(summary = "分配角色")
    @PostMapping("assign-role")
    @RequiresRoles(value = {RoleConstants.ADMIN_SUPER}, logical = Logical.OR)
    @OperateLog(name = "分配角色", type = OperateTypeEnum.UPDATE)
    public Result assignRole(
            @Parameter(description = "用户ID") String userId,
            @Parameter(description = "角色ID列表，多个以逗号分隔") String roleIds) {
        return roleService.assignRole(userId, roleIds);
    }

    @PostMapping("list-site")
    public Result listSite(@Parameter(description = "用户ID") @RequestParam(defaultValue = "0") int userId,
                           @CurrentUser LoginUserVO user) {
        if (user.getRoles().contains(UserConstants.ROLE_ADMIN)) {
            return Result.ok(websiteService.listByUserId(userId));
        }
        return Result.error("您没有权限");
    }

    @Operation(summary = "分配站点")
    @PostMapping("assign-site")
    @RequiresRoles(value = {RoleConstants.ADMIN_SUPER}, logical = Logical.OR)
    @OperateLog(name = "分配站点", type = OperateTypeEnum.UPDATE)
    public Result assignSite(
            @Parameter(description = "用户ID") String userId,
            @Parameter(description = "站点ID列表，多个以逗号分隔") String siteIds) {
        if (StrUtil.isBlank(userId)) {
            return Result.error("用户ID不能为空");
        }
        if (StrUtil.isBlank(siteIds)) {
            return Result.error("站点ID不能为空");
        }
        return Result.ok(websiteService.assignSite(userId, siteIds));
    }

    @Operation(summary = "添加用户")
    @PostMapping("save")
    @RequiresRoles(value = {RoleConstants.ADMIN_SUPER, RoleConstants.ADMIN_SMALL}, logical = Logical.OR)
    @OperateLog(name = "添加用户", type = OperateTypeEnum.UPDATE)
    public Result save(
            @Parameter(description = "用户信息") User user,
            @Parameter(description = "角色ID列表，多个以逗号分隔") @RequestParam(defaultValue = "") String roleIds,
            @RequestParam(required = false) Set<Integer> tenantIdSet,
            @RequestParam(required = false) Set<Integer> newMediaIdSet,
            @Parameter(hidden = true) @CurrentUser LoginUserVO loginUser) {
        return userService.save(user, roleIds, tenantIdSet, newMediaIdSet, loginUser);
    }

    @Operation(summary = "修改用户")
    @PostMapping("update")
    @RequiresRoles(value = {RoleConstants.ADMIN_SUPER, RoleConstants.ADMIN_SMALL}, logical = Logical.OR)
    @OperateLog(name = "修改用户", type = OperateTypeEnum.UPDATE)
    public Result update(
            @Parameter(description = "用户信息") User user,
            @Parameter(description = "角色ID列表，多个以逗号分隔") @RequestParam(defaultValue = "") String roleIds,
            @RequestParam(required = false) Set<Integer> tenantIdSet,
            @RequestParam(required = false) Set<Integer> newMediaIdSet,
            @Parameter(hidden = true) @CurrentUser LoginUserVO loginUser) {
        return userService.update(user, roleIds, tenantIdSet, newMediaIdSet, loginUser);
    }

    @Operation(summary = "修改用户状态")
    @PostMapping("update-status")
    @RequiresRoles(value = {RoleConstants.ADMIN_SUPER, RoleConstants.ADMIN_SMALL}, logical = Logical.OR)
    @OperateLog(name = "修改状态", type = OperateTypeEnum.UPDATE)
    public Result updateStatus(
            @Parameter(description = "用户ID") @RequestParam(defaultValue = "") String userId,
            @Parameter(hidden = true) @CurrentUser LoginUserVO loginUser) {
        return userService.updateStatus(userId, loginUser);
    }

    @Operation(summary = "修改当前用户密码")
    @PostMapping("update-my-pwd")
    @OperateLog(name = "修改自己的密码", type = OperateTypeEnum.UPDATE)
    public Result updateMyPwd(
            @Parameter(description = "新密码") @RequestParam(defaultValue = "") String newPwd,
            @Parameter(description = "旧密码") @RequestParam(defaultValue = "") String oldPwd,
            @Parameter(hidden = true) @CurrentUser LoginUserVO loginUser) {
        return userService.updateMyPwd(String.valueOf(loginUser.getUserId()), newPwd, oldPwd);
    }

    @Operation(summary = "重置用户密码")
    @PostMapping("reset-pwd")
    @RequiresRoles(value = {RoleConstants.ADMIN_SUPER}, logical = Logical.OR)
    @OperateLog(name = "重置密码", type = OperateTypeEnum.UPDATE)
    public Result updateResetPwd(
            @Parameter(description = "用户ID") @RequestParam(defaultValue = "") String userId) {
        return userService.updateResetPwd(userId);
    }
}
