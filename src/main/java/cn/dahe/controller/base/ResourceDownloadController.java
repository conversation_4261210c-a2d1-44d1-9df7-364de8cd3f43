package cn.dahe.controller.base;

/**
 * <AUTHOR>
 * @date 2023-08-11
 */

import cn.dahe.common.annotation.CurrentUser;
import cn.dahe.common.aop.log.OperateLog;
import cn.dahe.common.aop.role.Logical;
import cn.dahe.common.aop.role.RequiresRoles;
import cn.dahe.common.constants.RoleConstants;
import cn.dahe.model.dto.Result;
import cn.dahe.entity.ResourceDownload;
import cn.dahe.enums.OperateTypeEnum;
import cn.dahe.model.query.ResourceDownloadQuery;
import cn.dahe.service.ResourceDownloadService;
import cn.dahe.model.vo.LoginUserVO;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 网站信息管理
 * <AUTHOR>
 */
@RestController
@RequestMapping("/pro/resource-download")
@AllArgsConstructor
@Tag(name = "资源下载模块")
public class ResourceDownloadController {

    @Resource
    private ResourceDownloadService resourceDownloadService;

    //分页
    @PostMapping("list")
    public Result page(ResourceDownloadQuery query) {
        return Result.ok(resourceDownloadService.page(query));
    }



    //添加
    @PostMapping("save")
    @RequiresRoles(value = {RoleConstants.ADMIN_SUPER}, logical = Logical.OR)
    @OperateLog(name = "添加", type = OperateTypeEnum.UPDATE)
    public Result save(ResourceDownload resourceDownload, @CurrentUser LoginUserVO user) {
        return resourceDownloadService.save(resourceDownload, user);
    }


    //修改状态
    @PostMapping("update-status")
    @RequiresRoles(value = {RoleConstants.ADMIN_SUPER}, logical = Logical.OR)
    @OperateLog(name = "修改状态", type = OperateTypeEnum.UPDATE)
    public Result updateStatus(@RequestParam(defaultValue = "") String id, @CurrentUser LoginUserVO user) {
        return resourceDownloadService.updateStatus(id, user);
    }

}
