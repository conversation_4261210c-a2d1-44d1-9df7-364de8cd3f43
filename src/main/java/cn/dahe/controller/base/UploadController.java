package cn.dahe.controller.base;

import cn.dahe.common.aop.log.OperateLog;
import cn.dahe.enums.OperateTypeEnum;
import cn.dahe.model.dto.Result;
import cn.dahe.utils.ws.UploadWsUtil;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * 上传
 */
@RestController
@RequestMapping("/pro/upload")
@Slf4j
@Tag(name = "上传文件")
public class UploadController {
    
     @PostMapping("")
     @OperateLog(name = "上传文件", type = OperateTypeEnum.UPDATE)
     public Result uploadFile(MultipartFile attach) {
         if (attach == null) {
             return Result.error("请选择要上传的附件");
         }
         return Result.ok(UploadWsUtil.uploadFile(attach));
     }

}
