package cn.dahe.controller.base;

import cn.dahe.common.auth.LoginBody;
import cn.dahe.common.auth.SecurityUtils;
import cn.dahe.common.auth.ServletUtils;
import cn.dahe.common.constants.CacheConstants;
import cn.dahe.common.constants.LoginConstants;
import cn.dahe.common.constants.PermissionConstants;
import cn.dahe.common.constants.RoleConstants;
import cn.dahe.common.constants.StatusConstants;
import cn.dahe.common.constants.UserConstants;
import cn.dahe.common.executor.ExecutorConfig;
import cn.dahe.common.redis.RedisService;
import cn.dahe.dao.TeamDao;
import cn.dahe.dao.TenantDao;
import cn.dahe.dao.WebsiteDao;
import cn.dahe.entity.LoginLog;
import cn.dahe.entity.Permission;
import cn.dahe.entity.Role;
import cn.dahe.entity.User;
import cn.dahe.enums.ProcessTypeEnum;
import cn.dahe.model.dto.Result;
import cn.dahe.model.vo.LoginUserVO;
import cn.dahe.service.LoginLogService;
import cn.dahe.service.PermissionService;
import cn.dahe.service.RoleService;
import cn.dahe.service.UserService;
import cn.dahe.utils.JwtUtils;
import cn.dahe.utils.SmsUtil;
import cn.dahe.utils.StringUtils;
import cn.hutool.captcha.CaptchaUtil;
import cn.hutool.captcha.CircleCaptcha;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.auth0.jwt.JWT;
import com.auth0.jwt.exceptions.JWTDecodeException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.util.Base64Utils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Tag(name = "登录相关")
@Controller
@RequestMapping
@Slf4j
public class LoginController {
    @Resource
    private TenantDao tenantDao;
    @Resource
    private WebsiteDao websiteDao;
    @Resource
    private TeamDao teamDao;
    @Resource
    private RedisService redisService;
    @Resource
    private ExecutorConfig executorConfig;
    @Resource
    private LoginLogService loginLogService;
    @Resource
    private UserService userService;
    @Resource
    private RoleService roleService;
    @Resource
    private PermissionService permissionService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Value("${spring.profiles.active}")
    private String env;

    @Value("${anxun.cache.login.token-expire}")
    private long tokenExpire;
    private final Set<Integer> newMediaSet = Arrays.stream(ProcessTypeEnum.values())
                                                   .filter(type -> type.getValue() > 0 && type.enabled)
                                                   .map(ProcessTypeEnum::getValue)
                                                   .collect(Collectors.toSet());


    @Operation(summary = "登录获取token")
    @PostMapping({"/login", "/"})
    @ResponseBody
    public Result<?> login(LoginBody form, HttpServletRequest request) {
//        生产环境进行验证码校验 //
        if (!StrUtil.containsAny(env, LoginConstants.LOGIN_IN_ACTIVE)) {
            if (StringUtils.isBlank(form.getImageCode())) {
                return Result.error("请输入验证码");
            }
            String cacheImageCode = redisService.getCacheObject(CacheConstants.KEY_USER_CHECK_CODE + form.getKey());
            if (StringUtils.isBlank(cacheImageCode)) {
                return Result.error("图形验证码已经过期，请重新点击刷新。");
            }
            if (StringUtils.isBlank(form.getImageCode())) {
                return Result.error(LoginConstants.LOGIN_FAIL_IMG_CODE_NOT_EXIST);
            }
            if (!form.getImageCode().equalsIgnoreCase(cacheImageCode)) {
                return Result.error(LoginConstants.LOGIN_FAIL_IMG_CODE_ERROR);
            }
            redisService.deleteObject(CacheConstants.KEY_USER_CHECK_CODE + form.getKey());
        }

        String trimAccount = Objects.isNull(form.getAccount()) ? null : form.getAccount().trim();
        String trimPassword = Objects.isNull(form.getPassword()) ? null : form.getPassword().trim();
        User user = userService.getByAccount(trimAccount);
        if (ObjectUtil.isNull(user)) {
            return Result.error(LoginConstants.LOGIN_FAIL_USER_NOT_EXIST);
        }
        if (user.getStatus() != UserConstants.STATUS_NORMAL) {
            return Result.error(LoginConstants.LOGIN_FAIL_ACCOUNT_DISABLED);
        }
        String encodePhone = Base64.encode(form.getAccount());
        Object cacheCodeObj = redisService.getCacheObject(CacheConstants.SMS_CODE_LOGIN_PHONE + encodePhone);
        String cacheImageCode = cacheCodeObj == null ? null : String.valueOf(cacheCodeObj);
        if (!StrUtil.containsAny(env, LoginConstants.LOGIN_IN_ACTIVE)) {
            if (StringUtils.isBlank(cacheImageCode)) {
                return Result.error("验证码已经过期，请重新获取。");
            }
        } else {
            cacheImageCode = LoginConstants.LOGIN_TEST_CODE;
        }
        if (!cacheImageCode.equals(trimPassword)) {
            return Result.error(LoginConstants.LOGIN_FAIL_PASSPORT_ERROR);
        }
        LoginUserVO loginUser = getLoginUser(user);
        int userId = loginUser.getUserId();
        String token = JwtUtils.createToken(userId);
        redisService.setCacheObject(CacheConstants.LOGIN_TOKEN_KEY + userId, token, tokenExpire, TimeUnit.MINUTES);
        redisService.setCacheObject(CacheConstants.LOGIN_USER_KEY + userId, loginUser, tokenExpire, TimeUnit.MINUTES);
        // 记录登陆日志
        addLoginLog(loginUser, ServletUtils.getUserAgent(request), ServletUtil.getClientIP(request));
        return Result.ok(token);
    }

    @Operation(summary = "发送短信验证码")
    @PostMapping(value = "/sms")
    @ResponseBody
    public Result sms(@Parameter(description = "手机号码") String iphone) {
        iphone = iphone.trim();
        if (StringUtils.isBlank(iphone)) {
            return Result.error("请输入手机号");
        }
        boolean mobileNum = StringUtils.isMobileNum(iphone);
        if (!mobileNum) {
            return Result.error("请输入正确的手机号");
        }
        User user = userService.getByPhone(iphone);
        if (user == null) {
            return Result.error(LoginConstants.LOGIN_FAIL_USER_NOT_EXIST);
        }
        if (user.getStatus() != StatusConstants.COMMON_NORMAL) {
            return Result.error(LoginConstants.LOGIN_FAIL_ACCOUNT_DISABLED);
        }
        return SmsUtil.sendLoginSmsAccount(iphone);
    }


    public LoginUserVO getLoginUser(User user) {
        LoginUserVO LoginUserVO = new LoginUserVO();
        LoginUserVO.setUserId(user.getUserId());
        LoginUserVO.setUsername(user.getUserName());
        LoginUserVO.setAccount(user.getAccount());
        LoginUserVO.setDepId(user.getDepId());
        LoginUserVO.setDepName(user.getDepName());
        // 存储用户的权限列表，角色列表。
        List<Role> roleList = roleService.listByUserId(LoginUserVO.getUserId());
        Set<String> userRoleSns = new HashSet<>();
        if (!roleList.isEmpty()) {
            userRoleSns = roleList.stream().map(Role::getSn).collect(Collectors.toSet());
        }
        if (userRoleSns.isEmpty()) {
            userRoleSns.add(RoleConstants.defaultRole);

        }
        List<Permission> permissions = permissionService.listByRoleSns(userRoleSns);
        // 确定用户的权限
        Set<String> permissionSns = permissions.stream()
                                               .map(Permission::getSn)
                                               .collect(Collectors.toSet());
        // 确定按钮权限
        Set<String> buttonPermissions = permissions.stream()
                                                   .filter(permission -> permission.getType() == PermissionConstants.TYPE_BUTTON)
                                                   .map(Permission::getSn)
                                                   .collect(Collectors.toSet());
        // 确定列表权限
        Set<String> listPermissions = permissions.stream()
                                                 .filter(permission -> permission.getType() == PermissionConstants.TYPE_LIST)
                                                 .map(Permission::getSn)
                                                 .collect(Collectors.toSet());
        LoginUserVO.setCityId(userService.getCityIdByUserId(LoginUserVO.getUserId()));
        LoginUserVO.setRoles(userRoleSns);
        LoginUserVO.setPermissions(permissionSns);
        LoginUserVO.setButtonPermissions(buttonPermissions);
        LoginUserVO.setListPermissions(listPermissions);
        LoginUserVO.setTeam(teamDao.selectById(user.getTeamId()));
        boolean defaultRole = userRoleSns.contains(RoleConstants.defaultRole);
        if (userRoleSns.contains(RoleConstants.ADMIN_SUPER)) LoginUserVO.setTenantList(tenantDao.selectEnable());
        else LoginUserVO.setTenantList(tenantDao.selectEnableByUserId(LoginUserVO.getUserId()));
        Integer tenantId = LoginUserVO.getTenantList().get(0).getId();
        LoginUserVO.setNewMediaList(websiteDao.selectEnableByTenantIdAndUserAndProcessTypeIn(tenantId, user.getUserId(), defaultRole, newMediaSet));
        return LoginUserVO;

    }


    @Operation(summary = "退出登录")
    @GetMapping("/logout")
    @ResponseBody
    public Result<String> logout(HttpServletRequest request) throws UnsupportedEncodingException {
        request.getSession().invalidate();
        try {
            String token = SecurityUtils.getToken();
            JwtUtils.verifyToken(token);
            String s = JWT.decode(token).getAudience().get(0);
            redisService.deleteObject(CacheConstants.LOGIN_TOKEN_KEY + Integer.parseInt(s));
        } catch (JWTDecodeException e) {
            e.printStackTrace();
        } catch (NumberFormatException e) {
            e.printStackTrace();
        } finally {
            return Result.ok();
        }
    }


    private void addLoginLog(LoginUserVO user, String osSysyem, String ip) {
        executorConfig.asyncServiceExecutor().execute(() -> {
            LoginLog loginLog = new LoginLog();
            loginLog.setLoginTime(new Date());
            loginLog.setUserName(user.getUsername());
            loginLog.setPhone(user.getAccount());
            loginLog.setOsSystem(StringUtils.defaultIfBlank(osSysyem, ""));
            loginLog.setIp(StringUtils.defaultIfBlank(ip, ""));
            loginLog.setUserId(user.getUserId());
            loginLog.setContent(LoginConstants.LOGIN_SUCCESS);
            loginLogService.save(loginLog);
        });
    }


    @Operation(summary = "获取图形验证码")
    @GetMapping("/code")
    @ResponseBody
    public Result getCode() {

        HashMap<String, String> map = new HashMap<>();
        // 利用 hutool 工具，生成验证码图片资源
        CircleCaptcha captcha = CaptchaUtil.createCircleCaptcha(200, 100, 4, 5);
        // 获得生成的验证码字符
        String code = captcha.getCode();
        String key = UUID.fastUUID().toString(true);
        try {
            // 将验证码图片转换为 Base64 编码字符串
            String base64Image = convertImageToBase64(captcha.getImage());
            map.put("img", base64Image);
            map.put("key", key);
            redisService.setCacheObject(CacheConstants.KEY_USER_CHECK_CODE + key, code.toUpperCase(), 60L, TimeUnit.SECONDS);
            return Result.ok(map);
        } catch (IOException e) {
            e.printStackTrace();
            return Result.error("验证码获取失败");
        }
    }


    private String convertImageToBase64(BufferedImage image) throws IOException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ImageIO.write(image, "png", outputStream);
        byte[] imageBytes = outputStream.toByteArray();
        return "data:image/png;base64," + Base64Utils.encodeToString(imageBytes);
    }


}
