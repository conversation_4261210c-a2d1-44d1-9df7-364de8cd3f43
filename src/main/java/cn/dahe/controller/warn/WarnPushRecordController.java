package cn.dahe.controller.warn;

import cn.dahe.common.aop.log.OperateLog;
import cn.dahe.enums.OperateTypeEnum;
import cn.dahe.entity.WarnPushRecord;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.Result;
import cn.dahe.model.query.WarnPushRecordQuery;
import cn.dahe.model.vo.WarnPushRecordVO;
import cn.dahe.service.WarnPushRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/pro/warn-push-record")
@Tag(name = "预警推送记录")
@RequiredArgsConstructor
@Slf4j
public class WarnPushRecordController {

    private final WarnPushRecordService warnPushRecordService;

    @Operation(summary = "预警推送记录-分页查询")
    @PostMapping("list")
    public Result<PageResult<WarnPushRecordVO>> list(WarnPushRecordQuery query) {
        long startTime = System.currentTimeMillis();
        log.info("WarnPushRecord list query: {}", query);
        
        try {
            PageResult<WarnPushRecordVO> result = warnPushRecordService.page(query);
            
            long endTime = System.currentTimeMillis();
            log.info("WarnPushRecord查询完成，耗时: {}ms, 返回记录数: {}, 分页信息: page={}, pageSize={}, total={}", 
                    endTime - startTime, result.getList().size(), result.getPage(), result.getPageSize(), result.getTotal());
            
            return Result.ok(result);
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("WarnPushRecord查询失败，耗时: {}ms, 错误: {}", endTime - startTime, e.getMessage(), e);
            throw e;
        }
    }

    @Operation(summary = "预警推送记录-新增")
    @PostMapping("create")
    @OperateLog(name = "新增预警推送记录", type = OperateTypeEnum.CREATE)
    public Result<Integer> create(@RequestBody WarnPushRecord record) {
        return Result.ok(warnPushRecordService.create(record));
    }
}


