package cn.dahe.controller.warn;

import cn.dahe.common.aop.log.OperateLog;
import cn.dahe.common.annotation.XssFilter;
import cn.dahe.enums.OperateTypeEnum;
import cn.dahe.entity.WarnUser;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.Result;
import cn.dahe.model.dto.WarnUserDTO;
import cn.dahe.model.query.WarnUserQuery;
import cn.dahe.service.WarnUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/pro/warn-user")
@Tag(name = "预警接收人管理")
@RequiredArgsConstructor
public class WarnUserController {

    private final WarnUserService warnUserService;

    @Operation(summary = "修改预警接收人用户名")
    @PostMapping("update-username")
    @OperateLog(name = "修改预警接收人用户名", type = OperateTypeEnum.UPDATE)
//    @XssFilter(fields = {"userName"})
    public Result<Boolean> updateUsername(@RequestParam Integer id, @RequestParam String userName) {
        boolean ok = warnUserService.updateUsername(id, userName);
        return ok ? Result.ok(true) : Result.error("修改失败，记录不存在或已删除");
    }

    @Operation(summary = "新增预警接收人")
    @PostMapping("create")
    @OperateLog(name = "新增预警接收人", type = OperateTypeEnum.CREATE)
//    @XssFilter(fields = {"userName", "userOpenId", "userPhone"})
    public Result<Integer> create( @RequestBody WarnUser user) {
        return Result.ok(warnUserService.create(user));
    }

    @Operation(summary = "删除预警接收人")
    @PostMapping("delete")
    @OperateLog(name = "删除预警接收人", type = OperateTypeEnum.DELETE)
    public Result<Boolean> delete(@RequestParam Integer id) {
        boolean ok = warnUserService.deleteById(id);
        return ok ? Result.ok(true) : Result.error("记录不存在或已删除");
    }

    @Operation(summary = "获取单个预警接收人（敏感字段加密）")
    @PostMapping("get")
    public Result<WarnUserDTO> get(@RequestParam Integer id) {
        WarnUserDTO dto = warnUserService.getByIdMasked(id);
        return dto == null ? Result.error("记录不存在") : Result.ok(dto);
    }

    @Operation(summary = "分页查询预警接收人（敏感字段加密）")
    @PostMapping("page")
    public Result<PageResult<WarnUserDTO>> page( WarnUserQuery query) {
        return Result.ok(warnUserService.pageMasked(query));
    }
}


