package cn.dahe.controller.warn;

import cn.dahe.common.aop.log.OperateLog;
import cn.dahe.enums.OperateTypeEnum;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.Result;
import cn.dahe.model.query.WarnPlanQuery;
import cn.dahe.model.vo.WarnPlanVO;
import cn.dahe.model.request.WarnPlanSaveRequest;
import cn.dahe.service.WarnPlanService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/pro/warn-plan")
@Tag(name = "预警方案管理")
@RequiredArgsConstructor
public class WarnPlanController {

    private final WarnPlanService warnPlanService;

    @Operation(summary = "分页查询预警方案（含关联列表）")
    @PostMapping("list")
    public Result<PageResult<WarnPlanVO>> list(WarnPlanQuery query) {
        return Result.ok(warnPlanService.page(query));
    }

    @Operation(summary = "获取预警方案详情（含关联列表）")
    @PostMapping("get")
    public Result<WarnPlanVO> get(@RequestParam Integer warnPlanId) {
        return Result.ok(warnPlanService.get(warnPlanId));
    }

    @Operation(summary = "新增预警方案及其关联")
    @PostMapping("create")
    @OperateLog(name = "新增预警方案", type = OperateTypeEnum.CREATE)
    public Result<Integer> create(@RequestBody WarnPlanSaveRequest req) {
        return Result.ok(warnPlanService.create(req));
    }

    @Operation(summary = "更新预警方案及其关联（全量覆盖）")
    @PostMapping("update")
    @OperateLog(name = "修改预警方案", type = OperateTypeEnum.UPDATE)
    public Result<Boolean> update(@RequestBody WarnPlanSaveRequest req) {
        //  TODO 有点慢
        return Result.ok(warnPlanService.update(req));
    }

    @Operation(summary = "删除预警方案（级联删除关联数据）")
    @PostMapping("delete")
    @OperateLog(name = "删除预警方案", type = OperateTypeEnum.DELETE)
    public Result<Boolean> delete(@RequestParam Integer warnPlanId) {
        return Result.ok(warnPlanService.delete(warnPlanId));
    }
}


