package cn.dahe.controller.warn;

import cn.dahe.entity.WarnPushRecord;
import cn.dahe.model.dto.Result;
import cn.dahe.model.dto.WarnCheckDetailDto;
import cn.dahe.model.dto.WarnPushDataDto;
import cn.dahe.service.WarnDetailService;
import cn.dahe.service.WarnPushRecordService;
import cn.dahe.utils.AESUtils;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import java.util.Date;
import java.util.Map;

import static cn.dahe.common.model.ResultCode.NotFindError;

@Slf4j
@Tag(name = "预警推送数据接口")
@RestController
@RequestMapping("/get-warn-details-data")
public class WarnH5Controller {

    @Resource
    private WarnPushRecordService warnPushRecordService;
    @Resource
    private WarnDetailService warnDetailService;
    @Resource
    ObjectMapper objectMapper;

    @SneakyThrows
    @GetMapping
    public Result<Object> getWarnDetailsData(@RequestParam(required = false) String id,
                                             @RequestParam(required = false) String sign,
                                             @RequestParam(required = false) String check) {
        log.info("获取数据标识：id:{} sign:{} check:{}", id, sign, check);
        if (StrUtil.isNotBlank(id)) {
            // 获取推送记录id
            try {
                id = AESUtils.getDecPushId(id);
            } catch (Exception e) {
                return Result.error("参数错误");
            }
            // 使用解密后的ID执行业务逻辑
            WarnPushRecord record = warnPushRecordService.getById(id);
            if (record == null) {
                return Result.error("参数无效");
            }
            if (StrUtil.isBlank(record.getData())) {
                return Result.error("没数据");
            }
            WarnPushDataDto data = JSONObject.parseObject(record.getData(), WarnPushDataDto.class);
            return Result.ok(data);
        }
        //  内容检查  其他也行
        if (StrUtil.isNotBlank(sign)) {
            WarnCheckDetailDto object = warnDetailService.getDtoBySign(sign);
            if (object == null) {
                return Result.error(NotFindError);
            }
            return Result.ok(object);
        }
        if (StrUtil.isNotBlank(check)) {
            WarnPushRecord record = warnPushRecordService.getOne(Wrappers.<WarnPushRecord>lambdaQuery().likeLeft(WarnPushRecord::getWarnLink, check));
            warnPushRecordService.updateById(record.setActualPushTime(new Date()));
            return Result.ok(objectMapper.readValue(record.getData(), Map.class));
        }
        return Result.error("参数错误");
    }
}
