package cn.dahe.controller;

import cn.dahe.common.annotation.CurrentUser;
import cn.dahe.common.aop.log.OperateLog;
import cn.dahe.entity.ChkAutoRecheckTask;
import cn.dahe.enums.OperateTypeEnum;
import cn.dahe.model.dto.*;
import cn.dahe.model.query.ChkAutoRecheckTaskQuery;
import cn.dahe.model.query.ChkTransferTaskQuery;
import cn.dahe.model.vo.LoginUserVO;
import cn.dahe.service.ChkAutoRecheckTaskService;
import cn.dahe.service.ChkTransferTaskService;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/pro/chk-chain-track")
@AllArgsConstructor
@Tag(name = "整改追踪系列接口")
public class ChkChainTrackController {

    @Resource
    private ChkTransferTaskService chkTransferTaskService;
    @Resource
    private ChkAutoRecheckTaskService chkAutoRecheckTaskService;

    @OperateLog(name = "整改下发", type = OperateTypeEnum.UPDATE, isArticleOperation = true)
    @Operation(summary = "整改下发")
    @PostMapping("/transfer-send")
    public Result<Boolean> transferSend(ChkTransferTaskDto chkTransferTaskDto, @CurrentUser LoginUserVO user) {
        return Result.ok(chkTransferTaskService.transferSend(chkTransferTaskDto, user));
    }

    @Operation(summary = "转办督办-统计")
    @PostMapping("/transfer-count")
    public Result<?> transferCount(ChkTransferTaskQuery query) {
        return Result.ok(chkTransferTaskService.count(query));
    }

    @Operation(summary = "转办督办-列表")
    @PostMapping("/transfer-page")
    public Result<PageResult<?>> transferPage(ChkTransferTaskQuery query, @CurrentUser LoginUserVO user) {
            return Result.ok(chkTransferTaskService.page(query));
    }

    @OperateLog(name = "整改反馈添加", type = OperateTypeEnum.CREATE)
    @Operation(summary = "转办督办-整改反馈添加")
    @PostMapping("/transfer-replay-add")
    public Result<?> transferReplayAdd(ChkTransferReplayDto chkTransferReplay, @CurrentUser LoginUserVO user) {
        if (chkTransferReplay.getTransferTaskId() == null){
            return Result.error("转办督办Id不能为空");
        }
        //整改状态
        if (chkTransferReplay.getRectifyStatus() == null) {
            return Result.error("整改状态不能为空");
        }
        if (chkTransferReplay.getRectifyStatus() != 2 && chkTransferReplay.getRectifyStatus() != 3) {
            return Result.error("整改状态错误");
        }
        int add = chkTransferTaskService.add(chkTransferReplay, user);
        if (add > 0) {
            return Result.ok();
        }
        return Result.error("添加失败");
    }

    @OperateLog(name = "整改反馈修改", type = OperateTypeEnum.UPDATE)
    @Operation(summary = "转办督办-整改反馈修改")
    @PostMapping("/transfer-replay-update")
    public Result<?> transferReplayEdit(ChkTransferReplayDto chkTransferReplay, @CurrentUser LoginUserVO user) {
        int update = chkTransferTaskService.update(chkTransferReplay, user);
        if (update > 0) {
            return Result.ok();
        }
        return Result.error("更新失败");
    }

    @Operation(summary = "转办督办-整改反馈详情")
    @PostMapping("/transfer-replay-detail")
    public Result<?> transferReplayGet(String id, @CurrentUser LoginUserVO user) {
        return Result.ok(chkTransferTaskService.getDetail(Long.parseLong(id)));
    }

    @Operation(summary = "创建自动复查任务")
    @PostMapping("/auto-recheck-add")
    public Result<?> autoRecheckAdd(ChkAutoRecheckTaskDto chkAutoRecheckTaskDto, @CurrentUser LoginUserVO user) {
        if (StrUtil.isBlank(chkAutoRecheckTaskDto.getTaskName())) {
            return Result.error("任务名称不能为空");
        }
        if (StrUtil.isBlank(chkAutoRecheckTaskDto.getSiteIds())) {
            return Result.error("信源id不能为空");
        }
        if (chkAutoRecheckTaskDto.getCheckBeginTime() == null) {
            return Result.error("复查开始时间不能为空");
        }
        if (chkAutoRecheckTaskDto.getCheckEndTime() == null) {
            return Result.error("复查结束时间不能为空");
        }
        return Result.ok(chkAutoRecheckTaskService.add(chkAutoRecheckTaskDto, user));
    }

    @Operation(summary = "自动复查-统计")
    @PostMapping("/auto-recheck-count")
    public Result<?> autoRecheckCount(ChkAutoRecheckTaskQuery query) {
        return Result.ok(chkAutoRecheckTaskService.count(query));
    }

    @Operation(summary = "自动复查-列表")
    @PostMapping("/auto-recheck-page")
    public Result<PageResult<?>> autoRecheckPage(ChkAutoRecheckTaskQuery query, @CurrentUser LoginUserVO user) {
        return Result.ok(chkAutoRecheckTaskService.page(query));
    }

    @Operation(summary = "自动复查-详情")
    @PostMapping("/auto-recheck-detail")
    public Result<?> autoRecheckDetail(String id, @CurrentUser LoginUserVO user) {
        return Result.ok(chkAutoRecheckTaskService.getById(Long.parseLong(id)));
    }

}
