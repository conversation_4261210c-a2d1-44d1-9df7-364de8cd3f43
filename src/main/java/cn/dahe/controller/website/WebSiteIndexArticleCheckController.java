package cn.dahe.controller.website;

import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.Result;
import cn.dahe.model.query.WebSiteIndexCheckQuery;
import cn.dahe.model.vo.WebSiteIndexCheckDetailVO;
import cn.dahe.model.vo.WebSiteIndexCheckVO;
import cn.dahe.service.WebSiteIndexArticleCheckService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 网站首页文章检查Controller
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Tag(name = "网站首页文章检查")
@Slf4j
@RestController
@RequestMapping("/pro/website/indexArticleCheck")
public class WebSiteIndexArticleCheckController {
    @Resource
    private WebSiteIndexArticleCheckService WebSiteIndexArticleCheckService;

    /**
     * 根据查询条件分页查询网站首页更新检查详情
     * 实现注释中的需求：
     * 1、查询webSite表 查询processType的为0 的数据 （网站）
     * 2、根据网站id查询webSiteIndexArticleCheck表 查询websiteId的为网站id的数据
     * 3、封装数据返回，包括网站序号、地址、首页是否更新、更新天数、连续未更新天数、连续更新天数、最后更新时间
     */
    @Operation(summary = "分页查询网站首页更新检查详情")
    @PostMapping("page-detail")
    public Result<PageResult<WebSiteIndexCheckDetailVO>> pageDetail( WebSiteIndexCheckQuery query) {
        try {
            // 设置默认分页参数
            if (query.getPage() == null || query.getPage() < 1) {
                query.setPage(1);
            }
            if (query.getLimit() == null || query.getLimit() < 1) {
                query.setLimit(10);
            }
            // 分页查询
            PageResult<WebSiteIndexCheckDetailVO> result = WebSiteIndexArticleCheckService.pageByQuery(query);
            return Result.ok(result);
        } catch (Exception e) {
            log.error("分页查询网站首页更新检查详情失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

       /**
     * 导出网站首页更新检查数据到Excel（使用EasyExcel）
     */
    @Operation(summary = "导出网站首页更新检查数据到Excel")
    @GetMapping("export-excel")
    public void exportExcel(WebSiteIndexCheckQuery query, HttpServletResponse response) {
        try {
            log.info("开始导出Excel，查询条件: {}", query);
            
            // 使用EasyExcel导出
            WebSiteIndexArticleCheckService.exportToExcelWithEasyExcel(query, response);
            
            log.info("导出Excel成功");
        } catch (Exception e) {
            log.error("导出Excel失败", e);
            try {
                response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "导出失败: " + e.getMessage());
            } catch (IOException ioException) {
                log.error("发送错误响应失败", ioException);
            }
        }
    }

    /**
     * 批量生成所有站点的首页昨日更新检查数据
     *
     * @return 生成的记录数量
     */
    @Operation(summary = "批量生成所有站点的首页昨日更新检查数据")
    @PostMapping("generate-yesterday")
    public Result<Integer> generateYesterday() {
        try {
            int generatedCount = WebSiteIndexArticleCheckService.generateAllSitesYesterdayIndexCheckData();
            return Result.ok(generatedCount);
        } catch (Exception e) {
            log.error("批量生成所有站点的首页昨日更新检查数据失败", e);
            return Result.error("生成失败：" + e.getMessage());
        }
    }
}
