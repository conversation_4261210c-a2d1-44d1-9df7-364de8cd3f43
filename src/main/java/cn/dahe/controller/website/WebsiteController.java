package cn.dahe.controller.website;

import cn.dahe.common.annotation.CurrentUser;
import cn.dahe.common.aop.role.Logical;
import cn.dahe.common.aop.role.RequiresRoles;
import cn.dahe.common.auth.SecurityUtils;
import cn.dahe.common.constants.RoleConstants;
import cn.dahe.common.constants.UserConstants;
import cn.dahe.entity.User;
import cn.dahe.entity.Website;
import cn.dahe.enums.CheckStrategyEnum;
import cn.dahe.model.dto.Result;
import cn.dahe.model.query.WebsiteQuery;
import cn.dahe.model.request.WebsiteSaveRequest;
import cn.dahe.model.vo.LoginUserVO;
import cn.dahe.model.vo.SearchWebsiteVO;
import cn.dahe.service.AppendWebsiteService;
import cn.dahe.service.WebsiteService;
import cn.dahe.task.DataPullScheduleTask;
import cn.dahe.task.SiteIndexUpdateTask;
import cn.dahe.utils.StringUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 网站信息管理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/pro/web-site")
@AllArgsConstructor
@Tag(name = "网站管理模块")
public class WebsiteController {

    @Resource
    private WebsiteService websiteService;

    @Resource
    private DataPullScheduleTask dataPullScheduleTask;

    @Resource
    private SiteIndexUpdateTask siteIndexUpdateTask;

    @Operation(summary = "分页查询网站列表")
    @PostMapping("list")
    public Result page(WebsiteQuery query) {
        return Result.ok(websiteService.page(query));
    }

    // 分页-按照状态
    @PostMapping("list-by-status")
    public Result listByStatus(int status) {
        return Result.ok(websiteService.listByStatus(status));
    }

    @PostMapping("list-all")
    public Result listAll(WebsiteQuery query, @CurrentUser LoginUserVO user) {
        if (user.getRoles().contains(UserConstants.ROLE_SON)) {
            return Result.ok(websiteService.listByUserId(user.getUserId()));
        }
        QueryWrapper<Website> wrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(query.getName())) {
            wrapper.like("web_name", query.getName());
        }
        if (StringUtils.isNotBlank(query.getUrl())) {
            wrapper.like("web_url", query.getUrl());
        }
        if (StringUtils.isNotBlank(query.getStatus())) {
            wrapper.eq("status", query.getStatus());
        }
        if (StringUtils.isNotBlank(query.getWebId())) {
            wrapper.in("site_id", Arrays.asList(query.getWebId().split(",")));
        }
        return Result.ok(websiteService.list(wrapper));
    }

    @Operation(summary = "根据名称查询站点")
    @PostMapping("search")
    public Result<List<SearchWebsiteVO>> search(@Schema(description = "站点名称") String name) {
        return Result.ok(appendWebsiteService.searchWebsiteWithStatus(name));
    }

    @Resource
    private AppendWebsiteService appendWebsiteService;

    @Operation(summary = "批量添加网站站点")
    @PostMapping("save-batch")
    public Result<?> saveBatch(@Schema(description = "站点id集合，多个逗号拼接") String ids,
                               @Schema(description = "站点名称集合，多个逗号拼接") String names,
                               @CurrentUser LoginUserVO user) {
        if (StrUtil.isAllNotBlank(ids, names)) {
            List<String> idsList = Arrays.asList(ids.split(","));
            List<String> namesList = Arrays.asList(names.split(","));
            List<Website> list = new java.util.ArrayList<>();
            for (int i = 0; i < idsList.size(); i++) {
                int id = Integer.parseInt(idsList.get(i));
                Website dbSite = websiteService.getWebsiteBySiteId(id);
                if (dbSite != null) {
                    continue;
                }
                Website website = appendWebsiteService.makeWebsite(id, namesList.get(i), user);
                list.add(website);
            }
            if (CollUtil.isEmpty(list)) {
                return Result.ok("没有新增数据");
            }
            websiteService.saveBatch(list);
            // 异步刷新首页更新结果集
            siteIndexUpdateTask.syncSaveIndexUpdateResultData();
            return Result.ok();
        }
        return Result.ok("没有新增数据");
    }

    @Operation(summary = "单个添加网站站点")
    @PostMapping("save-single")
    public Result<?> saveSingle(WebsiteSaveRequest request) {
        Integer siteId = request.getSiteId();
        Website dbSite = websiteService.getWebsiteBySiteId(siteId);
        if (dbSite != null) {
            return Result.ok(dbSite.getWebName() + "已添加");
        }
        LoginUserVO user = SecurityUtils.getLoginUser();
        Website website = appendWebsiteService.makeWebsite(siteId, request.getName(), user);
        website.setGroupId(request.getGroupId());
        websiteService.save(website);
        // 异步刷新首页更新结果集
        siteIndexUpdateTask.syncSaveIndexUpdateResultData();
        //异步触发站点栏目订阅以及脏数据清理
        dataPullScheduleTask.syncSiteColumnData();
        return Result.ok();
    }

    /**
     * 在用吗？
     */
    @PostMapping("save")
    public Result save(Website vo, @CurrentUser LoginUserVO user) {
        return websiteService.save(vo, user);
    }

    /**
     * 添加新媒体站点
     */
    @PostMapping("save-media-site")
    public Result saveMediaSite(Website vo, @CurrentUser LoginUserVO user) {
        return websiteService.saveNewMedia(vo, user);
    }

    // 添加
    @PostMapping("update")
    public Result update(Website vo, @CurrentUser LoginUserVO user) {
        return websiteService.update(vo, user);
    }

    // 修改状态
    @PostMapping("update-status")
    public Result updateStatus(@RequestParam(defaultValue = "") String id,
                               @CurrentUser LoginUserVO user) {
        return websiteService.updateStatus(id, user);
    }


    @Operation(summary = "更新网站巡查精准度")
    @PostMapping("update/check-strategy")
    public Result<String> updateCheckStrategy(@RequestParam List<Long> websiteIds,
                                              @RequestParam Integer checkStrategy) {
        boolean success = websiteService.updateCheckStrategy(websiteIds, CheckStrategyEnum.getByValue(checkStrategy));
        return success ? Result.ok("更新成功") : Result.error("更新失败");
    }

    @RequiresRoles(value = {RoleConstants.ADMIN_SUPER, RoleConstants.ADMIN_SMALL}, logical = Logical.OR)
    @Operation(summary = "站点用户列表", parameters = @Parameter(name = "option", description = "1：已关联到站点、2：已订阅服务号"))
    @RequestMapping("user/list")
    Result<List<User>> userList(@NotNull Integer id, @NotNull Integer tenantId, @NotNull Integer option) {
        return Result.ok(websiteService.userList(id, tenantId, option));
    }
}
