package cn.dahe.controller.website;

import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.Result;
import cn.dahe.model.query.WebSiteChannelCheckQuery;
import cn.dahe.model.vo.WebSiteChannelCheckDetailVO;
import cn.dahe.model.vo.WebSiteChannelCheckExportVO;
import cn.dahe.model.vo.WebSiteChannelStatisticsVO;
import cn.dahe.service.WebSiteChannelCheckService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Tag(name = "网站栏目更新检查")
@Slf4j
@RestController
@RequestMapping("/pro/website/channelArticleCheck")
public class WebSiteChannelCheckController {

    @Resource
    private WebSiteChannelCheckService webSiteChannelCheckService;

    /**
     * 生成栏目检查结果
     */
    @Operation(summary = "生成栏目检查结果")
    @PostMapping("/generate")
    public Result<String> generateChannelCheckData() {
        try {
            log.info("开始生成栏目检查数据");
            Integer count = webSiteChannelCheckService.generateChannelCheckData();
            String message = String.format("栏目检查数据生成成功，共生成%d条记录", count);
            log.info(message);
            return Result.ok(message);
        } catch (Exception e) {
            log.error("生成栏目检查数据失败", e);
            return Result.error("生成失败：" + e.getMessage());
        }
    }

    /**
     * 异步生成栏目检查结果（多线程优化版本）
     */
    @Operation(summary = "异步生成栏目检查结果（多线程优化）")
    @PostMapping("/generate-async")
    public Result<String> generateChannelCheckDataAsync() {
        try {
            log.info("开始异步生成栏目检查数据");
            webSiteChannelCheckService.generateChannelCheckDataAsync();
            String message = "栏目检查数据异步生成任务已提交，请稍后查看结果";
            log.info(message);
            return Result.ok(message);
        } catch (Exception e) {
            log.error("异步生成栏目检查数据失败", e);
            return Result.error("异步生成失败：" + e.getMessage());
        }
    }

    /**
     * 分页查询栏目检查结果
     * @param query
     * @return
     */
    @Operation(summary = "分页查询栏目检查结果")
    @PostMapping("/page-detail")
    public Result<PageResult<WebSiteChannelCheckDetailVO>> pageChannelCheckDetail(WebSiteChannelCheckQuery query) {
        try {
            long startTime = System.currentTimeMillis();
            // 设置默认分页参数
            if (query.getPage() == null || query.getPage() < 1) {
                query.setPage(1);
            }
            if (query.getLimit() == null || query.getLimit() < 1) {
                query.setLimit(10);
            }
            log.info("开始查询栏目检查结果，查询条件: {}", query);
            PageResult<WebSiteChannelCheckDetailVO> result = webSiteChannelCheckService.pageChannelCheckDetail(query);
            long endTime = System.currentTimeMillis();
            log.info("查询栏目检查结果成功，共查询到{}条数据，耗时{}ms", result.getTotal(), endTime - startTime);
            return Result.ok(result);
        } catch (Exception e) {
            log.error("查询栏目检查结果失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }


    /**
     * 查询栏目检查结果统计
     */
    @Operation(summary = "查询栏目检查结果统计")
    @PostMapping("/statistics")
    public Result<List<WebSiteChannelStatisticsVO>> getChannelCheckStatistics(WebSiteChannelCheckQuery query) {
        try {
            long startTime = System.currentTimeMillis();
            
            log.info("开始查询栏目检查统计结果，查询条件: {}", query);
            List<WebSiteChannelStatisticsVO> result = webSiteChannelCheckService.getChannelCheckStatistics(query);
            
            long endTime = System.currentTimeMillis();
            log.info("查询栏目检查统计结果成功，共查询到{}个站点的数据，耗时{}ms", result.size(), endTime - startTime);
            return Result.ok(result);
        } catch (Exception e) {
            log.error("查询栏目检查统计结果失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 导出栏目检查结果
     */
    @Operation(summary = "导出栏目检查结果")
    @GetMapping("/export")
    public void exportChannelCheckData(WebSiteChannelCheckQuery query, HttpServletResponse response) {
        try {
            long startTime = System.currentTimeMillis();
            log.info("开始导出栏目检查结果，查询条件: {}", query);
            
            // 获取导出数据
            List<WebSiteChannelCheckExportVO> exportData = webSiteChannelCheckService.exportChannelCheckData(query);
            
            // 设置响应头
            String fileName = "栏目检查结果_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".xlsx";
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName);
            
            // 使用EasyExcel导出
            EasyExcel.write(response.getOutputStream(), WebSiteChannelCheckExportVO.class)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .sheet("栏目检查结果")
                    .doWrite(exportData);
            
            long endTime = System.currentTimeMillis();
            log.info("导出栏目检查结果成功，共导出{}条数据，耗时{}ms", exportData.size(), endTime - startTime);
            
        } catch (IOException e) {
            log.error("导出栏目检查结果失败", e);
            try {
                response.setContentType("application/json");
                response.setCharacterEncoding("utf-8");
                response.getWriter().write("{\"code\":500,\"message\":\"导出失败\"}");
            } catch (IOException ex) {
                log.error("写入错误响应失败", ex);
            }
        } catch (Exception e) {
            log.error("导出栏目检查结果失败", e);
            try {
                response.setContentType("application/json");
                response.setCharacterEncoding("utf-8");
                response.getWriter().write("{\"code\":500,\"message\":\"导出失败：" + e.getMessage().replace("\"", "\\\"") + "\"}");
            } catch (IOException ex) {
                log.error("写入错误响应失败", ex);
            }
        }
    }

}
