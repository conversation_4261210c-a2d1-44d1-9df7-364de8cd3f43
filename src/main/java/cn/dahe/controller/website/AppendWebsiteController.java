package cn.dahe.controller.website;

import cn.dahe.common.annotation.CurrentUser;
import cn.dahe.common.constants.UserConstants;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.Result;
import cn.dahe.model.query.AppendWebsiteQuery;
import cn.dahe.model.vo.LoginUserVO;
import cn.dahe.model.vo.WebsiteVO;
import cn.dahe.service.UserWebsiteService;
import cn.dahe.service.WebsiteGroupService;
import cn.dahe.service.WebsiteService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 网站信息管理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/pro/website")
@AllArgsConstructor
@Tag(name = "网站管理模块-追加接口")
public class AppendWebsiteController {

    @Resource
    private WebsiteService websiteService;

    @Resource
    private WebsiteGroupService websiteGroupService;


    @Operation(summary = "分页查询网站列表，需要区分新媒体/网站")
    @PostMapping("list")
    public Result<PageResult<WebsiteVO>> page(AppendWebsiteQuery query) {
        return Result.ok(websiteService.pageList(query));
    }

    @Resource
    private UserWebsiteService userWebsiteService;

    @Operation(summary = "查询所有可用网站/账号列表")
    @PostMapping("list-select")
    public Result<List<WebsiteVO>> listForSelect(AppendWebsiteQuery query, @CurrentUser LoginUserVO user) {
        List<WebsiteVO> results;
        //  用于下拉站点列表？ TODO 有权限的话 其他站点筛选也有限制
        if (user.getRoles().contains(UserConstants.ROLE_SON)) {
            List<Long> websiteIds = userWebsiteService.listWebsiteIdsByUserId(user.getUserId());
            query.setWebsiteIds(websiteIds);
        }
        results = websiteService.listSelectByQuery(query);
        return Result.ok(results);
    }







}
