package cn.dahe.controller.website;

import cn.dahe.common.annotation.CurrentUser;
import cn.dahe.entity.Article;
import cn.dahe.entity.NewMediaDailyStatistic;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.Result;
import cn.dahe.model.vo.LoginUserVO;
import cn.dahe.service.impl.NewMediaService;
import cn.dahe.utils.fan.FanCollectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.Map;
import java.util.Set;

/**
 * 新媒体管理
 *
 * <AUTHOR>
 */
@Validated
@RestController
@RequestMapping("pro/new/media")
@AllArgsConstructor
@Tag(name = "新媒体管理列表")
public class NewMediaController {
    final NewMediaService newMediaService;

    /**
     * 根据新媒体名称查询新媒体站点
     *
     * @param name
     * @param type
     * @return
     */
    @Operation(summary = "根据名称查询站点")
    @PostMapping("/search")
    public Result search(@Schema(description = "新媒体名称") String name, Integer type) {
        if (StrUtil.isNotBlank(name)) {
            JSONObject jsonObject = FanCollectUtil.getNewMedia(name, type);
            Boolean success = jsonObject.getBoolean("success");
            if (success) {
                return Result.ok(jsonObject);
            } else {
                return Result.error("未搜索到相关信息,请输入其他关键词进行搜索");
            }
        }
        return Result.ok();
    }

    @Operation(summary = "新媒体统计页")
    @RequestMapping("statistic/page")
    Result<PageResult<Map<String, Object>>> statisticPage(@RequestParam(required = false) Set<Integer> platformSet,
                                                          @RequestParam(required = false) Set<Integer> groupIdSet,
                                                          @RequestParam(required = false) Set<Long> newMediaIdSet,
                                                          LocalDate startDate, LocalDate endDate,
                                                          Boolean updated,
                                                          @NotNull Integer tenantId, Integer sort, Integer noUpdateDay,
                                                          @RequestParam(defaultValue = "1") int page,
                                                          @RequestParam(defaultValue = "10") @Max(200) int limit,
                                                          @CurrentUser LoginUserVO loginUser) {
        return Result.ok(newMediaService.statisticPage(platformSet, groupIdSet, newMediaIdSet,
                                                       startDate, endDate,
                                                       updated,
                                                       tenantId, sort, noUpdateDay, page, limit,
                                                       loginUser));
    }

    @Operation(summary = "新媒体统计")
    @RequestMapping("statistic")
    Result<Map<String, Object>> statistic(@RequestParam(required = false) Set<Integer> platformSet,
                                          @RequestParam(required = false) Set<Integer> groupIdSet,
                                          @RequestParam(required = false) Set<Long> newMediaIdSet,
                                          LocalDate startDate, LocalDate endDate,
                                          @NotNull Integer tenantId,
                                          @CurrentUser LoginUserVO loginUser) {
        return Result.ok(newMediaService.statistic(platformSet, groupIdSet, newMediaIdSet, startDate, endDate, tenantId, loginUser));
    }

    @Operation(summary = "新媒体统计总数页")
    @RequestMapping("statistic/total/page")
    Result<PageResult<NewMediaDailyStatistic>> statisticTotalPage(LocalDate startDate, LocalDate endDate,
                                                                  @NotNull Long newMediaId,
                                                                  @RequestParam(defaultValue = "1") int page,
                                                                  @RequestParam(defaultValue = "10") @Max(200) int limit) {
        return Result.ok(newMediaService.statisticTotalPage(startDate, endDate, newMediaId, page, limit));
    }

    @Operation(summary = "新媒体统计导出")
    @RequestMapping("statistic/export")
    void statisticExport(@RequestParam(required = false) Set<Integer> platformSet,
                         @RequestParam(required = false) Set<Integer> groupIdSet,
                         @RequestParam(required = false) Set<Long> newMediaIdSet,
                         LocalDate startDate, LocalDate endDate,
                         @NotNull Integer tenantId,
                         @CurrentUser LoginUserVO loginUser) {
        newMediaService.statisticExport(platformSet, groupIdSet, newMediaIdSet, startDate, endDate, tenantId, loginUser);
    }

    @Operation(summary = "新媒体文章页")
    @RequestMapping("article/page")
    Result<PageResult<Article>> articlePage(@RequestParam(required = false) Set<Integer> platformSet,
                                            @RequestParam(required = false) Set<Integer> groupIdSet,
                                            @RequestParam(required = false) Set<Long> newMediaIdSet,
                                            String startTime, String endTime, String keyword,
                                            @NotNull Integer tenantId, Integer sort,
                                            @RequestParam(defaultValue = "1") int page, @RequestParam(defaultValue = "10") @Max(200) int limit,
                                            @CurrentUser LoginUserVO loginUser) {
        return Result.ok(newMediaService.articlePage(platformSet, groupIdSet, newMediaIdSet,
                                                     startTime, endTime, keyword,
                                                     tenantId, sort, page, limit,
                                                     loginUser));
    }

    @Operation(summary = "新媒体文章导出")
    @RequestMapping("article/export")
    void articleExport(@RequestParam(required = false) Set<Integer> platformSet,
                       @RequestParam(required = false) Set<Integer> groupIdSet,
                       @RequestParam(required = false) Set<Long> newMediaIdSet,
                       String startTime, String endTime, String keyword,
                       @NotNull Integer tenantId, @RequestParam(defaultValue = "1") int page, @RequestParam(defaultValue = "10") @Max(200) int limit,
                       @CurrentUser LoginUserVO loginUser) {
        newMediaService.articleExport(platformSet, groupIdSet, newMediaIdSet, startTime, endTime, keyword, tenantId, page, limit, loginUser);
    }

    @Operation(summary = "新媒体页")
    @RequestMapping("page")
    Result<PageResult<Map<String, Object>>> page(@RequestParam(required = false) Set<Integer> platformSet,
                                                 @RequestParam(required = false) Set<Integer> groupIdSet,
                                                 @RequestParam(required = false) Set<Long> newMediaIdSet,
                                                 String name, String link,
                                                 @NotNull Integer tenantId, Integer status,
                                                 @RequestParam(defaultValue = "1") int page, @RequestParam(defaultValue = "10") @Max(200) int limit,
                                                 @CurrentUser LoginUserVO loginUser) {
        return Result.ok(newMediaService.page(platformSet, groupIdSet, newMediaIdSet, name, link, tenantId, status, page, limit, loginUser));
    }
}
