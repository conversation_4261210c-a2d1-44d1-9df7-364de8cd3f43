package cn.dahe.controller.website;

import cn.dahe.common.annotation.CurrentUser;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.Result;
import cn.dahe.model.dto.BatchUpdateChannelTypeDto;
import cn.dahe.entity.Channel;
import cn.dahe.model.query.ChannelQuery;
import cn.dahe.service.ChannelService;
import cn.dahe.model.vo.LoginUserVO;
import cn.dahe.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.Operation;

import javax.annotation.Resource;
import java.util.List;

/**
 * 网站信息管理
 */
@Slf4j
@RestController
@RequestMapping("/pro/channel")
@AllArgsConstructor
@Tag(name = "网站管理模块")
public class ChannelController {

    @Resource
    private ChannelService channelService;

    //分页
    @PostMapping("list")
    public Result<PageResult<Channel>> page(ChannelQuery query) {
        return Result.ok(channelService.page(query));
    }

    @PostMapping("list-all")
    public Result<List<Channel>> listAll(ChannelQuery query) {
        QueryWrapper<Channel> wrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(query.getName())) {
            wrapper.like("name", query.getName());
        }
        if (StringUtils.isNotBlank(query.getUrl())) {
            wrapper.like("url", query.getUrl());
        }
        if (StringUtils.isNotBlank(query.getEnable())) {
            wrapper.eq("enable", query.getEnable());
        }
        if (StringUtils.isNotBlank(query.getSiteId())) {
            wrapper.eq("site_id", query.getSiteId());
        }
        return Result.ok(channelService.list(wrapper));
    }


    //添加
    @PostMapping("save")
    public Result<String> save(Channel vo, @CurrentUser LoginUserVO user) {
        return channelService.save(vo, user);
    }

    //添加
    @PostMapping("update")
    public Result<String> update(Channel vo, @CurrentUser LoginUserVO user) {
        return channelService.update(vo, user);
    }

    /**
     * 更新栏目类型（单个）
     * 
     * @param id 栏目ID
     * @param channelType 栏目类型
     * @param user 当前登录用户
     * @return 更新结果
     */
    @PostMapping("update-type")
    @Operation(summary = "更新栏目类型", description = "更新单个栏目的类型")
    public Result<String> updateChannelType(@RequestParam("id") Integer id,
                                        @RequestParam("channelType") Long channelType,
                                        @CurrentUser LoginUserVO user) {
        if (id == null || id <= 0) {
            return Result.error("栏目ID不能为空且必须大于0");
        }
        if (channelType == null) {
            return Result.error("栏目类型不能为空");
        }
        return channelService.updateType(id, channelType, user);
    }

    /**
     * 批量更新栏目类型
     * 
     * @param batchDto 批量更新参数
     * @param user 当前登录用户
     * @return 更新结果
     */
    @PostMapping("batch-update-type")
    @Operation(summary = "批量更新栏目类型", description = "批量更新多个栏目的类型，支持逗号分隔的ID字符串")
    public Result<String> batchUpdateChannelType(BatchUpdateChannelTypeDto batchDto,
                                               @CurrentUser LoginUserVO user) {
        log.info("接收到批量更新请求，参数: {}", batchDto);
        
        if (batchDto == null) {
            return Result.error("批量更新参数不能为空");
        }
        if (!StringUtils.hasText(batchDto.getChannelIds())) {
            return Result.error("栏目ID列表不能为空");
        }
        if (batchDto.getChannelTypeId() == null) {
            return Result.error("栏目类型ID不能为空");
        }
        
        // 校验栏目ID格式
        try {
            if (!batchDto.isValidChannelIds()) {
                return Result.error("栏目ID格式错误，请使用逗号分隔的正整数，如：1,2,3");
            }
        } catch (IllegalArgumentException e) {
            return Result.error(e.getMessage());
        }
        
        log.info("参数校验通过，开始批量更新，栏目ID列表: {}, 栏目类型ID: {}", 
                batchDto.getChannelIds(), batchDto.getChannelTypeId());
        
        return channelService.batchUpdateType(batchDto, user);
    }

    //修改状态
    @PostMapping("update-status")
    public Result<String> updateStatus(@RequestParam(defaultValue = "") String id,
                               @CurrentUser LoginUserVO user) {
        return channelService.updateStatus(id, user);
    }

}