package cn.dahe.controller.website;

import cn.dahe.common.annotation.CurrentUser;
import cn.dahe.model.dto.Result;
import cn.dahe.model.query.AppendWebsiteQuery;
import cn.dahe.model.request.WebsiteGroupRelateRequest;
import cn.dahe.model.vo.LoginUserVO;
import cn.dahe.model.vo.WebsiteGroupVO;
import cn.dahe.service.WebsiteGroupService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 网站分组管理
 */
@RestController
@RequestMapping("/pro/website/group")
@AllArgsConstructor
@Tag(name = "网站分组管理模块")
public class WebsiteGroupController {

    @Resource
    private WebsiteGroupService websiteGroupService;

    @Operation(summary = "获取所有分组列表，不含站点，暂时不受角色权限影响")
    @PostMapping("list")
    public Result<List<WebsiteGroupVO>> list() {
        return Result.ok(websiteGroupService.listAvailableGroup());
    }

    @Operation(summary = "分组列表，包含按媒体类型的站点，包含禁用站点")
    @PostMapping("list-websites")
    public Result<List<WebsiteGroupVO>> listWebsites(AppendWebsiteQuery query) {
        query.setShowDisable(true);
        return Result.ok(websiteGroupService.listSelectByQuery(query));
    }

    @Operation(summary = "分组列表，包含按媒体类型的站点，受角色、权限、分配影响")
    @PostMapping("list-select")
    public Result<List<WebsiteGroupVO>> listByGroup(AppendWebsiteQuery query) {
        query.setShowDisable(false);
        return Result.ok(websiteGroupService.listSelectByQuery(query));
    }

    @Operation(summary = "添加分组")
    @PostMapping("save")
    public Result<String> save(String groupName) {
        websiteGroupService.saveByGroupName(groupName);
        return Result.ok();
    }

    @Operation(summary = "添加分组")
    @PostMapping("update")
    public Result<String> update(Long groupId,String groupName) {
        websiteGroupService.updateGroupName(groupId,groupName);
        return Result.ok();
    }

    @Operation(summary = "删除分组")
    @PostMapping("delete")
    public Result<String> delete(@RequestParam Integer groupId, @CurrentUser LoginUserVO user) {
        websiteGroupService.delete(groupId, user);
        return Result.ok();
    }

    @Operation(summary = "关联网站分组")
    @PostMapping("relate")
    public Result<Long> relate(WebsiteGroupRelateRequest request) {
        websiteGroupService.relateByRequest(request);
        return Result.ok();
    }
} 