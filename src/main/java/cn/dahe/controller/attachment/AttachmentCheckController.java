package cn.dahe.controller.attachment;

import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.Result;
import cn.dahe.model.query.AttachmentCheckQuery;
import cn.dahe.model.vo.AttachmentCheckVO;
import cn.dahe.service.AttachmentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;

import static cn.dahe.common.model.ResultCode.NotFindError;

/**
 * 附件检查Controller - 对应原型图功能
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@RestController
@RequestMapping("/pro/attachment-check")
@AllArgsConstructor
@Tag(name = "附件检查管理")
public class AttachmentCheckController {

    @Resource
    private AttachmentService attachmentService;

    @Operation(summary = "分页查询附件检查记录", description = "获取附件检查记录列表，包含网站名称、来源网站、来源页面等信息")
    @PostMapping("/page")
    public Result<PageResult<AttachmentCheckVO>> page(AttachmentCheckQuery query) {
        return Result.ok(attachmentService.pageAttachmentAndCheckResults(query));
    }


    @Operation(summary = "获取附件检查详情（快照）", description = "根据ID获取附件检查详细信息")
    @PostMapping("/snapshot/{attachmentId}")
    public Result<AttachmentCheckVO> getAttachmentSnapshotResults(@PathVariable("attachmentId") Long attachmentId, AttachmentCheckQuery query) {
        AttachmentCheckVO result = attachmentService.getAttachmentAndCheckResults(attachmentId, query, false);
        return result == null ? Result.error(NotFindError) : Result.ok(result);
    }

    // ==================== 数据导出 ====================

    // @Resubmit(5000)
    @Operation(summary = "导出附件检查记录", description = "导出附件检查记录到Excel")
    @GetMapping("export")
    public void export(AttachmentCheckQuery query) throws IOException {
        attachmentService.exportByQuery(query);
    }

}
