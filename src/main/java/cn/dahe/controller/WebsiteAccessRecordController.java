package cn.dahe.controller;

import cn.dahe.common.annotation.CurrentUser;
import cn.dahe.model.dto.WebsiteAccessOverviewDto;
import cn.dahe.model.dto.WebsiteUpdateStatsDto;
import cn.dahe.entity.WebsiteAccessRecord;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.Result;
import cn.dahe.model.query.ChkUpdateSiteIndexQuery;
import cn.dahe.model.vo.LoginUserVO;
import cn.dahe.model.query.WebsiteAccessRecordQuery;
import cn.dahe.service.WebsiteAccessRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2025-07-08
 */
@RestController
@RequestMapping("/pro/web/access-record")
@AllArgsConstructor
@Tag(name= "网站连通性检查模块")
public class WebsiteAccessRecordController {

    @Resource
    private WebsiteAccessRecordService websiteAccessRecordService;

    @Operation(summary = "分页查询网站访问详细记录", description = "获取网站访问的详细记录信息")
    @PostMapping("page-home-access-detail")
    public Result<PageResult<WebsiteAccessRecord>> page(
            @Parameter(description = "查询参数") WebsiteAccessRecordQuery query,
            @Parameter(hidden = true) @CurrentUser LoginUserVO user) {
        return Result.ok(websiteAccessRecordService.page(query));
    }

    @Operation(summary = "分页查询网站访问统计信息", description = "获取网站访问的统计概览信息")
    @PostMapping("page-home-access-stats")
    public Result<PageResult<WebsiteAccessOverviewDto>> pageStats(
            @Parameter(description = "查询参数") WebsiteAccessRecordQuery query,
            @Parameter(hidden = true) @CurrentUser LoginUserVO user) {
        return Result.ok(websiteAccessRecordService.pageStats(query));
    }

    @Operation(summary = "获取网站访问总体统计", description = "获取网站访问的总体概览统计信息")
    @PostMapping("home-access-total-stats")
    public Result<WebsiteUpdateStatsDto> totalStats(
            @Parameter(description = "查询参数") WebsiteAccessRecordQuery query,
            @Parameter(hidden = true) @CurrentUser LoginUserVO user) {
        return Result.ok(websiteAccessRecordService.totalStats(query));
    }

    @Operation(summary = "导出网站连通性检查记录", description = "导出首页更新检查记录到Excel")
    @GetMapping("export")
    public void export(
            WebsiteAccessRecordQuery query,
            @CurrentUser LoginUserVO user,
            HttpServletResponse response) throws IOException {
        websiteAccessRecordService.export(query, user, response);
    }


}
