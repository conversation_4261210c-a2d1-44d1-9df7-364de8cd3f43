package cn.dahe.controller;

import cn.dahe.common.annotation.CurrentUser;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.Result;
import cn.dahe.entity.ChkUpdateSiteIndex;
import cn.dahe.model.query.ChkUpdateSiteIndexQuery;
import cn.dahe.service.ChkUpdateSiteIndexService;
import cn.dahe.model.vo.ChkUpdateSiteIndexVO;
import cn.dahe.model.vo.LoginUserVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

/**
 * 首页更新检查Controller - 专注于首页更新检查功能
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@RestController
@RequestMapping("/pro/chk-update-site-index")
@AllArgsConstructor
@Tag(name = "首页更新检查")
public class ChkUpdateSiteIndexController {

    @Resource
    private ChkUpdateSiteIndexService chkUpdateSiteIndexService;

    // ==================== 首页更新检查概览 ====================

    @Operation(summary = "获取首页更新检查概览统计", description = "获取检测网站数、更新网站、未更新网站统计数据")
    @PostMapping("overview-statistics")
    public Result<Map<String, Object>> getOverviewStatistics(
            @Parameter(description = "查询参数") ChkUpdateSiteIndexQuery query,
            @CurrentUser LoginUserVO user) {
        return Result.ok(chkUpdateSiteIndexService.getOverviewStatistics(query));
    }

    // ==================== 首页更新检查记录 ====================

    @Operation(summary = "分页查询站点首页更新结果", description = "分页查询站点首页更新结果")
    @PostMapping("page")
    public Result<PageResult<ChkUpdateSiteIndexVO>> page(
            @Parameter(description = "查询参数") ChkUpdateSiteIndexQuery query,
            @CurrentUser LoginUserVO user) {
        return Result.ok(chkUpdateSiteIndexService.page(query));
    }

    @Operation(summary = "分页查询首页更新的文章列表", description = "分页查询chk_update_site_index表中的详细记录")
    @PostMapping("/detail")
    public Result<PageResult<ChkUpdateSiteIndex>> detail(
            @Parameter(description = "查询参数") ChkUpdateSiteIndexQuery query,
            @CurrentUser LoginUserVO user) {
        return Result.ok(chkUpdateSiteIndexService.pageDetail(query));
    }

    // ==================== 数据导出 ====================

    @Operation(summary = "导出检查记录", description = "导出首页更新检查记录到Excel")
    @GetMapping("export")
    public void export(
            ChkUpdateSiteIndexQuery query,
            @CurrentUser LoginUserVO user,
            HttpServletResponse response) throws IOException {
        chkUpdateSiteIndexService.export(query, user, response);
    }
}
