package cn.dahe.controller;

import cn.dahe.common.annotation.CurrentUser;
import cn.dahe.common.aop.role.Logical;
import cn.dahe.common.aop.role.RequiresRoles;
import cn.dahe.common.constants.RoleConstants;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.Result;
import cn.dahe.model.dto.TeamDto;
import cn.dahe.model.dto.TenantDto;
import cn.dahe.model.vo.LoginUserVO;
import cn.dahe.service.impl.TenantService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.hibernate.validator.constraints.Range;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;

@Tag(name = "租户")
@Validated
@RequestMapping("pro/tenant")
@RequiredArgsConstructor
@RestController
public class TenantController {
    final TenantService tenantService;

    @RequiresRoles(value = RoleConstants.ADMIN_SUPER)
    @Operation(summary = "租户添加")
    @RequestMapping("add")
    Result<Void> add(Integer userCount, Integer siteCount, Integer newMediaCount, Integer groupCount, Integer teamCount, Integer warnPlanCount,
                     @NotBlank String name, String remark,
                     LocalDate expireDate,
                     @CurrentUser LoginUserVO loginUser) {
        tenantService.add(userCount, siteCount, newMediaCount, groupCount, teamCount, warnPlanCount, name, remark, expireDate, loginUser);
        return Result.ok();
    }

    @RequiresRoles(value = RoleConstants.ADMIN_SUPER)
    @Operation(summary = "租户删除")
    @RequestMapping("delete")
    Result<Void> delete(@NotNull Integer id, @CurrentUser LoginUserVO loginUser) {
        tenantService.delete(id, loginUser);
        return Result.ok();
    }

    @RequiresRoles(value = RoleConstants.ADMIN_SUPER)
    @Operation(summary = "租户修改")
    @RequestMapping("update")
    Result<Void> update(@NotNull Integer id, @Range(min = 1, max = 2) Integer status,
                        Integer userCount, Integer siteCount, Integer newMediaCount, Integer groupCount, Integer teamCount, Integer warnPlanCount,
                        String name, String remark,
                        LocalDate expireDate,
                        @CurrentUser LoginUserVO loginUser) {
        tenantService.update(id, status, userCount, siteCount, newMediaCount, groupCount, teamCount, warnPlanCount,
                             name, remark,
                             expireDate,
                             loginUser);
        return Result.ok();
    }

    @RequiresRoles(value = RoleConstants.ADMIN_SUPER)
    @Operation(summary = "租户页")
    @RequestMapping("page")
    Result<PageResult<TenantDto>> page(String name,
                                       @Range(min = 1, max = 3) Integer status,
                                       @RequestParam(defaultValue = "1") int page, @RequestParam(defaultValue = "10") @Max(200) int limit) {
        return tenantService.page(name, status, page, limit);
    }

    @RequiresRoles(value = {RoleConstants.ADMIN_SUPER, RoleConstants.ADMIN_SMALL}, logical = Logical.OR)
    @Operation(summary = "团队添加")
    @RequestMapping("team/add")
    Result<Void> teamAdd(@NotNull Integer tenantId, @NotBlank String name, String remark, @CurrentUser LoginUserVO loginUser) {
        tenantService.teamAdd(tenantId, name, remark, loginUser);
        return Result.ok();
    }

    @RequiresRoles(value = {RoleConstants.ADMIN_SUPER, RoleConstants.ADMIN_SMALL}, logical = Logical.OR)
    @Operation(summary = "团队删除")
    @RequestMapping("team/delete")
    Result<Void> teamDelete(@NotNull Integer id, @CurrentUser LoginUserVO loginUser) {
        tenantService.teamDelete(id, loginUser);
        return Result.ok();
    }

    @RequiresRoles(value = {RoleConstants.ADMIN_SUPER, RoleConstants.ADMIN_SMALL}, logical = Logical.OR)
    @Operation(summary = "团队修改")
    @RequestMapping("team/update")
    Result<Void> teamUpdate(@NotNull Integer id, @NotNull Integer tenantId, String name, String remark, @CurrentUser LoginUserVO loginUser) {
        tenantService.teamUpdate(id, tenantId, name, remark, loginUser);
        return Result.ok();
    }

    @RequiresRoles(value = {RoleConstants.ADMIN_SUPER, RoleConstants.ADMIN_SMALL}, logical = Logical.OR)
    @Operation(summary = "团队页")
    @RequestMapping("team/page")
    Result<PageResult<TeamDto>> teamPage(String name,
                                         @NotNull Integer tenantId,
                                         @RequestParam(defaultValue = "1") int page, @RequestParam(defaultValue = "10") @Max(200) int limit) {
        return tenantService.teamPage(name, tenantId, page, limit);
    }
}
