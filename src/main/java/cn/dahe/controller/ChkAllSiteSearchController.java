package cn.dahe.controller;

import cn.dahe.common.annotation.CurrentUser;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.Result;
import cn.dahe.model.query.AllSiteSearchQuery;
import cn.dahe.model.query.ArticleCheckQuery;
import cn.dahe.model.query.ChkUpdateSiteColumnQuery;
import cn.dahe.model.vo.ArticleAllSiteSearchVO;
import cn.dahe.model.vo.ArticleCheckVO;
import cn.dahe.model.vo.ChkUpdateSiteColumnVO;
import cn.dahe.model.vo.LoginUserVO;
import cn.dahe.service.ArticleService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Slf4j
@RestController
@RequestMapping("/pro/chk-all-site-search")
@AllArgsConstructor
@Tag(name = "全站搜索")
public class ChkAllSiteSearchController {

    @Resource
    private ArticleService articleService;

    @Operation(summary = "分页查询文章内容及检查结果")
    @PostMapping(value = "/page")
    public Result<PageResult<ArticleAllSiteSearchVO>> pageArticleChecks(AllSiteSearchQuery query) {
        IPage<ArticleAllSiteSearchVO> result = articleService.pageAllSiteSearch(query);
        return Result.ok(PageResult.page(result));
    }

    @Operation(summary = "导出全站检索记录", description = "导出全站检索记录")
    @GetMapping("export")
    public void allSiteSearchExport(AllSiteSearchQuery query, @CurrentUser LoginUserVO user,
            HttpServletResponse response) throws IOException {
        articleService.allSiteSearchExport(query, user, response);
    }

}
