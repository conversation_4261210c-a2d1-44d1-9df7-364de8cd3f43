package cn.dahe.controller.article;

import cn.dahe.check.service.CheckService;
import cn.dahe.enums.AuditStatusEnum;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.Result;
import cn.dahe.model.query.ArticleCheckQuery;
import cn.dahe.model.request.CheckResultAuditRequest;
import cn.dahe.model.vo.ArticleCheckVO;
import cn.dahe.service.ArticleService;
import cn.dahe.service.CheckResultService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

import static cn.dahe.common.model.ResultCode.NotFindError;

/**
 * 文章检查控制器
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Tag(name = "内容巡检")
@RestController
@RequestMapping("/pro/article-check")
public class ArticleCheckController {

    @Resource
    private CheckResultService checkResultService;
    @Resource
    private ArticleService articleService;
    @Resource
    private CheckService checkService;

    @Operation(summary = "分页查询文章内容及检查结果")
    @PostMapping(value = "/page")
    public Result<PageResult<ArticleCheckVO>> pageArticleChecks(ArticleCheckQuery query) {
        IPage<ArticleCheckVO> result = articleService.pageArticleAndCheckResults(query);
        return Result.ok(PageResult.page(result));
    }

    // @Resubmit(5000)
    @Operation(summary = "导出内容检查记录", description = "导出内容检查记录到Excel")
    @GetMapping("/export")
    public void export(ArticleCheckQuery query) {
        articleService.exportByQuery(query);
    }

    @Operation(summary = "获取文章内容及检查结果（非快照）")
    @GetMapping("/info/{articleId}")
    public Result<ArticleCheckVO> getArticleChecks(@PathVariable("articleId") Long articleId, ArticleCheckQuery query) {
        ArticleCheckVO result = articleService.getArticleAndCheckResults(articleId, query);
        return result == null ? Result.error(NotFindError) : Result.ok(result);
    }

    @Operation(summary = "获取文章内容及检查结果（快照）")
    @GetMapping("/snapshot/{articleId}")
    public Result<ArticleCheckVO> getArticleSnapshotResults(@PathVariable("articleId") Long articleId, ArticleCheckQuery query) {
        ArticleCheckVO result = articleService.getArticleAndSnapshotResults(articleId, query);
        return result == null ? Result.error(NotFindError) : Result.ok(result);
    }

    @Operation(summary = "错词审核")
    @PostMapping("/result/audit")
    public Result<String> updateAuditStatusReject(CheckResultAuditRequest request) {
        AuditStatusEnum auditStatusEnum = AuditStatusEnum.getByValue(request.getAuditStatus());
        Long articleId = request.getArticleId();
        List<Long> resultIds = request.getResultIds();
        boolean success = checkService.updateArticleAuditStatus(articleId, resultIds, auditStatusEnum);
        return success ? Result.ok() : Result.error();
    }


}