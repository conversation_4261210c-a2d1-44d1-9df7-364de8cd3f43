package cn.dahe.controller.article;


import cn.dahe.common.aop.role.Logical;
import cn.dahe.common.aop.role.RequiresRoles;
import cn.dahe.common.constants.RoleConstants;
import cn.dahe.entity.ArticleOperateLog;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.Result;
import cn.dahe.model.query.OperateLogQuery;
import cn.dahe.service.ArticleOperateLogService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/pro/article/log")
@AllArgsConstructor
public class ArticleLogController {

    private final ArticleOperateLogService articleOperateLogService;

    @PostMapping("list")
    @RequiresRoles(value = {RoleConstants.ADMIN_SUPER}, logical = Logical.OR)
    public Result<PageResult<ArticleOperateLog>> page(OperateLogQuery query) {
        PageResult<ArticleOperateLog> page = articleOperateLogService.page(query);
        return Result.ok(page);
    }


}