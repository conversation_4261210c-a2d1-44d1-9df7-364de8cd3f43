package cn.dahe.controller.article;

import cn.dahe.common.annotation.CurrentUser;
import cn.dahe.common.aop.log.OperateLog;
import cn.dahe.entity.Article;
import cn.dahe.entity.Website;
import cn.dahe.enums.AuditStatusEnum;
import cn.dahe.enums.OperateTypeEnum;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.Result;
import cn.dahe.model.query.ChannelArticleQuery;
import cn.dahe.model.query.IndexArticleQuery;
import cn.dahe.model.vo.ArticleVO;
import cn.dahe.model.vo.LoginUserVO;
import cn.dahe.service.ArticleService;
import cn.dahe.service.WebsiteService;
import cn.dahe.utils.caiji.WebSiteGetHtmlUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.util.List;

/**
 * 采集文章Controller
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Tag(name = "文章管理")
@RestController
@Slf4j
@RequestMapping("/pro/article")
public class ArticleController {

    @Resource
    private ArticleService articleService;
    @Resource
    private WebsiteService websiteService;




    @Operation(summary = "文章审核-驳回")
    @PostMapping("audit-reject")
    @OperateLog(name = "审核驳回", type = OperateTypeEnum.UPDATE, isArticleOperation = true)
    public Result<String> updateStatusReject(
            @Parameter(description = "文章ID列表，逗号分隔") @RequestParam List<Long> articleIds) {
        boolean success = articleService.updateAuditStatus(articleIds, AuditStatusEnum.REJECT);
        return success ? Result.ok("操作成功") : Result.error("操作失败");
    }


    @Operation(summary = "文章审核-通过")
    @PostMapping("audit-pass")
    @OperateLog(name = "审核通过", type = OperateTypeEnum.UPDATE, isArticleOperation = true)
    public Result<String> updateStatusPass(
            @Parameter(description = "文章ID列表，逗号分隔") @RequestParam List<Long> articleIds) {
        boolean success = articleService.updateAuditStatus(articleIds, AuditStatusEnum.PASS);
        return success ? Result.ok("操作成功") : Result.error("操作失败");
    }


    @Operation(summary = "文章审核-撤回")
    @PostMapping("audit-withdraw")
    @OperateLog(name = "撤回审核", type = OperateTypeEnum.UPDATE, isArticleOperation = true)
    public Result<String> updateStatusWithdraw(
            @Parameter(description = "文章ID列表，逗号分隔") @RequestParam List<Long> articleIds) {
        boolean success = articleService.updateAuditStatus(articleIds, AuditStatusEnum.WAITING_FOR_REVIEW);
        return success ? Result.ok("操作成功") : Result.error("操作失败");
    }


    @Operation(summary = "处置接口")
    @PostMapping("update-disposal-status")
    @OperateLog(name = "处置接口",   type = OperateTypeEnum.UPDATE, isArticleOperation = true)
    public Result<String> updateDisposalStatus(@RequestParam(defaultValue = "") String articleIds,
                                               @RequestParam(defaultValue = "0") int disposalStatus,
                                               @RequestParam(defaultValue = "") String disposalRemark,
                                               @CurrentUser LoginUserVO loginUserVO) {
        return articleService.updateDisposalStatus(articleIds, disposalStatus, disposalRemark, loginUserVO);
    }


    // 根据栏目ID获取新闻列表 分页
    @Operation(summary = "根据栏目ID获取新闻列表 分页")
    @PostMapping("page-by-column-id")
    public Result<PageResult<ArticleVO>> listByColumnId(ChannelArticleQuery query) {
        PageResult<ArticleVO> result = PageResult.page(articleService.pageChannelArticles(query));
        return Result.ok(result);
    }

    //检查新闻是否在首页
    @Operation(summary = "检查新闻是否在首页")
    @PostMapping("check-index")
    public Result<String> checkIndex() {
        List<Website> websites = websiteService.listByStatus(1);
        log.info("查询到站点个数{}", websites.size());
        for (Website website : websites) {
            log.info("查询到站点{}", website.getWebName());
            String webSourceCode = WebSiteGetHtmlUtils.getWebSourceCode(website.getWebUrl());
            //根据站点信息获取文章信息
            List<Article> articles = articleService.listByWebsiteId(website.getId());
            for (Article article : articles) {
                log.info("查询到文章{}", article.getTitle());
                if (webSourceCode.contains(article.getTitle())) {
                    log.info("文章{}在站点{}中", article.getTitle(), website.getWebName());
                    article.setIsIndex(1);
                    articleService.updateById(article);
                } else {
                    log.info("文章{}不在站点{}中", article.getTitle(), website.getWebName());
                }
            }
            log.info("站点{}文章检查完成", website.getWebName());
        }
        return Result.ok("检查完成");
    }




    //查询首页文章
    @Operation(summary = "查询首页文章")
    @PostMapping("list-index")
    public Result<PageResult<Article>> listIndex(IndexArticleQuery query) {
        if (query.getWebsiteId() == null) {
            return Result.error("站点ID不能为空");
        }
        // 设置默认分页参数
        if (query.getPage() == null || query.getPage() < 1) {
            query.setPage(1);
        }
        if (query.getLimit() == null || query.getLimit() < 1) {
            query.setLimit(20);
        }
        PageResult<Article> articlePageResult = articleService.pageIndexArticles(query);
        return Result.ok(articlePageResult);
    }
    //导出首页文章
    @Operation(summary = "导出首页文章")
    @GetMapping("export-index")
    public void exportIndex(IndexArticleQuery query, HttpServletResponse response) {
        try {
            log.info("开始导出首页文章，查询条件: {}", query);
            
            if (query.getWebsiteId() == null) {
                response.sendError(HttpServletResponse.SC_BAD_REQUEST, "站点ID不能为空");
                return;
            }
            
            // 使用EasyExcel导出
            articleService.exportIndexArticlesToExcel(query, response);
            
            log.info("导出首页文章成功");
        } catch (Exception e) {
            log.error("导出首页文章失败", e);
            try {
                response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "导出失败: " + e.getMessage());
            } catch (IOException ioException) {
                log.error("发送错误响应失败", ioException);
            }
        }
    }
}