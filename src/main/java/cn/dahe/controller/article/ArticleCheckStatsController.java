package cn.dahe.controller.article;

import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.Result;
import cn.dahe.model.query.ArticleCheckStatQuery;
import cn.dahe.model.query.ArticleCheckWordStatQuery;
import cn.dahe.service.ArticleCheckStatsService;
import cn.dahe.model.vo.WebsiteArticleCheckStatsVO;
import cn.dahe.model.vo.WebsiteArticleCheckTotalStatsVO;
import cn.dahe.model.vo.WebsiteArticleCheckWordStatsVO;
import cn.dahe.model.vo.WebsiteArticleCheckWordTotalStatsVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 内容检查-统计
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Tag(name = "统计接口")
@RestController
@RequestMapping("/pro/article-check/stats")
public class ArticleCheckStatsController {

    @Resource
    private ArticleCheckStatsService articleCheckStatsService;


    /**
     * @param query 查询条件，包含网站ID列表、发布时间区间等
     * @return 包含总体概况的统计结果
     */
    @Operation(summary = "巡检统计-总体概况")
    @PostMapping("/total")
    public Result<WebsiteArticleCheckTotalStatsVO> queryTotalStats(ArticleCheckStatQuery query) {
        WebsiteArticleCheckTotalStatsVO result = articleCheckStatsService.queryTotalStats(query);
        return Result.ok(result);
    }

    /**
     * @param query 查询条件，包含网站ID列表、发布时间区间等
     * @return 包含分页信息的网站错误统计结果
     */
    @Operation(summary = "巡检统计-网站详情")
    @PostMapping("/page")
    public Result<PageResult<WebsiteArticleCheckStatsVO>> pageStats(ArticleCheckStatQuery query) {
        PageResult<WebsiteArticleCheckStatsVO> result = PageResult.page(articleCheckStatsService.pageStats(query));
        return Result.ok(result);
    }

    @Operation(summary = "巡检统计-网站详情-导出")
    @GetMapping("/export")
    public void exportStats(ArticleCheckStatQuery query) {
        articleCheckStatsService.exportStats(query);
    }

    @Operation(summary = "勘误统计-总体概况")
    @PostMapping("word/total")
    public Result<WebsiteArticleCheckWordTotalStatsVO> queryWordTotalStats(ArticleCheckWordStatQuery query) {
        WebsiteArticleCheckWordTotalStatsVO result = articleCheckStatsService.queryWordTotalStats(query);
        return Result.ok(result);
    }

    @Operation(summary = "勘误统计-内容详情")
    @PostMapping("word/page")
    public Result<PageResult<WebsiteArticleCheckWordStatsVO>> pageWordStats(ArticleCheckWordStatQuery query) {
        PageResult<WebsiteArticleCheckWordStatsVO> result = PageResult.page(articleCheckStatsService.pageWordStats(query));
        return Result.ok(result);
    }

    @Operation(summary = "勘误统计-内容详情-导出")
    @GetMapping("word/export")
    public void exportWordStats(ArticleCheckWordStatQuery query) {
        articleCheckStatsService.exportWordStats(query);
    }


}
