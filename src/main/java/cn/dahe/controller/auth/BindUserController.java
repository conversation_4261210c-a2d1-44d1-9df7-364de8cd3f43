package cn.dahe.controller.auth;

import cn.dahe.common.annotation.XssFilter;
import cn.dahe.model.dto.Result;
import cn.dahe.service.BindUserService;
import cn.dahe.service.WarnUserService;
import cn.dahe.utils.SmsUtil;
import cn.dahe.utils.XssFilterUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 绑定预警人员短信接口
 * <AUTHOR>
 */
@RestController
@RequestMapping("/bind/")
@Tag(name = "预警接收人管理")
@RequiredArgsConstructor
public class BindUserController {

    private final BindUserService bindUserService;

    @Operation(summary = "发送短信验证码")
    @PostMapping("/sms/send")
    public Result update(String phone) {
        // XSS过滤
        phone = XssFilterUtil.filter(phone);
        return bindUserService.sendSmsCode(phone);
    }

    /**
     * 绑定预警人员
     * @param token 加密后的openId
     * @param phone 手机号
     * @param code 验证码
     * @return 返回绑定结果
     */
    @Operation(summary = "绑定预警人员")
    @PostMapping("/wx/usr")
    public Result bind(String token, String userName, String phone, String code) {
        // 批量XSS过滤
        String[] filtered = XssFilterUtil.filterStrings(token, userName, phone, code);
        token = filtered[0];
        userName = filtered[1];
        phone = filtered[2];
        code = filtered[3];
        return bindUserService.bind(token, userName, phone, code);
    }
}


