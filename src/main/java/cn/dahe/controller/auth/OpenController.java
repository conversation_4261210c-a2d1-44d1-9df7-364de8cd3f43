package cn.dahe.controller.auth;

import cn.dahe.common.aop.log.OperateLog;
import cn.dahe.enums.OperateTypeEnum;
import cn.dahe.model.dto.Result;
import cn.dahe.service.WarnUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/open/warn-user")
@Tag(name = "预警接收人管理")
@RequiredArgsConstructor
public class OpenController {

    private final WarnUserService warnUserService;

    @Operation(summary = "取消同步更新")
    @PostMapping("subscribe")
    @OperateLog(name = "更新预警接收人", type = OperateTypeEnum.UPDATE)
    public Result<Boolean> update(String token, String openId, Integer subscribeStatus) {
        if (!"FO+dkr3*tW#3OP5@".equals(token)) {
            return Result.error("检查token失败");
        }
        return warnUserService.updateForSubscribe(openId, subscribeStatus);
    }
}
