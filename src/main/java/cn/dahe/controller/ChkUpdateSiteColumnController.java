package cn.dahe.controller;

import cn.dahe.check.service.CheckService;
import cn.dahe.common.annotation.CurrentUser;
import cn.dahe.entity.Website;
import cn.dahe.enums.ProcessTypeEnum;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.Result;
import cn.dahe.model.query.ChkUpdateSiteColumnQuery;
import cn.dahe.model.request.LingcaiPushDataDto;
import cn.dahe.model.vo.ChkUpdateSiteColumnCountVO;
import cn.dahe.model.vo.ChkUpdateSiteColumnDetailVO;
import cn.dahe.model.vo.ChkUpdateSiteColumnVO;
import cn.dahe.model.vo.LoginUserVO;
import cn.dahe.service.ArticlePushControlService;
import cn.dahe.service.ChannelService;
import cn.dahe.service.ChkUpdateSiteColumnService;
import cn.dahe.service.WebsiteService;
import cn.dahe.task.DataPullScheduleTask;
import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 栏目更新检查Controller - 对应原型图功能
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/pro/chk-update-site-column")
@AllArgsConstructor
@Tag(name = "栏目更新检查管理")
public class ChkUpdateSiteColumnController {

    @Resource
    private ChkUpdateSiteColumnService chkUpdateSiteColumnService;
    @Resource
    private WebsiteService websiteService;
    @Resource
    private ChannelService channelService;
    @Value("${spring.profiles.active}")
    private String env;

    // ==================== 栏目更新检查概览 ====================
    @Operation(summary = "获取栏目更新检查概览统计",
            description = "获取栏目统计（栏目总数、正常个数、采集异常个数、不检测更新个数）和检测结果统计（检测结果总数、正常个数、严重逾期个数、即将逾期个数）")
    @PostMapping("overview-statistics")
    public Result<ChkUpdateSiteColumnCountVO> getOverviewStatistics(
            @Parameter(description = "查询参数") ChkUpdateSiteColumnQuery query,
            @CurrentUser LoginUserVO user) {
        return Result.ok(chkUpdateSiteColumnService.getOverviewStatistics(query));
    }

    // ==================== 栏目更新检查记录 ====================

    @Operation(summary = "分页查询栏目更新检查记录", description = "获取栏目更新检查记录列表")
    @PostMapping("page")
    public Result<PageResult<ChkUpdateSiteColumnVO>> page(
            @Parameter(description = "查询参数") ChkUpdateSiteColumnQuery query,
            @CurrentUser LoginUserVO user) {
        return Result.ok(chkUpdateSiteColumnService.page(query));
    }

    @Operation(summary = "获取栏目更新检查详情", description = "根据ID获取栏目更新检查详细信息")
    @PostMapping("detail")
    public Result<PageResult<ChkUpdateSiteColumnDetailVO>> detail(
            @Parameter(description = "记录ID") @RequestParam Long channelId,
            @Parameter(description = "检测时间") @RequestParam String updateTime,
            @Parameter(description = "页码") @RequestParam int page,
            @Parameter(description = "每页数量") @RequestParam int limit,
            @CurrentUser LoginUserVO user) {
        return Result.ok(chkUpdateSiteColumnService.get(channelId, updateTime, page, limit));
    }

    // ==================== 数据导出 ====================

    @Operation(summary = "导出栏目更新检查记录", description = "导出栏目更新检查记录到Excel")
    @GetMapping("export")
    public void export(
            ChkUpdateSiteColumnQuery query,
            @CurrentUser LoginUserVO user,
            HttpServletResponse response) throws IOException {
        chkUpdateSiteColumnService.export(query, user, response);
    }

    @Resource
    private CheckService checkService;
    @Resource
    private ArticlePushControlService articlePushControlService;

    @Operation(summary = "接收推送栏目更新数据", description = "被动接收采集中心推送的栏目更新检查数据并初始化表数据")
    @PostMapping("receive-push-data")
    public Result<String> receivePushData(LingcaiPushDataDto pushData) {
        if (pushData.getPub_time().before(DateUtil.parse("2025-08-20")))
            return Result.ok("发布时间在8月20日前的不再接收");

        //在推送时判断是否要取消订阅
        Long siteId = pushData.getSite_id();
        Set<Integer> setIds = new HashSet<>();
        setIds.add(siteId.intValue());
        List<Website> websites = websiteService.listBySiteIds(setIds);
        if (websites == null || websites.isEmpty()) {
            List<Long> channelIds = channelService.getWebsiteChannelIds(Lists.newArrayList(siteId));
            log.error("取消订阅site_id: {}", siteId);
            if (channelIds != null && !channelIds.isEmpty()) {
                DataPullScheduleTask.pushLingCai(channelIds,2, env);
                return Result.error("取消订阅site_id：" + siteId);
            }
            return Result.error("取消订阅site_id：" + siteId);
        }

        //  推送数据存档
        articlePushControlService.saveByPushData(pushData);
        try {
            try {
                log.info("----------新闻标题：{}", pushData.getTitle());
                log.info("----------新闻链接：{}", pushData.getUrl());
                log.info("----------采集时间：{}", DateUtil.format(pushData.getCreate_time(), "yyyy-MM-dd HH:mm:ss"));
                log.info("----------发布时间：{}", DateUtil.format(pushData.getPub_time(), "yyyy-MM-dd HH:mm:ss"));
                checkService.savePushData(pushData);
            } catch (Exception e) {
                log.error("保存文章数据失败", e);
            }
            return Result.ok("保存数据成功");
        } catch (Exception e) {
            log.error("接收推送栏目更新检查数据失败", e);
            return Result.error("接收推送数据失败：" + e.getMessage());
        }
    }

}
