package cn.dahe.controller;

import cn.dahe.common.annotation.CurrentUser;
import cn.dahe.common.aop.log.OperateLog;
import cn.dahe.common.aop.role.Logical;
import cn.dahe.common.aop.role.RequiresRoles;
import cn.dahe.common.constants.RoleConstants;
import cn.dahe.entity.ChannelOperationLog;
import cn.dahe.enums.OperateTypeEnum;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.Result;
import cn.dahe.model.query.ChannelOperationLogQuery;
import cn.dahe.model.vo.LoginUserVO;
import cn.dahe.service.ChannelOperationLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 栏目操作日志控制器
 */
@RestController
@RequestMapping("/pro/channel-operation-log")
@AllArgsConstructor
@Tag(name = "栏目操作日志管理")
public class ChannelOperationLogController {
    
    private final ChannelOperationLogService channelOperationLogService;

    /**
     * 分页查询栏目操作日志
     */
    @PostMapping("list")
    @Operation(summary = "分页查询栏目操作日志")
    @RequiresRoles(value = {RoleConstants.ADMIN_SUPER}, logical = Logical.OR)
    public Result<PageResult<ChannelOperationLog>> page(ChannelOperationLogQuery query) {
        PageResult<ChannelOperationLog> page = channelOperationLogService.page(query);
        return Result.ok(page);
    }

    /**
     * 根据ID查询栏目操作日志详情
     */
    @PostMapping("id")
    @Operation(summary = "查询栏目操作日志详情")
    @RequiresRoles(value = {RoleConstants.ADMIN_SUPER}, logical = Logical.OR)
    public Result<ChannelOperationLog> getById(@RequestParam String id) {
        ChannelOperationLog channelOperationLog = channelOperationLogService.getById(id);
        return Result.ok(channelOperationLog);
    }

    /**
     * 新增栏目操作日志
     */
    @PostMapping("save")
    @Operation(summary = "新增栏目操作日志")
    @OperateLog(name = "新增栏目操作日志", type = {OperateTypeEnum.CREATE})
    @RequiresRoles(value = {RoleConstants.ADMIN_SUPER}, logical = Logical.OR)
    public Result<String> save(@RequestBody ChannelOperationLog channelOperationLog, @CurrentUser LoginUserVO user) {
        channelOperationLogService.save(channelOperationLog, user);
        return Result.ok();
    }

    /**
     * 修改栏目操作日志
     */
    @PostMapping("update")
    @Operation(summary = "修改栏目操作日志")
    @OperateLog(name = "修改栏目操作日志", type = {OperateTypeEnum.UPDATE})
    @RequiresRoles(value = {RoleConstants.ADMIN_SUPER}, logical = Logical.OR)
    public Result<String> update(@RequestParam String id, @RequestBody ChannelOperationLog channelOperationLog) {
        channelOperationLogService.update(id, channelOperationLog);
        return Result.ok();
    }

    /**
     * 删除栏目操作日志（逻辑删除）
     */
    @PostMapping("update-status")
    @Operation(summary = "删除栏目操作日志")
    @OperateLog(name = "删除栏目操作日志", type = {OperateTypeEnum.DELETE})
    @RequiresRoles(value = {RoleConstants.ADMIN_SUPER}, logical = Logical.OR)
    public Result<String> updateStatus(@RequestParam String id) {
        channelOperationLogService.updateStatus(id);
        return Result.ok();
    }
}