package cn.dahe.task;

import cn.dahe.check.service.CheckService;
import cn.dahe.check.service.CollectService;
import cn.dahe.common.constants.ScheduledTaskConstants;
import cn.dahe.entity.Article;
import cn.dahe.entity.ArticleContent;
import cn.dahe.service.ArticleContentService;
import cn.dahe.service.ArticleService;
import cn.hutool.core.collection.CollUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 获取附件内容
 *
 * <AUTHOR>
 * @date 2024-03-21
 */
@Slf4j
@Component
public class ArticleCheckInitTask {

    @Resource
    private ArticleService articleService;
    @Resource
    private CheckService checkService;
    @Resource
    private CollectService collectService;
    @Resource
    private ArticleContentService articleContentService;

    @Scheduled(cron = ScheduledTaskConstants.EVERY_5_MINUTES)
    @XxlJob("articleCheckInitTask")
    public void articleCheckInit() {
        List<Article> noContentArticle = articleService.listNoContentArticle();
        if (CollUtil.isNotEmpty(noContentArticle)) {
            log.info("找到{}条无内容文章，通过查询补充", noContentArticle.size());
            List<ArticleContent> articleContents = collectService.collectArticleContents(noContentArticle);
            articleContentService.saveOrUpdateBatch(articleContents);
            log.info("补充文章内容成功，共{}条", articleContents.size());
        }
        List<Article> articles = articleService.listUncheckedArticle();
        if (CollUtil.isEmpty(articles)) {
            return;
        }
        log.info("找到{}条需要分配检查的文章", articles.size());
        for (Article article : articles) {
            try {
                checkService.initCheckTaskForArticle(article);
            } catch (Exception e) {
                log.error("{}-文章分配检查失败", article.getId(), e);
            }
        }
    }
}