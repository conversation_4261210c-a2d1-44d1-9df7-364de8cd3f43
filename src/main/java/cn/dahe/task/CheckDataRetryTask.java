package cn.dahe.task;

import cn.dahe.check.service.CheckService;
import cn.dahe.common.constants.ScheduledTaskConstants;
import cn.dahe.entity.CheckTask;
import cn.dahe.enums.CheckStatusEnum;
import cn.dahe.service.CheckTaskService;
import cn.hutool.core.collection.CollUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 检查任务消息重试定时任务
 * 每10分钟检查一次没有对应article和已完成检查任务的记录，并重新推送消息
 *
 * <AUTHOR>
 * @date 2024-03-21
 */
@Slf4j
@Component
public class CheckDataRetryTask {

    @Resource
    private CheckService checkService;
    @Resource
    private CheckTaskService checkTaskService;

    @Scheduled(cron = ScheduledTaskConstants.EVERY_10_MINUTES)
    @XxlJob("checkDataRetryTask")
    public void checkDataRetryTask() {
        // 查询需要的栏目记录
        List<CheckTask> checkTasks = checkTaskService.listNoResultTaskWithCheckStatus(CheckStatusEnum.FAILED_RETRY);
        if (CollUtil.isEmpty(checkTasks)) {
            return;
        }
        log.info("找到{}条需要检查重试的检查任务", checkTasks.size());
        for (CheckTask checkTask : checkTasks) {
            try {
                checkService.doCheck(checkTask);
            } catch (Exception e) {
                log.error("{}-执行检查重试任务消息失败", checkTask.getId(), e);
            }
        }

    }
}