package cn.dahe.task;

import cn.dahe.common.constants.WarnPushConstants;
import cn.dahe.dao.ChkUpdateSiteColumnResultDao;
import cn.dahe.dao.WarnUserDao;
import cn.dahe.entity.*;
import cn.dahe.model.dto.WarnPushDataDto;
import cn.dahe.model.dto.WarnPushMsgDto;
import cn.dahe.service.*;
import cn.dahe.service.impl.NewMediaService;
import cn.dahe.utils.AESUtils;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class SiteColumnUpdateTask {

    @Autowired
    private ChannelService channelService;

    @Autowired
    private ChkUpdateSiteColumnService chkUpdateSiteColumnService;

    @Resource
    private WarnPushRecordService warnPushRecordService;

    @Resource
    private WarnPlanService warnPlanService;

    @Resource
    private NewMediaService newMediaService;

    @Resource
    private WarnUserDao warnUserDao;


    @Value("${anxun.base-url}")
    private String warnBaseUrl;

    /**
     * 栏目更新统计数据统计任务 - 每天凌晨3点执行
     */
    @Async
    @XxlJob("syncSaveColumnUpdateResultData")
    public void syncSaveColumnUpdateResultData() {
        XxlJobHelper.log("======================>开始执行栏目更新检查结果持久化任务!!!!");
        try {
            List<ChkUpdateSiteColumnResult> updateSiteColumnResults = new ArrayList<>();
            //数据收集
            List<Map<String, Object>> res = chkUpdateSiteColumnService.selectSiteColumnTaskInsertTodayData();

            List<ChkUpdateSiteColumnResult> collect = res.stream().map(result -> {
                ChkUpdateSiteColumnResult chkUpdateSiteColumnResult = new ChkUpdateSiteColumnResult();
                String channelId = result.get("channel_id").toString();
                String channelName = result.get("channel_name").toString();
                String siteId = result.get("site_id").toString();
                String siteName = result.get("site_name").toString();
                String groupId = result.get("group_id") == null ? "0" : result.get("group_id").toString();
                Integer notUpdateDaysNum = Integer.parseInt(result.get("continuous_not_update_days_num").toString());
                String notUpdateDaysNumDesc = result.get("continuous_not_update_days_desc") == null ? "" : result.get("continuous_not_update_days_desc").toString();
                Integer updateDays = Integer.parseInt(result.get("update_days").toString());
                String updateDaysDesc = result.get("update_days_desc") == null ? "" : result.get("update_days_desc").toString();
                LocalDateTime lastUpdateTime = result.get("last_update_time") == null ? null : (LocalDateTime) result.get("last_update_time");
                chkUpdateSiteColumnResult.setColumnId(Long.parseLong(channelId));
                chkUpdateSiteColumnResult.setColumnName(channelName);
                chkUpdateSiteColumnResult.setSiteId(Integer.parseInt(siteId));
                chkUpdateSiteColumnResult.setSiteName(siteName);
                chkUpdateSiteColumnResult.setGroupId(Long.parseLong(groupId));
                chkUpdateSiteColumnResult.setContinuousNotUpdateDays(notUpdateDaysNum);
                chkUpdateSiteColumnResult.setContinuousNotUpdateDaysDetail(notUpdateDaysNumDesc);
                chkUpdateSiteColumnResult.setUpdateDays(updateDays);
                chkUpdateSiteColumnResult.setUpdateDaysDetail(updateDaysDesc);
                chkUpdateSiteColumnResult.setLastUpdateTime(lastUpdateTime);
                chkUpdateSiteColumnResult.setUpdateTime(LocalDateTime.now().toLocalDate().atStartOfDay());
                chkUpdateSiteColumnResult.setProcessType(0);
                log.info("栏目ID: {}, 栏目名称: {}, 连续不更新天数: {}, 更新天数: {}",
                        channelId, channelName, notUpdateDaysNum, updateDays);
                updateSiteColumnResults.add(chkUpdateSiteColumnResult);
                return chkUpdateSiteColumnResult;
            }).collect(Collectors.toList());
            //清理并保存数据
            List<Long> waitDeleteSiteColumnIds = chkUpdateSiteColumnService.getWaitDeleteSiteColumnIds();
            chkUpdateSiteColumnService.delUpdateSiteColumnData(waitDeleteSiteColumnIds, updateSiteColumnResults);
            //开始推送
            if (!collect.isEmpty()) {
                log.info("<<<<<<<<<<<<<<<<<<<<开始推送栏目未更新数据>>>>>>>>>>>>>>>>>>>>");
                pushSiteColumnUpdateData(collect);
            }
        } catch (Exception e) {
            log.error("栏目更新统计数据定时任务执行失败", e);
            throw e;
        }
    }

    private void pushSiteColumnUpdateData(List<ChkUpdateSiteColumnResult> records){
        Map<Integer, List<ChkUpdateSiteColumnResult>> groupedBySiteId = records.stream()
                .collect(Collectors.groupingBy(ChkUpdateSiteColumnResult::getSiteId));
        for (Map.Entry<Integer, List<ChkUpdateSiteColumnResult>> entry : groupedBySiteId.entrySet()) {
            List<ChkUpdateSiteColumnResult> siteRecordsOrigin = entry.getValue();
            List<ChkUpdateSiteColumnResult> siteRecords = siteRecordsOrigin.stream().filter(result -> result.getContinuousNotUpdateDays() > 0).collect(Collectors.toList());
            Integer siteId = entry.getKey();
            // 预警前置处理-预警方案过滤
            List<WarnPlan> warnPlans = warnPlanService.listAll();
            for (WarnPlan warnPlan : warnPlans) {
                //找预警模板,判断是否有该站点的预警
                Integer warnPlanId = warnPlan.getId();
                List<Integer> siteIds = warnPlanService.getSiteIdsByWarnPlanId(warnPlanId);
                if (siteIds.isEmpty() || !siteIds.contains(siteId)) {
                    continue;
                }
                //判断是否开启更新预警通知
                if (!warnPlan.getUpdateWarn()) {
                    continue;
                }
                //准备数据
                WarnPushRecord target = new WarnPushRecord();
                Date checkTime = new Date();
                target.setActualPushTime(checkTime);
                //找预警模板
                target.setWarnPlanId(warnPlan.getId());
                //找预警推送人
                List<WarnPlanPushUser> warnPlanPushUsers = warnPlanService.getPushUserByWarnPlanId(warnPlan.getId());
                if (warnPlanPushUsers != null && !warnPlanPushUsers.isEmpty()) {
                    target.setReceiverName(warnPlanPushUsers.stream().map(WarnPlanPushUser::getUserName).collect(Collectors.joining(",")));
                    target.setReceiverUserId(warnPlanPushUsers.stream().map(WarnPlanPushUser::getUserId).map(String::valueOf).collect(Collectors.joining(",")));
                }
                //设置推送数据
                WarnPushDataDto warnPushDataDto = new WarnPushDataDto();
                warnPushDataDto.setWarnType(WarnPushConstants.PUSH_TYPE_UPDATE);
                warnPushDataDto.setWarnContentType(WarnPushConstants.WARN_CONTENT_TYPE_COLUMN);
                warnPushDataDto.setSiteName(siteRecordsOrigin.get(0).getSiteName());
                warnPushDataDto.setCheckTime(DateUtil.formatDateTime(checkTime));
                WarnPushDataDto.UpdateCheckDto updateCheckDto = new WarnPushDataDto.UpdateCheckDto();
                updateCheckDto.setTotalSitesOrColumns(siteRecordsOrigin.size());
                updateCheckDto.setContinuousNotUpdateSitesOrColumns(siteRecords.size());
                List<WarnPushDataDto.UpdateCheckDetailDto> updateCheckDetailList = new ArrayList<>();
                for (int i = 0; i < siteRecords.size(); i++) {
                    ChkUpdateSiteColumnResult e = siteRecords.get(i);
                    WarnPushDataDto.UpdateCheckDetailDto detail = new WarnPushDataDto.UpdateCheckDetailDto();
                    detail.setNo(i + 1);
                    detail.setName(e.getSiteName());
                    Channel channel = channelService.getById(e.getColumnId());
                    if (channel != null) {
                        detail.setUrl(channel.getUrl());
                    }
                    updateCheckDetailList.add(detail);
                }
                updateCheckDto.setUpdateCheckDetailList(updateCheckDetailList);
                //其他规则
                target.setArticleErrorWarn(warnPlan.getArticleErrorWarn());
                target.setWarnPushType(WarnPushConstants.METHOD_OF_SYSTEM);
                target.setWarnType(WarnPushConstants.PUSH_TYPE_UPDATE);
                warnPushDataDto.setWarnContentType(WarnPushConstants.WARN_CONTENT_TYPE_COLUMN);
                //找推送人
                warnPlanPushUsers.forEach(el->{
                    WarnPushRecord tmp = new WarnPushRecord();
                    BeanUtils.copyProperties(target, tmp);
                    warnPushDataDto.setAccountName(el.getUserName());
                    warnPushDataDto.setUpdateCheckDto(updateCheckDto);
                    tmp.setData(JSON.toJSONString(warnPushDataDto));
                    boolean b = warnPushRecordService.save(tmp);
                    if (b) {
                        tmp.setWarnLink(warnBaseUrl + "/mobile-alert-site-update.html?id=" + AESUtils.genEncPushId(String.valueOf(tmp.getId())));
                        warnPushRecordService.updateById(tmp);
                        if (el.getPushType() == 0) {
                            //找微信用户
                            WarnUser warnUser = warnUserDao.selectById(el.getUserId());
                            if (warnUser == null || StrUtil.isBlank(warnUser.getUserOpenId())) {
                                return;
                            }
                            //微信发送预警
                            WarnPushMsgDto warnPushMsgDto = new WarnPushMsgDto();
                            warnPushMsgDto.setTime(DateUtil.formatDateTime(new Date()));
                            warnPushMsgDto.setType("栏目更新异常-TYPE");
                            warnPushMsgDto.setContent("站点：" + warnPushDataDto.getSiteName() + "，栏目更新异常");
                            warnPushMsgDto.setLink(target.getWarnLink());
                            newMediaService.sendWarn(warnPushMsgDto, warnUser.getUserOpenId());
                        } else {
                            //TODO 手机号推送预警
                        }
                    }
                });
            }

        }
    }

}