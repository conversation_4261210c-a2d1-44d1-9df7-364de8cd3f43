package cn.dahe.task;

import cn.dahe.common.constants.ScheduledTaskConstants;
import cn.dahe.entity.Article;
import cn.dahe.entity.Website;
import cn.dahe.service.ArticleService;
import cn.dahe.service.WebSiteIndexArticleCheckService;
import cn.dahe.service.WebsiteService;
import cn.dahe.utils.caiji.WebSiteGetHtmlUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 临时刷新数据库 监测是否在首页文章检查索引任务
 * <AUTHOR>
 * @date 2023/12/20
 */
@Slf4j
@Component
public class ArticleCheckIndexTask {
    @Resource
    private WebSiteIndexArticleCheckService webSiteIndexArticleCheckService;
    /**
     * 检查索引
     */
    //凌晨1点执行
    @Scheduled(cron = ScheduledTaskConstants.DAILY_1_AM)
    public void checkIndex(){
        //生成所有站点的首页更新检查数据
        Integer i = webSiteIndexArticleCheckService.generateAllSitesIndexCheckData();
        log.info("生成所有站点的首页更新检查数据{}", i);
    }
}