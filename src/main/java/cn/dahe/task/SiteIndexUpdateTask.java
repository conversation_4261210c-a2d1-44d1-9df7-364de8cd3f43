package cn.dahe.task;

import cn.dahe.common.constants.WarnPushConstants;
import cn.dahe.dao.WarnUserDao;
import cn.dahe.entity.*;
import cn.dahe.model.dto.WarnPushDataDto;
import cn.dahe.model.dto.WarnPushMsgDto;
import cn.dahe.service.*;
import cn.dahe.service.impl.NewMediaService;
import cn.dahe.utils.AESUtils;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class SiteIndexUpdateTask {

    @Autowired
    private ChkUpdateSiteIndexService chkUpdateSiteIndexService;

    @Resource
    private WarnPushRecordService warnPushRecordService;

    @Resource
    private WarnPlanService warnPlanService;

    @Resource
    private NewMediaService newMediaService;

    @Resource
    private WarnUserDao warnUserDao;

    @Value("${anxun.base-url}")
    private String warnBaseUrl;


    /**
     * 首页更新统计数据统计任务 - 每天凌晨3点30分执行
     */
    @Async
    @XxlJob("syncSaveIndexUpdateResultData")
    public void syncSaveIndexUpdateResultData() {
        XxlJobHelper.log("======================>开始执行首页更新检查结果持久化任务!!!!");
        try {
            List<ChkUpdateSiteIndexResult> updateSiteIndexResults = new ArrayList<>();
            //数据收集
            List<Map<String, Object>> res = chkUpdateSiteIndexService.selectSiteIndexTaskInsertTodayData();
            List<ChkUpdateSiteIndexResult> collect = res.stream().map(result -> {
                ChkUpdateSiteIndexResult chkUpdateSiteIndexResult = new ChkUpdateSiteIndexResult();
                String siteId = result.get("site_id").toString();
                String siteName = result.get("site_name").toString();
                String siteUrl = result.get("site_url").toString();
                String groupId = result.get("group_id") == null ? "0" : result.get("group_id").toString();
                Integer notUpdateDaysNum = Integer.parseInt(result.get("continuous_not_update_days_num").toString());
                String notUpdateDaysNumDesc = result.get("continuous_not_update_days_desc") == null ? "" : result.get("continuous_not_update_days_desc").toString();
                Integer updateDays = Integer.parseInt(result.get("update_days").toString());
                String updateDaysDesc = result.get("update_days_desc") == null ? "" : result.get("update_days_desc").toString();
                LocalDateTime lastUpdateTime = result.get("last_update_time") == null ? null : (LocalDateTime) result.get("last_update_time");
                chkUpdateSiteIndexResult.setSiteId(Integer.parseInt(siteId));
                chkUpdateSiteIndexResult.setSiteName(siteName);
                chkUpdateSiteIndexResult.setSiteUrl(siteUrl);
                chkUpdateSiteIndexResult.setGroupId(Long.parseLong(groupId));
                chkUpdateSiteIndexResult.setContinuousNotUpdateDaysNum(notUpdateDaysNum);
                chkUpdateSiteIndexResult.setContinuousNotUpdateDaysDesc(notUpdateDaysNumDesc);
                chkUpdateSiteIndexResult.setUpdateDays(updateDays);
                chkUpdateSiteIndexResult.setUpdateDaysDesc(updateDaysDesc);
                chkUpdateSiteIndexResult.setLastUpdateTime(lastUpdateTime);
                chkUpdateSiteIndexResult.setUpdateTime(LocalDateTime.now().toLocalDate().atStartOfDay());
                chkUpdateSiteIndexResult.setProcessType(0);
                log.info("网站ID: {}, 站点: {}, 连续不更新天数: {}, 更新天数: {}",
                        siteId, siteName, notUpdateDaysNum, updateDays);
                updateSiteIndexResults.add(chkUpdateSiteIndexResult);
                return chkUpdateSiteIndexResult;
            }).collect(Collectors.toList());
            //清理并保存数据
            List<Long> waitDeleteSiteIndexIds = chkUpdateSiteIndexService.getWaitDeleteSiteIndexIds();
            chkUpdateSiteIndexService.delUpdateSiteIndexData(waitDeleteSiteIndexIds,updateSiteIndexResults);
            //开始推送
            if (!collect.isEmpty()) {
                log.info("<<<<<<<<<<<<<<<<<<<<开始推送首页未更新数据>>>>>>>>>>>>>>>>>>>>");
                pushSiteIndexUpdateData(collect.size(), collect);
            }
        } catch (Exception e) {
            log.error("首页更新统计数据定时任务执行失败", e);
            throw e;
        }
    }

    private void pushSiteIndexUpdateData(int total, List<ChkUpdateSiteIndexResult> records){
        records = records.stream().filter(e -> e.getContinuousNotUpdateDaysNum() > 0).collect(Collectors.toList());
        for (int i = 0; i < records.size(); i++) {
            // 预警前置处理-推送连通性数据
            ChkUpdateSiteIndexResult e = records.get(i);
            List<WarnPlan> warnPlans = warnPlanService.listAll();
            for (WarnPlan warnPlan : warnPlans) {
                List<Integer> siteIds = warnPlanService.getSiteIdsByWarnPlanId(warnPlan.getId());
                //判断是否有该站点的预警
                if (siteIds.isEmpty() || !siteIds.contains(e.getSiteId())) {
                    continue;
                }
                //判断是否开启首页更新预警，且超出阀值，然后继续下面的逻辑
                int limitDays = warnPlan.getWechatUnupdateDays() == null ? 0 : warnPlan.getWechatUnupdateDays();
                if (warnPlan.getUpdateWarn() == true && e.getContinuousNotUpdateDaysNum() > limitDays) {
                    Date checkTime = new Date();
                    WarnPushRecord target = new WarnPushRecord();
                    target.setActualPushTime(checkTime);
                    //找预警模板
                    target.setWarnPlanId(warnPlan.getId());
                    //找预警推送人
                    List<WarnPlanPushUser> warnPlanPushUsers = warnPlanService.getPushUserByWarnPlanId(warnPlan.getId());
                    if (warnPlanPushUsers != null && !warnPlanPushUsers.isEmpty()) {
                        target.setReceiverName(warnPlanPushUsers.stream().map(WarnPlanPushUser::getUserName).collect(Collectors.joining(",")));
                        target.setReceiverUserId(warnPlanPushUsers.stream().map(WarnPlanPushUser::getUserId).map(String::valueOf).collect(Collectors.joining(",")));
                    }
                    //其他规则
                    target.setArticleErrorWarn(warnPlan.getArticleErrorWarn());
                    //这里是系统预警
                    target.setWarnPushType(WarnPushConstants.METHOD_OF_SYSTEM);
                    target.setWarnType(WarnPushConstants.PUSH_TYPE_UPDATE);
                    //设置推送数据
                    WarnPushDataDto warnPushDataDto = new WarnPushDataDto();
                    warnPushDataDto.setWarnType(WarnPushConstants.PUSH_TYPE_UPDATE);
                    warnPushDataDto.setWarnContentType(WarnPushConstants.WARN_CONTENT_TYPE_INDEX);
                    warnPushDataDto.setSiteName(e.getSiteName());
                    warnPushDataDto.setCheckTime(DateUtil.formatDateTime(checkTime));
                    WarnPushDataDto.UpdateCheckDto updateCheckDto = new WarnPushDataDto.UpdateCheckDto();
                    updateCheckDto.setTotalSitesOrColumns(total);
                    updateCheckDto.setContinuousNotUpdateSitesOrColumns(records.size());
                    //设置预警详情
                    List<WarnPushDataDto.UpdateCheckDetailDto> updateCheckDetailList = new ArrayList<>();
                    WarnPushDataDto.UpdateCheckDetailDto detail = new WarnPushDataDto.UpdateCheckDetailDto();
                    detail.setNo(1);
                    detail.setName(e.getSiteName());
                    detail.setUrl(e.getSiteUrl());
                    updateCheckDetailList.add(detail);
                    updateCheckDto.setUpdateCheckDetailList(updateCheckDetailList);
                    //其他设置
                    warnPushDataDto.setWarnContentType(WarnPushConstants.WARN_CONTENT_TYPE_INDEX);
                    //推送
                    warnPlanPushUsers.forEach(el->{
                        WarnPushRecord tmp = new WarnPushRecord();
                        BeanUtils.copyProperties(target, tmp);
                        //保存生成id
                        warnPushDataDto.setUpdateCheckDto(updateCheckDto);
                        warnPushDataDto.setAccountName(el.getUserName());
                        tmp.setData(JSON.toJSONString(warnPushDataDto));
                        boolean b = warnPushRecordService.save(tmp);
                        if (b) {
                            tmp.setWarnLink(warnBaseUrl + "/mobile-alert-site-update.html?id=" + AESUtils.genEncPushId(String.valueOf(tmp.getId())));
                            warnPushRecordService.updateById(tmp);
                            //推送
                            if (el.getPushType() == 0) {
                                //找微信用户
                                WarnUser warnUser = warnUserDao.selectById(el.getUserId());
                                if (warnUser == null || StrUtil.isBlank(warnUser.getUserOpenId())) {
                                    return;
                                }
                                //微信发送预警
                                WarnPushMsgDto warnPushMsgDto = new WarnPushMsgDto();
                                warnPushMsgDto.setTime(DateUtil.formatDateTime(new Date()));
                                warnPushMsgDto.setType("首页更新异常-TYPE");
                                warnPushMsgDto.setContent("站点：" + e.getSiteName() + "，首页更新异常");
                                warnPushMsgDto.setLink(tmp.getWarnLink());
                                newMediaService.sendWarn(warnPushMsgDto, warnUser.getUserOpenId());
                            } else {
                                //TODO 手机号推送预警
                            }
                        }
                    });
                }
            }
        }
    }

}