package cn.dahe.task;

import cn.dahe.check.service.CheckService;
import cn.dahe.common.constants.ScheduledTaskConstants;
import cn.dahe.entity.CheckTask;
import cn.dahe.enums.CheckStatusEnum;
import cn.dahe.service.CheckTaskService;
import cn.hutool.core.collection.CollUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 检查任务消息重试定时任务
 * 每5分钟检查一次没有对应article和已完成检查任务的记录，并重新推送消息
 *
 * <AUTHOR>
 * @date 2024-03-21
 */
@Slf4j
@Component
public class CheckDataTask {

    @Resource
    private CheckService checkService;
    @Resource
    private CheckTaskService checkTaskService;
    @Resource
    private CheckWordUpdateTask checkWordUpdateTask;

    @Scheduled(cron = ScheduledTaskConstants.EVERY_5_MINUTES)
    @XxlJob("checkDataTask")
    public void checkDataTask() {
        List<CheckTask> checkTasks = checkTaskService.listNoResultTaskWithCheckStatus(CheckStatusEnum.UNCHECKED);
        if (CollUtil.isEmpty(checkTasks)) {
            return;
        }
        log.info("找到{}条需要检查的检查任务", checkTasks.size());
        for (CheckTask checkTask : checkTasks) {
            try {
                checkService.doCheck(checkTask);
            } catch (Exception e) {
                log.error("{}-执行检查任务消息失败", checkTask.getId(), e);
            }
        }
        checkWordUpdateTask.checkWordUpdateTask();
    }
}