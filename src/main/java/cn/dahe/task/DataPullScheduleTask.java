package cn.dahe.task;

import cn.dahe.common.constants.ScheduledTaskConstants;
import cn.dahe.common.constants.WarnPushConstants;
import cn.dahe.dao.WarnUserDao;
import cn.dahe.entity.*;
import cn.dahe.model.dto.WarnPushDataDto;
import cn.dahe.model.dto.WarnPushMsgDto;
import cn.dahe.service.*;
import cn.dahe.service.impl.NewMediaService;
import cn.dahe.utils.AESUtils;
import cn.dahe.utils.StringUtils;
import cn.dahe.utils.fan.FanCollectUtil;
import cn.dahe.utils.fan.SiteQueryResult;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.security.MessageDigest;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class DataPullScheduleTask {

    @Resource
    private WebsiteService websiteService;

    @Resource
    private ChannelService channelService;

    @Resource
    private WebsiteAccessRecordService websiteAccessRecordService;

    @Resource
    private WebsiteDeadLinkCheckRecordService websiteDeadLinkCheckRecordService;

    @Resource
    private WebsiteOutLinkCheckRecordService websiteOutLinkCheckRecordService;

    @Resource
    private ChkUpdateSiteIndexService chkUpdateSiteIndexService;

    @Resource
    private WarnPushRecordService warnPushRecordService;

    @Resource
    private WarnPlanService warnPlanService;

    @Resource
    private NewMediaService newMediaService;

    @Resource
    private WarnUserDao warnUserDao;

    @Value("${anxun.base-url}")
    private String warnBaseUrl;

    /**
     * 同步异常链接数据
     * 每1分钟执行一次
     */
    @XxlJob("syncLinkCheckData")
    public void syncLinkCheckData() {
        XxlJobHelper.log("======================>开始执行异常链接数据同步定时任务");

        try {
            // 获取所有启用的网站
            List<Website> websites = websiteService.listByStatus(1);
            if (websites.isEmpty()) {
                log.info("没有找到启用的网站，跳过同步");
                return;
            }

            // 提取网站URL和ID
            List<String> websiteUrls = websites.stream()
                                               .filter(el-> el.getProcessType() == 0)
                                               .map(Website::getWebUrl)
                                               .filter(Objects::nonNull)
                                               .collect(Collectors.toList());

            List<Integer> siteIds = websites.stream()
                                            .map(Website::getSiteId)
                                            .filter(Objects::nonNull)
                                            .collect(Collectors.toList());

            // 设置时间范围（最近7天）
            LocalDateTime endTime = LocalDateTime.now();
            LocalDateTime startTime = endTime.minusDays(7);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'");

            // Link1: 连通性检查数据同步
            syncConnectivityData(websiteUrls, websites);

            // Link2: 死链数据同步
            syncDeadLinksData(siteIds, websites, startTime.format(formatter), endTime.format(formatter));

            // Link3: 外链数据同步
            syncExternalLinksData(siteIds, websites, startTime.format(formatter), endTime.format(formatter));

            log.info("异常链接数据同步定时任务执行完成");

        } catch (Exception e) {
            log.error("异常链接数据同步定时任务执行失败", e);
        }
    }

    /**
     * 同步连通性检查数据到t_website_access_record
     */
    private void syncConnectivityData(List<String> websiteUrls, List<Website> websites) {
        try {
            log.info("Link1:开始连通性数据同步定时任务,同步数据到t_website_access_record");

            if (websiteUrls.isEmpty()) {
                log.info("没有网站URL需要检查连通性");
                return;
            }

            // 调用采集中心API检查连通性
            JSONObject result = FanCollectUtil.checkConnectivity(websiteUrls);

            if (!result.getBoolean("success")) {
                log.error("连通性检查API调用失败: {}", result.getString("message"));
                return;
            }

            JSONArray dataArray = result.getJSONArray("data");
            if (dataArray == null || dataArray.isEmpty()) {
                log.info("连通性检查返回数据为空");
                return;
            }

            int savedCount = 0;
            Date currentTime = new Date();

            // 创建URL到Website的映射
            Map<String, Website> urlToWebsiteMap = websites.stream()
                                                           .filter(w -> w.getWebUrl() != null)
                                                           .collect(Collectors.toMap(Website::getWebUrl, w -> w, (existing, replacement) -> existing));

            //01-数据分组处理
            Map<Integer, String> siteData = new HashMap<>();
            Map<Integer, List<WebsiteAccessRecord>> siteGroupData = new HashMap<>();
            for (int i = 0; i < dataArray.size(); i++) {
                JSONObject connectivityData = dataArray.getJSONObject(i);
                String url = connectivityData.getString("url");
                Website website = urlToWebsiteMap.get(url);

                if (website == null) {
                    log.warn("未找到URL对应的网站: {}", url);
                    continue;
                }

                // 生成唯一签名用于去重
                String signData = String.format("%d_%s_%s",
                                                website.getSiteId(),
                                                url,
                                                currentTime.toString().substring(0, 10)); // 按天去重
                String sign = generateSign(signData);

                // 检查是否已存在相同签名的记录
                QueryWrapper<WebsiteAccessRecord> checkWrapper = new QueryWrapper<>();
                checkWrapper.eq("sign", sign);

//                if (websiteAccessRecordService.count(checkWrapper) > 0) {
//                    log.debug("跳过重复的连通性检查记录: webId={}, url={}", website.getSiteId(), url);
//                    continue;
//                }

                WebsiteAccessRecord record = new WebsiteAccessRecord();
                record.setWebId(website.getSiteId());
                record.setWebName(website.getWebName());
                record.setWebUrl(url);
                record.setProcessType(website.getProcessType());
                record.setLinkUrl(url);

                // 设置访问结果
                Integer status = connectivityData.getInteger("status");
                if (status != null) {
                    record.setHttpCode(status);
                    record.setSuccess(status == 200 ? 1 : 0);
                } else {
                    record.setHttpCode(0);
                    record.setSuccess(0);
                }

                // 设置访问耗时
                Integer latencyMs = connectivityData.getInteger("latency_ms");
                if (latencyMs != null) {
                    record.setAccessTimeConsuming(latencyMs.doubleValue());
                }

                record.setType(0); // 内链
                record.setContentType("text/html");
                record.setCheckTime(currentTime);
                record.setCreateTime(currentTime);
                record.setSign(sign);

                // 保存记录
                websiteAccessRecordService.save(record);
                savedCount++;
                if (status == 200) {
                    continue;
                }
                // 02-预警前置处理-保存分组数据
                siteData.put(website.getSiteId(), website.getWebName());
                List<WebsiteAccessRecord> records = siteGroupData.get(website.getSiteId());
                if (records == null) {
                    records = new ArrayList<>();
                    records.add(record);
                    siteGroupData.put(website.getSiteId(), records);
                } else {
                    records.add(record);
                    siteGroupData.put(website.getSiteId(), records);
                }
            }
            //03-开始推送
            log.info("<<<<<<<<<<<<<<<<<<<<开始推送连通性数据>>>>>>>>>>>>>>>>>>>>");
            pushConnect1(siteData, siteGroupData);
            log.info("连通性数据同步完成，共保存{}条记录", savedCount);
        } catch (Exception e) {
            log.error("连通性数据同步失败", e);
        }
    }

    /**
     * 同步死链数据到t_website_dead_link_check_record
     */
    private void syncDeadLinksData(List<Integer> siteIds, List<Website> websites, String startTime, String endTime) {
        try {
            log.info("Link2:开始死链数据同步定时任务，同步数据到t_website_dead_link_check_record");

            if (siteIds.isEmpty()) {
                log.info("没有站点ID需要检查死链");
                return;
            }

            // 调用采集中心API获取死链数据
            JSONObject result = FanCollectUtil.getDeadLinks(siteIds, 1, 100, startTime, endTime);

            if (!result.getBoolean("success")) {
                log.error("死链检查API调用失败: {}", result.getString("message"));
                return;
            }

            JSONObject data = result.getJSONObject("data");
            if (data == null) {
                log.info("死链检查返回数据为空");
                return;
            }

            JSONArray brokenLinks = data.getJSONArray("broken_links");
            if (brokenLinks == null || brokenLinks.isEmpty()) {
                log.info("没有发现死链");
                return;
            }

            int savedCount = 0;
            Date currentTime = new Date();

            // 创建站点ID到Website的映射
            Map<Integer, Website> siteIdToWebsiteMap = websites.stream()
                                                               .filter(w -> w.getId() != null)
                                                               .collect(Collectors.toMap(Website::getSiteId, w -> w, (existing, replacement) -> existing));

            //01-数据分组处理
            Map<Integer, String> siteData = new HashMap<>();
            Map<Integer, List<WebsiteDeadLinkCheckRecord>> siteGroupData = new HashMap<>();
            for (int i = 0; i < brokenLinks.size(); i++) {
                JSONObject linkData = brokenLinks.getJSONObject(i);
                Integer siteId = linkData.getInteger("site_id");
                Website website = siteIdToWebsiteMap.get(siteId);

                if (website == null) {
                    log.warn("未找到站点ID对应的网站: {}", siteId);
                    continue;
                }

                String linkUrl = linkData.getString("link_url");
                String parentUrl = linkData.getString("parent_url");
                String parentPubTime = linkData.getString("parent_pub_time");
                String parentWebCode = linkData.getString("parent_web_code");
                String collectTime = linkData.getString("collect_time");

                // 生成唯一签名用于去重
                String signData = String.format("%d_%s_%s_%d",
                                                website.getSiteId(),
                                                linkUrl,
                                                parentUrl,
                                                linkData.getIntValue("status_code"));
                String sign = generateSign(signData);

                // 检查是否已存在相同签名的记录
                QueryWrapper<WebsiteDeadLinkCheckRecord> checkWrapper = new QueryWrapper<>();
                checkWrapper.eq("sign", sign);

                if (websiteDeadLinkCheckRecordService.count(checkWrapper) > 0) {
                    log.debug("跳过重复的死链检查记录: webId={}, linkUrl={}", website.getSiteId(), linkUrl);
                    continue;
                }

                WebsiteDeadLinkCheckRecord record = new WebsiteDeadLinkCheckRecord();
                record.setWebId(website.getSiteId());
                record.setWebName(website.getWebName());
                record.setWebUrl(website.getWebUrl());
                record.setProcessType(website.getProcessType());
                record.setLinkUrl(linkUrl);
                record.setSourcePage(parentUrl);
                record.setSnapshot(parentWebCode);
                record.setHttpCode(linkData.getIntValue("status_code"));
                record.setHttpCodeDesc(linkData.getIntValue("status_code")+"");
                record.setPubTime(parentPubTime != null ? DateUtil.parseDateTime(parentPubTime) : null);

                // 根据content_type设置类型
                String contentType = linkData.getString("content_type");
                if ("image".equals(contentType)) {
                    record.setType(1); // 图片
                } else if ("application".equals(contentType)) {
                    record.setType(2); // 附件
                } else {
                    record.setType(0); // 其他
                }

                record.setLatest(1);
                record.setCheckTime(collectTime != null ? DateUtil.parseDateTime(collectTime) : null);
                record.setCreateTime(currentTime);
                record.setSign(sign);

                System.out.println("死链采集信息：" + JSONUtil.toJsonPrettyStr(record));

                // 保存记录
                websiteDeadLinkCheckRecordService.save(record);
                savedCount++;
                // 02-预警前置处理-保存分组数据
                siteData.put(website.getSiteId(), website.getWebName());
                List<WebsiteDeadLinkCheckRecord> records = siteGroupData.get(website.getSiteId());
                if (records == null) {
                    records = new ArrayList<>();
                    records.add(record);
                    siteGroupData.put(website.getSiteId(), records);
                } else {
                    records.add(record);
                    siteGroupData.put(website.getSiteId(), records);
                }
            }
            //03-开始推送
            log.info("<<<<<<<<<<<<<<<<<<<<开始推送死链数据>>>>>>>>>>>>>>>>>>>>");
            pushConnect2(siteData, siteGroupData);
            log.info("死链数据同步完成，共保存{}条记录", savedCount);

        } catch (Exception e) {
            log.error("死链数据同步失败", e);
        }
    }

    /**
     * 同步外链数据到t_website_out_link_check_record
     */
    private void syncExternalLinksData(List<Integer> siteIds, List<Website> websites, String startTime, String endTime) {
        try {
            log.info("Link3:开始外链数据同步定时任务，同步数据到t_website_out_link_check_record");

            if (siteIds.isEmpty()) {
                log.info("没有站点ID需要检查外链");
                return;
            }

            // 调用采集中心API获取外链数据
            JSONObject result = FanCollectUtil.getExternalLinks(siteIds, 1, 100, startTime, endTime);

            if (!result.getBoolean("success")) {
                log.error("外链检查API调用失败: {}", result.getString("message"));
                return;
            }

            JSONObject data = result.getJSONObject("data");
            if (data == null) {
                log.info("外链检查返回数据为空");
                return;
            }

            JSONArray externalLinks = data.getJSONArray("external_links");
            if (externalLinks == null || externalLinks.isEmpty()) {
                log.info("没有发现异常外链");
                return;
            }

            int savedCount = 0;
            Date currentTime = new Date();

            // 创建站点ID到Website的映射
            Map<Integer, Website> siteIdToWebsiteMap = websites.stream()
                                                               .filter(w -> w.getId() != null)
                                                               .collect(Collectors.toMap(Website::getSiteId, w -> w, (existing, replacement) -> existing));
            //01-数据分组处理
            Map<Integer, String> siteData = new HashMap<>();
            Map<Integer, List<WebsiteOutLinkCheckRecord>> siteGroupData = new HashMap<>();
            for (int i = 0; i < externalLinks.size(); i++) {
                JSONObject linkData = externalLinks.getJSONObject(i);
                Integer siteId = linkData.getInteger("site_id");
                Website website = siteIdToWebsiteMap.get(siteId);

                if (website == null) {
                    log.warn("未找到站点ID对应的网站: {}", siteId);
                    continue;
                }

                String linkUrl = linkData.getString("link_url");
                String sourcePage = linkData.getString("parent_url");
                String linkType = linkData.getString("link_type");

                // 生成唯一签名用于去重
                String signData = String.format("%d_%s_%s_%s",
                                                website.getSiteId(),
                                                linkUrl,
                                                sourcePage,
                                                linkType != null ? linkType : "normal");
                String sign = generateSign(signData);

                // 检查是否已存在相同签名的记录
                QueryWrapper<WebsiteOutLinkCheckRecord> checkWrapper = new QueryWrapper<>();
                checkWrapper.eq("sign", sign);

                if (websiteOutLinkCheckRecordService.count(checkWrapper) > 0) {
                    log.debug("跳过重复的外链检查记录: webId={}, linkUrl={}", website.getSiteId(), linkUrl);
                    continue;
                }

                WebsiteOutLinkCheckRecord record = new WebsiteOutLinkCheckRecord();
                record.setWebId(website.getSiteId());
                record.setWebName(website.getWebName());
                record.setWebUrl(website.getWebUrl());
                record.setProcessType(website.getProcessType());
                record.setLinkUrl(linkUrl);
                record.setSourcePage(sourcePage);
                record.setSnapshot(linkData.getString("web_code"));
                record.setSign(sign);

                // 根据link_type设置类型
                if ("normal".equals(linkType)) {
                    record.setType(0); // 正常
                } else if ("file".equals(linkType)) {
                    record.setType(1); // 附件
                } else {
                    record.setType(0); // 默认正常
                }

                record.setFilter(0); // 默认未过滤
                record.setCheckTime(currentTime);
                record.setCreateTime(currentTime);

                // 保存记录
                websiteOutLinkCheckRecordService.save(record);
                savedCount++;
                // 02-预警前置处理-保存分组数据
                siteData.put(website.getSiteId(), website.getWebName());
                List<WebsiteOutLinkCheckRecord> records = siteGroupData.get(website.getSiteId());
                if (records == null) {
                    records = new ArrayList<>();
                    records.add(record);
                    siteGroupData.put(website.getSiteId(), records);
                } else {
                    records.add(record);
                    siteGroupData.put(website.getSiteId(), records);
                }
            }
            //03-开始推送
            log.info("<<<<<<<<<<<<<<<<<<<<开始推送外链数据>>>>>>>>>>>>>>>>>>>>");
            pushConnect3(siteData, siteGroupData);
            log.info("外链数据同步完成，共保存{}条记录", savedCount);
        } catch (Exception e) {
            log.error("外链数据同步失败", e);
        }
    }

    /**
     * 同步站点栏目数据
     * 每30分钟执行一次
     * 改造：由于新媒体不再作为栏目，而是与网站并存在website表，此处不再同步新媒体，而是在用户页面搜索添加时单独处理新增、订阅
     * 注：虽然新媒体没有栏目的概念，但也会同步复制到channel表，保持从website到channel到article结构统一
     */
    @Async
    @XxlJob("syncSiteColumnData")
    public void syncSiteColumnData() {
        XxlJobHelper.log("======================>开始执行站点栏目数据同步定时任务");
        try {
            // 获取所有启用的网站
            List<Website> websites = websiteService.listByStatusAndProcessTypeIn(1, Collections.singletonList(0));
            if (websites.isEmpty()) {
                return;
            }
            int processedCount = 0;
            int updatedCount = 0;
            int deletedCount = 0;
            //  TODO 检查后批量改 ？

            List<Integer> columnIdsAll = new ArrayList<>();
            for (Website website : websites) {
                String websiteName = website.getWebName();
                int websiteId = website.getSiteId();
                int id = website.getId().intValue();
                try {
                    // 使用FanCollectUtil.searchSitesByName根据name查询站点
                    JSONArray siteInfo = FanCollectUtil.searchSitesByName(websiteName);
                    if (siteInfo != null) {
                        Integer remoteWebsiteId  = siteInfo.getInteger(0);
                        String remoteWebsiteUrl  = siteInfo.getString(1);
                        if (remoteWebsiteId != websiteId || website.getWebUrl() ==  null || !remoteWebsiteUrl.equals(website.getWebUrl())) {
                            // 使用新的updateId方法更新网站ID
                            websiteService.updateId(id, remoteWebsiteId, remoteWebsiteUrl);
                            // 更新本地website对象的ID值，以便后续使用
                            website.setId((long) remoteWebsiteId);
                            updatedCount++;
                            log.info("更新网站{}的远程站点ID: {}", websiteName, remoteWebsiteId);
                        }
                        // 同步栏目数据
                        List<Integer> columnIds = syncChannelsForSite(website, remoteWebsiteId);
                        if (!columnIds.isEmpty()) {
                            columnIdsAll.addAll(columnIds);
                        }
                    } else {
                        // 没有找到匹配的站点，删除本地记录
                        websiteService.removeById(id);
                        deletedCount++;
                        log.info("删除未找到远程匹配的网站: {}", websiteName);
                    }
                    processedCount++;
                } catch (Exception e) {
                    log.error("处理网站{}时发生错误", websiteName, e);
                }
            }

            log.info("站点栏目数据同步定时任务执行完成，处理{}个网站，更新{}个，删除{}个",
                     processedCount, updatedCount, deletedCount);
            log.info("==================开始灵采推送订阅栏目id集合=======================");
            String s = pushLingCai(columnIdsAll);
            log.info("==================结束灵采推送订阅栏目id集合结果:{}", s);
        } catch (Exception e) {
            log.error("站点栏目数据同步定时任务执行失败", e);
        }
    }

    /**
     * 为指定站点同步栏目数据
     *
     * @param website      网站信息
     * @param remoteSiteId 远程站点ID
     */
    private List<Integer> syncChannelsForSite(Website website, Integer remoteSiteId) {
        List<Integer> columnIds = new ArrayList<>();
//        JSONObject response = FanCollectUtil.getNewMedia(website.getWebName());
//        if (response.getBoolean("success")) for (Object data : response.getJSONArray("data")) {
//            JSONObject json = (JSONObject) data;
//            int id = json.getIntValue("id");
//            if (channelService.getById(id) != null) continue;
//            columnIds.add(id);
//            channelService.save(new Channel().setId(id)
//                                             .setName(json.getString("name"))
//                                             .setUrl(json.getString("url"))
//                                             .setSiteId(website.getSiteId())
//                                             .setSiteName(website.getWebName())
//                                             .setEnable(1)
//                                             .setAsync(1)
//                                             .setProcessType(json.getInteger("site_id"))
//                                             .setSchedulerInterval(60)
//                                             .setCreateTime(new Date())
//                                             .setLastModifyTime(new Date()));
//        }
        try {
            // 调用FanCollectUtil.getSiteColumns获取远程栏目数据
            JSONObject columnsResult = FanCollectUtil.getSiteColumns(remoteSiteId);

            if (!columnsResult.getBoolean("success")) {
                log.warn("获取站点{}的栏目数据失败: {}", website.getWebName(), columnsResult.getString("message"));
                return columnIds;
            }

            JSONObject data = columnsResult.getJSONObject("data");
            if (data == null) {
                log.warn("站点{}的栏目数据为空", website.getWebName());
                return columnIds;
            }

            JSONArray columns = data.getJSONArray("columns");
            if (columns == null || columns.isEmpty()) {
                log.info("站点{}没有栏目数据", website.getWebName());
                return columnIds;
            }

            int addedCount = 0;
            int updatedCount = 0;

            for (int i = 0; i < columns.size(); i++) {
                JSONObject columnData = columns.getJSONObject(i);
                Integer remoteColumnId = columnData.getInteger("column_id");
                //TODO 向精准采集推送订阅栏目id集合
                columnIds.add(remoteColumnId);
                String columnName = columnData.getString("name");
                String columnUrl = columnData.getString("url");

                if (remoteColumnId == null || StringUtils.isBlank(columnUrl)) {
                    log.warn("栏目数据不完整，跳过: {}", columnData.toJSONString());
                    continue;
                }

                // 根据sjzChannelId查找本地栏目
                QueryWrapper<Channel> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("id", remoteColumnId);
                Channel existingChannel = channelService.getOne(queryWrapper);

                if (existingChannel != null) {
                    // 更新现有栏目
                    boolean needUpdate = false;

                    if (!Objects.equals(existingChannel.getName(), columnName)) {
                        existingChannel.setName(columnName);
                        needUpdate = true;
                    }

                    if (!Objects.equals(existingChannel.getUrl(), columnUrl)) {
                        existingChannel.setUrl(columnUrl);
                        needUpdate = true;
                    }

                    if (!Objects.equals(existingChannel.getSiteId(), website.getSiteId())) {
                        existingChannel.setSiteId(website.getSiteId());
                        existingChannel.setSiteName(website.getWebName());
                        needUpdate = true;
                    }

                    //更新栏目类型
                    if (website.getProcessType() != null && existingChannel.getProcessType() != null&& website.getProcessType().intValue() != existingChannel.getProcessType().intValue()) {
                        existingChannel.setProcessType(website.getProcessType());
                        needUpdate = true;
                    }

                    if (needUpdate) {
                        existingChannel.setLastModifyTime(new Date());
                        channelService.updateById(existingChannel);
                        updatedCount++;
                        log.debug("更新栏目: {} - {}", columnName, columnUrl);
                    }

                } else {
                    // 新增栏目
                    Channel newChannel = new Channel();
                    newChannel.setId(remoteColumnId);
                    newChannel.setName(columnName);
                    newChannel.setUrl(columnUrl);
                    newChannel.setSiteId(website.getSiteId());
                    newChannel.setSiteName(website.getWebName());
                    //设置栏目类型
                    newChannel.setProcessType(website.getProcessType()); // 默认网站类型
                    newChannel.setCreateTime(new Date());
                    channelService.saveById(newChannel);
                    addedCount++;
                    log.debug("新增栏目: {} - {}", columnName, columnUrl);
                }
            }
            if (addedCount > 0 || updatedCount > 0) {
                log.info("站点{}栏目同步完成，新增{}个，更新{}个", website.getWebName(), addedCount, updatedCount);
            }
        } catch (Exception e) {
            log.error("同步站点{}栏目数据时发生错误", website.getWebName(), e);
        }
        return columnIds;
    }

    public static String pushLingCai(List<Integer> ids) {
        // 请求URL
        String url = "https://collection-api.dahe.cn/custom/anxun/column_sub/";

        // 创建表单参数Map
        //operation_type（1 订阅 2 删除订阅）
        //  plan_id(方案id: 49 测试线   50 正式线)
        //  column_id
        Map<String, Object> params = new HashMap<>();
        params.put("operation_type", 1);
        params.put("plan_id", 49);
        params.put("column_id", StrUtil.join(",", ids)); // 这里是空字符串，根据实际需求填写

        // 发送POST请求
        HttpResponse response = HttpUtil.createPost(url)
                                        .header("accept", "application/json")
                                        .form(params) // 设置表单参数，会自动设置Content-Type为application/x-www-form-urlencoded
                                        .execute();

        // 处理响应
        if (response.isOk()) {
            String result = response.body();
            return result;
        } else {
            log.error("请求灵采失败，状态码：" + response.getStatus());
        }
        return "";
    }

    public static String pushLingCai(List<Long> ids, int subStatus, String env) {
        // 请求URL
        String url = "https://collection-api.dahe.cn/custom/anxun/column_sub/";

        // 创建表单参数Map
        //operation_type（1 订阅 2 删除订阅）
        //  plan_id(方案id: 49 测试线   50 正式线)
        //  column_id
        Map<String, Object> params = new HashMap<>();
        params.put("operation_type", subStatus == 1 ? 1 : 2);
        params.put("plan_id", env.equals("dev") ? 49 : 50);
        params.put("column_id", StrUtil.join(",", ids)); // 这里是空字符串，根据实际需求填写

        // 发送POST请求
        HttpResponse response = HttpUtil.createPost(url)
                .header("accept", "application/json")
                .form(params) // 设置表单参数，会自动设置Content-Type为application/x-www-form-urlencoded
                .execute();

        // 处理响应
        if (response.isOk()) {
            String result = response.body();
            return result;
        } else {
            log.error("请求灵采失败，状态码：" + response.getStatus());
        }
        return "";
    }

    /**
     * 生成数据唯一签名用于去重
     *
     * @param data 原始数据字符串
     * @return MD5签名
     */
    private String generateSign(String data) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(data.getBytes("UTF-8"));
            StringBuilder sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (Exception e) {
            log.error("生成签名失败: {}", e.getMessage());
            // 如果生成签名失败，返回原始数据的hashCode作为备用
            return String.valueOf(data.hashCode());
        }
    }

    /**
     * 连通性
     * @param siteData
     * @param siteGroupData
     */
    private void pushConnect1(Map<Integer, String> siteData, Map<Integer, List<WebsiteAccessRecord>> siteGroupData) {
        // 预警前置处理-推送连通性数据
        List<WarnPlan> warnPlans = warnPlanService.listAll();
        for (WarnPlan warnPlan : warnPlans) {
            //判断是否开启连通性预警通知
            if (warnPlan.getConnectivityWarn() == true) {
                //连通性异常推送，分站点批次推
                siteData.forEach((siteId, siteName) -> {
                    List<Integer> siteIds = warnPlanService.getSiteIdsByWarnPlanId(warnPlan.getId());
                    //判断是否有该站点的预警
                    if (siteIds.isEmpty() || !siteIds.contains(siteId)) {
                        return;
                    }
                    List<WebsiteAccessRecord> websiteAccessRecord = siteGroupData.get(siteId);
                    Date checkTime = new Date();
                    WarnPushRecord target = new WarnPushRecord();
                    target.setActualPushTime(checkTime);
                    target.setWarnSourceName(siteName);
                    //找预警模板
                    target.setWarnPlanId(warnPlan.getId());
                    //找预警推送人
                    List<WarnPlanPushUser> warnPlanPushUsers = warnPlanService.getPushUserByWarnPlanId(warnPlan.getId());
                    if (warnPlanPushUsers != null && !warnPlanPushUsers.isEmpty()) {
                        target.setReceiverName(warnPlanPushUsers.stream().map(WarnPlanPushUser::getUserName).collect(Collectors.joining(",")));
                        target.setReceiverUserId(warnPlanPushUsers.stream().map(WarnPlanPushUser::getUserId).map(String::valueOf).collect(Collectors.joining(",")));
                    }
                    //其他规则
                    target.setArticleErrorWarn(warnPlan.getArticleErrorWarn());
                    //这里是系统预警
                    target.setWarnPushType(WarnPushConstants.METHOD_OF_SYSTEM);
                    target.setWarnType(WarnPushConstants.PUSH_TYPE_LINK);
                    //设置推送数据
                    WarnPushDataDto warnPushDataDto = new WarnPushDataDto();
                    warnPushDataDto.setWarnType(WarnPushConstants.PUSH_TYPE_LINK);
                    warnPushDataDto.setWarnContentType(WarnPushConstants.WARN_CONTENT_TYPE_CONNECT);
                    warnPushDataDto.setSiteName(siteName);
                    warnPushDataDto.setCheckTime(DateUtil.formatDateTime(checkTime));
                    List<WarnPushDataDto.URLDto> urlList = new ArrayList<>();
                    for (int i = 0; i < websiteAccessRecord.size(); i++) {
                        WebsiteAccessRecord e = websiteAccessRecord.get(i);
                        WarnPushDataDto.IndexConnectDto indexConnectDto = new WarnPushDataDto.IndexConnectDto();
                        indexConnectDto.setNo(i + 1);
                        indexConnectDto.setAccessTime(e.getCheckTime());
                        Double spendTime = e.getAccessTimeConsuming() == null ? null :
                                new BigDecimal(e.getAccessTimeConsuming())
                                        .divide(new BigDecimal("1000"), 3, RoundingMode.HALF_UP)
                                        .doubleValue();
                        indexConnectDto.setAccessTimeSecond(spendTime);
                        urlList.add(indexConnectDto);
                    }
                    //找推送人
                    for (WarnPlanPushUser el : warnPlanPushUsers) {
                        warnPushDataDto.setAccountName(el.getUserName());
                        warnPushDataDto.setSiteName(siteName);
                        warnPushDataDto.setConnectList(urlList);
                        warnPushDataDto.setWarnContentType(WarnPushConstants.WARN_CONTENT_TYPE_CONNECT);
                        target.setData(JSON.toJSONString(warnPushDataDto));
                        WarnPushRecord tmp = new WarnPushRecord();
                        BeanUtils.copyProperties(target, tmp);
                        boolean b = warnPushRecordService.save(tmp);
                        if (b) {
                            tmp.setWarnLink(warnBaseUrl + "/mobile-alert-access.html?id=" + AESUtils.genEncPushId(String.valueOf(tmp.getId())));
                            warnPushRecordService.updateById(tmp);
                            if (el.getPushType() == 0) {
                                //找微信用户
                                WarnUser warnUser = warnUserDao.selectById(el.getUserId());
                                if (warnUser == null || StrUtil.isBlank(warnUser.getUserOpenId())) {
                                    return;
                                }
                                //微信发送预警
                                WarnPushMsgDto warnPushMsgDto = new WarnPushMsgDto();
                                warnPushMsgDto.setTime(DateUtil.formatDateTime(new Date()));
                                warnPushMsgDto.setType("连通性异常-TYPE");
                                warnPushMsgDto.setContent("站点：" + warnPushDataDto.getSiteName() + "，连通性异常");
                                warnPushMsgDto.setLink(target.getWarnLink());
                                newMediaService.sendWarn(warnPushMsgDto, warnUser.getUserOpenId());
                            } else {
                                //TODO 手机号推送预警
                            }
                        }
                    }
                });
            }
        }
    }

    /**
     * 死链接
     * @param siteData
     * @param siteGroupData
     */
    private void pushConnect2(Map<Integer, String> siteData, Map<Integer, List<WebsiteDeadLinkCheckRecord>> siteGroupData) {
        // 预警前置处理-推送连通性数据
        List<WarnPlan> warnPlans = warnPlanService.listAll();
        for (WarnPlan warnPlan : warnPlans) {
            //判断是否开启连通性预警通知
            if (warnPlan.getDeadLinkWarn() == true) {
                //连通性异常推送，分站点批次推
                siteData.forEach((siteId, siteName) -> {
                    List<Integer> siteIds = warnPlanService.getSiteIdsByWarnPlanId(warnPlan.getId());
                    //判断是否有该站点的预警
                    if (siteIds.isEmpty() || !siteIds.contains(siteId)) {
                        return;
                    }
                    List<WebsiteDeadLinkCheckRecord> records = siteGroupData.get(siteId);
                    Date checkTime = new Date();
                    WarnPushRecord target = new WarnPushRecord();
                    target.setActualPushTime(checkTime);
                    target.setWarnSourceName(siteName);
                    //找预警模板
                    target.setWarnPlanId(warnPlan.getId());
                    //找预警推送人
                    List<WarnPlanPushUser> warnPlanPushUsers = warnPlanService.getPushUserByWarnPlanId(warnPlan.getId());
                    if (warnPlanPushUsers != null && !warnPlanPushUsers.isEmpty()) {
                        target.setReceiverName(warnPlanPushUsers.stream().map(WarnPlanPushUser::getUserName).collect(Collectors.joining(",")));
                        target.setReceiverUserId(warnPlanPushUsers.stream().map(WarnPlanPushUser::getUserId).map(String::valueOf).collect(Collectors.joining(",")));
                    }
                    //其他规则
                    target.setArticleErrorWarn(warnPlan.getArticleErrorWarn());
                    //这里是系统预警
                    target.setWarnPushType(WarnPushConstants.METHOD_OF_SYSTEM);
                    target.setWarnType(WarnPushConstants.PUSH_TYPE_LINK);
                    //设置推送数据
                    WarnPushDataDto warnPushDataDto = new WarnPushDataDto();
                    warnPushDataDto.setWarnType(WarnPushConstants.PUSH_TYPE_LINK);
                    warnPushDataDto.setWarnContentType(WarnPushConstants.WARN_CONTENT_TYPE_DEAD);
                    warnPushDataDto.setSiteName(siteName);
                    warnPushDataDto.setCheckTime(DateUtil.formatDateTime(checkTime));
                    List<WarnPushDataDto.URLDto> urlList = new ArrayList<>();
                    for (int i = 0; i < records.size(); i++) {
                        WebsiteDeadLinkCheckRecord e = records.get(i);
                        WarnPushDataDto.DeadLinkDto deadLinkDto = new WarnPushDataDto.DeadLinkDto();
                        deadLinkDto.setNo(i + 1);
                        deadLinkDto.setErrorLink(e.getLinkUrl());
                        deadLinkDto.setStatusCode(e.getHttpCode());
                        deadLinkDto.setErrorPage(e.getSourcePage());
                        urlList.add(deadLinkDto);
                    }
                    //找推送人
                    for (WarnPlanPushUser el : warnPlanPushUsers) {
                        warnPushDataDto.setAccountName(el.getUserName());
                        warnPushDataDto.setSiteName(siteName);
                        warnPushDataDto.setConnectList(urlList);
                        warnPushDataDto.setWarnContentType(WarnPushConstants.WARN_CONTENT_TYPE_DEAD);
                        target.setData(JSON.toJSONString(warnPushDataDto));
                        WarnPushRecord tmp = new WarnPushRecord();
                        BeanUtils.copyProperties(target, tmp);
                        boolean b = warnPushRecordService.save(tmp);
                        if (b) {
                            tmp.setWarnLink(warnBaseUrl + "/mobile-alert-access.html?id=" + AESUtils.genEncPushId(String.valueOf(tmp.getId())));
                            warnPushRecordService.updateById(tmp);
                            if (el.getPushType() == 0) {
                                //找微信用户
                                WarnUser warnUser = warnUserDao.selectById(el.getUserId());
                                if (warnUser == null || StrUtil.isBlank(warnUser.getUserOpenId())) {
                                    return;
                                }
                                //微信发送预警
                                WarnPushMsgDto warnPushMsgDto = new WarnPushMsgDto();
                                warnPushMsgDto.setTime(DateUtil.formatDateTime(new Date()));
                                warnPushMsgDto.setType("死链异常-TYPE");
                                warnPushMsgDto.setContent("站点：" + warnPushDataDto.getSiteName() + "，死链异常");
                                warnPushMsgDto.setLink(target.getWarnLink());
                                newMediaService.sendWarn(warnPushMsgDto, warnUser.getUserOpenId());
                            } else {
                                //TODO 手机号推送预警
                            }
                        }
                    }
                });
            }
        }
    }

    /**
     * 外链
     * @param siteData
     * @param siteGroupData
     */
    private void pushConnect3(Map<Integer, String> siteData, Map<Integer, List<WebsiteOutLinkCheckRecord>> siteGroupData) {
        // 预警前置处理-推送连通性数据
        List<WarnPlan> warnPlans = warnPlanService.listAll();
        for (WarnPlan warnPlan : warnPlans) {
            //判断是否开启连通性预警通知
            if (warnPlan.getExternalLinkWarn() == true) {
                //连通性异常推送，分站点批次推
                siteData.forEach((siteId, siteName) -> {
                    List<Integer> siteIds = warnPlanService.getSiteIdsByWarnPlanId(warnPlan.getId());
                    //判断是否有该站点的预警
                    if (siteIds.isEmpty() || !siteIds.contains(siteId)) {
                        return;
                    }
                    List<WebsiteOutLinkCheckRecord> records = siteGroupData.get(siteId);
                    Date checkTime = new Date();
                    WarnPushRecord target = new WarnPushRecord();
                    target.setActualPushTime(checkTime);
                    target.setWarnSourceName(siteName);
                    //找预警模板
                    target.setWarnPlanId(warnPlan.getId());
                    //找预警推送人
                    List<WarnPlanPushUser> warnPlanPushUsers = warnPlanService.getPushUserByWarnPlanId(warnPlan.getId());
                    if (warnPlanPushUsers != null && !warnPlanPushUsers.isEmpty()) {
                        target.setReceiverName(warnPlanPushUsers.stream().map(WarnPlanPushUser::getUserName).collect(Collectors.joining(",")));
                        target.setReceiverUserId(warnPlanPushUsers.stream().map(WarnPlanPushUser::getUserId).map(String::valueOf).collect(Collectors.joining(",")));
                    }
                    //其他规则
                    target.setArticleErrorWarn(warnPlan.getArticleErrorWarn());
                    //这里是系统预警
                    target.setWarnPushType(WarnPushConstants.METHOD_OF_SYSTEM);
                    target.setWarnType(WarnPushConstants.PUSH_TYPE_LINK);
                    //设置推送数据
                    WarnPushDataDto warnPushDataDto = new WarnPushDataDto();
                    warnPushDataDto.setWarnType(WarnPushConstants.PUSH_TYPE_LINK);
                    warnPushDataDto.setWarnContentType(WarnPushConstants.WARN_CONTENT_TYPE_OUT);
                    warnPushDataDto.setSiteName(siteName);
                    warnPushDataDto.setCheckTime(DateUtil.formatDateTime(checkTime));
                    List<WarnPushDataDto.URLDto> urlList = new ArrayList<>();
                    for (int i = 0; i < records.size(); i++) {
                        WebsiteOutLinkCheckRecord e = records.get(i);
                        WarnPushDataDto.DeadLinkDto deadLinkDto = new WarnPushDataDto.DeadLinkDto();
                        deadLinkDto.setNo(i + 1);
                        deadLinkDto.setErrorLink(e.getLinkUrl());
                        deadLinkDto.setStatusCode(-1);
                        deadLinkDto.setErrorPage(e.getSourcePage());
                        urlList.add(deadLinkDto);
                    }
                    //找推送人
                    for (WarnPlanPushUser el : warnPlanPushUsers) {
                        warnPushDataDto.setAccountName(el.getUserName());
                        warnPushDataDto.setSiteName(siteName);
                        warnPushDataDto.setConnectList(urlList);
                        warnPushDataDto.setWarnContentType(WarnPushConstants.WARN_CONTENT_TYPE_OUT);
                        target.setData(JSON.toJSONString(warnPushDataDto));
                        WarnPushRecord tmp = new WarnPushRecord();
                        BeanUtils.copyProperties(target, tmp);
                        boolean b = warnPushRecordService.save(tmp);
                        if (b) {
                            target.setWarnLink(warnBaseUrl + "/mobile-alert-access.html?id=" + AESUtils.genEncPushId(String.valueOf(tmp.getId())));
                            warnPushRecordService.updateById(tmp);
                            if (el.getPushType() == 0) {
                                //找微信用户
                                WarnUser warnUser = warnUserDao.selectById(el.getUserId());
                                if (warnUser == null || StrUtil.isBlank(warnUser.getUserOpenId())) {
                                    return;
                                }
                                //微信发送预警
                                WarnPushMsgDto warnPushMsgDto = new WarnPushMsgDto();
                                warnPushMsgDto.setTime(DateUtil.formatDateTime(new Date()));
                                warnPushMsgDto.setType("外链异常-TYPE");
                                warnPushMsgDto.setContent("站点：" + warnPushDataDto.getSiteName() + "，外链异常");
                                warnPushMsgDto.setLink(target.getWarnLink());
                                newMediaService.sendWarn(warnPushMsgDto, warnUser.getUserOpenId());
                            } else {
                                //TODO 手机号推送预警
                            }
                        }
                    }
                });
            }
        }
    }


}