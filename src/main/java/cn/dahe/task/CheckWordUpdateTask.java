package cn.dahe.task;

import cn.dahe.entity.CheckResult;
import cn.dahe.enums.CheckErrorLevelEnum;
import cn.dahe.enums.CheckErrorTypeEnum;
import cn.dahe.model.dto.CheckResultDto;
import cn.dahe.service.CheckResultService;
import cn.dahe.service.CheckTaskService;
import cn.dahe.service.CheckWordService;
import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 统一的错词更新和关联
 */
@Slf4j
@Component
public class CheckWordUpdateTask {
    @Resource
    private CheckTaskService checkTaskService;
    @Resource
    private CheckResultService checkResultService;
    @Resource
    private CheckWordService checkWordService;

    // @Scheduled(cron = ScheduledTaskConstants.EVERY_5_MINUTES)
    // @XxlJob("checkWordUpdateTask")
    public void checkWordUpdateTask() {
        //  TODO 先搁置，影响推送
        //  更新已有错词的关联
        checkResultService.updateUnrelatedCheckWordResult();
        //  检查新词
        List<CheckResult> checkResults = checkResultService.listUnrelatedWordResult();
        if (CollUtil.isEmpty(checkResults)) {
            return;
        }
        List<CheckResultDto> results = checkResults.stream().map(checkResult -> {
            CheckResultDto checkResultDto = new CheckResultDto();
            checkResultDto.setErrorWord(checkResult.getErrorWord());
            checkResultDto.setSuggestWord(checkResult.getSuggestWord());
            checkResultDto.setErrorLevel(CheckErrorLevelEnum.getByValue(checkResult.getErrorLevel()));
            checkResultDto.setErrorType(CheckErrorTypeEnum.getByValue(checkResult.getErrorType()));
            return checkResultDto;
        }).collect(Collectors.toList());
        checkWordService.batchGetOrCreateCheckWords(results);
        //  更新已有错词的关联
        checkResultService.updateUnrelatedCheckWordResult();
    }
}
