package cn.dahe.task;

import cn.dahe.common.constants.ScheduledTaskConstants;
import cn.dahe.service.WebSiteIndexArticleCheckService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 网站首页更新检查定时任务
 *
 * <AUTHOR>
 * @date 2025-09-03
 */
@Slf4j
@Component
public class WebSiteIndexCheckTask {

    @Resource
    private WebSiteIndexArticleCheckService WebSiteIndexArticleCheckService;

    /**
     * 每天凌晨2点执行，生成前一天的首页更新检查数据
     */
    @Scheduled(cron = ScheduledTaskConstants.DAILY_2_AM)
    public void generateDailyIndexCheckData() {
        log.info("开始执行每日首页更新检查数据生成任务");
        try {
            Integer count = WebSiteIndexArticleCheckService.generateAllSitesYesterdayIndexCheckData();
            log.info("每日首页更新检查数据生成任务执行完成，共生成{}条记录", count);
        } catch (Exception e) {
            log.error("每日首页更新检查数据生成任务执行失败", e);
        }
    }
}
