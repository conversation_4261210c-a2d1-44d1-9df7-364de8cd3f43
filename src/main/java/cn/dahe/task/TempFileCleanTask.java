package cn.dahe.task;

import cn.dahe.entity.TempFileRecord;
import cn.dahe.service.TempFileRecordService;
import cn.hutool.core.io.FileUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.util.Date;
import java.util.List;

@Slf4j
@Component
public class TempFileCleanTask {

    @Resource
    private TempFileRecordService tempFileRecordService;

    /**
     * 每5分钟执行一次清理任务
     */
    @XxlJob("cleanTempFiles")
    public void cleanTempFiles() {
        // log.info("开始执行临时文件清理任务");

        // 获取待删除的文件记录
        List<TempFileRecord> records = tempFileRecordService.lambdaQuery()
                .ne(TempFileRecord::getStatus, 1)
                .list();

        if (records.isEmpty()) {
            return;
        }
        log.info("待删除的文件记录数：{}", records.size());
        for (TempFileRecord record : records) {
            try {
                File file = new File(record.getFilePath());
                if (!file.exists()) {
                    // 文件不存在，直接标记为删除成功
                    updateRecordStatus(record, 1);
                    continue;
                }

                // 尝试删除文件
                FileUtil.del(file);

                // 检查文件是否还存在
                if (!file.exists()) {
                    // 删除成功
                    updateRecordStatus(record, 1);
                } else {
                    // 删除失败，更新尝试次数
                    record.setLastTryTime(new Date());
                    record.setStatus(2);
                    tempFileRecordService.updateById(record);
                }
            } catch (Exception e) {
                log.error("删除临时文件失败：{}，错误：{}", record.getFilePath(), e.getMessage());
                updateRecordStatus(record, 2);
            }
        }
    }

    private void updateRecordStatus(TempFileRecord record, Integer status) {
        record.setStatus(status);
        record.setLastTryTime(new Date());
        tempFileRecordService.updateById(record);
    }
}
