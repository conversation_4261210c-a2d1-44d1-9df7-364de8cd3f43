package cn.dahe.task;

import cn.dahe.check.service.CheckService;
import cn.dahe.common.constants.ScheduledTaskConstants;
import cn.dahe.entity.Attachment;
import cn.dahe.service.AttachmentService;
import cn.hutool.core.collection.CollUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 获取附件内容
 *
 * <AUTHOR>
 * @date 2024-03-21
 */
@Slf4j
@Component
public class AttachmentCheckInitTask {

    @Resource
    private AttachmentService attachmentService;
    @Resource
    private CheckService checkService;


    // 每10分钟执行一次
    @Scheduled(cron = ScheduledTaskConstants.EVERY_10_MINUTES)
    @XxlJob("attachmentCheckInitTask")
    public void attachmentCheckInit() {
        List<Attachment> noTaskAttachments = attachmentService.listNoTaskAttachment();
        if (CollUtil.isNotEmpty(noTaskAttachments)) {
            log.info("找到{}条需要分配检查任务的附件", noTaskAttachments.size());
            for (Attachment attachment : noTaskAttachments) {
                try {
                    checkService.initCheckTaskForAttachment(attachment);
                } catch (Exception e) {
                    // 忽略
                }
            }
        }
        List<Attachment> noContentAttachments = attachmentService.listNoContentAttachment();
        if (CollUtil.isNotEmpty(noContentAttachments)) {
            log.info("找到{}条需要获取的附件任务", noContentAttachments.size());
            for (Attachment attachment : noContentAttachments) {
                try {
                    checkService.getContentForAttachment(attachment);
                } catch (Exception e) {
                    log.error("{}-获取附件失败", attachment.getId(), e);
                }
            }
        }

    }
}