package cn.dahe.task;

import cn.dahe.common.constants.ScheduledTaskConstants;
import cn.dahe.entity.Article;
import cn.dahe.service.ArticleService;
import cn.dahe.service.ReprintSourceService;
import cn.hutool.core.collection.CollUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 转载信源更新
 *
 * <AUTHOR>
 * @date 2024-03-21
 */
@Slf4j
@Component
public class ReprintSourceUpdateTask {

    @Resource
    private ArticleService articleService;
    @Resource
    private ReprintSourceService reprintSourceService;

    // 每5分钟执行一次
    @Scheduled(cron = ScheduledTaskConstants.EVERY_5_MINUTES)
    @XxlJob("updateReprintSource")
    public void updateReprintSource() {
        List<Article> articles = articleService.listUnrelatedReprintSourceArticles();
        if (CollUtil.isEmpty(articles)) {
            return;
        }
        log.info("找到{}条需要分配配置转载信源的文章", articles.size());
        //  articles按转载信源、栏目id分组处理
        articles.stream().collect(Collectors.groupingBy(Article::getWebsiteId))
                .forEach((websiteId, articleList) -> {
                    Map<String, List<Long>> map = articleList.stream().collect(Collectors.groupingBy(Article::getReprintSource,
                            Collectors.mapping(
                                    Article::getId,
                                    Collectors.toList()
                            )));
                    Set<String> reprintSources = map.keySet();
                    //   批量添加新的转载信源
                    reprintSourceService.batchCreate(CollUtil.newArrayList(websiteId), reprintSources);
                    //   更新关联 TODO 批量插入sql
                    map.forEach((reprintSource, articleIds) -> {
                        Long reprintSourceId = reprintSourceService.getOrCreateByWebsiteId(websiteId, reprintSource);
                        articleService.batchUpdateReprintSourceId(articleIds, reprintSourceId);
                        log.info("更新{}条文章的转载信源为{}", articleIds.size(), reprintSource);
                    });
                });
        log.info("更新转载信源任务完成");


    }
}