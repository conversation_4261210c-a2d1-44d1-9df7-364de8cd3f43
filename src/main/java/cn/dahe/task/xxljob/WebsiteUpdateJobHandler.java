package cn.dahe.task.xxljob;

import cn.dahe.service.WebSiteIndexArticleCheckService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 网站更新检查任务处理器
 *
 * <AUTHOR>
 * @date 2025-09-09
 */
@Slf4j
@Component
public class WebsiteUpdateJobHandler {

    @Resource
    private WebSiteIndexArticleCheckService webSiteIndexArticleCheckService;

    /**
     * 生成每日首页更新检查数据
     * 每天凌晨2点执行
     */
    @XxlJob("dailyIndexCheckJob")
    public void dailyIndexCheckJob() {
        XxlJobHelper.log("开始执行每日首页更新检查数据生成任务");
        
        try {
            Integer count = webSiteIndexArticleCheckService.generateAllSitesYesterdayIndexCheckData();
            String result = String.format("每日首页更新检查数据生成任务执行完成，共生成%d条记录", count);
            XxlJobHelper.log(result);
            log.info(result);
            
        } catch (Exception e) {
            String errorMsg = "每日首页更新检查数据生成任务执行失败: " + e.getMessage();
            XxlJobHelper.log(errorMsg);
            log.error("每日首页更新检查数据生成任务执行失败", e);
            throw new RuntimeException(errorMsg, e);
        }
    }
}