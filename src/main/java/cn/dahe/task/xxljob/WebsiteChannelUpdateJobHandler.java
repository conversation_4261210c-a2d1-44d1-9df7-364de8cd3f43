package cn.dahe.task.xxljob;

import cn.dahe.service.WebSiteChannelCheckService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 网站栏目更新检查任务处理器
 *
 * <AUTHOR>
 * @date 2025-09-09
 */
@Slf4j
@Component
public class WebsiteChannelUpdateJobHandler {

    @Resource
    private WebSiteChannelCheckService webSiteChannelCheckService;

    /**
     * 生成每日首页更新检查数据
     * 每天凌晨1点执行
     */
    @XxlJob("channelPushNewsCheckJob")
    public void channelPushNewsCheckJob() {
        XxlJobHelper.log("开始执行每日首页更新检查数据生成任务");
        
        try {
            Integer count = webSiteChannelCheckService.generateChannelCheckData();
            String result = String.format("每日网站栏目更新检查数据生成任务执行完成，共生成%d条记录", count);
            XxlJobHelper.log(result);
            log.info(result);
            
        } catch (Exception e) {
            String errorMsg = "每日首页更新检查数据生成任务执行失败: " + e.getMessage();
            XxlJobHelper.log(errorMsg);
            log.error("每日首页更新检查数据生成任务执行失败", e);
            throw new RuntimeException(errorMsg, e);
        }
    }
}