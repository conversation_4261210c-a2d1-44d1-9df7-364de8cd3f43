package cn.dahe.task;

import cn.dahe.check.service.CheckService;
import cn.dahe.entity.CheckTask;
import cn.dahe.service.CheckTaskService;
import cn.hutool.core.collection.CollUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 获取附件内容
 *
 * <AUTHOR>
 * @date 2024-03-21
 */
@Slf4j
@Component
public class ReviewCheckDataTask {

    @Resource
    private CheckService checkService;
    @Resource
    private CheckTaskService checkTaskService;

    // 每5分钟执行一次
    @XxlJob("reviewCheckDataTask")
    public void reviewCheckDataTask() {
        List<CheckTask> checkTasks = checkTaskService.listNoContentReviewTask();
        if (CollUtil.isEmpty(checkTasks)) {
            return;
        }
        log.info("开始获取{}条复查文章内容...", checkTasks.size());
        for (CheckTask checkTask : checkTasks) {
            try {
                checkService.getReviewContentForArticle(checkTask);
            } catch (Exception e) {
                log.error("{}-复查获取内容失败", checkTask.getId(), e);
            }
        }

    }
}
