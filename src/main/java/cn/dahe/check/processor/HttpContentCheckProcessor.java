package cn.dahe.check.processor;

import cn.dahe.check.exception.CheckException;
import cn.dahe.check.processor.reference.ApiCheckData;
import cn.dahe.check.processor.reference.ApiCheckResult;
import cn.dahe.check.processor.reference.ApiResponse;
import cn.dahe.check.processor.reference.CheckResponseDataTypeReference;
import cn.dahe.check.service.TokenCacheService;
import cn.dahe.enums.CheckErrorLevelEnum;
import cn.dahe.enums.CheckErrorTypeEnum;
import cn.dahe.enums.CheckStrategyEnum;
import cn.dahe.model.dto.CheckApiExecuteDto;
import cn.dahe.model.dto.CheckResultDto;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.http.HttpException;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * HTTP接口检查处理器实现类
 * 通过HTTP请求调用外部API进行检查
 */
@Slf4j
@Service("httpContentCheckProcessor")
public class HttpContentCheckProcessor implements ContentCheckProcessor {

    @Value("${anxun.content-check.api.check-url}")
    private String checkUrl;

    @Value("${anxun.content-check.api.timeout}")
    private int apiTimeout;

    @Value("${anxun.content-check.api.app-key}")
    private String appKey;

    @Resource
    private CheckResponseDataTypeReference checkResponseDataTypeReference;

    @Resource
    private TokenCacheService tokenCacheService;


    private Map<String, Object> makeParamsByCheckStrategy(CheckStrategyEnum checkStrategy, String text) {
        Map<String, Object> params = new HashMap<>();
        params.put("appKey", appKey);
        params.put("text", text);
        params.put("type", 0);
        int checkType;
        switch (checkStrategy) {
            case FIRST:
                //  意识形态
                checkType = 1;
                break;
            case SECOND:
                //  错别字
                checkType = 2;
                break;
            case DEFAULT:
            default:
                //  默认全部
                checkType = 0;
                break;
        }
        params.put("checkType", checkType);
        return params;
    }

    public CheckApiExecuteDto process(CheckStrategyEnum checkStrategy, String text) {
        return process(checkStrategy, text, false);
    }

    private CheckApiExecuteDto process(CheckStrategyEnum checkStrategy, String text, boolean isTryAgain) {
        try {
            // 获取token
            String token = tokenCacheService.getAvailableToken();
            // 构建请求参数
            Map<String, Object> params = makeParamsByCheckStrategy(checkStrategy, text);
            HttpRequest requestExecute = HttpUtil.createPost(checkUrl).timeout(apiTimeout)
                    .header("token", token).contentType("application/json")
                    .body(JSON.toJSONString(params));
            HttpResponse response = requestExecute.execute();
            if (!response.isOk()) {
                return CheckApiExecuteDto.responseStatusFail(checkUrl, response.getStatus());
            }
            String responseBody = response.body();
            // 解析响应
            ApiResponse<ApiCheckData> result = JSON.parseObject(responseBody, checkResponseDataTypeReference);
            if (!result.isSuccess() || result.getData() == null || result.getError() != null) {
                Integer code = result.getCode();
                if (!isTryAgain && (code == 1011 || code == 1012)) {
                    tokenCacheService.cleanTokenCache();
                    return process(checkStrategy, text, true);
                }
                throw CheckException.apiFail(checkUrl, result.getCode(), result.getMsg(), response);
            }
            // 转换检查结果
            ApiCheckData checkResponseData = result.getData();
            if (CollUtil.isNotEmpty(checkResponseData.getCheckResult())) {
                log.info("发现检查结果：{}", checkResponseData.getCheckResult());
                List<CheckResultDto> checkResults = checkResponseData.getCheckResult().stream().map(this::mapResponseToResult).collect(Collectors.toList());
                return CheckApiExecuteDto.success(checkResults);
            }
            return CheckApiExecuteDto.successWithNoResult();
        } catch (CheckException e) {
            return CheckApiExecuteDto.checkExceptionFail(e);
        } catch (HttpException | JSONException e) {
            return CheckApiExecuteDto.responseResultFail(checkUrl, e);
        } catch (Exception e) {
            return CheckApiExecuteDto.localExceptionFail(e);
        }
    }

    private CheckResultDto mapResponseToResult(ApiCheckResult error) {
        CheckResultDto check = new CheckResultDto();
        check.setErrorWord(error.getErrorWords());
        check.setSuggestWord(error.getCorrectWords());
        check.setPosition(error.getPos());
        //  1：错误  2：疑错  3：严重错误
        CheckErrorLevelEnum errorLevel;
        switch (error.getLevel()) {
            case 3:
                errorLevel = CheckErrorLevelEnum.SERIOUS;
                break;
            case 2:
                errorLevel = CheckErrorLevelEnum.SUSPICIOUS;
                break;
            case 1:
            default:
                errorLevel = CheckErrorLevelEnum.NORMAL;
        }
        check.setErrorLevel(errorLevel);
        CheckErrorTypeEnum errorType;
        //  0 错词 1 敏感词
        switch (error.getErrorWordType()) {
            case 1:
                errorType = CheckErrorTypeEnum.SENSITIVE_WORD;
                break;
            case 0:
            default:
                errorType = CheckErrorTypeEnum.WRONG_WORD;
        }
        check.setErrorType(errorType);
        return check;
    }


}