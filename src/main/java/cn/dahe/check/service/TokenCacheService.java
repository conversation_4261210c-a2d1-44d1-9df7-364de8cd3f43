package cn.dahe.check.service;

import cn.dahe.check.exception.CheckException;
import cn.dahe.check.processor.reference.ApiResponse;
import cn.dahe.check.processor.reference.ApiTokenData;
import cn.dahe.check.processor.reference.TokenResponseDataTypeReference;
import cn.dahe.common.redis.RedisService;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Token缓存服务
 */
@Slf4j
@Service
public class TokenCacheService {

    private static final String TOKEN_CACHE_KEY = "content_check_token";
    private static final String REFRESH_TOKEN_CACHE_KEY = "content_check_refresh_token";

    @Resource
    private TokenResponseDataTypeReference tokenResponseDataTypeReference;

    @Value("${anxun.content-check.api.token-url}")
    private String tokenUrl;

    @Value("${anxun.content-check.api.refresh-token-url}")
    private String refreshTokenUrl;

    @Value("${anxun.content-check.api.timeout}")
    private int apiTimeout;

    @Value("${anxun.content-check.api.app-key}")
    private String appKey;

    @Value("${anxun.content-check.api.app-secret}")
    private String appSecret;
    @Resource
    private RedisService redisService;

    private final Object lock = new Object();

    @PostConstruct
    private void init() {
        getAvailableToken();
    }

    /**
     * 获取可用的token
     */
    public String getAvailableToken() {
        String token = getTokenCache();
        if (token == null) {
            synchronized (lock) {
                // 双重检查，确保在获取锁的过程中token没有被其他线程设置
                token = getTokenCache();
                if (token == null) {
                    token = requestNewToken();
                    cacheToken(token);
                }
            }
        }
        return token;
    }


    /**
     * 请求新token
     */
    private String requestNewToken() {
        Map<String, Object> params = new HashMap<>();
        params.put("appKey", appKey);
        params.put("appSecret", appSecret);
        HttpRequest request = HttpUtil.createPost(tokenUrl)
                .timeout(apiTimeout)
                .body(JSON.toJSONString(params));
        try (HttpResponse response = request.execute()) {
            if (!response.isOk()) {
                throw CheckException.statusFail(tokenUrl, response.getStatus());
            }
            ApiResponse<ApiTokenData> result = JSON.parseObject(
                    response.body(), tokenResponseDataTypeReference
            );
            if (!result.isSuccess() || result.getData() == null || StrUtil.isBlank(result.getData().getToken())) {
                throw CheckException.apiFail(tokenUrl,
                        StrUtil.format("接口调用失败：{} {}", result.getCode(), result.getMsg()),
                        response.body());
            }
            return result.getData().getToken();
        } catch (CheckException e) {
            throw e;
        } catch (Exception e) {
            cleanTokenCache();
            throw CheckException.responseFail(tokenUrl, e);
        }
    }


    private void cacheToken(String token) {
        redisService.setCacheObject(TOKEN_CACHE_KEY, token, 110L, TimeUnit.MINUTES);
    }

    private String getTokenCache() {
        return redisService.getCacheObject(TOKEN_CACHE_KEY);
    }

    public void cleanTokenCache() {
        redisService.deleteObject(TOKEN_CACHE_KEY);
    }
}
