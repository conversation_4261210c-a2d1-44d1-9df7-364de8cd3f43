package cn.dahe.check.service;

import cn.dahe.check.exception.ContentGetException;
import cn.dahe.entity.*;
import cn.dahe.enums.*;
import cn.dahe.model.dto.AttachmentContentDTO;
import cn.dahe.model.dto.PushRecordDto;
import cn.dahe.model.dto.WarnCheckDetailDto;
import cn.dahe.model.request.LingcaiPushDataDto;
import cn.dahe.model.vo.CheckResultVO;
import cn.dahe.model.vo.CheckSnapshotVO;
import cn.dahe.service.*;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.net.url.UrlBuilder;
import cn.hutool.core.net.url.UrlQuery;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CheckService {

    @Resource
    private CheckTaskService checkTaskService;
    @Resource
    private CheckResultService checkResultService;
    @Resource
    private CheckContentService checkContentService;

    public void doCheck(CheckTask checkTask) {
        Long checkId = checkTask.getId();
        CheckStatusEnum checkStatus = CheckStatusEnum.getByValue(checkTask.getCheckStatus());
        switch (checkStatus) {
            case CHECKED:
                //  已检查则不进行检查
                return;
            case FAILED_RETRY:
            case UNCHECKED:
            default:
                //  清理之前的检查数据
                checkResultService.clearHistoryCheckResult(checkId);
                checkTaskService.executeCheckTask(checkTask, checkStatus, result -> {
                    try {
                        this.decideToPush(checkId);
                    } catch (Exception e) {
                        log.error("推送检查任务{}失败", checkId, e);
                    }
                });

        }
    }

    @Resource
    private WarnPushMsgService warnPushMsgService;
    @Resource
    private ArticleService articleService;
    @Resource
    private WarnDetailService warnDetailService;
    @Value("${anxun.base-url}")
    private String warnBaseUrl;
    @Value("${anxun.h5-warn.check-site-path}")
    private String siteCheckPath;
    @Value("${anxun.h5-warn.check-weibo-path}")
    private String mobileCheckPath;
    @Resource
    private WarnPlanService warnPlanService;
    @Resource
    private WarnPushRecordService warnPushRecordService;
    @Resource
    private PushService pushService;

    //  TODO 换个executor
    @Async("taskExecutor")
    protected void decideToPush(Long checkId) {
        //  检查任务是否有检查结果
        CheckTask checkTask = checkTaskService.getById(checkId);
        if (!CheckResultStatusEnum.HAS_RESULT.getValue().equals(checkTask.getResultStatus())) {
            return;
        }
        Long relationId = checkTask.getRelationId();
        WarnDetailRelateTypeEnum warnType;
        Long websiteId;
        ProcessTypeEnum processType;
        String linkPath;
        WarnCheckDetailDto h5Detail;
        switch (CheckTaskRelationTypeEnum.getByValue(checkTask.getRelationType())) {
            //  文章检查
            case ARTICLE_REVIEW:
            case ARTICLE:
                warnType = WarnDetailRelateTypeEnum.ARTICLE;
                Article article = articleService.getById(relationId);
                websiteId = article.getWebsiteId();
                processType = ProcessTypeEnum.getByValue(article.getProcessType());
                linkPath = processType.equals(ProcessTypeEnum.WEBSITE) ? siteCheckPath : mobileCheckPath;
                h5Detail = articleService.getWarnArticleInfo(relationId);
                break;
            //  附件检查
            case ATTACHMENT:
                warnType = WarnDetailRelateTypeEnum.ATTACHMENT;
                Attachment attachment = attachmentService.getById(relationId);
                websiteId = attachment.getWebsiteId();
                //  附件目前只有网站出，不过还是走个流程
                processType = ProcessTypeEnum.getByValue(attachment.getProcessType());
                //  附件目前没有标题，暂时使用新媒体检查页面
                linkPath = mobileCheckPath;
                h5Detail = attachmentService.getWarnAttachmentInfo(relationId);
                break;
            default:
                return;
        }
        this.appendForDetail(checkId, h5Detail);
        String type = warnType.getDesc();
        List<Long> planIds = warnPlanService.listAvailablePlanWithCondition(websiteId, warnType);
        if (CollUtil.isEmpty(planIds)) {
            return;
        }
        List<CheckResultVO> errorObjs = h5Detail.getErrorObjs();
        if (CollUtil.isEmpty(errorObjs)) {
            return;
        }
        String words = errorObjs.stream().map(CheckResultVO::getErrorWord).distinct().collect(Collectors.joining("、"));
        String content = StrUtil.format("预警词语：{}", words);
        WarnDetail detail = warnDetailService.initWarnDetail(warnType, checkId, h5Detail);
        String link = UrlBuilder.of(warnBaseUrl + linkPath)
                .setQuery(new UrlQuery(true).add("sign", detail.getSign()))
                .build();
        link = StrUtil.subAfter(link, warnBaseUrl, false);
        //  调用推送，是否推、推给谁由其业务决定
        WarnPushMsg warnPushMsg = warnPushMsgService.makeWarnPushMsg(detail.getRelateType(), detail.getId(), type, new Date(), content, link);
        PushRecordDto recordDto = warnPushRecordService.createContentPushWithPlans(planIds, warnPushMsg, websiteId);
        //  推送
        pushService.pushWithUsers(recordDto.getWarnUsers(), warnPushMsg);
        //  更新推送时间 TODO 推送成功失败？
        warnPushRecordService.updateCheckPushRecords(
                CollUtil.map(recordDto.getRecords(), WarnPushRecord::getId, true),
                warnPushMsg
        );
    }


    private void appendForDetail(Long taskId, WarnCheckDetailDto vo) {
        CheckTask checkTask = checkTaskService.getById(taskId);
        //  检测时间
        vo.setCheckTime(checkTask.getCreateTime());
        List<CheckResultVO> errorObjs = checkResultService.getErrorObjsByTaskId(taskId);
        CheckContent checkContent = checkContentService.getById(taskId);
        Map<ArticleLocationEnum, List<CheckResultVO>> errorMap = errorObjs.stream()
                .collect(Collectors.groupingBy(
                        e -> ArticleLocationEnum.getByValue(e.getArticleLocation())
                ));

        List<CheckResultVO> titleErrors = errorMap.get(ArticleLocationEnum.TITLE);
        String title = checkContent.getCleanedTitle();
        String markedTitle = checkResultService.markText(title, titleErrors, false,checkResultVO -> new Pair<>(checkResultVO.getErrorWord(), checkResultVO.getPosition()));
        vo.setMarkedTitle(markedTitle);

        List<CheckResultVO> contentErrors = errorMap.get(ArticleLocationEnum.CONTENT);
        String htmlContent = checkContent.getCompressedContent();
        String markedContent = checkResultService.markText(htmlContent, contentErrors, true,checkResultVO -> new Pair<>(checkResultVO.getHtmlErrorWord(), checkResultVO.getHtmlPosition()));
        vo.setMarkedContent(markedContent);

        //  标记上下文
        for (CheckResultVO errorObj : errorObjs) {
            String context = errorObj.getContext();
            String markedContext = checkResultService.markText(context, CollUtil.newArrayList(errorObj), false,checkResultVO -> new Pair<>(checkResultVO.getErrorWord(), checkResultVO.getContextPosition()));
            errorObj.setMarkedContext(markedContext);
        }

        vo.setErrorObjs(errorObjs);
    }

    @Resource
    private AttachmentService attachmentService;

    /**
     * 创建文章检查任务
     */
    public void initCheckTaskForAttachment(Attachment attachment) {
        Long attachmentId = attachment.getId();
        Long websiteId = attachment.getWebsiteId();
        //  TODO 改为检查栏目策略
        CheckStrategyEnum checkStrategy = webSiteService.getWebsiteCheckStrategy(websiteId);
        CheckTask checkTask = checkTaskService.initCheckTask(CheckTaskRelationTypeEnum.ATTACHMENT, attachmentId, checkStrategy);
        Long checkId = checkTask.getId();
        attachmentService.relateWithCheckTask(attachmentId, checkId);
    }

    /**
     * 创建文章复查任务
     *
     * @param article 文章信息
     * @return 复查任务id
     */
    public CheckTask initCheckTaskForArticleReview(Article article) {
        Long articleId = article.getId();
        CheckTask initCheckTask = checkTaskService.getById(article.getCheckId());
        CheckStrategyEnum checkStrategy = CheckStrategyEnum.getByValue(initCheckTask.getCheckStrategy());
        CheckTask reviewCheckTask = checkTaskService.initCheckTask(CheckTaskRelationTypeEnum.ARTICLE_REVIEW, articleId, checkStrategy);
        return reviewCheckTask;
    }

    @Resource
    private WebsiteService webSiteService;

    /**
     * 创建文章检查任务
     */
    public void initCheckTaskForArticle(Article article) {
        Long websiteId = article.getWebsiteId();
        Long articleId = article.getId();
        //  TODO 检查策略改为和栏目绑定
        CheckStrategyEnum checkStrategy = webSiteService.getWebsiteCheckStrategy(websiteId);
        CheckTask checkTask = checkTaskService.initCheckTask(CheckTaskRelationTypeEnum.ARTICLE, articleId, checkStrategy);
        Long checkId = checkTask.getId();
        //  这里居然为空，article和content必须绑定存在
        ArticleContent articleContent = articleContentService.getById(articleId);
        if (articleContent == null) {
            log.warn("文章{}没有内容，需要检查", articleId);
            return;
        }
        //  根据文章内容初始化检查内容
        checkContentService.saveContent(checkId, articleContent.getTitle(), articleContent.getContent(), articleContent.getWebCode());
        checkTask.setContentStatus(CheckContentStatusEnum.SAVE_SUCCESS.getValue());
        checkTaskService.updateById(checkTask);
        articleService.relateWithCheckTask(articleId, checkId);
    }


    public void getContentForAttachment(Attachment attachment) {
        //  这里默认都是有checkTask的
        Long checkId = attachment.getCheckId();
        if (checkId == null) {
            return;
        }
        CheckTask checkTask = checkTaskService.getById(checkId);
        if (checkTask == null) {
            return;
        }
        Long attachmentId = attachment.getId();
        Integer contentStatus = checkTask.getContentStatus();
        CheckContentStatusEnum checkContentStatusEnum = CheckContentStatusEnum.getByValue(contentStatus);
        if (checkContentStatusEnum.equals(CheckContentStatusEnum.SAVE_SUCCESS) || checkContentStatusEnum.equals(CheckContentStatusEnum.SAVE_FAIL_END)) {
            return;
        }
        AttachmentContentDTO contentDTO = null;
        try {
            //  TODO 大附件处理
            contentDTO = attachmentService.getAndTransferAttachmentContent(attachment);
        } catch (ContentGetException e) {
            checkTaskService.updateContentFail(checkId, e.getContentStatus(), e.getMessage(), e.getErrorDetail());
        } catch (Exception e) {
            checkTaskService.updateContentFail(checkId, CheckContentStatusEnum.SAVE_FAIL_END.getValue(), e.getMessage(), ExceptionUtil.stacktraceToString(e));
        }
        if (contentDTO != null) {
            attachmentService.updateByDto(attachmentId, contentDTO);
            //  大于1G跳过
            if (NumberUtil.compare(contentDTO.getFileSize(), 1024 * 1024 * 1024) <= 0) {
                //  这里没有设置源码，源码=正文
                checkTaskService.updateContentSuccess(checkId, null, contentDTO.getContent(), null);
            } else {
                checkTaskService.updateContentFail(checkId, CheckContentStatusEnum.SAVE_FAIL_END.getValue(), "文件超出处理范畴，暂时跳过", StrUtil.format("文件超出处理范畴，暂时跳过，文件大小为{}M", contentDTO.getFileSize() / 1024 / 1024));
            }
        }
    }

    @Resource
    private CollectService collectService;

    public void getReviewContentForArticle(CheckTask checkTask) {
        if (!CheckTaskRelationTypeEnum.ARTICLE_REVIEW.getValue().equals(checkTask.getRelationType())) {
            return;
        }
        Long articleId = checkTask.getRelationId();
        Article article = articleService.getById(articleId);
        //  目前暂时只支持网站
        if (!ProcessTypeEnum.WEBSITE.getValue().equals(article.getProcessType())) {
            return;
        }
        Long checkId = checkTask.getId();
        try {
            List<ArticleContent> articleContents = collectService.recollectArticleContents(CollUtil.newArrayList(article));
            for (ArticleContent articleContent : articleContents) {
                if (articleContent.getId().equals(articleId)) {
                    checkTaskService.updateContentSuccess(checkId, articleContent.getTitle(), articleContent.getContent(), articleContent.getWebCode());
                    return;
                }
            }
            checkTaskService.updateContentFail(checkId, CheckContentStatusEnum.SAVE_FAIL_RETRY.getValue(), "复采未匹配", "未匹配到文章内容");
        } catch (Exception e) {
            checkTaskService.updateContentFail(checkId, CheckContentStatusEnum.SAVE_FAIL_RETRY.getValue(), e.getMessage(), ExceptionUtil.stacktraceToString(e));
        }
    }

    @Resource
    private ArticleContentService articleContentService;

    public void savePushData(LingcaiPushDataDto pushData) {
        Long articleId = pushData.getId();
        /*------------------初始化文章--------------------------*/
        if (articleId == null) {
            log.error("推送数据ID为空");
            return;
        }
        //  TODO 采集数据源码因请求失败而推送非全文的情况，判断和更新
        Long siteId = pushData.getSite_id();
        Website website = webSiteService.getWebsiteBySiteId(siteId.intValue());
        if (website == null) {
            log.error("推送数据站点不存在，siteId={}", siteId);
        }
        String indexUrl = website.getWebUrl();
        String body = HttpUtil.createGet(indexUrl).execute().body();
        boolean isIndex = body.contains(pushData.getTitle());
        pushData.setIsIndex(isIndex ? 1 : 0);
        Article article = articleService.getById(articleId);
        if (article == null) {
            //  保存文章和正文
            article = articleService.saveByPushData(pushData);
            //  关联检查任务，初始化检查内容
            this.initCheckTaskForArticle(article);
        } else {
            article.setIsIndex(isIndex ? 1 : 0);
            articleService.save(article);
        }
        //  保存附件 TODO 并发重复插入问题
        if (CollUtil.isNotEmpty(pushData.getFile_link())) {
            attachmentService.saveByPushData(article, pushData.getFile_link());
        }

    }

    public boolean updateArticleAuditStatus(Long articleId, List<Long> resultIds, AuditStatusEnum auditStatus) {
        if (auditStatus == null || articleId == null || CollUtil.isEmpty(resultIds)) {
            return false;
        }
        resultIds = checkResultService.confirmResultIdsFromArticle(articleId, resultIds);
        if (CollUtil.isEmpty(resultIds)) {
            return false;
        }
        return checkResultService.updateAuditStatus(resultIds, auditStatus);
    }


    public CheckSnapshotVO snapshotResult(Long taskId) {
        CheckTask checkTask = checkTaskService.getById(taskId);
        if (checkTask == null) {
            return null;
        }
        Long relationId = checkTask.getRelationId();
        CheckSnapshotVO vo;
        CheckTaskRelationTypeEnum relationType = CheckTaskRelationTypeEnum.getByValue(checkTask.getRelationType());
        switch (relationType) {
            case ATTACHMENT:
                vo = attachmentService.getSnapshotInfo(relationId);
                break;
            case ARTICLE:
            case ARTICLE_REVIEW:
            default:
                vo = articleService.getSnapshotInfo(relationId);
        }
        vo.setTaskId(taskId);
        vo.setRelationType(checkTask.getRelationType());
        vo.setCheckTime(checkTask.getCheckTime());
        List<CheckResultVO> checkResults = checkResultService.listSnapshotCheckResultByTask(taskId, relationType);
        vo.setErrorObjs(checkResults);
        return vo;
    }

    public String snapshotContent(Long taskId) {
        CheckContent checkContent = checkContentService.lambdaQuery()
                .eq(CheckContent::getId, taskId)
                .select(CheckContent::getCompressedCode, CheckContent::getCompressedContent)
                .one();
        String compressedCode = checkContent.getCompressedCode();
        //  附件用正文 其他用源码
        return compressedCode == null ? checkContent.getCompressedContent() : compressedCode;
    }
}
