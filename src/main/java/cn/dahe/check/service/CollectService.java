package cn.dahe.check.service;

import cn.dahe.check.feign.model.request.ArticleCollectDto;
import cn.dahe.check.feign.model.request.ArticleCollectRequest;
import cn.dahe.check.feign.model.request.ArticleDetailRequest;
import cn.dahe.check.feign.model.response.ArticleCollectData;
import cn.dahe.check.feign.model.response.ArticleCollectResponse;
import cn.dahe.check.feign.model.response.ArticleDetailResponse;
import cn.dahe.check.feign.service.CollectApiService;
import cn.dahe.entity.Article;
import cn.dahe.entity.ArticleContent;
import cn.dahe.enums.ProcessTypeEnum;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 采集服务封装
 */
@Slf4j
@Service
public class CollectService {
    @Resource
    private CollectApiService collectApiService;

    public List<ArticleContent> collectArticleContents(List<Article> articles) {
        List<ArticleContent> articleContents = CollUtil.newArrayList();
        Map<Long, Article> articleIdMap = articles.stream().collect(Collectors.toMap(Article::getId, article -> article));
        Set<Long> articleIds = articleIdMap.keySet();
        ArticleDetailRequest request = new ArticleDetailRequest().setArticle_ids(articleIds);
        try {
            ArticleDetailResponse response = collectApiService.articleDetail(request);
            if (response.getSuccess() && CollUtil.isNotEmpty(response.getData())) {
                //  这里成功时 success可能为空，所以只需判断不是false
                response.getData().stream().filter(data -> data.getSuccess() == null || !data.getSuccess())
                        .map(data -> {
                            Long articleId = data.getId();
                            Article article = articleIdMap.get(articleId);
                            ArticleContent articleContent = new ArticleContent()
                                    .setId(articleId)
                                    .setContent(data.getContent());
                            //  微博不保留标题
                            if (!ProcessTypeEnum.Weibo.getValue().equals(article.getProcessType())) {
                                articleContent.setTitle(data.getTitle());
                            }
                            return articleContent;
                        }).filter(articleContent -> StrUtil.isNotBlank(articleContent.getContent()))
                        .collect(Collectors.toCollection(() -> articleContents));
            }
        } catch (Exception e) {
            log.warn("获取文章详情失败：{}", articleIds, e);
        }
        return articleContents;
    }

    public List<ArticleContent> recollectArticleContents(List<Article> articles) {
        List<ArticleContent> articleContents = CollUtil.newArrayList();
        if (CollUtil.isEmpty(articles)) {
            return articleContents;
        }
        List<ArticleCollectDto> dtoList = articles.stream().filter(article -> article.getId() != null && StrUtil.isNotBlank(article.getArticleUrl()))
                .map(article -> {
                    return new ArticleCollectDto().setId(article.getId()).setUrl(article.getArticleUrl());
                }).collect(Collectors.toList());
        ArticleCollectRequest request = new ArticleCollectRequest().setArticles(dtoList);
        try {
            ArticleCollectResponse response = collectApiService.articleCollect(request);
            if (response.getSuccess() && CollUtil.isNotEmpty(response.getData())) {
                response.getData().stream().filter(ArticleCollectData::getSuccess)
                        .map(data -> {
                            return new ArticleContent()
                                    .setId(data.getId())
                                    .setTitle(data.getTitle())
                                    .setContent(data.getContent())
                                    .setWebCode(data.getHtml_source());
                            //  目前新媒体只能提供源码
                        }).filter(content -> StrUtil.isNotBlank(content.getWebCode()))
                        .collect(Collectors.toCollection(() -> articleContents));
            }
        } catch (Exception e) {
            log.warn("重新采集文章失败", e);
        }
        return articleContents;
    }

}
