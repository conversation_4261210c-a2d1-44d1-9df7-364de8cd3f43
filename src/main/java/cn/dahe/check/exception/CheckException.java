package cn.dahe.check.exception;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import lombok.Getter;

/**
 * 内容检查异常基类
 */
@Getter
public class CheckException extends RuntimeException {
    /**
     * 失败类型
     */
    private final Integer failType;

    /**
     * 错误详情
     */
    private final String errorDetail;

    public CheckException(Integer failType, String message, String errorDetail) {
        super(message);
        this.failType = failType;
        this.errorDetail = errorDetail;
    }

    public CheckException(Integer failType, String message, String errorDetail, Throwable cause) {
        super(message, cause);
        this.failType = failType;
        this.errorDetail = errorDetail;
    }

    public static CheckException apiFail(String url, String format, String responseBody) {
        return new CheckException(3,
                format,
                StrUtil.format("接口：{} \n 响应结果：{}", url, responseBody)
        );
    }

    public static CheckException apiFail(String url, Integer code, String msg, HttpResponse response) {
        return apiFail(url,
                StrUtil.format("接口调用失败：{} {}", code, msg),
                response.body());
    }

    public static CheckException responseFail(String url, Exception e) {
        return new CheckException(2,
                e.getMessage(),
                StrUtil.format("接口：{} \n 堆栈：{}", url, ExceptionUtil.stacktraceToString(e))
        );
    }

    public static CheckException statusFail(String tokenUrl, int status) {
        return new CheckException(2,
                "接口状态码：" + status,
                tokenUrl + ":" + status
        );
    }

}
