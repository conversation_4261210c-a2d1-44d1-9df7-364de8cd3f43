package cn.dahe.check.exception;

import cn.dahe.enums.ContentFailTypeEnum;
import cn.dahe.enums.CheckContentStatusEnum;
import lombok.Getter;

/**
 * 获取附件异常基类
 */
@Getter
public class ContentGetException extends RuntimeException {

    private final Integer contentStatus;
    /**
     * 失败类型
     */
    private final Integer failType;
    /**
     * 错误详情
     */
    private final String errorDetail;

    public ContentGetException(Integer contentStatus, Integer failType, String message, String errorDetail) {
        super(message);
        this.contentStatus = contentStatus;
        this.failType = failType;
        this.errorDetail = errorDetail;
    }

    public static ContentGetException fileTypeFail(String reason) {
        return new ContentGetException(
                CheckContentStatusEnum.SAVE_FAIL_END.getValue(),
                ContentFailTypeEnum.CONTENT_GET_FAILED.getValue(),
                "不支持的文件类型",
                reason
        );
    }

    public static ContentGetException downloadFail(String reason) {
        return new ContentGetException(
                CheckContentStatusEnum.SAVE_FAIL_RETRY.getValue(),
                ContentFailTypeEnum.CONTENT_GET_FAILED.getValue(),
                "下载出错",
                reason);
    }

    public static ContentGetException downloadFailEnd(String reason) {
        return new ContentGetException(
                CheckContentStatusEnum.SAVE_FAIL_END.getValue(),
                ContentFailTypeEnum.CONTENT_GET_FAILED.getValue(),
                "下载出错",
                reason);
    }

    public static ContentGetException parseFail(String message, String reason) {
        return new ContentGetException(
                CheckContentStatusEnum.SAVE_FAIL_END.getValue(),
                ContentFailTypeEnum.CONTENT_PARSE_FAILED.getValue(),
                message,
                reason
        );
    }

    public static ContentGetException parseFailRetry(String message, String reason) {
        return new ContentGetException(
                CheckContentStatusEnum.SAVE_FAIL_RETRY.getValue(),
                ContentFailTypeEnum.CONTENT_PARSE_FAILED.getValue(),
                message,
                reason
        );
    }


}
