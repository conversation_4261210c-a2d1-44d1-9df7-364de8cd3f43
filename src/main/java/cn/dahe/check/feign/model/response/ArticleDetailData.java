package cn.dahe.check.feign.model.response;

import lombok.Data;

/**
 * 文章采集详情DTO
 */
@Data
public class ArticleDetailData {
    /**
     * 是否成功
     */
    private Boolean success;

    private String error;

    private Long column_id;

    private Long site_id;
    /**
     * 文章ID
     */
    private Long id;

    /**
     * 文章URL
     */
    private String url;

    /**
     * 文章标题
     */
    private String title;

    /**
     * 文章正文
     */
    private String content;

    /**
     * 发布时间
     */
    private String pub_time;

    /**
     * 创建时间
     */
    private String create_time;

    /**
     * 来源
     */
    private String source;

    /**
     * 编辑
     */
    private String editor;


}
