package cn.dahe.check.feign.model.response;

import lombok.Data;

import java.util.Map;

/**
 * 站点查询结果
 * {
 *       "query": "河南省人民",
 *       "site_id": {
 *         "1071": "河南省人民防空办公室",
 *         "1287": "河南省人民检察院",
 *         "401": "河南省人民政府外事办公室",
 *         "409": "河南省人民政府国有资产监督管理委员会",
 *         "4211": "河南省人民政府研究室",
 *         "4222": "河南省人民政府法制办公室",
 *         "4225": "河南省人民政府驻北京办事处",
 *         "4228": "河南省人民政府参事室",
 *         "6": "河南省人民政府"
 *       },
 *       "success": True,
 * }
 */
@Data
public class SiteQueryResponse {

    private String query;

    private Map<Integer, String> site_id;

    private Boolean success;
}
