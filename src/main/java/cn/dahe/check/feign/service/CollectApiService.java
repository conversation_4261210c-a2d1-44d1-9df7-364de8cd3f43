package cn.dahe.check.feign.service;

import cn.dahe.check.feign.config.FeignConfiguration;
import cn.dahe.check.feign.model.request.ArticleCollectRequest;
import cn.dahe.check.feign.model.request.ArticleDetailRequest;
import cn.dahe.check.feign.model.request.SiteQueryRequest;
import cn.dahe.check.feign.model.response.ArticleCollectResponse;
import cn.dahe.check.feign.model.response.ArticleDetailResponse;
import cn.dahe.check.feign.model.response.SiteQueryResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "collectApiService", url = "${anxun.collect-center.url}", configuration = FeignConfiguration.class)
public interface CollectApiService {
    /**
     * 复采
     */
    @PostMapping(value = "/article/collect", consumes = MediaType.APPLICATION_JSON_VALUE)
    ArticleCollectResponse articleCollect(@RequestBody ArticleCollectRequest request);

    /**
     * 获取文章详情
     */
    @PostMapping(value = "/article/detail", consumes = MediaType.APPLICATION_JSON_VALUE)
    ArticleDetailResponse articleDetail(@RequestBody ArticleDetailRequest request);


    @PostMapping(value = "/site/query", consumes = MediaType.APPLICATION_JSON_VALUE)
    SiteQueryResponse siteQuery(@RequestBody SiteQueryRequest request);
}
