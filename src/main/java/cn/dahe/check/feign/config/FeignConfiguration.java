package cn.dahe.check.feign.config;

import feign.RequestInterceptor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Feign配置类
 */
@EnableFeignClients(basePackages = "cn.dahe.check.feign.service")
@Configuration
public class FeignConfiguration {

    @Value("${anxun.collect-center.api-key}")
    private String apiKey;

    @Bean
    public RequestInterceptor requestInterceptor() {
        return requestTemplate ->
                requestTemplate.header("X-DaheCollection-Key", apiKey);
    }
}
