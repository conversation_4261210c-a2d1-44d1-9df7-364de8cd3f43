package cn.dahe.check.html;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class ErrorWordContext {

    @Schema(description = "上下文")
    private String context;
    @Schema(description = "错词在上下文中的相对位置")
    private int relativePosition;
    @Schema(description = "上下文在原文中的起始位置")
    private int contextStart;
    @Schema(description = "上下文在原文中的结束位置")
    private int contextEnd;


}
