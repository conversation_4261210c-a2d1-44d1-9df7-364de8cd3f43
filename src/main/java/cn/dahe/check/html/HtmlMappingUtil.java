package cn.dahe.check.html;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HtmlUtil;
import com.hankcs.hanlp.utility.SentencesUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.nodes.Node;
import org.jsoup.nodes.TextNode;

import java.util.ArrayList;
import java.util.List;

/**
 * HTML和纯文本映射工具类
 * 提供HTML到纯文本的转换，以及位置映射功能
 */
@Slf4j
public class HtmlMappingUtil {

    @Data
    public static class HtmlMappingResult {
        private final String plainText;           // 提取的纯文本
        private final List<Integer> plainToHtml;  // 纯文本位置到HTML位置的映射
        private final String compressedHtml;      // 压缩后的HTML

        public HtmlMappingResult(String plainText, List<Integer> plainToHtml, String compressedHtml) {
            this.plainText = plainText;
            this.plainToHtml = plainToHtml;
            this.compressedHtml = compressedHtml;
        }

        /**
         * 获取纯文本中指定位置在HTML中的对应位置
         */
        public Integer getHtmlPosition(int plainTextPosition) {
            if (plainTextPosition >= 0 && plainTextPosition < plainToHtml.size()) {
                return plainToHtml.get(plainTextPosition);
            }
            return null;
        }

        /**
         * 获取纯文本中词语在HTML中的起始和结束位置
         * TODO 包含的一些不完整标签 不太好处理，比如词中间有一半被加粗
         * TODO 测试 只包含开始标签问题不是很大，但只包含结束标签会使得颜色标注被截取，所以考虑清除掉结束标签
         */
        public int[] getHtmlRange(int plainStart, int plainLength) {
            if (plainStart < 0 || plainStart >= plainText.length() ||
                    plainStart + plainLength > plainText.length()) {
                return null;
            }

            Integer htmlStart = getHtmlPosition(plainStart);
            Integer htmlEnd = getHtmlPosition(plainStart + plainLength - 1);

            if (htmlStart == null || htmlEnd == null) {
                return null;
            }

            return new int[]{htmlStart, htmlEnd + 1};
        }
    }

    public static String compressHtml(String htmlContent) {
        //  安全上的处理，清理 TODO
        Document parse = Jsoup.parse(htmlContent);
        return parse.html();
    }

    /**
     * 方法1：将HTML转换为纯文本，并生成位置映射
     */
    public static HtmlMappingResult extractTextFromHtml(String html) {
        if (StrUtil.isBlank(html)) {
            return new HtmlMappingResult(StrUtil.EMPTY, CollUtil.newArrayList(), StrUtil.EMPTY);
        }
        // 使用Jsoup解析HTML

        Document doc = Jsoup.parse(html);
        // 压缩HTML（移除多余空白）
        String compressedHtml = doc.html();
        // compressedHtml = HtmlUtil.unescape(compressedHtml);
        //  长度可能变化，需要更新html
        // log.info("len1: {} len2: {}", html.length(), compressedHtml.length());
        return extractTextFromCompressedHtml(doc, compressedHtml);
    }

    public static HtmlMappingResult extractTextFromCompressedHtml(Element body, String compressedHtml) {
        StringBuilder plainTextBuilder = new StringBuilder();
        //  纯文本映射html
        List<Integer> plainToHtml = CollUtil.newArrayList();

        processNode(body, compressedHtml, plainTextBuilder, plainToHtml);

        return new HtmlMappingResult(
                plainTextBuilder.toString(),
                plainToHtml,
                compressedHtml
        );
    }

    private static void processNode(Node node, String html, StringBuilder plainTextBuilding,
                                    List<Integer> plainToHtml) {
        if (node instanceof TextNode) {
            TextNode textNode = (TextNode) node;
            // String text = textNode.text();
            String wholeText = textNode.getWholeText();
            String text = StrUtil.trim(HtmlUtil.unescape(wholeText));
            String htmlText = textNode.outerHtml();
            if (StrUtil.isNotBlank(text)) {
                //  找到上一个片段的结尾位置
                int htmlPos = CollUtil.isNotEmpty(plainToHtml) ? CollUtil.getLast(plainToHtml) : 0;
                //  找到这段文本的起始位置
                int htmlFindStart = html.indexOf(htmlText, htmlPos);
                if (htmlFindStart >= 0) {
                    List<String> textSplits = StrUtil.split(text, ' ');
                    int findStart = htmlFindStart;
                    for (String textSplit : textSplits) {
                        if (StrUtil.isEmpty(textSplit)) {
                            continue;
                        }
                        if (StrUtil.isNotBlank(textSplit)) {
                            textSplit = StrUtil.trim(textSplit);
                        }
                        // log.info("textSplit: {}", textSplit);
                        int splitStart = html.indexOf(textSplit, findStart);
                        // log.info("after: {} {}", splitStart, StrUtil.subSuf(html, splitStart));
                        if (splitStart >= 0) {
                            for (int i = 0; i < textSplit.length(); i++) {
                                plainToHtml.add(splitStart + i);
                            }
                            plainTextBuilding.append(textSplit);
                            findStart = splitStart + textSplit.length();
                        }
                    }
                }
            }
        }
        for (Node child : node.childNodes()) {
            String nodeName = child.nodeName();
            if (StrUtil.equalsAny(nodeName, "head", "script", "style", "meta", "link", "title")) {
                continue;
            }
            processNode(child, html, plainTextBuilding, plainToHtml);
        }
    }

    @Data
    public static class HtmlWordLocation {
        private final int startPosition;    // HTML中的起始位置
        private final int endPosition;      // HTML中的结束位置
        private final String htmlErrorWord; // HTML中的实际文本（可能包含标签）

        public HtmlWordLocation(int start, int end, String htmlWord) {
            this.startPosition = start;
            this.endPosition = end;
            this.htmlErrorWord = htmlWord;
        }
    }

    /**
     * 方法2：在HTML中定位纯文本位置的词语，并返回包含HTML标签的实际文本
     */
    public static HtmlWordLocation findInHtml(String word, int plainTextPosition, HtmlMappingResult mapping) {
        if (word == null || word.isEmpty() || mapping == null ||
                plainTextPosition < 0 || plainTextPosition >= mapping.getPlainText().length()) {
            return null;
        }

        // 验证词语在该位置是否匹配
        String plainText = mapping.getPlainText();
        if (!plainText.substring(plainTextPosition).startsWith(word)) {
            return null;
        }

        int[] range = mapping.getHtmlRange(plainTextPosition, word.length());
        if (range == null) {
            return null;
        }
        String compressedHtml = mapping.getCompressedHtml();
        log.info("compressedHtml:{} range:{}-{}", compressedHtml.length(), range[0], range[1]);
        // 获取HTML中的实际文本（包含可能的标签）
        String htmlWord = compressedHtml.substring(range[0], range[1]);
        log.info("htmlWord:{}", htmlWord);
        return new HtmlWordLocation(range[0], range[1], htmlWord);
    }

    @Data
    public static class SentenceLocation {
        private final String content;          // 句子内容
        private final int startPosition;       // 句子在纯文本中的起始位置
        private final int endPosition;         // 句子在纯文本中的结束位置

        public SentenceLocation(String content, int start, int end) {
            this.content = content;
            this.startPosition = start;
            this.endPosition = end;
        }
    }

    private static final int MAX_CONTEXT_LENGTH = 100;  // 上下文最大长度限制

    /**
     * 方法3：使用HanLP将纯文本分句，并保留每句的位置信息
     */
    public static List<SentenceLocation> splitSentences(String plainText) {
        if (StrUtil.isBlank(plainText)) {
            return new ArrayList<>();
        }

        List<SentenceLocation> sentences = new ArrayList<>();

        List<String> sentenceList = SentencesUtil.toSentenceList(plainText, false);

        int currentPosition = 0;
        for (String sentence : sentenceList) {
            if (!sentence.trim().isEmpty()) {
                int startPos = plainText.indexOf(sentence, currentPosition);
                if (startPos != -1) {
                    int endPos = startPos + sentence.length();
                    sentences.add(new SentenceLocation(sentence, startPos, endPos));
                    currentPosition = endPos;
                }
            }
        }

        return sentences;
    }

    private static final String SENTENCE_DELIMITERS = "。！？；.!?;";  // 分句分隔符

    /**
     * 方法4：获取错词的上下文（基于分句，并处理长句）
     *
     * @param sentences         已分好的句子列表
     * @param errorWordPosition 错词在纯文本中的起始位置
     * @param errorWordLength   错词长度
     * @return 错词的上下文信息
     */
    public static ErrorWordContext getErrorWordContext(List<SentenceLocation> sentences,
                                                       int errorWordPosition, int errorWordLength) {
        if (CollUtil.isEmpty(sentences) || errorWordPosition < 0) {
            return null;
        }

        // 找到错词所在的句子
        SentenceLocation targetSentence = null;
        for (SentenceLocation sentence : sentences) {
            if (errorWordPosition >= sentence.getStartPosition() &&
                    errorWordPosition < sentence.getEndPosition()) {
                targetSentence = sentence;
                break;
            }
        }

        if (targetSentence == null) {
            return null;
        }

        String sentence = targetSentence.getContent();
        int relativePos = errorWordPosition - targetSentence.getStartPosition();

        // 如果句子长度在限制范围内，直接返回整句
        if (sentence.length() <= MAX_CONTEXT_LENGTH) {
            return new ErrorWordContext(
                    sentence,
                    relativePos,
                    targetSentence.getStartPosition(),
                    targetSentence.getEndPosition()
            );
        }

        // 处理长句：先判断句子中是否包含标点符号
        boolean hasDelimiter = false;
        for (char c : sentence.toCharArray()) {
            if (SENTENCE_DELIMITERS.indexOf(c) != -1) {
                hasDelimiter = true;
                break;
            }
        }

        int contextStart = relativePos;
        int contextEnd = relativePos + errorWordLength;

        // 只有当句子中包含标点符号时，才进行标点分割
        if (hasDelimiter) {
            // 向前找最近的标点符号
            for (int i = relativePos - 1; i >= 0; i--) {
                if (SENTENCE_DELIMITERS.indexOf(sentence.charAt(i)) != -1 ||
                        relativePos - i > MAX_CONTEXT_LENGTH / 2) {
                    contextStart = i + 1;
                    break;
                }
            }

            // 向后找最近的标点符号
            for (int i = relativePos + errorWordLength; i < sentence.length(); i++) {
                if (SENTENCE_DELIMITERS.indexOf(sentence.charAt(i)) != -1 ||
                        i - relativePos > MAX_CONTEXT_LENGTH / 2) {
                    contextEnd = i + 1;
                    break;
                }
            }

            // 如果截取后长度超过限制，进行长度限制
            if (contextEnd - contextStart > MAX_CONTEXT_LENGTH) {
                if (relativePos - contextStart > MAX_CONTEXT_LENGTH / 2) {
                    contextStart = relativePos - MAX_CONTEXT_LENGTH / 2;
                }
                if (contextEnd - relativePos > MAX_CONTEXT_LENGTH / 2) {
                    contextEnd = relativePos + MAX_CONTEXT_LENGTH / 2;
                }
            }
        } else {
            // 如果没有标点符号，返回整个句子
            contextStart = 0;
            contextEnd = sentence.length();
        }

        String context = sentence.substring(contextStart, contextEnd);
        return new ErrorWordContext(
                context,
                relativePos - contextStart,
                targetSentence.getStartPosition() + contextStart,
                targetSentence.getStartPosition() + contextEnd
        );
    }

    /**
     * 获取错词的上下文并标记错词
     */
    @Data
    public static class MarkedErrorContext {
        private final String context;          // 原始上下文
        private final int contextStart;        // 上下文在原文中的起始位置
        private final int contextEnd;          // 上下文在原文中的结束位置

        public MarkedErrorContext(String context, int contextStart, int contextEnd) {
            this.context = context;
            this.contextStart = contextStart;
            this.contextEnd = contextEnd;
        }
    }


}