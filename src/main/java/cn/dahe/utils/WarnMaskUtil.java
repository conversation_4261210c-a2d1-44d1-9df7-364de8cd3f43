package cn.dahe.utils;

public class WarnMaskUtil {

    public static String phoneEncrypt(String phone) {
        if (phone == null || phone.length() < 8) {
            return phone;
        }
        return phone.substring(0, 3) + "****" + phone.substring(phone.length() - 4);
    }

    public static String openIdEncrypt(String openId) {
        if (openId == null || openId.length() < 4) {
            return openId;
        }
        return openId.substring(0, 2) + "*****" + openId.substring(openId.length() - 2);
    }
}


