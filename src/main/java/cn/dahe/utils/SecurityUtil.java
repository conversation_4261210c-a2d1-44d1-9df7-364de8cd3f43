package cn.dahe.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
@Slf4j
public class SecurityUtil {
    public static String MD5(String password){
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.update(password.getBytes());
            String key = new BigInteger(1, md.digest()).toString(16);
            return StringUtils.leftPad(key,32,"0");
        } catch (NoSuchAlgorithmException e) {
            log.info("MD5加密出错");
            e.printStackTrace();
        }
        return null;
    }

}
