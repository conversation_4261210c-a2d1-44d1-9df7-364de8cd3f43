package cn.dahe.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;

/**
 * <AUTHOR>
 * @date 2023-08-07
 */
public class NumberUtils {


    /**
     * 计算 num1 相对于 num2 的百分比（如：num1 / num2 * 100），支持自定义保留小数位数。
     *
     * @param numerator 分子（被除数）
     * @param denominator 分母（除数）
     * @param scale 保留的小数位数，如 0、1、2 等
     * @return 百分比字符串，如 "23.56%"；当 denominator 为 0 时返回 "-"
     */
    public static String calculateRatioPercentage(double numerator, double denominator, int scale) {
        if (denominator == 0) {
            return "-";
        }

        double result = (numerator / denominator) * 100;

        // 构造格式化字符串，如 "%.2f%%"
        String format = "%." + scale + "f%%";
        return String.format(format, result);
    }


    public static double parseNumberString(String numberStr) {
        if (numberStr == null || numberStr.isEmpty() || "-".equals(numberStr)) {
            return 0;
        }

        numberStr = numberStr.replaceAll(",", ""); // 移除可能的逗号分隔符

        BigDecimal multiplier = BigDecimal.ONE;
        if (numberStr.endsWith("万")) {
            multiplier = new BigDecimal("10000");
            numberStr = numberStr.replace("万", "");
        } else if (numberStr.endsWith("亿")) {
            multiplier = new BigDecimal("100000000");
            numberStr = numberStr.replace("亿", "");
        }

        BigDecimal value = new BigDecimal(numberStr).multiply(multiplier);
        return value.longValue();
    }


    /**
     * 计算 num1 与 num2 的倍数比值，并四舍五入返回 long 类型结果。
     * 例如：num1 = 350，num2 = 100，则 350 / 100 = 3.5，四舍五入后返回 4。
     * 如果 num2 为 0，返回 0，避免除以 0 的异常。
     *
     * @param num1 被除数
     * @param num2 除数
     * @return 四舍五入后的倍数比值（long 类型）
     */

    /**
     * 计算 num1 与 num2 的倍数比值，保留指定小数位数并返回 double。
     * 例如：num1 = 356，num2 = 100，scale = 2 → 返回 3.56。
     * 如果 num2 为 0，返回 null，避免除以 0。
     *
     * @param num1  被除数
     * @param num2  除数
     * @param scale 保留的小数位数（必须 >= 0）
     * @return 保留指定小数位的结果（double 类型）
     */
    public static Double calculateRoundedRatio(double num1, double num2, int scale) {
        if (num2 == 0 || scale < 0) {
            return null;
        }

        BigDecimal ratio = BigDecimal.valueOf(num1)
                .divide(BigDecimal.valueOf(num2), scale, RoundingMode.HALF_UP);
        return ratio.doubleValue();
    }


    /**
     * 将 Double 转为字符串，去除无意义的 .0，小数保留最多两位。
     * 如果为 null，则返回 "-"
     *
     * @param value 要转换的 Double 值
     * @return 格式化后的字符串
     */
    public static String formatDouble(Double value) {
        if (value == null) {
            return "-";
        }

        // 如果是整数则转换为 long 类型后转字符串，避免 .0
        if (value == Math.floor(value)) {
            return String.valueOf(value.longValue());
        }

        // 否则保留最多两位小数
        return new DecimalFormat("#.##").format(value);
    }


    /**
     * 将数字字符串转换为带单位的格式，如万、亿。
     *
     * @param numberStr 数字字符串，如 "12345678"
     * @return 格式化后的字符串，如 "1234.57万"
     */
    public static String formatToWanOrYi(String numberStr) {
        try {
            double num = Double.parseDouble(numberStr);
            if (num >= 100_000_000) {
                return String.format("%.2f亿", num / 100_000_000);
            } else if (num >= 10_000) {
                return String.format("%.2f万", num / 10_000);
            } else {
                return numberStr;
            }
        } catch (NumberFormatException e) {
            return numberStr; // 非数字直接返回原值
        }
    }

}
