package cn.dahe.utils;

import org.apache.commons.lang3.StringEscapeUtils;

/**
 * XSS过滤工具类
 * 提供手动过滤方法，适用于无法使用注解的场景
 *
 * <AUTHOR>
 */
public class XssFilterUtil {

    /**
     * HTML4转义（默认推荐）
     */
    public static String escapeHtml4(String value) {
        return value != null ? StringEscapeUtils.escapeHtml4(value) : null;
    }

    /**
     * HTML转义
     */
    public static String escapeHtml(String value) {
        return value != null ? StringEscapeUtils.escapeHtml4(value) : null;
    }

    /**
     * JavaScript转义
     */
    public static String escapeJavaScript(String value) {
        return value != null ? StringEscapeUtils.escapeHtml4(value) : null;
    }

    /**
     * 批量过滤字符串数组
     */
    public static String[] escapeHtml4Batch(String... values) {
        if (values == null) {
            return null;
        }
        String[] result = new String[values.length];
        for (int i = 0; i < values.length; i++) {
            result[i] = escapeHtml4(values[i]);
        }
        return result;
    }

    /**
     * 过滤多个String参数（返回数组）
     */
    public static String[] filterStrings(String... strings) {
        return escapeHtml4Batch(strings);
    }

    /**
     * 过滤并返回第一个参数（适用于单个参数过滤）
     */
    public static String filter(String value) {
        return escapeHtml4(value);
    }
}
