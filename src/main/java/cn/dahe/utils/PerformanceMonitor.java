package cn.dahe.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 性能监控工具类
 * 用于监控接口和方法的性能指标
 *
 * <AUTHOR>
 * @date 2025-09-09
 */
@Slf4j
@Component
public class PerformanceMonitor {

    private static final ConcurrentHashMap<String, AtomicLong> METHOD_CALL_COUNT = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<String, AtomicLong> METHOD_TOTAL_TIME = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<String, AtomicLong> METHOD_MAX_TIME = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<String, AtomicLong> METHOD_MIN_TIME = new ConcurrentHashMap<>();

    /**
     * 记录方法执行时间
     *
     * @param methodName 方法名
     * @param executeTime 执行时间（毫秒）
     */
    public static void recordMethodTime(String methodName, long executeTime) {
        METHOD_CALL_COUNT.computeIfAbsent(methodName, k -> new AtomicLong(0)).incrementAndGet();
        METHOD_TOTAL_TIME.computeIfAbsent(methodName, k -> new AtomicLong(0)).addAndGet(executeTime);
        
        // 更新最大执行时间
        METHOD_MAX_TIME.computeIfAbsent(methodName, k -> new AtomicLong(0))
                .updateAndGet(current -> Math.max(current, executeTime));
        
        // 更新最小执行时间
        METHOD_MIN_TIME.computeIfAbsent(methodName, k -> new AtomicLong(Long.MAX_VALUE))
                .updateAndGet(current -> current == Long.MAX_VALUE ? executeTime : Math.min(current, executeTime));
        
        log.debug("方法 {} 执行耗时: {}ms", methodName, executeTime);
    }

    /**
     * 获取方法平均执行时间
     *
     * @param methodName 方法名
     * @return 平均执行时间（毫秒）
     */
    public static double getAverageTime(String methodName) {
        AtomicLong totalTime = METHOD_TOTAL_TIME.get(methodName);
        AtomicLong callCount = METHOD_CALL_COUNT.get(methodName);
        
        if (totalTime == null || callCount == null || callCount.get() == 0) {
            return 0.0;
        }
        
        return (double) totalTime.get() / callCount.get();
    }

    /**
     * 获取方法调用次数
     *
     * @param methodName 方法名
     * @return 调用次数
     */
    public static long getCallCount(String methodName) {
        AtomicLong count = METHOD_CALL_COUNT.get(methodName);
        return count != null ? count.get() : 0;
    }

    /**
     * 获取方法最大执行时间
     *
     * @param methodName 方法名
     * @return 最大执行时间（毫秒）
     */
    public static long getMaxTime(String methodName) {
        AtomicLong maxTime = METHOD_MAX_TIME.get(methodName);
        return maxTime != null ? maxTime.get() : 0;
    }

    /**
     * 获取方法最小执行时间
     *
     * @param methodName 方法名
     * @return 最小执行时间（毫秒）
     */
    public static long getMinTime(String methodName) {
        AtomicLong minTime = METHOD_MIN_TIME.get(methodName);
        return minTime != null && minTime.get() != Long.MAX_VALUE ? minTime.get() : 0;
    }

    /**
     * 打印性能统计信息
     *
     * @param methodName 方法名
     */
    public static void printStatistics(String methodName) {
        long callCount = getCallCount(methodName);
        double avgTime = getAverageTime(methodName);
        long maxTime = getMaxTime(methodName);
        long minTime = getMinTime(methodName);
        
        log.info("=== 性能统计 [{}] ===", methodName);
        log.info("调用次数: {}", callCount);
        log.info("平均耗时: {:.2f}ms", avgTime);
        log.info("最大耗时: {}ms", maxTime);
        log.info("最小耗时: {}ms", minTime);
        log.info("========================");
    }

    /**
     * 清除指定方法的统计信息
     *
     * @param methodName 方法名
     */
    public static void clearStatistics(String methodName) {
        METHOD_CALL_COUNT.remove(methodName);
        METHOD_TOTAL_TIME.remove(methodName);
        METHOD_MAX_TIME.remove(methodName);
        METHOD_MIN_TIME.remove(methodName);
        log.info("已清除方法 {} 的性能统计信息", methodName);
    }

    /**
     * 清除所有统计信息
     */
    public static void clearAllStatistics() {
        METHOD_CALL_COUNT.clear();
        METHOD_TOTAL_TIME.clear();
        METHOD_MAX_TIME.clear();
        METHOD_MIN_TIME.clear();
        log.info("已清除所有性能统计信息");
    }

    /**
     * 性能监控装饰器
     * 使用方式：
     * <pre>
     * PerformanceMonitor.monitor("methodName", () -> {
     *     // 执行业务逻辑
     *     return result;
     * });
     * </pre>
     */
    public static <T> T monitor(String methodName, java.util.function.Supplier<T> supplier) {
        long startTime = System.currentTimeMillis();
        try {
            return supplier.get();
        } finally {
            long endTime = System.currentTimeMillis();
            recordMethodTime(methodName, endTime - startTime);
        }
    }

    /**
     * 无返回值的性能监控装饰器
     */
    public static void monitor(String methodName, Runnable runnable) {
        long startTime = System.currentTimeMillis();
        try {
            runnable.run();
        } finally {
            long endTime = System.currentTimeMillis();
            recordMethodTime(methodName, endTime - startTime);
        }
    }
}