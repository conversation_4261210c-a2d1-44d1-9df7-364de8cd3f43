package cn.dahe.utils;

import cn.dahe.common.constants.DateConstant;
import cn.hutool.core.codec.Base64;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import lombok.SneakyThrows;
import org.apache.commons.lang3.time.DateFormatUtils;


import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 处理指定季度、月份起止日期
 */
public class TimeUtil {


    public static final String START_TIME = "startTime";
    public static final String END_TIME = "endTime";

    // 季度
    public static final int KIND_QUARTER = 0;
    // 月份
    public static final int KIND_MONTH = 1;
    // 年份
    public static final int KIND_YEAR = 2;


    /**
     * 获取指定年份指定季度 开始、结束 日期 字符串格式
     *
     * @param kind          0 季度，1 月份
     * @param year          年份
     * @param number        季度
     * @param formatPattern 格式化类型
     * @param begin         开始或结束日期
     * @return 日期
     */
    public static String getTimeByQuarterString(int kind, String year, int number, String formatPattern, boolean begin) {
        Map<String, Date> map = new HashMap<>();
        if (kind == KIND_QUARTER) {
            map = getTimeByQuarter(year, number);

        } else {
            map = getTimeByMonth(year, number);
        }
        if (begin) {
            return DateUtil.format(map.get(START_TIME), formatPattern);
        } else {
            return DateUtil.format(map.get(END_TIME), formatPattern);
        }


    }


    /**
     * 获取指定年份指定季度 开始时间、结束时间
     *
     * @param year
     * @param year
     * @return
     */
    public static Map<String, Date> getTimeByYear(String year) {
        Map<String, Date> map = new HashMap<>();
        //获取开始时间
        Date startTime = getCurrentYearStartTime(year);
        Date endTime = getCurrentYearEndTime(year);
        map.put(START_TIME, startTime);
        map.put(END_TIME, endTime);
        return map;
    }

    /**
     * 获取指定年份指定季度 开始时间、结束时间
     *
     * @param year
     * @param quarter
     * @return
     */
    public static Map<String, Date> getTimeByQuarter(String year, int quarter) {
        Map<String, Date> map = new HashMap<>();
        SimpleDateFormat format = new SimpleDateFormat("yyyy");
        Date date;
        try {
            date = format.parse(year);
        } catch (Exception e) {
            System.out.println("err:TimeUtil.class-----{getTimeByQuarter}----" + e.getMessage());
            date = new Date();
        }
        //获取开始时间
        Date startTime = getQuarterTime(date, quarter, 0);
        Date endTime = getQuarterTime(date, quarter, 1);
        map.put(START_TIME, startTime);
        map.put(END_TIME, endTime);
        return map;
    }

    /**
     * 获取指定年份指定月份 开始时间、结束时间
     *
     * @param year
     * @param month
     * @return
     */
    public static Map<String, Date> getTimeByMonth(String year, int month) {
        Map<String, Date> map = new HashMap<>();
        SimpleDateFormat format = new SimpleDateFormat("yyyy");
        Date date;
        try {
            date = format.parse(year);
        } catch (Exception e) {
            System.out.println("err:TimeUtil.class---{getTimeByMonth}------" + e.getMessage());
            date = new Date();
        }
        //获取开始时间
        Date startTime = getMonthTime(date, month, 0);
        Date endTime = getMonthTime(date, month, 1);
        map.put("startTime", startTime);
        map.put("endTime", endTime);
        return map;
    }

    /**
     * 获取指定季度的具体时间(区分开始时间、结束时间)
     *
     * @param date
     * @param quarter 季度 (1-4)
     * @param type    0:开始；1：结束
     * @return
     */
    private static Date getQuarterTime(Date date, int quarter, int type) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        //获取开始时间
        if (type == 0) {
            //00:00:00:0000
            setBeginHms(calendar);
            //第一季度
            if (quarter == 1) {
                calendar.set(Calendar.MONTH, 0);
                calendar.set(Calendar.DAY_OF_MONTH, 1);
                return calendar.getTime();
            } else if (quarter == 2) {
                calendar.set(Calendar.MONTH, 3);
                calendar.set(Calendar.DAY_OF_MONTH, 1);
                return calendar.getTime();
            } else if (quarter == 3) {
                calendar.set(Calendar.MONTH, 6);
                calendar.set(Calendar.DAY_OF_MONTH, 1);
                return calendar.getTime();
            } else if (quarter == 4) {
                calendar.set(Calendar.MONTH, 9);
                calendar.set(Calendar.DAY_OF_MONTH, 1);
                return calendar.getTime();
            } else {
                System.out.println("TimeUtil.class 出现错误-----{getQuarterTime}-----{季度有误！}");
                return date;
            }
        }
        //获取结束时间 23:59:59:999
        else {
            setEndHms(calendar);
            //第一季度
            if (quarter == 1) {
                calendar.set(Calendar.MONTH, 3);
                calendar.set(Calendar.DATE, 0);
                return calendar.getTime();
            } else if (quarter == 2) {
                calendar.set(Calendar.MONTH, 6);
                calendar.set(Calendar.DATE, 0);
                return calendar.getTime();
            } else if (quarter == 3) {
                calendar.set(Calendar.MONTH, 9);
                calendar.set(Calendar.DATE, 0);
                return calendar.getTime();
            } else if (quarter == 4) {
                calendar.set(Calendar.MONTH, 11);
                calendar.set(Calendar.DATE, calendar.getActualMaximum(Calendar.DATE));
                return calendar.getTime();
            } else {
                System.out.println("TimeUtil.class 出现错误-----{getQuarterTime}-----{季度有误！}");
                return date;
            }
        }
    }

    /**
     * 获取指定月份的具体时间(区分开始时间、结束时间)
     *
     * @param date
     * @param month 月份（1-12）
     * @param type  0:开始；1：结束
     * @return
     */
    private static Date getMonthTime(Date date, int month, int type) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        //
        if (!(month >= 1 && month <= 12)) {
            System.out.println("TimeUtil.class 异常------{getMonthTime}------{月份有误！}");
            return date;
        }
        calendar.set(Calendar.MONTH, month - 1);
        //处理月份
        if (type == 0) {
            //00:00:00:0000
            setBeginHms(calendar);
            calendar.set(Calendar.DAY_OF_MONTH, 1);
        }
        //获取结束时间 23:59:59:999
        else {
            setEndHms(calendar);
            calendar.set(Calendar.DATE, calendar.getActualMaximum(Calendar.DATE));
        }
        return calendar.getTime();
    }

    /**
     * 指定年的开始时间
     *
     * @return
     */
    public static Date getCurrentYearStartTime(String year) {
        Calendar calendar = Calendar.getInstance();
        try {
            calendar.set(Calendar.YEAR, Integer.valueOf(year));
            calendar.set(Calendar.MONTH, 0);
            calendar.set(Calendar.DATE, 1);
            setBeginHms(calendar);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return calendar.getTime();
    }

    /**
     * 指定年的结束时间
     *
     * @return
     */
    public static Date getCurrentYearEndTime(String year) {
        Calendar calendar = Calendar.getInstance();
        try {
            calendar.set(Calendar.YEAR, Integer.valueOf(year));
            calendar.set(Calendar.MONTH, 11);
            calendar.set(Calendar.DATE, 31);
            setEndHms(calendar);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return calendar.getTime();
    }


    /**
     * 指定年的开始时间
     *
     * @return
     */
    public static Date getDayStartTime(int year, int month, int day) {
        Calendar calendar = Calendar.getInstance();
        try {
            calendar.set(Calendar.YEAR, year);
            calendar.set(Calendar.MONTH, month - 1);
            calendar.set(Calendar.DATE, day);
            setBeginHms(calendar);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return calendar.getTime();
    }

    /**
     * 获取指定周的开始时间
     *
     * @return
     */
    public static Date getWeekBegin(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.setTime(date);
        calendar.setFirstDayOfWeek(Calendar.MONDAY);
        calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        setBeginHms(calendar);
        return calendar.getTime();
    }

    /**
     * 获取指定周的结束时间
     *
     * @return
     */
    public static Date getWeekEnd(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.setTime(date);
        calendar.setFirstDayOfWeek(Calendar.MONDAY);
        calendar.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);
        setEndHms(calendar);
        return calendar.getTime();
    }


    /**
     * 获取指定月的开始时间
     *
     * @return
     */
    public static Date getMonthStart(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.setTime(date);
        calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), 1);
        setBeginHms(calendar);
        return calendar.getTime();
    }

    /**
     * 获取指定月的结束时间
     *
     * @return
     */
    public static Date getMonthEnd(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.setTime(date);
        int maxMonthDay = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
        calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), maxMonthDay);
        setEndHms(calendar);
        return calendar.getTime();
    }


    public static Date getDayBegin(Date time) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(time);
        setBeginHms(cal);
        return cal.getTime();
    }

    public static Date getDayEnd(Date time) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(time);
        setEndHms(cal);
        return cal.getTime();
    }


    /**
     * 计算某日期所在季度开始日期
     */
    public static Date getQuarterEnd(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int month = calendar.get(Calendar.MONTH);
        calendar.set(Calendar.MONTH, (month + 3) / 3 * 3);
        calendar.set(Calendar.DATE, 1);
        setEndHms(calendar);

        return new Date(calendar.getTime().getTime() - 24 * 60 * 60 * 1000);
    }

    /**
     * 计算某日期所在季度结束日期
     */
    public static Date getQuarterStart(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int month = calendar.get(Calendar.MONTH);
        calendar.set(Calendar.MONTH, month / 3 * 3);
        calendar.set(Calendar.DATE, 1);
        setBeginHms(calendar);
        return calendar.getTime();
    }

    /**
     * 获取指定年份，指定季度，指定月份的开始和结束时间
     *
     * @param year    （2023）年份 不传值的时候为当前月份
     * @param month   （1-12） 0：不筛选月
     * @param quarter （1-4） 0：不筛选季度
     * @return map(" startTime " : " 开始时间 ", " endTime " : " 结束时间 ")
     */
    public static Map<String, Date> getStartAndEndTime(String year, int month, int quarter) {
        if (StringUtils.isBlank(year)) {
            year = String.valueOf(Calendar.getInstance().get(Calendar.YEAR));
        }
        if (month != 0) {
            return getTimeByMonth(year, month);
        } else if (quarter != 0) {
            return getTimeByQuarter(year, quarter);
        } else {
            return getTimeByYear(year);
        }
    }

    /**
     * 根据类型获取指定年份，指定季度，指定月份的开始结束时间
     *
     * @param year    （2023）年份 不传值的时候为当前月份
     * @param month   （1-12） 0：不筛选月
     * @param quarter （1-4） 0：不筛选季度
     * @return map(" startTime " : " 开始时间 ", " endTime " : " 结束时间 ")
     */
    @SneakyThrows
    public static String getTimeByType(String year, int quarter, int month, int type) {
        if (StringUtils.isBlank(year)) {
            year = String.valueOf(Calendar.getInstance().get(Calendar.YEAR));
        }
        SimpleDateFormat format = new SimpleDateFormat("yyyy");
        Date date = format.parse(year);
        if (month != 0) {
            date = getMonthTime(date, month, type);
        } else if (quarter != 0) {
            date = getQuarterTime(date, quarter, type);
        } else {
            date = type == 0 ? getCurrentYearStartTime(year) : getCurrentYearEndTime(year);
        }
        return DateFormatUtils.format(date, DateConstant.DATE_YMD_HSM);
    }


    /**
     * 设置起始时分秒 0:0:0:0
     *
     * @param calendar
     */
    public static void setBeginHms(Calendar calendar) {
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
    }

    /**
     * 设置结束时分秒 23:59:59:999
     *
     * @param calendar
     */
    public static void setEndHms(Calendar calendar) {
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.MILLISECOND, 999);
    }


    /**
     * 获取指定日期的年份
     *
     * @param date 指定日期
     * @return 返回年份
     */
    public static int getYear(Date date) {
        int year = 0;
        try {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            year = calendar.get(Calendar.YEAR);
        } catch (Exception e) {
            System.out.println("TimeUtil.class 异常------{getYear}---获取年份时发生异常：" + e.getMessage());
        }
        return year;
    }

    /**
     * 获取指定日期的月份
     *
     * @param date 指定日期
     * @return 返回月份
     */
    public static int getMonth(Date date) {
        int month = 0;
        try {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            month = calendar.get(Calendar.MONTH) + 1; // 月份从0开始计数，需要加上1
        } catch (Exception e) {
            System.out.println("TimeUtil.class 异常------{getMonth}---获取月份时发生异常：" + e.getMessage());
        }
        return month;
    }


}
