// //
// // Source code recreated from a .class file by IntelliJ IDEA
// // (powered by FernFlower decompiler)
// //
//
// package cn.dahe.utils.ees;
//
// import com.alibaba.fastjson.JSON;
// import com.amazonaws.AmazonServiceException;
// import com.amazonaws.SdkClientException;
// import com.amazonaws.auth.AWSCredentials;
// import com.amazonaws.auth.AWSStaticCredentialsProvider;
// import com.amazonaws.auth.BasicAWSCredentials;
// import com.amazonaws.auth.DefaultAWSCredentialsProviderChain;
// import com.amazonaws.auth.profile.ProfilesConfigFile;
// import com.amazonaws.auth.profile.internal.BasicProfile;
// import com.amazonaws.client.builder.AwsClientBuilder;
// import com.amazonaws.regions.Regions;
// import com.amazonaws.services.s3.AmazonS3;
// import com.amazonaws.services.s3.AmazonS3ClientBuilder;
// import com.amazonaws.services.s3.model.PutObjectRequest;
// import com.amazonaws.services.s3.transfer.TransferManager;
// import com.amazonaws.services.s3.transfer.TransferManagerBuilder;
// import com.amazonaws.util.StringUtils;
//
//
// import java.io.File;
// import java.io.IOException;
// import java.io.UnsupportedEncodingException;
// import java.util.HashMap;
// import java.util.Map;
// import java.util.TreeMap;
// import java.util.UUID;
//
// public class UploadManager {
//     private AmazonS3 s3;
//     private String apiEndpoint;
//     private String accessKey;
//     private String secretKey;
//     private String wcsEndpoint;
//
//     public AmazonS3 getS3() {
//         return this.s3;
//     }
//
//     public UploadManager() {
//         this((String)null, (String)null, (String)null, (String)null);
//     }
//
//     public UploadManager(String wcsEndpoint, String apiEndpoint) {
//         this((String)null, (String)null, wcsEndpoint, apiEndpoint);
//     }
//
//     public UploadManager(String accessKey, String secretKey, String wcsEndpoint, String apiEndpoint) {
//         AmazonS3ClientBuilder s3ClientBuilder = AmazonS3ClientBuilder.standard();
//         BasicProfile profile = null;
//
//         try {
//             ProfilesConfigFile profilesConfigFile = new ProfilesConfigFile();
//             profile = (BasicProfile)profilesConfigFile.getAllBasicProfiles().get("default");
//         } catch (Exception var8) {
//         }
//
//         this.initAwsAkSk(s3ClientBuilder, StringUtils.trim(accessKey), StringUtils.trim(secretKey));
//         this.initEesAkSk(profile);
//         this.initEndpoint(s3ClientBuilder, StringUtils.trim(wcsEndpoint), StringUtils.trim(apiEndpoint), profile);
//         s3ClientBuilder.withPathStyleAccessEnabled(true);
//         this.s3 = (AmazonS3)s3ClientBuilder.build();
//     }
//
//     private void initEndpoint(AmazonS3ClientBuilder s3ClientBuilder, String wcsEndpoint, String apiEndpoint, BasicProfile profile) {
//         if (StringUtils.hasValue(wcsEndpoint) && StringUtils.hasValue(apiEndpoint)) {
//             s3ClientBuilder.withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(wcsEndpoint, String.valueOf(Regions.DEFAULT_REGION)));
//             this.apiEndpoint = apiEndpoint;
//             this.wcsEndpoint = wcsEndpoint;
//         } else {
//             wcsEndpoint = StringUtils.trim(System.getenv("EES_WCS_ENDPOINT"));
//             apiEndpoint = StringUtils.trim(System.getenv("EES_API_ENDPOINT"));
//             if (StringUtils.hasValue(wcsEndpoint) && StringUtils.hasValue(apiEndpoint)) {
//                 s3ClientBuilder.withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(wcsEndpoint, String.valueOf(Regions.DEFAULT_REGION)));
//                 this.apiEndpoint = apiEndpoint;
//                 this.wcsEndpoint = wcsEndpoint;
//             } else {
//                 wcsEndpoint = StringUtils.trim(System.getProperty("EES_WCS_ENDPOINT"));
//                 apiEndpoint = StringUtils.trim(System.getProperty("EES_API_ENDPOINT"));
//                 if (StringUtils.hasValue(wcsEndpoint) && StringUtils.hasValue(apiEndpoint)) {
//                     s3ClientBuilder.withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(wcsEndpoint, String.valueOf(Regions.DEFAULT_REGION)));
//                     this.apiEndpoint = apiEndpoint;
//                     this.wcsEndpoint = wcsEndpoint;
//                 } else {
//                     if (profile != null) {
//                         wcsEndpoint = StringUtils.trim(profile.getPropertyValue("EES_WCS_ENDPOINT"));
//                         apiEndpoint = StringUtils.trim(profile.getPropertyValue("EES_API_ENDPOINT"));
//                         if (StringUtils.hasValue(wcsEndpoint) && StringUtils.hasValue(apiEndpoint)) {
//                             s3ClientBuilder.withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(wcsEndpoint, String.valueOf(Regions.DEFAULT_REGION)));
//                             this.apiEndpoint = apiEndpoint;
//                             this.wcsEndpoint = wcsEndpoint;
//                         }
//                     }
//
//                 }
//             }
//         }
//     }
//
//     private void initEesAkSk(BasicProfile profile) {
//         String accessKey = StringUtils.trim(System.getenv("EES_ACCESSKEY_ID"));
//         String secretKey = StringUtils.trim(System.getenv("EES_ACCESSKEY_SECRET"));
//         if (StringUtils.hasValue(accessKey) && StringUtils.hasValue(secretKey)) {
//             RequestClient.eesAccesskeyId = accessKey;
//             RequestClient.eesAccesskeySecret = secretKey;
//         } else {
//             accessKey = StringUtils.trim(System.getProperty("EES_ACCESSKEY_ID"));
//             secretKey = StringUtils.trim(System.getProperty("EES_ACCESSKEY_SECRET"));
//             if (StringUtils.hasValue(accessKey) && StringUtils.hasValue(secretKey)) {
//                 RequestClient.eesAccesskeyId = accessKey;
//                 RequestClient.eesAccesskeySecret = secretKey;
//             } else {
//                 if (profile != null) {
//                     accessKey = StringUtils.trim(profile.getPropertyValue("EES_ACCESSKEY_ID"));
//                     secretKey = StringUtils.trim(profile.getPropertyValue("EES_ACCESSKEY_SECRET"));
//                     if (StringUtils.hasValue(accessKey) && StringUtils.hasValue(secretKey)) {
//                         RequestClient.eesAccesskeyId = accessKey;
//                         RequestClient.eesAccesskeySecret = secretKey;
//                     }
//                 }
//
//             }
//         }
//     }
//
//     private void initAwsAkSk(AmazonS3ClientBuilder s3ClientBuilder, String accessKey, String secretKey) {
//         if (StringUtils.hasValue(accessKey) && StringUtils.hasValue(secretKey)) {
//             s3ClientBuilder.withCredentials(new AWSStaticCredentialsProvider(new BasicAWSCredentials(accessKey, secretKey)));
//             this.accessKey = accessKey;
//             this.secretKey = secretKey;
//         } else {
//             AWSCredentials awsCredentials = DefaultAWSCredentialsProviderChain.getInstance().getCredentials();
//             this.accessKey = awsCredentials.getAWSAccessKeyId();
//             this.secretKey = awsCredentials.getAWSSecretKey();
//         }
//     }
//
//     public String put(String bucket_name, String key_name, String localFilePath, Map<String, String> params) throws IOException {
//         try {
//             TransferManager tm = TransferManagerBuilder.standard().withS3Client(this.s3).build();
//             PutObjectRequest request = new PutObjectRequest(bucket_name, key_name, new File(localFilePath));
//             String task_id = UUID.randomUUID().toString().replaceAll("-", "");
//             this.startTaskById(task_id);
//             request.setGeneralProgressListener((progressEvent) -> {
//                 if (progressEvent.getEventType().toString().equals("TRANSFER_COMPLETED_EVENT")) {
//                     try {
//                         this.taskSubmit(bucket_name, key_name, params, task_id);
//                     } catch (UnsupportedEncodingException var7) {
//                         var7.printStackTrace();
//                     }
//                 }
//
//             });
//             tm.upload(request);
//             return task_id;
//         } catch (AmazonServiceException var8) {
//             var8.printStackTrace();
//         } catch (SdkClientException var9) {
//             var9.printStackTrace();
//         }
//
//         return "";
//     }
//
//     public String getTaskByTaskId(String taskId) throws UnsupportedEncodingException {
//         Map<String, Object> MDParams = new TreeMap();
//         RequestClient client = new RequestClient();
//         String sign = client.makeUrl(MDParams);
//         return HttpUtil.get(this.apiEndpoint + "/wct/task/getByTaskId/" + taskId + "?" + sign);
//     }
//
//     private String startTaskById(String taskId) throws UnsupportedEncodingException {
//         Map<String, Object> MDParams = new TreeMap();
//         RequestClient client = new RequestClient();
//         String sign = client.makeUrl(MDParams);
//         return HttpUtil.get(this.apiEndpoint + "/wct/task/start/" + taskId + "?" + sign);
//     }
//
//     private String taskSubmit(String bucket_name, String key_name, Map<String, String> params, String task_id) throws UnsupportedEncodingException {
//         Map<String, String> out = new HashMap();
//         out.put("url", this.wcsEndpoint);
//         out.put("ak", this.accessKey);
//         out.put("sk", this.secretKey);
//         out.put("bucket", bucket_name);
//         out.put("key", key_name);
//         out.put("outKey", params.getOrDefault("outKey", key_name));
//         Map<String, String> callback = new HashMap();
//         callback.put("type", "standard");
//         callback.put("url", params.getOrDefault("callbackUrl", ""));
//         callback.put("headTokenKey", params.getOrDefault("callbackHeadTokenKey", ""));
//         callback.put("headTokenValue", params.getOrDefault("callbackHeadTokenValue", ""));
//         Map<String, Object> jsonParams = new HashMap();
//         jsonParams.put("taskId", task_id);
//         jsonParams.put("fopsType", params.get("fopsType"));
//         jsonParams.put("fops", params.get("fops"));
//         jsonParams.put("out", out);
//         jsonParams.put("callback", callback);
//         jsonParams.put("extendParam", params.get("extendParam"));
//         String param = JSON.toJSONString(jsonParams);
//         Map<String, Object> MDParams = new TreeMap();
//         RequestClient client = new RequestClient();
//         String sign = client.makeUrl(MDParams);
//         return HttpUtil.post(this.apiEndpoint + "/wct/task/submit/?" + sign, param);
//     }
//
//     public String TaskSubmit(String bucket_name, String key_name, Map<String, String> params) throws UnsupportedEncodingException {
//         String task_id = UUID.randomUUID().toString().replaceAll("-", "");
//         Map<String, String> out = new HashMap();
//         out.put("url", this.wcsEndpoint);
//         out.put("ak", this.accessKey);
//         out.put("sk", this.secretKey);
//         out.put("bucket", bucket_name);
//         out.put("key", key_name);
//         out.put("outKey", params.getOrDefault("outKey", key_name));
//         Map<String, String> callback = new HashMap();
//         callback.put("type", "standard");
//         callback.put("url", params.getOrDefault("callbackUrl", ""));
//         callback.put("headTokenKey", params.getOrDefault("callbackHeadTokenKey", ""));
//         callback.put("headTokenValue", params.getOrDefault("callbackHeadTokenValue", ""));
//         Map<String, Object> jsonParams = new HashMap();
//         jsonParams.put("taskId", task_id);
//         jsonParams.put("fopsType", params.get("fopsType"));
//         jsonParams.put("fops", params.get("fops"));
//         jsonParams.put("out", out);
//         jsonParams.put("callback", callback);
//         jsonParams.put("extendParam", params.get("extendParam"));
//         String param = JSON.toJSONString(jsonParams);
//         Map<String, Object> MDParams = new TreeMap();
//         RequestClient client = new RequestClient();
//         String sign = client.makeUrl(MDParams);
//         return HttpUtil.post(this.apiEndpoint + "/wct/task/submit/?" + sign, param);
//     }
// }
