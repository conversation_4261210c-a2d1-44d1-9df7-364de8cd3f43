package cn.dahe.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.tomcat.util.codec.binary.Base64;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import javax.crypto.*;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.util.Arrays;


@Slf4j
public class SM4Util {

    static {
        Security.addProvider(new BouncyCastleProvider());
    }

    private static final String ENCODING = "UTF-8";
    public static final String ALGORITHM_NAME = "SM4";
    // 加密算法/分组加密模式/分组填充方式
    // PKCS5Padding-以8个字节为一组进行分组加密
    // 定义分组加密模式使用：PKCS5Padding
    public static final String ALGORITHM_NAME_ECB_PADDING = "SM4/ECB/PKCS5Padding";
    // 128-32位16进制；256-64位16进制
    public static final int DEFAULT_KEY_SIZE = 128;
    private static final BouncyCastleProvider PROVIDER = new BouncyCastleProvider();
    // 自定义密钥或使用generateKey生成密钥
    public static String secret_key = "Glv#^ct@JYS^Bk7o";

    /**
     * 自动生成密钥
     *
     * @return
     * @throws NoSuchAlgorithmException
     * @throws NoSuchProviderException
     * @explain
     */
    public static byte[] generateKey() throws Exception {
        return generateKey(DEFAULT_KEY_SIZE);
    }

    /**
     * @param keySize
     * @return
     * @throws Exception
     * @explain
     */
    public static byte[] generateKey(int keySize) throws Exception {
        KeyGenerator kg = KeyGenerator.getInstance(ALGORITHM_NAME, BouncyCastleProvider.PROVIDER_NAME);
        kg.init(keySize, new SecureRandom());
        return kg.generateKey().getEncoded();
    }


    /**
     * 加密
     *
     * @param content 加密的字符串
     */
    public static String encrypt(String content) {
        if (StringUtils.isBlank(content)) {
            return "";
        }
        //设置Cipher对象
        Cipher cipher = null;
        byte[] b = new byte[0];
        try {
            cipher = Cipher.getInstance(ALGORITHM_NAME_ECB_PADDING, PROVIDER);
            cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(secret_key.getBytes(), ALGORITHM_NAME));

            //调用doFinal
            b = cipher.doFinal(content.getBytes(StandardCharsets.UTF_8));
        } catch (NoSuchAlgorithmException | InvalidKeyException | NoSuchPaddingException | IllegalBlockSizeException |
                BadPaddingException e) {
            throw new RuntimeException(e);
        }


        // 转base64
        return Base64.encodeBase64String(b);
    }

    /**
     * 解密
     *
     * @param encryptStr 解密的字符串
     */
    public static String decrypt(String encryptStr) {

        if (StringUtils.isBlank(encryptStr)) {
            return "";
        }
        //base64格式的key字符串转byte
        byte[] decodeBase64 = new byte[0];
        try {
            decodeBase64 = Base64.decodeBase64(encryptStr);
        } catch (Exception e) {
            log.error(">>>>>>>>>sm4字符串base64解码失败:{}<<<<<<<<<<", encryptStr);
            e.printStackTrace();
            return "";
        }
        byte[] decryptBytes = new byte[0];
        try {
            //设置Cipher对象
            Cipher cipher = Cipher.getInstance(ALGORITHM_NAME_ECB_PADDING, PROVIDER);
            cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(secret_key.getBytes(), ALGORITHM_NAME));

            //调用doFinal解密
            decryptBytes = cipher.doFinal(decodeBase64);
        } catch (NoSuchAlgorithmException | NoSuchPaddingException | InvalidKeyException | IllegalBlockSizeException |
                BadPaddingException e) {
            log.error(">>>>>>>>>sm4字符串解密失败:{}<<<<<<<<<<", encryptStr);
            e.printStackTrace();
            return "";
        }

        return new String(decryptBytes);
    }


    /**
     * 通过自定义字符串加密
     *
     * @param content 加密的字符串
     */
    public static String encryptByCustormString(String content, String key) {
        if (StringUtils.isBlank(content)) {
            return "";
        }
        //设置Cipher对象
        Cipher cipher = null;
        byte[] b = new byte[0];
        try {
            cipher = Cipher.getInstance(ALGORITHM_NAME_ECB_PADDING, PROVIDER);
            cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(key.getBytes(), ALGORITHM_NAME));

            //调用doFinal
            b = cipher.doFinal(content.getBytes(StandardCharsets.UTF_8));
        } catch (NoSuchAlgorithmException | InvalidKeyException | NoSuchPaddingException | IllegalBlockSizeException |
                BadPaddingException e) {
            throw new RuntimeException(e);
        }

        // 转base64
        return Base64.encodeBase64String(b);
    }


    /**
     * 通过自定义字符串解密
     *
     * @param encryptStr 解密的字符串
     */
    public static String decryptByCustormString(String encryptStr, String key) {

        if (StringUtils.isBlank(encryptStr)) {
            return "";
        }
        //base64格式的key字符串转byte
        byte[] decodeBase64 = new byte[0];
        try {
            decodeBase64 = Base64.decodeBase64(encryptStr);
        } catch (Exception e) {
            log.error(">>>>>>>>>sm4字符串base64解码失败:{}<<<<<<<<<<", encryptStr);
            e.printStackTrace();
            return "";
        }
        byte[] decryptBytes = new byte[0];
        try {
            //设置Cipher对象
            Cipher cipher = Cipher.getInstance(ALGORITHM_NAME_ECB_PADDING, PROVIDER);
            cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(key.getBytes(), ALGORITHM_NAME));

            //调用doFinal解密
            decryptBytes = cipher.doFinal(decodeBase64);
        } catch (NoSuchAlgorithmException | NoSuchPaddingException | InvalidKeyException | IllegalBlockSizeException |
                BadPaddingException e) {
            log.error(">>>>>>>>>sm4字符串解密失败:{}<<<<<<<<<<", encryptStr);
            e.printStackTrace();
            return "";
        }
        return new String(decryptBytes);
    }


    /**
     * 密码生成 AES 密钥，并返回 SecretKeySpec 对象
     * @param password 密码
     * @param keySize 密钥长度 （sm4的密钥长度固定为128bit，即16个字节。）
     * @return SecretKeySpec 密钥对象
     * @throws NoSuchAlgorithmException 当加密算法不可用时抛出该异常
     */
    public static SecretKeySpec generateAESKeyFromPassword(String password, int keySize) {
        // 使用 SHA-256 哈希算法生成本地消息摘要实例
        try {
            MessageDigest sha = MessageDigest.getInstance("SHA-256");
            // 计算输入密码的 SHA-256 哈希值
            byte[] hash = sha.digest(password.getBytes(StandardCharsets.UTF_8));
            // 复制 hash 中的前 keySize个字节到 key 数组中
            byte[] key = Arrays.copyOf(hash, keySize);
            // 使用 SecretKeySpec 构造器基于 key 数组创建一个 AES 密钥对象
            return new SecretKeySpec(key, ALGORITHM_NAME);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
            log.error(">>>>>>>>>sm4密钥生成失败:{}<<<<<<<<<<", password);
            return null;
        }
    }

}