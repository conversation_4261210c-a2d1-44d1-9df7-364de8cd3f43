package cn.dahe.utils;


import cn.dahe.common.constants.TokenConstants;
import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import java.util.Date;

public class JwtUtils {

    private static final String SECRET = TokenConstants.SECRET;



    /**
     * 从数据声明生成令牌
     *
     * @return 令牌
     */
    public static String createToken(int userId) {
        return JWT.create().withAudience(String.valueOf(userId)).withIssuedAt(new Date()).sign(Algorithm.HMAC256(SECRET));
    }


    /**
     * 校验token
     *
     * @param token token
     */
    public static void verifyToken(String token) {
        // 校验token
        JWTVerifier jwtVerifier = JWT.require(Algorithm.HMAC256(SECRET)).build();
        jwtVerifier.verify(token);
    }


    public static void main(String[] args) {
        System.out.println(createToken(218));
    }


}
