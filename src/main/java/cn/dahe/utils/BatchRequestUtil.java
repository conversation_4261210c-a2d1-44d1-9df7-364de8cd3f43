package cn.dahe.utils;

import cn.dahe.utils.fan.FanCollectUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.fastjson.JSONArray;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * 批量请求工具类，用于处理Excel中的数据并发送批量请求
 */
public class BatchRequestUtil {
    // 日志记录器
    private static final Logger logger = LoggerFactory.getLogger(BatchRequestUtil.class);

    // 常量定义
    private static final String EXCEL_FILE_PATH = "C:\\Users\\<USER>\\Desktop\\大河安巡系统\\test.xlsx";
    private static final String AUTHORIZATION_HEADER = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiI3ODEiLCJpYXQiOjE3NTgyNDU3ODN9.8sKVWZOc8t3eBtEnoa4L8Oyyo4FJwkAkP-JnmR9Jtkw";
    private static final String SEARCH_MEDIA_URL = "http://localhost:8081/platform-api/pro/new/media/search";
    private static final String SAVE_MEDIA_SITE_URL = "http://localhost:8081/platform-api/pro/web-site/save-media-site";
    private static final String SAVE_SINGLE_SITE_URL = "http://localhost:8081/platform-api/pro/web-site/save-single";

    // 类型常量
    private static final int TYPE_WECHAT = 1;
    private static final int TYPE_WEIBO = 2;
    private static final int TYPE_HEADLINE = 3;

    public static void main(String[] args) {
        logger.info("开始处理批量请求任务");
        ExcelReader excelReader = null;
        try {
            excelReader = ExcelUtil.getReader(EXCEL_FILE_PATH);
            List<List<Object>> dataList = excelReader.read();

            if (dataList == null || dataList.isEmpty()) {
                logger.warn("Excel文件中没有数据");
                return;
            }

            processExcelData(dataList);
            logger.info("批量请求任务处理完成");
        } catch (Exception e) {
            logger.error("处理批量请求时发生异常", e);
        } finally {
            // 关闭资源
            if (excelReader != null) {
                try {
                    excelReader.close();
                } catch (Exception e) {
                    logger.error("关闭ExcelReader时发生异常", e);
                }
            }
        }
    }

    /**
     * 处理Excel中的数据
     */
    private static void processExcelData(List<List<Object>> dataList) {
        int rowNum = 0;
        for (List<Object> row : dataList) {
            rowNum++;
            // 跳过表头行
            if (rowNum == 1) {
                continue;
            }

            try {
                processRow(row, rowNum);
            } catch (Exception e) {
                logger.error("处理第{}行数据时发生异常", rowNum, e);
            }
        }
    }

    /**
     * 处理单行数据
     */
    private static void processRow(List<Object> row, int rowNum) {
        if (row.size() < 3) {
            logger.warn("第{}行数据不完整，跳过处理", rowNum);
            return;
        }

        String type = row.get(0) != null ? row.get(0).toString() : "";
        String name = row.get(1) != null ? row.get(1).toString() : "";
        String siteUrl = row.get(2) != null ? row.get(2).toString() : "";
        String groupId = row.get(3) != null ? row.get(3).toString() : "";

        logger.info("处理第{}行数据 - 类型: {}, 名称: {}", rowNum, type, name);

        // 处理微信、微博、头条号类型
        int mediaType = getMediaType(type);
        if (mediaType > 0) {
            processMediaRequest(name, mediaType, groupId);
        }

        // 处理网站类型
        if (type.contains("网站")) {
            processWebsiteRequest(name, siteUrl, rowNum, groupId);
        }
    }

    /**
     * 获取媒体类型
     */
    private static int getMediaType(String type) {
        if (type.contains("微信")) {
            return TYPE_WECHAT;
        }
        if (type.contains("微博")) {
            return TYPE_WEIBO;
        }
        if (type.contains("头条号")) {
            return TYPE_HEADLINE;
        }
        return 0;
    }

    /**
     * 处理媒体请求
     */
    private static void processMediaRequest(String name, int mediaType, String groupId) {
        try {
            // 发送搜索请求
            String searchResponse = HttpUtil.createPost(SEARCH_MEDIA_URL)
                    .header("Authorization", AUTHORIZATION_HEADER)
                    .form("name", name)
                    .form("type", mediaType)
                    .form("groupId", groupId)
                    .execute()
                    .body();

            JSONObject searchResult = JSONUtil.parseObj(searchResponse);

            if (searchResult.getInt("code") == 200) {
                JSONObject data = searchResult.getJSONObject("data").getJSONArray("data").getJSONObject(0);
                if (data != null) {
                    Integer channelId = data.getInt("id");
                    String webName = data.getStr("name");
                    String webUrl = data.getStr("url");
                    Integer siteId = data.getInt("site_id");

                    // 发送保存请求
                    String saveResponse = HttpUtil.createPost(SAVE_MEDIA_SITE_URL)
                            .header("Authorization", AUTHORIZATION_HEADER)
                            .form("processType", mediaType)
                            .form("siteId", siteId)
                            .form("webUrl", webUrl)
                            .form("webName", webName)
                            .form("channelId", channelId)
                            .form("groupId", groupId)
                            .execute()
                            .body();

                    logger.info("媒体保存成功 - 名称: {}, 响应: {}", name, saveResponse);
                } else {
                    logger.warn("未找到媒体数据 - 名称: {}", name);
                }
            } else {
                logger.error("搜索媒体失败 - 名称: {}, 响应: {}", name, searchResponse);
            }
        } catch (Exception e) {
            logger.error("处理媒体请求时发生异常 - 名称: {}", name, e);
        }
    }

    /**
     * 处理网站请求
     */
    private static void processWebsiteRequest(String name, String siteUrl, int rowNum, String groupId) {
        try {
            logger.info("处理网站 - 名称: {}, URL: {}", name, siteUrl);

            JSONArray siteInfo = FanCollectUtil.searchSitesByName(name);
            if (siteInfo != null) {
                Integer siteId = siteInfo.getInteger(0);
                HttpResponse response = HttpUtil.createPost(SAVE_SINGLE_SITE_URL)
                        .header("Authorization", AUTHORIZATION_HEADER)
                        .form("name", name)
                        .form("siteId", siteId)
                        .form("groupId", groupId)
                        .execute();
                logger.info("网站保存成功 - 第{}行, 响应: {}", rowNum, response.body());
            } else {
                logger.warn("未找到网站信息 - 第{}行, 名称: {}", rowNum, name);
            }
        } catch (Exception e) {
            logger.error("处理网站请求时发生异常 - 第{}行, 名称: {}", rowNum, name, e);
        }
    }
}