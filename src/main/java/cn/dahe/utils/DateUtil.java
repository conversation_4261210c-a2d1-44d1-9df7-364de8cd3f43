package cn.dahe.utils;

import cn.dahe.common.constants.DateConstant;
import org.apache.commons.lang3.time.FastDateFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.Objects;

/**
 * Created by lzd
 * 2018/11/28
 * 日期工具类
 *
 * <AUTHOR>
 */
public class DateUtil {
    private static final Logger logger = LoggerFactory.getLogger(DateUtil.class);

    private static final String NULL = "null";

    private static final String YEAR = "yyyy";

    private static final String SORT_YEAR = "yy";

    private static final String MONTH = "MM";

    private static final String MINUTE = "mm";

    private static final String SPACE = " ";

    private static final String ZERO = "0";

    private static final long ONE_MINUTE = 60;

    private static final long ONE_HOUR = 60 * 60;

    private static final long ONE_DAY = 60 * 60 * 24;

    /**
     * 获取几天前的时间戳
     *
     * @param day
     * @return
     */
    public static long getTimeByDay(int day) {
        return subDay(new Date(), day).getTime();
    }

    /**
     * 默认格式化时间
     *
     * @param date
     * @return
     */
    public static String formate(Date date) {
        return format(date, DateConstant.DATE_YMD_HSM);
    }

    /**
     * 日期转为字符串
     *
     * @param date    日期
     * @param pattern 规则
     * @return
     */
    public static String format(Date date, String pattern) {
        if (date == null) {
            return NULL;
        }
        if (pattern == null || "".equals(pattern) || NULL.equals(pattern)) {
            pattern = DateConstant.DATE_YMD_HSM;
        }
        return new SimpleDateFormat(pattern).format(date);
    }

    /**
     * 获得当前年份
     */
    public static String getYear(Date date) {
        if (date == null) {
            return NULL;
        }
        return new SimpleDateFormat(YEAR).format(date);
    }

    /**
     * 获得当前年份
     */
    public static int getMonth() {
        Calendar calendar = Calendar.getInstance();
        return calendar.get(Calendar.MONTH) + 1;
    }

    /**
     * 字符串转为日期
     *
     * @param date    字符串
     * @param pattern 规则
     * @return
     */
    public static Date format(String date, String pattern) {
        if (date == null || "".equals(date) || NULL.equals(date)) {
            return new Date();
        }
        if (pattern == null || "".equals(pattern) || NULL.equals(pattern)) {
            pattern = DateConstant.DATE_YMD_HSM;
        }
        Date d = null;
        try {
            d = new SimpleDateFormat(pattern).parse(date);
        } catch (ParseException e) {
            logger.info("string to date is error!!!");
        }
        return d;
    }


    /**
     * 格式化时间
     *
     * @param date
     * @param pattern
     * @return
     */
    public static Date format1(String date, String pattern) {
        if (date == null || "".equals(date) || NULL.equals(date)) {
            return new Date();
        }
        if (pattern == null || "".equals(pattern) || NULL.equals(pattern)) {
            pattern = DateConstant.DATE_YMD;
        }
        Date d = null;
        try {
            d = new SimpleDateFormat(pattern).parse(date);
        } catch (ParseException e) {
            logger.info("string to date is error!!!");
        }
        return d;
    }

    public static Date format(String date) {
        return format(date, null);
    }

    public static Date format1(String date) {
        return format1(date, null);
    }


    public static Date addDay(Date endTime, int n) {
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(endTime);
        calendar.add(Calendar.DATE, n);
        return calendar.getTime();
    }

    public static Date subDay(Date endTime, int n) {
        if (n <= 0) {
            return endTime;
        }
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(endTime);
        calendar.set(Calendar.DATE, calendar.get(Calendar.DATE) - n);
        return calendar.getTime();
    }

    public static String formatStringTime(String time, String pattern) {
        int timeLength = 9;
        if (time.length() < timeLength) {
            return "";
        }
        pattern = pattern.trim();
        StringBuffer sb = new StringBuffer();
        String space = pattern.replaceAll("[a-zA-Z]+", "");
        if (StringUtils.isNoneBlank(space)) {
            space = space.substring(0, 1);
        }
        try {
            if (pattern.contains(YEAR)) {
                sb.append(time.substring(0, 4));
                sb.append(space);
            }
            if (pattern.contains(SORT_YEAR) && !pattern.contains(YEAR)) {
                sb.append(time.substring(2, 4));
                sb.append(space);
            }
            if (pattern.contains(MONTH)) {
                sb.append(time.substring(5, 7));
                sb.append(space);
            }
            String day = "dd";
            if (pattern.contains(day)) {
                sb.append(time.substring(8, 10));
            }
            String hour = "HH";
            if (pattern.contains(hour)) {
                if (pattern.contains(SPACE)) {
                    sb.append(SPACE);
                } else {
                    sb.append(space);
                }
                sb.append(time.substring(11, 13));
            }
            if (pattern.contains(MINUTE)) {
                if (pattern.contains(SPACE) && !sb.toString().contains(SPACE)) {
                    sb.append(SPACE);
                } else {
                    sb.append(space);
                }
                sb.append(time.substring(14, 16));
            }
            String second = "ss";
            if (pattern.contains(second)) {
                if (pattern.contains(SPACE) && !sb.toString().contains(SPACE)) {
                    sb.append(SPACE);
                } else {
                    sb.append(space);
                }
                sb.append(time.substring(17, 19));
            }
        } catch (Exception e) {
            logger.info("time format error");
            e.printStackTrace();
        }
        return sb.toString();
    }


    /**
     * 把字符串转为当天开始的时间戳
     *
     * @param beginTime
     * @return
     */
    public static long getBeginTime(String beginTime) {
        SimpleDateFormat format = new SimpleDateFormat(DateConstant.DATE_YMD_HSM);
        long begin = 0L;
        try {
            if (StringUtils.isNotBlank(beginTime)) {
                if (ZERO.equals(beginTime)) {
                    return 0;
                }
                begin = format.parse(beginTime + " 00:00:00").getTime();
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return begin;
    }


    /**
     * 字符串转为当天结束的时间戳
     *
     * @param endTime
     * @return
     */
    public static long getEndTime(String endTime) {
        SimpleDateFormat format = new SimpleDateFormat(DateConstant.DATE_YMD_HSM);
        long begin = 0L;
        try {
            if (StringUtils.isNotBlank(endTime)) {
                if (ZERO.equals(endTime)) {
                    return 0;
                }
                begin = format.parse(endTime + " 23:59:59").getTime();
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return begin;
    }


    /**
     * 格式化时间戳转为字符串
     *
     * @param time
     * @return
     */
    public static String getDate(long time) {
        SimpleDateFormat format = new SimpleDateFormat(DateConstant.DATE_YMD_HSM);
        Date date = new Date(time);
        return format.format(date);
    }

    public static String getDate2(long time) {
        SimpleDateFormat format = new SimpleDateFormat(DateConstant.DATE_YMD);
        Date date = new Date(time);
        return format.format(date);
    }


    /**
     * 得到要求的开始时间
     *
     * @param days 从今天开始向前days天
     */
    public static String getBeginTime(int days) {
        if (days == -1) {
            return "";
        }
        return format(subDay(new Date(), days), DateConstant.DATE_YMD);
    }

    /**
     * 获得要求的时间的long型
     *
     * @param days 从今天开始向前days天
     */
    public static long getLongBeginTime(int days) {
        if (days == -1) {
            return 0;
        }
        return subDay(new Date(), days).getTime();
    }

    /**
     * 获取当天0点
     *
     * @return
     */
    public static long getTodayStartTime() {
        Calendar todayStart = Calendar.getInstance();
        todayStart.set(Calendar.HOUR, 0);
        todayStart.set(Calendar.MINUTE, 0);
        todayStart.set(Calendar.SECOND, 0);
        todayStart.set(Calendar.MILLISECOND, 0);
        return todayStart.getTime().getTime();
    }

    public static Date getTodayStartDate() {
        Calendar todayStart = Calendar.getInstance();
        todayStart.set(Calendar.HOUR, 0);
        todayStart.set(Calendar.MINUTE, 0);
        todayStart.set(Calendar.SECOND, 0);
        todayStart.set(Calendar.MILLISECOND, 0);
        return todayStart.getTime();
    }

    /**
     * 获取当天24点
     *
     * @return
     */
    public static long getTodayEndTime() {
        Calendar todayEnd = Calendar.getInstance();
        todayEnd.set(Calendar.HOUR, 23);
        todayEnd.set(Calendar.MINUTE, 59);
        todayEnd.set(Calendar.SECOND, 59);
        todayEnd.set(Calendar.MILLISECOND, 999);
        return todayEnd.getTime().getTime();
    }

    public static Date getTodayEndDate() {
        Calendar todayEnd = Calendar.getInstance();
        todayEnd.set(Calendar.HOUR, 23);
        todayEnd.set(Calendar.MINUTE, 59);
        todayEnd.set(Calendar.SECOND, 59);
        todayEnd.set(Calendar.MILLISECOND, 999);
        return todayEnd.getTime();
    }


    /**
     * 获取过去的天数
     *
     * @param date
     * @return
     */
    public static long pastDays(Date date) {
        long t = System.currentTimeMillis() - date.getTime();
        return t / (24 * 60 * 60 * 1000);
    }

    /**
     * 返回两个日期相差的天数
     *
     * @param beforeDate
     * @param afterDate
     * @return
     */
    public static int betweenDays(Date beforeDate, Date afterDate) {
        if (Objects.isNull(beforeDate) || Objects.isNull(afterDate)) {
            throw new RuntimeException("日期不能为空");
        }
        if (beforeDate.after(afterDate)) {
            return 0;
        }
        return (int) ((afterDate.getTime() - beforeDate.getTime()) / (1000 * 3600 * 24));
    }


    /**
     * 获得最近操作的戳
     *
     * @param date
     * @return
     */
    public static String getRecent(Date date) {
        if (date == null) {
            return NULL;
        }
        String pattern = DateConstant.DATE_YMDHMS;
        return new SimpleDateFormat(pattern).format(date);
    }

    public static String getSelfUrl(Date date) {
        String pattern = "yyyy/MM-dd/";
        return new SimpleDateFormat(pattern).format(date);
    }

    public static String conversionTime(long time) {
        String timeStr;
        if (time < ONE_MINUTE) {
            timeStr = time + "";
        } else if (time < ONE_HOUR) {
            long minute = time / ONE_MINUTE;
            time = time - minute * ONE_MINUTE;
            timeStr = minute + "分钟 " + time + "秒";
        } else if (time < ONE_DAY) {
            long hour = time / ONE_HOUR;
            time = time - hour * ONE_HOUR;
            long minute = time / ONE_MINUTE;
            time = time - minute * ONE_MINUTE;
            timeStr = hour + "小时" + minute + "分钟 " + time + "秒";
        } else {
            long day = time / ONE_DAY;
            time = time - day * ONE_DAY;
            long hour = time / ONE_HOUR;
            time = time - hour * ONE_HOUR;
            long minute = time / ONE_MINUTE;
            time = time - minute * ONE_MINUTE;
            timeStr = day + "天" + hour + "小时" + minute + "分钟" + time + "秒";
        }
        return timeStr;
    }


    private static final String CRON_DATE_FORMAT = "ss mm HH dd MM ? yyyy";

    public static String getCron(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat(CRON_DATE_FORMAT);
        String formateStr = "";
        if (date != null) {
            formateStr = sdf.format(date);
        }
        return formateStr;
    }


    /**
     * date类型转换为long类型
     *
     * @param date
     * @return
     */
    public static long dateToLong(Date date) {
        return date.getTime();
    }


    /**
     * 将时间转化为yyMMdd格式
     *
     * @param date
     * @return
     */
    public static String getDate3(Date date) {
        if (date == null) {
            return NULL;
        }
        String pattern = DateConstant.DATE_YMD_WITHOUT_PATTERN;
        return new SimpleDateFormat(pattern).format(date);
    }


    public static SimpleDateFormat format = new SimpleDateFormat(DateConstant.DATE_YMD);

    /**
     * 获取当前月第一天
     *
     * @return
     */
    public static String getMonthFirstDay() {
        //获取当前月第一天：
        Calendar c = Calendar.getInstance();
        c.add(Calendar.MONTH, 0);
        //设置为1号,当前日期既为本月第一天
        c.set(Calendar.DAY_OF_MONTH, 1);
        String first = format.format(c.getTime());
        return first;
    }

    /**
     * 获取当前月最后一天
     *
     * @return
     */
    public static String getMonthLastDay() {
        //获取当前月最后一天
        Calendar ca = Calendar.getInstance();
        ca.set(Calendar.DAY_OF_MONTH, ca.getActualMaximum(Calendar.DAY_OF_MONTH));
        String last = format.format(ca.getTime());
        return last;
    }

    /**
     * 获取季度的最后一天 1--4对应第一到第四季度
     *
     * @param num
     * @return
     */
    public static String getEndQuarter(int num) {
        String end = "";
        // 设置本年的季
        Calendar quarterCalendar;
        switch (num) {
            case 1:
                quarterCalendar = Calendar.getInstance();
                quarterCalendar.set(Calendar.MONTH, 3);
                quarterCalendar.set(Calendar.DATE, 1);
                quarterCalendar.add(Calendar.DATE, -1);
                end = DateUtil.format(quarterCalendar.getTime(), "yyyy-MM-dd");
                break;
            case 2:
                quarterCalendar = Calendar.getInstance();
                quarterCalendar.set(Calendar.MONTH, 6);
                quarterCalendar.set(Calendar.DATE, 1);
                quarterCalendar.add(Calendar.DATE, -1);
                end = DateUtil.format(quarterCalendar.getTime(), "yyyy-MM-dd");
                break;
            case 3:
                quarterCalendar = Calendar.getInstance();
                quarterCalendar.set(Calendar.MONTH, 9);
                quarterCalendar.set(Calendar.DATE, 1);
                quarterCalendar.add(Calendar.DATE, -1);
                end = DateUtil.format(quarterCalendar.getTime(), "yyyy-MM-dd");
                break;
            case 4:
                quarterCalendar = Calendar.getInstance();
                quarterCalendar.set(Calendar.MONTH, 12);
                quarterCalendar.set(Calendar.DATE, 1);
                quarterCalendar.add(Calendar.DATE, -1);
                end = DateUtil.format(quarterCalendar.getTime(), "yyyy-MM-dd");
                break;
            default:
                break;
        }
        return end;
    }

    /**
     * 获取季度的第一天 1--4对应第一到第四季度
     *
     * @param num
     * @return
     */
    public static String getBeginQuarter(int num) {
        String begin = "";
        // 设置本年的季
        Calendar quarterCalendar;
        switch (num) {
            case 1:
                quarterCalendar = Calendar.getInstance();
                quarterCalendar.set(Calendar.MONTH, 0);
                quarterCalendar.set(Calendar.DATE, 1);
                quarterCalendar.add(Calendar.DATE, 0);
                begin = DateUtil.format(quarterCalendar.getTime(), "yyyy-MM-dd");
                break;
            case 2:
                quarterCalendar = Calendar.getInstance();
                quarterCalendar.set(Calendar.MONTH, 3);
                quarterCalendar.set(Calendar.DATE, 1);
                quarterCalendar.add(Calendar.DATE, 0);
                begin = DateUtil.format(quarterCalendar.getTime(), "yyyy-MM-dd");
                break;
            case 3:
                quarterCalendar = Calendar.getInstance();
                quarterCalendar.set(Calendar.MONTH, 6);
                quarterCalendar.set(Calendar.DATE, 1);
                quarterCalendar.add(Calendar.DATE, 0);
                begin = DateUtil.format(quarterCalendar.getTime(), "yyyy-MM-dd");
                break;
            case 4:
                quarterCalendar = Calendar.getInstance();
                quarterCalendar.set(Calendar.MONTH, 9);
                quarterCalendar.set(Calendar.DATE, 1);
                quarterCalendar.add(Calendar.DATE, 0);
                begin = DateUtil.format(quarterCalendar.getTime(), "yyyy-MM-dd");
                break;
            default:
                break;
        }
        return begin;
    }

    public static Date asDate(LocalDateTime localDateTime) {
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }


    /**
     * 字符串转为时间戳
     *
     * @param date    字符串
     * @param pattern 规则
     * @return
     */
    public static long formatLong(String date, String pattern) {
        if (date == null || "".equals(date) || NULL.equals(date)) {
            return new Date().getTime();
        }
        if (pattern == null || "".equals(pattern) || NULL.equals(pattern)) {
            pattern = "yyyy-MM-dd HH:mm:ss";
        }
        Date d = null;
        try {
            d = FastDateFormat.getInstance(pattern).parse(date);
        } catch (ParseException e) {
            logger.error(">>>>>>>>>>>>parseException date {}", e.getMessage());
            e.printStackTrace();
        }
        return d.getTime();
    }


    public static Date convertData(String time) {
        boolean number1 = StringUtils.isNumeric(time);
        if (number1) {
            long longTime = Long.parseLong(time);
            SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String format = sf.format(new Date(longTime));
            Date rs = null;
            try {
                rs = sf.parse(format);
            } catch (ParseException e) {
                e.printStackTrace();
            }
            return rs;
        } else {
            return null;
        }
    }


    public static Date convertDataChinese(String time) {
        boolean number1 = StringUtils.isNumeric(time);
        if (number1) {
            long longTime = Long.parseLong(time);
            SimpleDateFormat sf = new SimpleDateFormat("yyyy年MM月dd日");
            String format = sf.format(new Date(longTime));
            Date rs = null;
            try {
                rs = sf.parse(format);
            } catch (ParseException e) {
                e.printStackTrace();
            }
            return rs;
        } else {
            return null;
        }
    }


    /**
     * 执行时间
     *
     * @param name 操作
     * @param time
     */
    public static void timeConsuming(String name, long time) {
        long vg = System.currentTimeMillis() - time;
        double duringTime = BigDecimalUtil.divide(vg, 1000L, 0);
        //只打印耗时大于1s的操作
        if (duringTime > 1) {
            logger.info("{} ----> 执行耗时： {} 秒", name, duringTime);
        }
    }

    /**
     * 判断当前时间是否在两个给定时间段内的方法
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public static boolean isCurrentTimeBetweenTwoTimes(Date startTime, Date endTime) {
        if (startTime == null || endTime == null) {
            return false;
        }
        Date currentTime = new Date();
        return currentTime.after(startTime) && currentTime.before(endTime);
    }
}
