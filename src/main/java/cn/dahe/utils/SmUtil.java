package cn.dahe.utils;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.KeyUtil;
import cn.hutool.crypto.symmetric.SM4;
import org.springframework.util.Base64Utils;

public class SmUtil {

    protected static final String ORIGINAL_KEY = "1YGQ8JL7ILPSM1NJHFI7R56CQJ3VFM2V8JIK6OHB2NJO7WP47H1WBZH1GUNB7046GRS840JZNNWK2FTL0PARERHCNF06GCI4RXBYCVM4OUKXXDZ34GPQOCMODC7PL50K";

    private static final SM4 sm4;

    static {
        byte[] encoded = KeyUtil.generateKey(SM4.ALGORITHM_NAME, StrUtil.subPre(ORIGINAL_KEY, 16).getBytes()).getEncoded();
        sm4 = cn.hutool.crypto.SmUtil.sm4(encoded);
    }

    public static String encrypt(String plaintext) {
        return sm4.encryptBase64(StrUtil.format("{}:{}", DateUtil.current(), plaintext));
    }

    public static String decrypt(String encryptedText) {
        try {
            String resultStr = sm4.decryptStr(encryptedText, CharsetUtil.CHARSET_UTF_8);
            return StrUtil.subAfter(resultStr, ":", false);
        } catch (Exception e) {
            return null;
        }
    }

    public static void main(String[] args) {
        String sss = encrypt("测试");
        System.out.println(sss);
        System.out.println(Base64Utils.encodeToString(sss.getBytes()));
    }
}
