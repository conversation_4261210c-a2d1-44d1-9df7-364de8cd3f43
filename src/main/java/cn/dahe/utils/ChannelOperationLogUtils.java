package cn.dahe.utils;

import cn.dahe.enums.ChannelOperationTypeEnum;
import cn.dahe.service.ChannelOperationLogService;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 栏目操作日志工具类
 * 提供便捷的日志记录方法，其他模块可直接调用
 */
@Slf4j
public class ChannelOperationLogUtils {

    /**
     * 记录栏目操作日志（带额外信息）
     *
     * @param channelId 栏目ID
     * @param username  操作用户名
     * @param type      操作类型
     * @param extraInfo 额外信息
     */
    public static void addLog(Integer channelId, String username, ChannelOperationTypeEnum type, String extraInfo) {
        addLogToData(channelId, username, LocalDateTime.now(), type, extraInfo);
    }

    /**
     * 记录栏目操作日志（完整参数）
     *
     * @param channelId 栏目ID
     * @param username  操作用户名
     * @param time      操作时间
     * @param type      操作类型
     * @param extraInfo 额外信息
     */
    public static void addLogToData(Integer channelId, String username, LocalDateTime time, ChannelOperationTypeEnum type, String extraInfo) {
        try {
            ChannelOperationLogService channelOperationLogService = SpringUtils.getBean(ChannelOperationLogService.class);
            channelOperationLogService.addLog(channelId, username, time, type, extraInfo);
        } catch (Exception e) {
            log.error("记录栏目操作日志失败: channelId={}, username={}, type={}, extraInfo={}", 
                    channelId, username, type, extraInfo, e);
        }
    }

    /**
     * 记录栏目新增日志
     *
     * @param channelId 栏目ID
     * @param username  操作用户名
     */
    public static void addCreateLog(Integer channelId, String username) {
        addLog(channelId, username, ChannelOperationTypeEnum.ADD, "新增栏目");
    }

    /**
     * 记录栏目修改日志
     *
     * @param channelId 栏目ID
     * @param username  操作用户名
     */
    public static void addUpdateLog(Integer channelId, String username) {
        addLog(channelId, username, ChannelOperationTypeEnum.UPDATE, "修改栏目");
    }

    /**
     * 记录栏目删除日志
     *
     * @param channelId 栏目ID
     * @param username  操作用户名
     */
    public static void addDeleteLog(Integer channelId, String username) {
        addLog(channelId, username, ChannelOperationTypeEnum.DELETE, "删除栏目");
    }

    /**
     * 记录栏目预警日志
     *
     * @param channelId 栏目ID
     * @param username  操作用户名
     */
    public static void addWarningLog(Integer channelId, String username) {
        addLog(channelId, username, ChannelOperationTypeEnum.WARNING, "新增栏目预警");
    }

    /**
     * 记录栏目修改日志（带修改内容说明）
     *
     * @param channelId    栏目ID
     * @param username     操作用户名
     * @param changeDetail 修改详情
     */
    public static void addUpdateLog(Integer channelId, String username, String changeDetail) {
        addLog(channelId, username, ChannelOperationTypeEnum.UPDATE, changeDetail);
    }

    /**
     * 记录栏目删除日志（带删除原因）
     *
     * @param channelId    栏目ID
     * @param username     操作用户名
     * @param deleteReason 删除原因
     */
    public static void addDeleteLog(Integer channelId, String username, String deleteReason) {
        addLog(channelId, username, ChannelOperationTypeEnum.DELETE, deleteReason);
    }

    /**
     * 记录栏目预警日志（带预警详情）
     *
     * @param channelId     栏目ID
     * @param username      操作用户名
     * @param warningDetail 预警详情
     */
    public static void addWarningLog(Integer channelId, String username, String warningDetail) {
        addLog(channelId, username, ChannelOperationTypeEnum.WARNING, warningDetail);
    }

    /**
     * 记录批量修改栏目类型日志
     *
     * @param channelIdList 栏目ID列表
     * @param username      操作用户名
     * @param updatePeriod  更新周期
     */
    public static void addUpdateTypeLog(List<Integer> channelIdList, String username,Integer updatePeriod) {
        channelIdList.forEach(channelId -> addUpdateLog(channelId, username, "修改栏目类型，更新周期：" + updatePeriod));
    }
}