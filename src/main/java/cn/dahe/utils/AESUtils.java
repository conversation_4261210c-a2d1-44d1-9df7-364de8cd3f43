package cn.dahe.utils;

import cn.hutool.core.codec.Base32;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.symmetric.SymmetricCrypto;
import lombok.SneakyThrows;
import org.springframework.util.StringUtils;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

/**
 * <AUTHOR>
 * @date 2024-02-27
 */
public class AESUtils {

    protected static final String ORIGINAL_KEY = "1YGQ8JL7ILPSM1NJHFI7R56CQJ3VFM2V8JIK6OHB2NJO7WP47H1WBZH1GUNB7046GRS840JZNNWK2FTL0PARERHCNF06GCI4RXBYCVM4OUKXXDZ34GPQOCMODC7PL50K";

    // Generate a proper 256-bit (32-byte) key from the original key using SHA-256
    private static final byte[] KEY = generateAESKey(ORIGINAL_KEY);

    /**
     * Generate a valid AES key from the original string using SHA-256 hash
     */
    private static byte[] generateAESKey(String originalKey) {
        try {
            MessageDigest sha256 = MessageDigest.getInstance("SHA-256");
            return sha256.digest(originalKey.getBytes());
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("SHA-256 algorithm not available", e);
        }
    }

    public static String encrypt(String plaintext) {
        // 创建 SymmetricCrypto 对象，指定密钥
        SymmetricCrypto aes = new SymmetricCrypto("AES", KEY);
        // 加密文本并返回URL安全的编码字符串（只包含字母数字）
        byte[] encrypted = aes.encrypt(plaintext);
        return Base32.encode(encrypted);
    }

    public static String decrypt(String encryptedText) {
        // 创建 SymmetricCrypto 对象，指定密钥
        SymmetricCrypto aes = new SymmetricCrypto("AES", KEY);
        // 解码Base32并解密返回原始字符串
        byte[] decoded = Base32.decode(encryptedText);
        return aes.decryptStr(decoded);
    }

    public static String genEncPushId(String id) {
        id = id + ":"+ RandomUtil.randomString(128-id.length()-1);
        // 创建 SymmetricCrypto 对象，指定密钥
        SymmetricCrypto aes = new SymmetricCrypto("AES", KEY);
        // 加密文本并返回URL安全的编码字符串（只包含字母数字）
        byte[] encrypted = aes.encrypt(id);
        return Base32.encode(encrypted);
    }

    public static String getDecPushId(String plaintext) {
        // 创建 SymmetricCrypto 对象，指定密钥
        SymmetricCrypto aes = new SymmetricCrypto("AES", KEY);
        // 解码Base32并解密密文返回原始字符串
        byte[] decoded = Base32.decode(plaintext);
        String s = aes.decryptStr(decoded);
        return s.split(":")[0];
    }
    static SecretKeySpec secretKeySpec;
    static {
        init();
    }

    @SneakyThrows
    static void init() {
        secretKeySpec = new SecretKeySpec(MessageDigest.getInstance("sha256").digest("是家柔软了时光".getBytes()), "aes");
    }
    @SneakyThrows
    public static String decryptFromAuthCenter(String text) {
        if (!StringUtils.hasText(text)) return null;
        Cipher cipher = Cipher.getInstance("aes");
        cipher.init(Cipher.DECRYPT_MODE, secretKeySpec);
        return new String(cipher.doFinal(Base64.getDecoder().decode(text)));
    }


    public static void main(String[] args) {
        System.out.println("Generated AES key length: " + KEY.length + " bytes");
        System.out.println("Encrypted Push ID: " + AESUtils.genEncPushId("1"));
    }
}