package cn.dahe.utils;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2017/8/3
 */
public class BigDecimalUtil {
    private BigDecimalUtil() {
    }

    /**
     * 提供精确加法计算的add方法
     *
     * @param v1 被加数
     * @param v2 加数
     */
    public static double add(double v1, double v2) {
        BigDecimal a = BigDecimal.valueOf(v1);
        BigDecimal b = BigDecimal.valueOf(v2);
        return a.add(b).doubleValue();
    }

    /**
     * 提供精确的除法运算方法
     *
     * @param v1 被除数
     * @param v1 除数
     * @param scale  精确范围
     */
    public static double divide(double v1, double v2, int scale) {
        if (scale < 0) {
            scale = 0;
        }
        BigDecimal a = BigDecimal.valueOf(v1);
        BigDecimal b = BigDecimal.valueOf(v2);
        return a.divide(b, scale, BigDecimal.ROUND_HALF_DOWN).doubleValue();
    }

    /**
     * 提供精确减法运算的sub方法
     *
     * @param value1 被减数
     * @param value2 减数
     */
    public static double sub(double value1, double value2) {
        BigDecimal b1 = BigDecimal.valueOf(value1);
        BigDecimal b2 = BigDecimal.valueOf(value2);
        return b1.subtract(b2).doubleValue();
    }

    /**
     * 提供精确乘法运算的mul方法
     *
     * @param value1 被乘数
     * @param value2 乘数
     */
    public static double mul(double value1, double value2) {
        BigDecimal b1 = BigDecimal.valueOf(value1);
        BigDecimal b2 = BigDecimal.valueOf(value2);
        return b1.multiply(b2).doubleValue();
    }

    public static double getDouble(double price, int scale) {
        try {
            return divide(price, 100, scale);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0;
    }
}
