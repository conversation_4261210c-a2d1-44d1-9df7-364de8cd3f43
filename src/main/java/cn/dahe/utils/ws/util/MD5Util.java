//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package cn.dahe.utils.ws.util;

import java.math.BigInteger;
import java.nio.charset.Charset;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public final class MD5Util {
    private static final int MD5_LENTH = 32;
    private static final int MD5_RADIX = 16;

    private MD5Util() {
    }

    static String md5Encrypt32Upper(String str) {
        byte[] digest = null;

        try {
            MessageDigest md5 = MessageDigest.getInstance("md5");
            digest = md5.digest(str.getBytes(Charset.forName("utf-8")));
        } catch (NoSuchAlgorithmException var4) {
            throw new RuntimeException("没有这个md5算法!");
        }

        String md5Str = (new BigInteger(1, digest)).toString(16);

        for(int i = 0; i < 32 - md5Str.length(); ++i) {
            md5Str = "0" + md5Str;
        }

        return md5Str.toUpperCase();
    }
}
