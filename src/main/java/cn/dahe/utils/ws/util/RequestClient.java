//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package cn.dahe.utils.ws.util;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Iterator;
import java.util.Map;

public class RequestClient {
    private static final String VERSION = "v1";
    public static final String EES_WCS_ENDPOINT = "EES_WCS_ENDPOINT";
    public static final String EES_API_ENDPOINT = "EES_API_ENDPOINT";
    public static final String EES_ACCESSKEY_ID = "EES_ACCESSKEY_ID";
    public static final String EES_ACCESSKEY_SECRET = "EES_ACCESSKEY_SECRET";
    public static String eesAccesskeyId = "ZGntfJuNw4pGUMQhy6xv";
    public static String eesAccesskeySecret = "KhAFxklN08ze3btv56OQsSc14BDmLaHfWGrnVSIR";

    public RequestClient() {
    }

    public String makeUrl(Map<String, Object> params) throws UnsupportedEncodingException {
        this.initBaseParam(params);
        String urlParamStr = this.getUrlParamStr(params, false);
        String signature = MD5Util.md5Encrypt32Upper(urlParamStr + eesAccesskeySecret);
        return this.getUrlParamStr(params, true) + "signature=" + signature;
    }

    private String getUrlParamStr(Map<String, Object> params, boolean urlEncode) throws UnsupportedEncodingException {
        StringBuilder urlParam = new StringBuilder();
        Iterator var4 = params.entrySet().iterator();

        while(var4.hasNext()) {
            Map.Entry<String, Object> entry = (Map.Entry)var4.next();
            String value = entry.getValue() == null ? "" : entry.getValue().toString();
            urlParam.append((String)entry.getKey()).append("=").append(urlEncode ? URLEncoder.encode(value, "utf-8") : value).append("&");
        }

        return urlParam.toString();
    }

    private void initBaseParam(Map<String, Object> params) {
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000L - 5L);
        params.put("timestamp", timestamp);
        params.put("version", "v1");
        params.put("accesskey_id", eesAccesskeyId);
        params.remove("signature");
    }
}
