package cn.dahe.utils.ws;

import cn.dahe.model.dto.Result;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * on 2024/01/17
 * description： 网宿处理文件工具类，网宿兼容标准的AWS S3
 */
@Slf4j
public class UploadWsUtil {

    // 桶所属用户ak
    private static final String ACCESS_KEY = " UN8WCY56K85MHXLU7BXU";
    // 桶所属用户sk
    private static final String SECRET_KEY = "o4rWAoFdddmnAreBrdeKAlAYLszztu9VtrZsw0aZ";
    // api服务地址
    private static final String WCS_ENDPOINT = "https://oss.dahe.cn";
    private static final String API_ENDPOINT = " https://oss.dahe.cn";
    // 存储桶
    private static final String BUCKET_NAME = "axxm";

    private static final List<String> IMG_TYPE = Arrays.stream("jpg,png,jpeg,gif".split(",")).map(String::trim).filter(StringUtils::isNoneBlank).collect(Collectors.toList());
    private static final List<String> MEDIA_TYPE = Arrays.stream("txt,xls,xlsx,xlsm,doc,docx,pdf,zip,rar,7z".split(",")).map(String::trim).filter(StringUtils::isNoneBlank).collect(Collectors.toList());
    private static final List<String> VIDEO_TYPE = Arrays.stream("mp4,wmv,avi,rmb,rmvb,flv".split(",")).map(String::trim).filter(StringUtils::isNoneBlank).collect(Collectors.toList());
    private static final List<String> AUDIO_TYPE = Arrays.stream("mp3,wav,wma".split(",")).map(String::trim).filter(StringUtils::isNoneBlank).collect(Collectors.toList());

    // 分片的大小，5M
    public static final int PART_SIZE = 5 * 1024 * 1024;

    private static AmazonS3 getAmazonS3() {
        UploadManager uploadManager = new UploadManager(ACCESS_KEY, SECRET_KEY, WCS_ENDPOINT, API_ENDPOINT);
        return uploadManager.getS3();
    }

    private static void setProxy(AmazonS3 s3) {
        List<CORSRule.AllowedMethods> rule1AM = new ArrayList<>();
        rule1AM.add(CORSRule.AllowedMethods.PUT);
        rule1AM.add(CORSRule.AllowedMethods.POST);
        rule1AM.add(CORSRule.AllowedMethods.DELETE);
        rule1AM.add(CORSRule.AllowedMethods.GET);
        List<String> headers = new ArrayList<>();
        headers.add("ETag");
        headers.add("x-amz-meta-custom-header");
        CORSRule rule = new CORSRule().withAllowedMethods(rule1AM).withAllowedOrigins(Arrays.asList("*")).withAllowedHeaders(Arrays.asList("*")).withExposedHeaders(headers);
        List<CORSRule> rules = new ArrayList<>();
        rules.add(rule);
        BucketCrossOriginConfiguration configuration = new BucketCrossOriginConfiguration();
        configuration.setRules(rules);
        s3.setBucketCrossOriginConfiguration(new SetBucketCrossOriginConfigurationRequest(BUCKET_NAME, new BucketCrossOriginConfiguration(rules)));
    }

    /**
     * 上传文件
     * @param attach 文件
     * @return 文件访问url
     */
    public static String uploadFile(MultipartFile attach) {
        try {
            AmazonS3 s3 = getAmazonS3();
            ObjectMetadata metadata = new ObjectMetadata();
            String ext = FilenameUtils.getExtension(attach.getOriginalFilename()).toLowerCase();// 获取文件后缀
            String newFileName = System.currentTimeMillis() + RandomStringUtils.randomNumeric(3) + "." + ext;
            String filePath = UploadWsUtil.createPath(ext, newFileName);
            s3.putObject(new PutObjectRequest(BUCKET_NAME, filePath, attach.getInputStream(), metadata)
                    //设置为公共读
                    .withCannedAcl(CannedAccessControlList.PublicRead));
            setProxy(s3);
            return WCS_ENDPOINT + "/" + BUCKET_NAME + "/" + filePath;
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
    }

    /**
     * 上传文件
     * @param attach 文件
     * @param filePath 文件路径
     * @return 文件访问url
     */
    public static String uploadFile(MultipartFile attach, String filePath) {
        try {
            AmazonS3 s3 = getAmazonS3();
            ObjectMetadata metadata = new ObjectMetadata();
            s3.putObject(new PutObjectRequest(BUCKET_NAME, filePath, attach.getInputStream(), metadata)
                    //设置为公共读
                    .withCannedAcl(CannedAccessControlList.PublicRead));
            setProxy(s3);
            return WCS_ENDPOINT + "/" + BUCKET_NAME + "/" + filePath;
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
    }

    /**
     * 上传文件
     * @param inputStream 文件流内容
     * @param suffix 文件后缀
     * @return 文件访问url
     */
    public static String uploadFile(InputStream inputStream, String suffix) {
        try {
            AmazonS3 s3 = getAmazonS3();
            ObjectMetadata metadata = new ObjectMetadata();
            String newFileName = System.currentTimeMillis() + RandomStringUtils.randomNumeric(3) + "." + suffix;
            String filePath = createPath(suffix, newFileName);
            s3.putObject(new PutObjectRequest(BUCKET_NAME, filePath, inputStream, metadata)
                    //设置为公共读
                    .withCannedAcl(CannedAccessControlList.PublicRead));
            setProxy(s3);
            return WCS_ENDPOINT + "/" + BUCKET_NAME + "/" + filePath;
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
    }

    /**
     * 删除文件
     * @param key 桶内文件的全路径
     */
    public static Result delete(String key) {
        try {
            AmazonS3 s3 = getAmazonS3();
            if (!s3.doesObjectExist(BUCKET_NAME, key)) {
                return Result.error("文件不存在，请检查文件路径：" + key);
            }
            s3.deleteObject(BUCKET_NAME, key);
            return Result.ok("删除成功");
        } catch (Exception e) {
            log.error("删除失败：" + e.getMessage());
            return Result.error("删除失败");
        }
    }

    /**
     * 生成文件路径，格式：文件类型/文件后缀/年/月/日/文件名
     * @param suffix 后缀
     * @param fileName 文件名
     * @return 生成文件路径
     */
    public static String createPath(String suffix, String fileName) {
        suffix = suffix.toLowerCase();
        String prefix = "";
        if (IMG_TYPE.contains(suffix)) {
            prefix = "image";
        } else if (MEDIA_TYPE.contains(suffix)) {
            prefix = "file";
        } else if (VIDEO_TYPE.contains(suffix)) {
            prefix = "video";
        } else if (AUDIO_TYPE.contains(suffix)) {
            prefix = "audio";
        }
        return prefix + "/" + suffix + "/" + cn.hutool.core.date.DateUtil.format(new Date(), "yyyy/MM/dd") + "/" + fileName;
    }

    public static String uploadVideoFile(MultipartFile attach, String filePath) {
        try {
            AmazonS3 s3 = getAmazonS3();
            InitiateMultipartUploadRequest initiateRequest = new InitiateMultipartUploadRequest(BUCKET_NAME, filePath);
            InitiateMultipartUploadResult initResponse = s3.initiateMultipartUpload(initiateRequest);
            String uploadId = initResponse.getUploadId();
            long fileLength = attach.getSize(); // 获取视频文件长度
            int numParts = (int) Math.ceil((double) fileLength / PART_SIZE); // 计算分片数量
            List<PartETag> partsList = new ArrayList<>();
            for (int i = 1; i <= numParts; ++i) {
                UploadPartRequest request = new UploadPartRequest().withBucketName(BUCKET_NAME).withKey(filePath).withUploadId(uploadId)
                        .withInputStream(attach.getInputStream())
                        .withFileOffset(((long) PART_SIZE * (i - 1)) % fileLength)
                        .withPartNumber(i)
                        .withPartSize(Math.min(PART_SIZE, fileLength - ((long) PART_SIZE * (i - 1))));
                PartETag partEtag = s3.uploadPart(request).getPartETag();
                partsList.add(partEtag);
            }
            CompleteMultipartUploadRequest completeRequest = new CompleteMultipartUploadRequest(BUCKET_NAME, filePath, uploadId, partsList);
            s3.completeMultipartUpload(completeRequest);
            return WCS_ENDPOINT + "/" + BUCKET_NAME + "/" + filePath;
        } catch (Exception e) {
            log.error("上传失败：{}", e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
    }

}
