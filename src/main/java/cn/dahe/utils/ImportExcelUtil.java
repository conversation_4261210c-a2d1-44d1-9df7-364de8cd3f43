// package cn.dahe.utils;
//
// import lombok.extern.slf4j.Slf4j;
// import org.apache.poi.hssf.usermodel.HSSFWorkbook;
// import org.apache.poi.ss.usermodel.*;
// import org.apache.poi.xssf.usermodel.XSSFWorkbook;
//
// import java.io.InputStream;
// import java.io.PushbackInputStream;
// import java.text.SimpleDateFormat;
// import java.util.*;
//
// /**
//  * Created by zyp on 2020/6/29.
//  */
// @Slf4j
// public class ImportExcelUtil {
//     /**
//      * 2003- 版本的excel
//      */
//     private final static String EXCEL_2003L = ".xls";
//     /**
//      * 2007+ 版本的excel
//      */
//     private final static String EXCEL_2007U = ".xlsx";
//
//     /**
//      * 获取IO流中的数据，组装成List<List<Object>>对象
//      *
//      * @param in       文件流
//      * @param fileName 文件名
//      * @return 解析值
//      */
//     public List<List<Object>> getBankListByExcel(InputStream in, String fileName) throws Exception {
//         List<List<Object>> list = new ArrayList<>();
//         // 创建Excel工作薄
//         Workbook work = this.getWorkbook(in, fileName);
//         if (null == work) {
//             throw new Exception("创建Excel工作薄为空！");
//         }
//         Sheet sheet;
//         Row row;
//         Cell cell;
//         int cellNum = 0;
//         // 遍历Excel中所有的sheet
//         for (int i = 0; i < work.getNumberOfSheets(); i++) {
//             sheet = work.getSheetAt(i);
//             if (sheet == null) {
//                 continue;
//             }
//             // 遍历当前sheet中的所有行
//             // 跳过取第一行表头的数据内容
//             for (int j = sheet.getFirstRowNum(), len = sheet.getLastRowNum(); j < len + 1; j++) {
//                 row = sheet.getRow(j);
//                 // 如果当前行为空 跳过
//                 if (row == null || row.getFirstCellNum() == j) {
//                     continue;
//                 }
//                 // 如果当前行 第一列无数据  则默认为是无效数据  跳过
//                 if (StringUtils.isBlank(getCellValue(row.getCell(row.getFirstCellNum())))) {
//                     continue;
//                 }
//                 //总列数
//                 cellNum = row.getPhysicalNumberOfCells();
//                 // 遍历所有的列
//                 List<Object> li = new ArrayList<>();
//                 for (int y = row.getFirstCellNum(); y < cellNum; y++) {
//                     cell = row.getCell(y);
//                     li.add(getCellValue(cell));
//                 }
//                 list.add(li);
//             }
//         }
//         return list;
//     }
//
//
//     /**
//      * 根据文件后缀，自适应上传文件的版本
//      *
//      * @param inStr    文件流
//      * @param fileName 文件名
//      * @return Workbook
//      */
//     public Workbook getWorkbook(InputStream inStr, String fileName) throws Exception {
//         if (!inStr.markSupported()) {
//             inStr = new PushbackInputStream(inStr, 8);
//         }
// //        if (POIFSFileSystem.hasPIFSHeader(inStr)) {
// //            return new HSSFWorkbook(inStr);
// //        }
// //        if (POIXMLDocument.hasOOXMLHeader(inStr)) {
// //            return new XSSFWorkbook(OPCPackage.open(inStr));
// //        }
//         Workbook wb;
//         if (fileName.endsWith(EXCEL_2003L)) {
//             // 2003
//             wb = new HSSFWorkbook(inStr);
//         } else if (fileName.endsWith(EXCEL_2007U)) {
//             // 2007
//             wb = new XSSFWorkbook(inStr);
//         } else {
//             throw new Exception("解析的文件格式有误！");
//         }
//         return wb;
//     }
//
//     /**
//      * 对表格中数值进行格式化
//      *
//      * @param cell 单元格数据
//      * @return String
//      */
//     public static String getCellValue(Cell cell) {
//         if (cell == null) {
//             return "";
//         }
//         CellType cellType = cell.getCellType();
//         switch (cellType) {
//             case NUMERIC:
//                 //解决日期03-五月-2018格式读入后的问题，POI读取后变成“02-十一月-2018”格式
//                 if (cell.toString().contains("-") && checkDate(cell.toString())) {
//                     //定义一个新的字符串
//                     return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(cell.getDateCellValue());
//                 }
//                 return String.valueOf(cell.getNumericCellValue());
//             case BOOLEAN:
//                 return String.valueOf(cell.getBooleanCellValue());
//             default:
//                 return cell.getStringCellValue();
//         }
//     }
//
//
//     /**
//      * 判断是否是“02-十一月-2018”格式的日期类型
//      */
//     private static boolean checkDate(String str) {
//         String[] dataArr = str.split("-");
//         try {
//             if (dataArr.length == 3) {
//                 int x = Integer.parseInt(dataArr[0]);
//                 int z = Integer.parseInt(dataArr[2]);
//                 if (x > 0 && x < 32 && z > 0 && z < 10000) {
//                     return true;
//                 }
//             }
//         } catch (Exception e) {
//             return false;
//         }
//         return false;
//     }
//
//
//     public static String getCellValueAsString(Cell cell) {
//         if (cell == null) {
//             return "";
//         }
//         String cellValue;
//         switch (cell.getCellType()) {
//             case STRING:
//                 cellValue = cell.getStringCellValue();
//                 break;
//             case NUMERIC:
//                 cellValue = String.valueOf(Double.valueOf(cell.getNumericCellValue()).longValue());
//                 break;
//             case BOOLEAN:
//                 cellValue = String.valueOf(cell.getBooleanCellValue());
//                 break;
//             case FORMULA:
//                 cellValue = cell.getCellFormula();
//                 break;
//             case BLANK:
//                 cellValue = "";
//                 break;
//             default:
//                 cellValue = "";
//                 break;
//         }
//         return cellValue;
//     }
//
//
// }
