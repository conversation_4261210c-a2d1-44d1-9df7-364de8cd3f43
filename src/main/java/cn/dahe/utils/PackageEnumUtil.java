package cn.dahe.utils;

import cn.dahe.enums.BaseEnum;

import java.util.Arrays;
import java.util.Objects;

/**
 * 枚举工具类
 *
 * <AUTHOR>
 * @date 2024-01-24
 */
public class PackageEnumUtil {

    /**
     * 根据value值获取对应的枚举实例
     *
     * @param enumClass 枚举类
     * @param value    值
     * @param <T>      枚举类型
     * @return 枚举实例
     */
    /**
     * 根据value值获取对应的枚举实例，如果未找到返回null
     *
     * @param enumClass 枚举类
     * @param value     值
     * @param <T>       枚举类型
     * @return 枚举实例
     */
    public static <T extends Enum<T> & BaseEnum> T getEnumByValue(Class<T> enumClass, Integer value) {
        return getEnumByValue(enumClass, value, null);
    }

    /**
     * 根据value值获取对应的枚举实例
     *
     * @param enumClass   枚举类
     * @param value       值
     * @param defaultEnum 默认值
     * @param <T>         枚举类型
     * @return 枚举实例
     */
    public static <T extends Enum<T> & BaseEnum> T getEnumByValue(Class<T> enumClass, Integer value, T defaultEnum) {
        if (value == null) {
            return defaultEnum;
        }
        return Arrays.stream(enumClass.getEnumConstants())
                .filter(e -> Objects.equals(e.getValue(), value))
                .findFirst()
                .orElse(defaultEnum);
    }
}