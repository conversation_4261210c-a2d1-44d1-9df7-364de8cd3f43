package cn.dahe.utils;


import cn.dahe.common.constants.CacheConstants;
import cn.dahe.model.dto.Result;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.RandomUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.springframework.data.redis.core.StringRedisTemplate;
import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * Description : 短信
 * Date : 2019/10/18 9:43
 *
 * <AUTHOR> fy
 */

@Slf4j
public class SmsUtil {

    private static final String smsApi = "https://sms.dahe.cn/dahe/sms/daheAx/bindPhone";

    private static final String smsLoginApi = "https://sms.dahe.cn/dahe/sms/daheAx/login";
    private static final String DHAX_BIND_PHONE = "SMS_493365447";

   private static final String DHAX_LOGIN = "SMS_493260500";


    public static Result<String> sendSmsAccount(String phoneNum) {
        String sign = Objects.requireNonNull(SecurityUtil.MD5(DHAX_BIND_PHONE +
                DateUtil.format(new Date(), "yyyyMMdd"))).toUpperCase();
        StringRedisTemplate stringRedisTemplate = SpringUtils.getBean(StringRedisTemplate.class);
        String encodePhone = Base64.encode(phoneNum);
        String s = stringRedisTemplate.opsForValue().get(CacheConstants.SMS_CODE_BIND_PHONE + encodePhone);
        if (StringUtils.isNotBlank(s)) {
            log.info("已发送的验证码信息：{}", s);
            return Result.error("请勿重复获取，上一个验证码5分钟内有效，请继续使用");
        }
        Map<String, String> map = new HashMap<>(2);
        String randomNumbers = RandomUtil.randomNumbers(6);
        map.put("mobile", phoneNum);
        map.put("token", sign);
        map.put("code", randomNumbers);
        Map<String, String> headers = new HashMap<>(1);
        Response response = OkHttpUtils.newsTimeOutInstance(60).doPostHeader(smsApi, map, headers);
        if (response == null || response.code() != 200) {
            try {
                log.info("返回参数：{}", response.body().string());
                //5分钟内有效

            } catch (IOException e) {
                e.printStackTrace();
            }
            return Result.error("请求失败，请联系技术人员！");
        }
        try {
            log.info("返回参数：{}", response.body().string());
            //5分钟内有效

        } catch (IOException e) {
            e.printStackTrace();
        }
        stringRedisTemplate.opsForValue().set(CacheConstants.SMS_CODE_BIND_PHONE + encodePhone, randomNumbers, 5, TimeUnit.MINUTES);
        return Result.ok();
    }
    public static Result<String> sendLoginSmsAccount(String phoneNum) {
        String sign = Objects.requireNonNull(SecurityUtil.MD5(DHAX_LOGIN +
                DateUtil.format(new Date(), "yyyyMMdd"))).toUpperCase();
        StringRedisTemplate stringRedisTemplate = SpringUtils.getBean(StringRedisTemplate.class);
        String encodePhone = Base64.encode(phoneNum);
        String s = stringRedisTemplate.opsForValue().get(CacheConstants.SMS_CODE_LOGIN_PHONE + encodePhone);
        if (StringUtils.isNotBlank(s)) {
            log.info("已发送的验证码信息：{}", s);
            return Result.error("请勿重复获取，上一个验证码5分钟内有效，请继续使用");
        }
        Map<String, String> map = new HashMap<>(2);
        String randomNumbers = RandomUtil.randomNumbers(6);
        map.put("mobile", phoneNum);
        map.put("token", sign);
        map.put("code", randomNumbers);
        Map<String, String> headers = new HashMap<>(1);
        Response response = OkHttpUtils.newsTimeOutInstance(60).doPostHeader(smsLoginApi, map, headers);
        if (response == null || response.code() != 200) {
            try {
                log.info("返回参数：{}", response.body().string());
                //5分钟内有效
            } catch (IOException e) {
                e.printStackTrace();
            }
            return Result.error("请求失败，请联系技术人员！");
        }
        try {
            log.info("返回参数：{}", response.body().string());
            //5分钟内有效

        } catch (IOException e) {
            e.printStackTrace();
        }
        stringRedisTemplate.opsForValue().set(CacheConstants.SMS_CODE_LOGIN_PHONE + encodePhone, randomNumbers, 5, TimeUnit.MINUTES);
        return Result.ok();
    }
    public static Result<String> checkSmsCode(String phone, String code) {
        StringRedisTemplate stringRedisTemplate = SpringUtils.getBean(StringRedisTemplate.class);
        String encodePhone = Base64.encode(phone);
        String s = stringRedisTemplate.opsForValue().get(CacheConstants.SMS_CODE_BIND_PHONE + encodePhone);
        if (StringUtils.isBlank(s)) {
            return Result.error("验证码不存在");
        }
        if (!s.equals(code)) {
            return Result.error("验证码错误");
        }
        return Result.ok();
    }

    /**
     * 删除验证码
     * @param phone
     */
    public static void removeSmsCode(String phone) {
        StringRedisTemplate stringRedisTemplate = SpringUtils.getBean(StringRedisTemplate.class);
        String encodePhone = Base64.encode(phone);
        stringRedisTemplate.delete(CacheConstants.SMS_CODE_BIND_PHONE + encodePhone);
    }
}
