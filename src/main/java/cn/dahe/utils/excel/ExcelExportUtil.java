package cn.dahe.utils.excel;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HtmlUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * Excel导出工具类
 * 提供通用的Excel导出功能
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Slf4j
public class ExcelExportUtil {

    /**
     * 通用导出Excel
     *
     * @param title    标题
     * @param headers  表头
     * @param columns  数据列
     * @param dataList 数据列表
     * @param response 响应对象
     */
    public static void commonExport(String title, String[] headers, String[] columns, List<Map<String, Object>> dataList,
                                    HttpServletResponse response) throws IOException {

        try {
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode(title + "_" + System.currentTimeMillis(), "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

            // 创建Excel工作簿
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet(title);

            // 创建样式
            CellStyle headerStyle = createHeaderStyle(workbook);
            CellStyle dataStyle = createDataStyle(workbook);

            // 创建标题行
            Row headerRow = sheet.createRow(0);
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }

            // 填充数据
            for (int i = 0; i < dataList.size(); i++) {
                Row row = sheet.createRow(i + 1);
                Map<String, Object> data = dataList.get(i);
                for (int j = 0; j < columns.length; j++) {
                    Cell cell = row.createCell(j);
                    if (j == 0 && columns[j].equals("id")) {
                        cell.setCellValue(i + 1);
                        cell.setCellStyle(dataStyle);
                    } else {
                        String cellValue = data.get(columns[j]) != null ? data.get(columns[j]).toString() : "";
                        cell.setCellValue(cellValue);
                        cell.setCellStyle(dataStyle);
                    }
                }
            }

            // 自动调整列宽
            for (int i = 0; i < headers.length; i++) {
                int maxLength = headers[i].getBytes().length;
                for (Map<String, Object> data : dataList) {
                    String cellValue = data.get(columns[i]) != null ? data.get(columns[i]).toString() : "";
                    if (cellValue.getBytes().length > maxLength) {
                        maxLength = cellValue.getBytes().length;
                    }
                }

                // 限制最大列宽，避免超过Excel限制
                // Excel最大列宽是255个字符，转换为单位是255*256=65280
                int columnWidth = Math.min((maxLength + 2) * 256, 255 * 256);
                sheet.setColumnWidth(i, columnWidth);
            }

            // 写入响应流
            workbook.write(response.getOutputStream());
            workbook.close();

        } catch (Exception e) {
            log.error("导出Excel失败", e);
            throw new IOException("导出失败：" + e.getMessage());
        }
    }

    /**
     * 创建表头样式
     *
     * @param workbook 工作簿
     * @return 表头单元格样式
     */
    private static CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle headerStyle = workbook.createCellStyle();

        // 设置背景色为灰色
        headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 设置字体加粗和大小
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerFont.setFontHeightInPoints((short) 12);  // 设置字体大小为12
        headerStyle.setFont(headerFont);

        // 设置边框
        headerStyle.setBorderTop(BorderStyle.THIN);
        headerStyle.setBorderBottom(BorderStyle.THIN);
        headerStyle.setBorderLeft(BorderStyle.THIN);
        headerStyle.setBorderRight(BorderStyle.THIN);

        // 设置居中对齐
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        return headerStyle;
    }

    /**
     * 创建数据行样式
     *
     * @param workbook 工作簿
     * @return 数据行单元格样式
     */
    private static CellStyle createDataStyle(Workbook workbook) {
        CellStyle dataStyle = workbook.createCellStyle();

        // 设置边框
        dataStyle.setBorderTop(BorderStyle.THIN);
        dataStyle.setBorderBottom(BorderStyle.THIN);
        dataStyle.setBorderLeft(BorderStyle.THIN);
        dataStyle.setBorderRight(BorderStyle.THIN);

        // 设置垂直居中
        dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        dataStyle.setAlignment(HorizontalAlignment.LEFT); // 添加这行

        // 设置字体大小
        Font dataFont = workbook.createFont();
        dataFont.setFontHeightInPoints((short) 12);  // 设置字体大小为12
        dataStyle.setFont(dataFont);

        return dataStyle;
    }

    /**
     * 写入单行数据到Excel
     *
     * @param sheet   工作表
     * @param rowNum  行号
     * @param rowData 行数据
     */
    private static void writeRow(Sheet sheet, int rowNum, String[] rowData) {
        Row row = sheet.createRow(rowNum);
        CellStyle dataStyle = createDataStyle(sheet.getWorkbook());

        for (int j = 0; j < rowData.length; j++) {
            Cell cell = row.createCell(j);
            cell.setCellValue(rowData[j] != null ? rowData[j] : "");
            cell.setCellStyle(dataStyle);
        }
    }

    /**
     * 格式化日期为字符串
     *
     * @param date 日期
     * @return 格式化后的日期字符串
     */
    public static String formatDate(Date date) {
        if (date == null) {
            return "";
        }
        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(date);
    }

    /**
     * 安全获取字符串值
     *
     * @param value 值
     * @return 字符串值，null时返回空字符串
     */
    public static String safeString(Object value) {
        return value != null ? value.toString() : StrUtil.EMPTY;
    }

    /**
     * 安全获取字符串值，并清除HTML标签
     *
     * @param value 值
     * @return 字符串值，null时返回空字符串，会清除HTML标签
     */
    public static String safeStringWithoutHtml(Object value) {
        if (value == null) {
            return StrUtil.EMPTY;
        }
        return HtmlUtil.cleanHtmlTag(value.toString());
    }

    public static <T> void exportSingleSheetExcel(HttpServletResponse response,
                                                  String fileName,
                                                  SheetConfig<T> sheetConfigs) throws IOException {
        exportMultiSheetExcel(response, fileName, Collections.singletonList(sheetConfigs));
    }

    /**
     * 多Sheet页Excel导出方法
     *
     * @param response     HTTP响应对象
     * @param fileName     文件名（不包含扩展名）
     * @param sheetConfigs Sheet配置列表，每个配置包含sheet名称、表头和数据
     * @throws IOException 导出异常
     */
    public static void exportMultiSheetExcel(HttpServletResponse response,
                                             String fileName,
                                             List<SheetConfig<?>> sheetConfigs) throws IOException {
        try {
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String encodedFileName = URLEncoder.encode(fileName + "_" + System.currentTimeMillis(), "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName + ".xlsx");

            Workbook workbook = new XSSFWorkbook();

            // 创建样式（在工作簿级别创建，避免重复创建）
            CellStyle headerStyle = createHeaderStyle(workbook);
            CellStyle dataStyle = createDataStyle(workbook);

            // 处理每个sheet
            for (SheetConfig<?> config : sheetConfigs) {
                Sheet sheet = workbook.createSheet(config.getSheetName());

                // 创建标题行
                Row headerRow = sheet.createRow(0);
                String[] headers = config.getHeaders();
                for (int i = 0; i < headers.length; i++) {
                    Cell cell = headerRow.createCell(i);
                    cell.setCellValue(headers[i]);
                    cell.setCellStyle(headerStyle);
                }

                // 填充数据
                int currentRow = 1;
                // 处理普通数据
                for (String[] rowData : config.mapDataList()) {
                    Row row = sheet.createRow(currentRow);
                    for (int j = 0; j < rowData.length; j++) {
                        Cell cell = row.createCell(j);
                        cell.setCellValue(rowData[j] != null ? rowData[j] : "");
                        cell.setCellStyle(dataStyle);
                    }
                    currentRow++;
                }

                // 自动调整列宽
                for (int i = 0; i < headers.length; i++) {
                    int maxLength = headers[i].getBytes().length;
                    for (String[] rowData : config.mapDataList()) {
                        if (i < rowData.length) {
                            String cellValue = rowData[i] != null ? rowData[i] : "";
                            if (cellValue.getBytes().length > maxLength) {
                                maxLength = cellValue.getBytes().length;
                            }
                        }
                    }

                    // 限制最大列宽，避免超过Excel限制
                    // Excel最大列宽是255个字符，转换为单位是255*256=65280
                    int columnWidth = Math.min((maxLength + 2) * 256, 255 * 256);
                    sheet.setColumnWidth(i, columnWidth);
                }
            }

            // 写入响应流
            workbook.write(response.getOutputStream());
            workbook.close();
        } catch (Exception e) {
            log.error("导出多Sheet页Excel失败", e);
            throw new IOException("导出失败：" + e.getMessage());
        }
    }
}