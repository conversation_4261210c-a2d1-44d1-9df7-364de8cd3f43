package cn.dahe.utils.excel;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Data;

import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Sheet配置类
 *
 * @param <T> 数据类型
 */
@Data
public class SheetConfig<T> {
    private String sheetName;
    private String[] headers;
    private List<T> dataList;
    private Function<T, String[]> dataMapper;

    public SheetConfig(String sheetName, String[] headers, List<T> dataList, Function<T, String[]> dataMapper) {
        this.sheetName = StrUtil.blankToDefault(sheetName, "sheet");
        this.headers = headers;
        this.dataList = dataList;
        this.dataMapper = dataMapper;
    }

    public List<String[]> mapDataList() {
        if (CollUtil.isEmpty(dataList)) {
            return CollUtil.newArrayList();
        }
        return dataList.stream()
                .map(dataMapper)
                .collect(Collectors.toList());
    }

    /**
     * 创建Sheet配置的静态工厂方法
     *
     * @param sheetName  sheet名称
     * @param headers    表头数组
     * @param dataList   数据列表
     * @param dataMapper 数据映射函数
     * @param <T>        数据类型
     * @return Sheet配置实例
     */
    public static <T> SheetConfig<T> of(String sheetName, String[] headers, List<T> dataList, Function<T, String[]> dataMapper) {
        return new SheetConfig<>(sheetName, headers, dataList, dataMapper);
    }

    /**
     * 使用字段映射创建Sheet配置
     *
     * @param sheetName     sheet名称
     * @param fieldMappings 字段映射列表
     * @param dataList      数据列表
     * @param <T>           数据类型
     * @return Sheet配置实例
     */
    public static <T> SheetConfig<T> of(String sheetName, List<FieldMapping<T, ?>> fieldMappings, List<T> dataList) {
        // 提取表头
        String[] headers = fieldMappings.stream()
                .map(FieldMapping::getHeaderName)
                .toArray(String[]::new);

        // 创建数据映射函数
        Function<T, String[]> dataMapper = data -> {
            String[] rowData = new String[fieldMappings.size()];
            for (int i = 0; i < fieldMappings.size(); i++) {
                FieldMapping<T, ?> mapping = fieldMappings.get(i);
                rowData[i] = mapping.getFieldValue(data);
            }
            return rowData;
        };

        return new SheetConfig<>(sheetName, headers, dataList, dataMapper);
    }

}
