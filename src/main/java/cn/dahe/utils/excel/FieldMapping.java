package cn.dahe.utils.excel;

import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Data;

import java.util.Date;
import java.util.function.Function;

/**
 * Excel字段映射配置
 */
@Data
public class FieldMapping<T, R> {
    /**
     * 字段名称
     */
    private String fieldName;

    /**
     * getter方法引用
     */
    private Function<T, R> fieldGetter;

    private Function<R, String> fieldMapper;
    /**
     * 表头名称
     */
    private String headerName;


    public FieldMapping(String fieldName, String headerName) {
        this.fieldName = fieldName;
        this.headerName = headerName;
    }

    public FieldMapping(Function<T, R> fieldGetter, Function<R, String> fieldMapper, String fieldName, String headerName) {
        this.fieldName = fieldName;
        this.headerName = headerName;
        this.fieldGetter = fieldGetter;
        this.fieldMapper = fieldMapper;
    }

    /**
     * 创建基础字段映射（使用字段名）
     */
    public static FieldMapping<?, ?> of(String fieldName, String headerName) {
        return new FieldMapping<>(fieldName, headerName);
    }

    /**
     * 创建带有自定义映射的字段映射（使用字段名）
     */
    public static <T, R> FieldMapping<T, R> of(Function<T, R> fieldGetter, String headerName) {
        return new FieldMapping<>(fieldGetter, null, null, headerName);
    }

    public static <T, R> FieldMapping<T, R> of(Function<T, R> fieldGetter, Function<R, String> fieldMapper, String headerName) {
        return new FieldMapping<>(fieldGetter, fieldMapper, null, headerName);
    }


    /**
     * 获取字段值
     */
    public String getFieldValue(T bean) {
        R value = fieldName != null ? defaultFieldGetter(bean) : fieldGetter.apply(bean);
        return fieldMapper != null ? fieldMapper.apply(value) : defaultFieldMapper(value);
    }

    private String defaultFieldMapper(R value) {
        if (value == null) {
            return StrUtil.EMPTY;
        }
        if (value instanceof Date) {
            return ExcelExportUtil.formatDate((Date) value);
        }
        return ExcelExportUtil.safeString(value);
    }

    private R defaultFieldGetter(T bean) {
        try {
            return (R) ReflectUtil.getFieldValue(bean, fieldName);
        } catch (Exception e) {
            return null;
        }
    }
}
