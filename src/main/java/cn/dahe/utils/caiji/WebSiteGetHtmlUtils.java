package cn.dahe.utils.caiji;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.net.*;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.zip.GZIPInputStream;

/**
 * 增强版网页内容采集工具类
 * 支持动态编码识别、超时设置、压缩处理、重定向跟随等功能
 *
 * <AUTHOR>
 * @date 2025-09-02
 */
public class WebSiteGetHtmlUtils {
    private static final Logger logger = LoggerFactory.getLogger(WebSiteGetHtmlUtils.class);

    // 默认连接超时时间(毫秒)
    private static final int DEFAULT_CONNECT_TIMEOUT = 5000;
    // 默认读取超时时间(毫秒)
    private static final int DEFAULT_READ_TIMEOUT = 10000;
    // 默认User-Agent
    private static final String DEFAULT_USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) " +
            "AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";

    public static void main(String[] args) {
        String urlStr = "https://www.zut.edu.cn";
        String title = "河南省普通高等学校电子信息类专业教指委工作会议暨“智能驱动·产教融合：新时代工科教育创新与学科发展”研讨会在我校举办";

        try {
            // 获取网页源代码
            String sourceCode = getWebSourceCode(urlStr);

            if (sourceCode != null) {
                // 判断标题是否存在
                boolean containsTitle = isTitlePresent(sourceCode, title);
                logger.info("标题是否存在: {}", containsTitle);
//                // 输出部分源代码（避免日志过大）
//                if (sourceCode.length() > 500) {
//                    logger.info("网页源代码前500字符: {}", sourceCode.substring(0, 500));
//                } else {
//                    logger.info("网页源代码: {}", sourceCode);
//                }
            }
        } catch (Exception e) {
            logger.error("获取网页内容失败", e);
        }
    }

    /**
     * 根据URL获取网页源代码
     *
     * @param urlStr 网页URL字符串
     * @return 网页源代码，失败时返回null
     */
    public static String getWebSourceCode(String urlStr) {
        if (urlStr == null || urlStr.trim().isEmpty()) {
            logger.error("URL不能为空");
            return null;
        }

        HttpURLConnection connection = null;
        BufferedReader reader = null;

        try {
            URL url = new URL(urlStr);
            connection = (HttpURLConnection) url.openConnection();

            // 设置基本请求属性
            connection.setRequestMethod("GET");
            connection.setUseCaches(false);
            connection.setInstanceFollowRedirects(true); // 自动跟随重定向
            connection.setConnectTimeout(DEFAULT_CONNECT_TIMEOUT);
            connection.setReadTimeout(DEFAULT_READ_TIMEOUT);

            // 设置请求头
            connection.setRequestProperty("User-Agent", DEFAULT_USER_AGENT);
            connection.setRequestProperty("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8");
            connection.setRequestProperty("Accept-Language", "zh-CN,zh;q=0.9");
            connection.setRequestProperty("Accept-Encoding", "gzip, deflate");

            // 建立连接
            connection.connect();

            // 检查响应码
            int responseCode = connection.getResponseCode();
            if (responseCode < 200 || responseCode >= 300) {
                logger.error("请求失败，响应码: {}", responseCode);
                return null;
            }

            // 获取内容编码
            String charset = getCharsetFromResponse(connection);

            // 处理可能的压缩
            InputStream inputStream = connection.getInputStream();
            String contentEncoding = connection.getContentEncoding();
            if (contentEncoding != null && contentEncoding.contains("gzip")) {
                inputStream = new GZIPInputStream(inputStream);
            }

            // 读取内容
            reader = new BufferedReader(new InputStreamReader(inputStream, charset));
            StringBuilder sourceCode = new StringBuilder();
            String line;

            while ((line = reader.readLine()) != null) {
                sourceCode.append(line).append("\n");
            }

            return sourceCode.toString();

        } catch (MalformedURLException e) {
            logger.error("URL格式不正确: {}", urlStr, e);
        } catch (SocketTimeoutException e) {
            logger.error("连接或读取超时: {}", urlStr, e);
        } catch (IOException e) {
            logger.error("IO异常: {}", urlStr, e);
        } finally {
            // 关闭资源
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e) {
                    logger.warn("关闭Reader失败", e);
                }
            }
            if (connection != null) {
                connection.disconnect();
            }
        }

        return null;
    }

    /**
     * 从响应中获取字符编码
     */
    private static String getCharsetFromResponse(HttpURLConnection connection) {
        String contentType = connection.getContentType();
        String charset = StandardCharsets.UTF_8.name();

        if (contentType != null && contentType.contains("charset=")) {
            try {
                String[] parts = contentType.split("charset=");
                if (parts.length > 1) {
                    String charsetCandidate = parts[1].split(";")[0].trim();
                    // 验证编码是否有效
                    if (Charset.isSupported(charsetCandidate)) {
                        charset = charsetCandidate;
                    }
                }
            } catch (Exception e) {
                logger.warn("解析字符编码失败，使用默认编码: {}", charset, e);
            }
        }

        logger.debug("使用字符编码: {}", charset);
        return charset;
    }

    /**
     * 判断标题是否在源码中（忽略HTML标签和大小写）
     */
    public static boolean isTitlePresent(String sourceCode, String title) {
        if (sourceCode == null || title == null) {
            return false;
        }

        // 简单处理：移除HTML标签后再判断
        String plainText = sourceCode.replaceAll("<[^>]+>", "");
        // 忽略大小写判断
        return plainText.toLowerCase().contains(title.toLowerCase());
    }
}
