package cn.dahe.utils;


import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Iterator;
import java.util.Map;
import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR>
 */
public class OkHttpUtils {

    private static final Logger logger = LoggerFactory.getLogger(OkHttpUtils.class);

    private static OkHttpClient mOkHttpClient;

    //2.暴露出一个方法，返回当前类的对象
    private static OkHttpUtils mInstance;

    public static OkHttpUtils newInstance() {
        if (mInstance == null) {
            //实例化对象
            //加上一个同步锁，只能有一个执行路径进入
            synchronized (OkHttpUtils.class) {
                if (mInstance == null) {
                    mInstance = new OkHttpUtils();
                }
                if (mOkHttpClient == null) {
                    mOkHttpClient = new OkHttpClient();
                }
            }
        }
        return mInstance;
    }


    /**
     * 带连接时间的实例
     *
     * @param seconds
     * @return
     */
    public static OkHttpUtils newsTimeOutInstance(int seconds) {
        if (mInstance == null) {
            //实例化对象
            //加上一个同步锁，只能有一个执行路径进入
            synchronized (OkHttpUtils.class) {
                if (mInstance == null) {
                    mInstance = new OkHttpUtils();
                }
                if (mOkHttpClient == null) {
                    mOkHttpClient = new OkHttpClient.Builder().readTimeout(seconds, TimeUnit.SECONDS).build();
                }
            }
        }
        return mInstance;
    }

    /**
     * Get请求
     *
     * @param url
     */
    public Response doGet(String url) {
        Response response = null;
        Request request = new Request.Builder()
                .url(url)
                .build();
        try {
            response = mOkHttpClient.newCall(request).execute();
        } catch (IOException e) {
            logger.info(">>>>>>>>>>>>okhttp get请求异常 ");
            e.printStackTrace();
        }
        return response;
    }

    /**
     * Post请求发送键值对数据
     *
     * @param
     * @param url
     * @param mapParams
     */
    public Response doPostHeader(String url, Map<String, String> mapParams, Headers headers) {
        Response response = null;
        FormBody.Builder builder = new FormBody.Builder();
        for (String key : mapParams.keySet()) {
            builder.add(key, mapParams.get(key));
        }
        Request request = new Request.Builder().headers(headers)
                .url(url)
                .post(builder.build())
                .build();
        try {
            response = mOkHttpClient.newCall(request).execute();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return response;
    }


    /**
     * Post请求发送键值对数据
     *
     * @param
     * @param url
     * @param mapParams
     */
    public Response doPost(String url, Map<String, String> mapParams) {
        Response response = null;
        FormBody.Builder builder = new FormBody.Builder();
        for (String key : mapParams.keySet()) {
            builder.add(key, mapParams.get(key));
        }
        Request request = new Request.Builder()
                .url(url)
                .post(builder.build())
                .build();
        try {
            response = mOkHttpClient.newCall(request).execute();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return response;
    }


    /**
     * Post请求发送键值对数据
     *
     * @param
     * @param url
     * @param mapParams
     */
    public Response doPostHeader(String url, Map<String, String> mapParams, Map<String, String> header) {

        Response response = null;
        FormBody.Builder builder = new FormBody.Builder();
        for (String key : mapParams.keySet()) {
            builder.add(key, mapParams.get(key));
        }
        Request request = new Request.Builder().headers(setHeaders(header))
                .url(url)
                .post(builder.build())
                .build();
        try {
            response = mOkHttpClient.newCall(request).execute();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return response;
    }

    public Response baiduPost(String url, String param) {
        Response response = null;
        RequestBody body = RequestBody.create(MediaType.parse("text/plain; charset=utf-8")
                , param);
        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .build();
        try {
            response = mOkHttpClient.newCall(request).execute();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return response;
    }

    /**
     * Post请求发送JSON数据
     * @param url
     * @param jsonParams
     */
    public Response doPost(String url, String jsonParams) {
        Response response;
        RequestBody body = RequestBody.create(MediaType.parse("application/json; charset=utf-8")
                , jsonParams);
        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .build();
        try {
            response = mOkHttpClient.newCall(request).execute();
        } catch (Exception e) {
            logger.info(">>>>>>>>>>okhttp post超时。");
            return null;
        }
        return response;
    }

    /**
     * 加token的post请求
     *
     * @param url
     * @param jsonParams
     * @param token
     * @return
     */
    public Response doPostJson(String url, String jsonParams, String token) {
        logger.info(" >>>>> url : {} token : {}", url, token);
        Response response;
        RequestBody body = RequestBody.create(MediaType.parse("application/json; charset=utf-8")
                , jsonParams);
        Request request = new Request.Builder().addHeader("token", token)
                .url(url)
                .post(body)
                .build();
        try {
            response = mOkHttpClient.newCall(request).execute();
        } catch (Exception e) {
            logger.info(">>>>>>>>>>okhttp post超时。");
            return null;
        }
        return response;
    }

    /**
     * 带header头的post请求
     *
     * @param url       请求地址
     * @param mapParams 参数
     * @param header    header
     */
    public Response doPostAddHeader(String url, Map<String, String> mapParams, Map<String, String> header) {
        Response response;
        FormBody.Builder builder = new FormBody.Builder();
        for (String key : mapParams.keySet()) {
            if (StringUtils.isNotBlank(mapParams.get(key))) {
                builder.add(key, mapParams.get(key));
            }
        }
        Headers sendHeader = setHeaders(header);
        logger.info("=========== header : {}", sendHeader.toString());
        Request request = new Request.Builder()
                .url(url)
                .post(builder.build()).headers(sendHeader)
                .build();
        try {
            response = mOkHttpClient.newCall(request).execute();
        } catch (Exception e) {
            logger.info(">>>>>>>>>> okhttp post超时。");
            return null;
        }
        return response;
    }


    public interface HttpCallBack {
        void onError(Response response, IOException e);
        void onSuccess(Response response, String result);
    }

    public void asyncGet(String url, final HttpCallBack httpCallBack) {
        final Request request = new Request.Builder().url(url).build();
        mOkHttpClient.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                logger.info(">>>>>>>>>>>>>>>>>>asyncGeet onFialure, msg {}", e.getMessage());
            }
            @Override
            public void onResponse(Call call, Response response) throws IOException {
                final String result = response.body().string();
                if (httpCallBack != null) {
                    httpCallBack.onSuccess(response, result);
                }
            }
        });
    }


    public void asyncPost(String url, RequestBody formBody, final HttpCallBack httpCallBack) {
        final Request request = new Request.Builder().url(url).post(formBody).build();
        mOkHttpClient.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                logger.info(">>>>>>>>>>>>>asyncPost onFailure,msg {}", e.getMessage());
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                final String result = response.body().string();
                if (httpCallBack != null) {
                    httpCallBack.onSuccess(response, result);
                }
            }
        });
    }

    /**
     * 添加header
     *
     * @param header header
     */
    private Headers setHeaders(Map<String, String> header) {
        Headers.Builder headersBuilder = new Headers.Builder();
        if (header != null) {
            Iterator<String> iterator = header.keySet().iterator();
            String key;
            while (iterator.hasNext()) {
                key = iterator.next();
                headersBuilder.add(key, header.get(key));
            }
        }
        return headersBuilder.build();
    }
}
