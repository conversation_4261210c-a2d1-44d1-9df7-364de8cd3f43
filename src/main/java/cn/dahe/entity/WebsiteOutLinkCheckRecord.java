package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.annotation.ColumnComment;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsAutoIncrement;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsKey;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
@TableName("t_website_out_link_check_record")
public class WebsiteOutLinkCheckRecord {

    @IsKey
    @IsAutoIncrement
    @Column("id")
    @ColumnComment("ID")
    private Integer id;

    @Column(value = "web_id", defaultValue = "0")
    @ColumnComment("网站Id")
    private Integer webId;

    @Column(value = "web_name")
    @ColumnComment("网站名称")
    private String webName;

    @Column(name="web_url", type = MySqlTypeConstant.TEXT)
    @ColumnComment("网站地址")
    private String webUrl;

    @Column(name="link_url", type = MySqlTypeConstant.TEXT)
    @ColumnComment("外链地址")
    private String linkUrl;

    @Column(value = "snapshot", type = MySqlTypeConstant.LONGTEXT)
    @ColumnComment("死链快照")
    private String snapshot;

    @Column(value = "source_page", type = MySqlTypeConstant.TEXT)
    @ColumnComment("来源页面")
    private String sourcePage;

    @Column(name = "type", comment = "类型：0普通 1附件", defaultValue = "0")
    private int type;

    @Column(name = "filter", comment = "是否过滤 0否 1是", defaultValue = "0")
    private int filter;

    @Column(name = "check_time", comment = "访问时间/检查时间", type = MySqlTypeConstant.DATETIME)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date checkTime;

    @Column(name = "create_time", comment = "添加时间", type = MySqlTypeConstant.DATETIME)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @Column("sign")
    @ColumnComment("唯一签名")
    private String sign;

    @Column(name = "process_type", comment = "解析处理类型，和栏目值保持一致", type = MySqlTypeConstant.INT)
    private Integer processType;

}
