package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsAutoIncrement;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsKey;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import lombok.Data;

import java.util.Date;

/**
 * 预警方案-第三方平台关联表
 * <AUTHOR>
 */
@Data
@TableName("t_warn_plan_push_user")
public class WarnPlanPushUser {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    @IsKey
    @IsAutoIncrement
    @Column(name = "id", type = MySqlTypeConstant.INT, length = 11, comment = "主键ID")
    private Integer id;

    /**
     * 预警方案ID
     */
    @Column(name = "warn_plan_id", comment = "预警方案ID")
    private Long warnPlanId;

    /**
     * 接收人ID
     */
    @Column(name = "user_id", type = MySqlTypeConstant.INT, length = 11, comment = "接收人ID")
    private Integer userId;

    /**
     * 接收人名称
     */
    @Column(name = "user_name", type = MySqlTypeConstant.VARCHAR, length = 255, comment = "接收人名称")
    private String userName;

    //推送平台类型 0是微信 1是短信
    @Column(name = "push_type", type = MySqlTypeConstant.TINYINT, length = 1, comment = "推送平台类型 0是微信 1是短信")
    private Integer pushType;

    /**
     * 创建时间
     */
    @Column(name = "create_time", type = MySqlTypeConstant.DATETIME, comment = "创建时间")
    private Date createTime;
}
