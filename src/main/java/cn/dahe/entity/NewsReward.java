package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gitee.sunchenbin.mybatis.actable.annotation.*;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 新闻奖项管理
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
@Data
@TableName("t_news_reward")
public class NewsReward {

    @IsKey
    @IsAutoIncrement
    @Column("id")
    @ColumnComment("ID")
    private Integer id;

    @Column("name")
    @ColumnComment("名称")
    private String name;
    /**
     * 状态 -1：删除 0：停用 1：启用
     */
    @Column(name = "status", comment = "状态 0：停用 1：启用")
    @DefaultValue("1")
    private Integer status;


    /**
     * 更新人
     */
    @Column(name = "update_user_id", comment = "更新人")
    @DefaultValue("0")
    private Integer updateUserId;


    /**
     * 更新人名称
     */
    @Column(name = "update_user_name", comment = "更新人名称")
    private String updateUserName;


    @Column(name = "begin_time", comment = "奖项开始时间", type = MySqlTypeConstant.DATETIME)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginTime;

    @Column(name = "end_time", comment = "奖项结束时间", type = MySqlTypeConstant.DATETIME)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;


    /**
     * 更新时间
     */
    @Column(name = "update_time", comment = "更新时间", type = MySqlTypeConstant.DATETIME)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
