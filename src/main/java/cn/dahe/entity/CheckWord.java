package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.*;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import lombok.Data;

import java.util.Date;

/**
 * 文章检查错误词库实体类
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Data
@TableName("t_check_word")
public class CheckWord {

    @IsKey
    @IsAutoIncrement
    @TableId
    @Column("id")
    @ColumnComment("主键ID")
    private Long id;

    /**
     * 唯一索引！
     */
    @Unique
    @Column("error_word")
    @ColumnComment("错误词")
    private String errorWord;

    @Column("suggest_word")
    @ColumnComment("建议正确用词")
    private String suggestWord;

    @Column("first_error_type")
    @ColumnComment("一级错误类型ID")
    private Long firstErrorType;

    @Column("second_error_type")
    @ColumnComment("二级错误类型ID")
    private Long secondErrorType;

    @Column("third_error_type")
    @ColumnComment("三级错误类型ID")
    private Long thirdErrorType;

    @Column("error_level")
    @ColumnComment("错误等级：(严重1 一般2 疑似3 自定义词4 风险提示5)")
    private Integer errorLevel;

    @Column(value = "filter_status", type = MySqlTypeConstant.TINYINT)
    @ColumnComment("过滤状态（0-不过滤，1-过滤）")
    @DefaultValue("0")
    private Boolean filterStatus;

    @Column(name = "create_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("创建时间")
    @DefaultValue("CURRENT_TIMESTAMP")
    private Date createTime;

    @Column(name = "update_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("更新时间")
    @IgnoreUpdate
    @DefaultValue("NULL ON UPDATE CURRENT_TIMESTAMP")
    private Date updateTime;
} 