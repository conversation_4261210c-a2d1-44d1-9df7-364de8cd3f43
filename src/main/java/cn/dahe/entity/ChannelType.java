package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.*;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.persistence.Entity;
import java.util.Date;
import java.util.List;

/**
 * 栏目类型
 * 
 * <AUTHOR>
 * @date 2025-09-16
 */
@Entity
@Data
@Accessors(chain = true)
@TableName("t_channel_type")
public class ChannelType {
    @IsKey
    @IsAutoIncrement
    @Column("id")
    @ColumnComment("编号")
    @Schema(description = "编号", example = "1")
    private Long id;

    @Column("name")
    @ColumnComment("名称")
    @Schema(description = "名称", example = "微信")
    private String name;
    /**
     * 上级类型
     */
    @Column("parent_id")
    @ColumnComment("上级类型")
    @Schema(description = "上级类型", example = "1")
    @DefaultValue("0")
    private Long parentId;

    /**
     * 更新期限 单位：天
     */
    @Column(name = "update_period", comment = "更新期限 单位：天")
    @DefaultValue("0")
    private Integer updatePeriod;
    /**
     * 创建时间
     */
    @Column(name = "create_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("创建时间")
    @DefaultValue("CURRENT_TIMESTAMP")
    private Date createTime;
    /**
     * 更新时间
     */
    @Column(name = "update_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("更新时间")
    @IgnoreUpdate
    @DefaultValue("NULL ON UPDATE CURRENT_TIMESTAMP")
    private Date updateTime;
    /**
     * 删除状态
     */
    @Column(name = "del_status", comment = "删除状态")
    @DefaultValue("0")
    private Integer delStatus;
    /**
     * 排序
     */
    @Column(name = "sort_num", comment = "排序")
    @DefaultValue("0")
    private Integer sortNum;

    @TableField(exist = false)
    @Schema(description = "子集")
    private List<ChannelType> children;
}
