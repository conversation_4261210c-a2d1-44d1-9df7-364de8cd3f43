package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsKey;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 采集文章实体类
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
@TableName("t_article_push_control")
@Schema(name = "文章推送对照", description = "保证推送文章id记录在这里，用于排查未保存的文章")
public class ArticlePushControl {

    @IsKey
    @TableId(type = IdType.INPUT)
    @Column(value = "id", comment = "主键ID")
    private Long id;

    @Column(value = "url", comment = "文章链接", type = MySqlTypeConstant.TEXT)
    private String url;

    @Column(value = "push_time", comment = "推送时间")
    private Date pushTime;

}