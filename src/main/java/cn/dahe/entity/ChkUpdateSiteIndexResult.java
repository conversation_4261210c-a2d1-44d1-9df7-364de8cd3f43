package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gitee.sunchenbin.mybatis.actable.annotation.*;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 首页更新结果表
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@TableName("chk_update_site_index_result")
@Schema(name = "首页更新结果表", description = "首页更新结果表")
public class ChkUpdateSiteIndexResult {

    @IsKey
    @IsAutoIncrement
    @Column("id")
    @ColumnComment("编号")
    @Schema(description = "编号", example = "1")
    private Long id;

    @Column("group_id")
    @ColumnComment("分组ID")
    @Schema(description = "分组ID", example = "1")
    private Long groupId;

    @Column("group_name")
    @ColumnComment("分组名称")
    @Schema(description = "分组名称", example = "政府网站")
    private String groupName;

    @Column("site_id")
    @ColumnComment("网站ID")
    @Schema(description = "网站ID", example = "1")
    private Integer siteId;

    @Column("site_name")
    @ColumnComment("网站名称")
    @Schema(description = "网站名称", example = "河南省人民政府")
    private String siteName;

    @Column("site_url")
    @ColumnComment("网站URL")
    @Schema(description = "网站URL", example = "https://www.henan.gov.cn")
    private String siteUrl;

    /**
     * 解析处理类型
     * 0：网站  1：微信  2：微博  3：头条号  6335：抖音号  6429：快手号  6452：微信视频号  6460：小红书
     */
    @Column(name = "process_type", comment = "解析处理类型")
    private Integer processType;

    @Column(name = "update_days_desc", type = MySqlTypeConstant.LONGTEXT)
    @ColumnComment("更新天数描述")
    @Schema(description = "更新天数描述", example = "页面上展示的时候，需要展示出一列日期：2025-07-29,2025-07-30")
    private String updateDaysDesc;

    @Column("update_days")
    @ColumnComment("更新天数")
    @Schema(description = "更新天数", example = "0")
    private Integer updateDays;

    @Column(name = "continuous_not_update_days_desc", type = MySqlTypeConstant.LONGTEXT)
    @ColumnComment("连续未更新天数描述")
    @Schema(description = "连续未更新天数描述", example = "页面上展示的时候，需要展示出一列日期：2025-07-29,2025-07-30")
    private String continuousNotUpdateDaysDesc;

    @Column("continuous_not_update_days_num")
    @ColumnComment("连续未更新天数")
    @Schema(description = "连续未更新天数", example = "0")
    private Integer continuousNotUpdateDaysNum;

    @Column(name = "last_update_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("最后一次更新时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "最后一次更新时间", example = "2025-07-30 10:30:00")
    private LocalDateTime lastUpdateTime;

    @Column(name = "update_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("更新时间，每天3:00更新")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间", example = "2025-07-30 10:30:00")
    private LocalDateTime updateTime;

}
