package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.*;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 附件检查实体类
 * 对应原型图功能需求
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@TableName("t_attachment")
@Schema(name = "附件检查", description = "网站附件检查信息")
public class Attachment {

    @IsKey
    @IsAutoIncrement
    @Column("id")
    @ColumnComment("编号")
    private Long id;

    @Column("website_id")
    private Long websiteId;

    @Column(name = "process_type", comment = "0：网站  1：微信  2：微博  3：头条号  6335：抖音号  6429：快手号  6452：微信视频号  6460：小红书")
    private Integer processType;

    @Column("attachment_name")
    @ColumnComment("附件名称，包含文件后缀")
    @Schema(description = "附件名称", example = "政策文件.pdf")
    private String attachmentName;

    @Column("attachment_type")
    @ColumnComment("附件类型")
    private String attachmentType;

    @Column("attachment_size")
    @ColumnComment("附件大小，单位KB")
    private Long attachmentSize;

    @Column("attachment_url")
    @ColumnComment("附件地址")
    @Schema(description = "附件地址", example = "https://www.example.com/files/policy.pdf")
    private String attachmentUrl;

    @Column("parent_article_id")
    @ColumnComment("附件所在的文章ID，先假设附件所在页面都应该是文章..?")
    private Long parentArticleId;

    @Column(name = "create_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("创建时间")
    @DefaultValue("CURRENT_TIMESTAMP")
    private Date createTime;

    @Column(name = "update_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("更新时间")
    @IgnoreUpdate
    @DefaultValue("NULL ON UPDATE CURRENT_TIMESTAMP")
    private Date updateTime;

    @Index
    @Column(name = "check_id")
    @ColumnComment("检测任务id")
    private Long checkId;


}
