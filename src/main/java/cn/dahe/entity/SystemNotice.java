package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.annotation.DefaultValue;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsAutoIncrement;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsKey;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
@TableName("t_system_notice")
@Schema(name = "系统公告", description = "系统公告信息")
public class SystemNotice {
    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    @IsKey
    @IsAutoIncrement
    @Column(name = "id", comment = "id")
    @Schema(description = "主键ID", example = "1")
    private Integer id;

    @Column(name = "title", length = 32)
    @Schema(description = "名称")
    private String title;

    @Column(name = "content", type = MySqlTypeConstant.TEXT)
    @Schema(description = "公告内容")
    private String content;

    /**
     * 添加人
     */
    @Column(name = "create_user_id", comment = "添加人")
    @DefaultValue("0")
    @Schema(description = "添加人ID", example = "0")
    private Integer createUserId;

    /**
     * 添加人名称
     */
    @Column(name = "create_user_name", comment = "添加人名称")
    @Schema(description = "添加人名称", example = "张三")
    private String createUserName;

    /**
     * 更新人
     */
    @Column(name = "update_user_id", comment = "更新人")
    @DefaultValue("0")
    @Schema(description = "更新人ID", example = "1")
    private Integer updateUserId;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name", comment = "更新人名称")
    @Schema(description = "更新人名称", example = "李四")
    private String updateUserName;

    /**
     * 状态
     */
    @Column(name = "status", comment = "状态 0/禁用 1/启用", type = MySqlTypeConstant.TINYINT)
    @DefaultValue("1")
    @Schema(description = "状态 0-禁用 1-启用 默认1", example = "1")
    private Integer status;

    /**
     * 添加时间
     */
    @Column(name = "create_time", comment = "添加时间", type = MySqlTypeConstant.DATETIME)
    @Schema(description = "添加时间", example = "2025-07-16 10:00:00")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time", comment = "更新时间", type = MySqlTypeConstant.DATETIME)
    @Schema(description = "更新时间", example = "2025-07-16 10:00:00")
    private Date updateTime;
}
