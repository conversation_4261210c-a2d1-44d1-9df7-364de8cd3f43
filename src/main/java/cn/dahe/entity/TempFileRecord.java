package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.*;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 临时文件记录
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("t_temp_file_record")
public class TempFileRecord {

    @IsKey
    @IsAutoIncrement
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 文件路径
     */
    @Column(name = "file_path", type = MySqlTypeConstant.TEXT)
    private String filePath;

    /**
     * 关联业务ID（比如附件ID）
     */
    @Column(name = "business_id", type = MySqlTypeConstant.BIGINT)
    private Long businessId;

    /**
     * 业务类型（比如：attachment）
     */
    @Column(name = "business_type", type = MySqlTypeConstant.VARCHAR)
    private String businessType;

    /**
     * 创建时间
     */
    @Column(name = "create_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("创建时间")
    @DefaultValue("CURRENT_TIMESTAMP")
    private Date createTime;

    /**
     * 最后一次尝试删除时间
     */
    @Column(name = "last_try_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("最后一次尝试删除时间")
    private Date lastTryTime;

    /**
     * 状态（0：待删除，1：删除成功，2：删除失败）
     */
    @Column(name = "status", type = MySqlTypeConstant.TINYINT)
    private Integer status;
}
