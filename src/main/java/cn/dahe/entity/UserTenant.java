package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsAutoIncrement;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsKey;
import com.gitee.sunchenbin.mybatis.actable.annotation.TableComment;
import com.gitee.sunchenbin.mybatis.actable.annotation.Unique;
import lombok.Data;
import lombok.experimental.Accessors;

@TableName("t_user_tenant")
@TableComment("用户租户关联")
@Accessors(chain = true)
@Data
public class UserTenant {
    @Unique(columns = {"user_id", "tenant_id"})
    @IsKey
    @IsAutoIncrement
    @Column(name = "id")
    private Integer id;

    @Column(name = "user_id", comment = "用户id")
    private Integer userId;

    @Column(name = "tenant_id", comment = "租户id")
    private Integer tenantId;
}
