package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsAutoIncrement;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsKey;
import com.gitee.sunchenbin.mybatis.actable.annotation.Unique;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import lombok.Data;

import java.util.Date;

/**
 * 预警方案-第三方平台关联表
 *
 * <AUTHOR>
 */
@Data
@TableName("t_warn_platform")
public class WarnPlanPlatform {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    @IsKey
    @IsAutoIncrement
    @Column(name = "id", type = MySqlTypeConstant.INT, length = 11, comment = "主键ID")
    private Integer id;

    /**
     * 预警方案ID
     */
    @Unique(columns = {"warn_plan_id", "website_id"})
    @Column(name = "warn_plan_id", type = MySqlTypeConstant.INT, length = 11, comment = "预警方案ID")
    private Integer warnPlanId;

    @Column(name = "website_id", comment = "website表网站ID")
    private Long websiteId;
    // /**
    //  * 第三方平台ID 0对应site_id 其余对应channel_id
    //  */
    // @Column(name = "platform_id", type = MySqlTypeConstant.INT, length = 11, comment = "第三方平台ID")
    // private Integer platformId;

    @Column(name = "platform_type", type = MySqlTypeConstant.INT, length = 11, comment = "0：网站  1：微信  2：微博  3：头条号  6335：抖音号  6429：快手号  6452：微信视频号  6460：小红书", defaultValue = "0")
    private Integer platformType;

    @Column(name = "create_time", type = MySqlTypeConstant.DATETIME, comment = "创建时间")
    private Date createTime;
}
