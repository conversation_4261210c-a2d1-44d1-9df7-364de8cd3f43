package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsAutoIncrement;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsKey;
import com.gitee.sunchenbin.mybatis.actable.annotation.TableComment;
import com.gitee.sunchenbin.mybatis.actable.annotation.Unique;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

@TableName("t_tenant")
@TableComment("租户")
@Accessors(chain = true)
@Data
public class Tenant {
    @Unique(columns = {"name", "delete_time"})
    @IsKey
    @IsAutoIncrement
    @Column(name = "id")
    private Integer id;

    @Column(name = "name", comment = "名称")
    private String name;

    @Column(name = "status", comment = "状态，1：启用、2：禁用、3：过期、4：删除", defaultValue = "1")
    private Integer status;

    @Column(name = "user_count", comment = "用户总数", defaultValue = "0")
    private Integer userCount;

    @Column(name = "site_count", comment = "网站总数", defaultValue = "0")
    private Integer siteCount;

    @Column(name = "new_media_count", comment = "新媒体总数", defaultValue = "0")
    private Integer newMediaCount;

    @Column(name = "group_count", comment = "分组总数", defaultValue = "0")
    private Integer groupCount;

    @Column(name = "team_count", comment = "团队总数", defaultValue = "0")
    private Integer teamCount;

    @Column(name = "warn_plan_count", comment = "预警计划总数", defaultValue = "0")
    private Integer warnPlanCount;

    @Column(name = "remark", comment = "备注")
    private String remark;

    @Column(name = "create_user_id", comment = "创建人id")
    private Integer createUserId;

    @Column(name = "last_update_user_id", comment = "上次更新人id")
    private Integer lastUpdateUserId;

    @Column(name = "delete_user_id", comment = "删除人id")
    private Integer deleteUserId;

    @Column(name = "expire_date", comment = "过期日期")
    private LocalDate expireDate;

    @Column(name = "create_time", comment = "创建时间", defaultValue = "current_timestamp")
    private LocalDateTime createTime;

    @Column(name = "last_update_time", comment = "上次更新时间", defaultValue = "null on update current_timestamp")
    private LocalDateTime lastUpdateTime;

    @Column(name = "delete_time", comment = "删除时间", defaultValue = "0")
    private Long deleteTime;

    @TableField(exist = false)
    private String createUserName;

    @TableField(exist = false)
    private String lastUpdateUserName;
}
