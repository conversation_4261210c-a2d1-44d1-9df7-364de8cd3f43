package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsAutoIncrement;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsKey;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 预警方案实体类
 * 对应数据库表：t_warn_plan
 */
@Data
@TableName("t_warn_plan")
public class WarnPlan {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    @IsKey
    @IsAutoIncrement
    @Column(name = "id", type = MySqlTypeConstant.INT, length = 11, comment = "主键ID")
    private Integer id;

    @Column(name = "scheme_name", type = MySqlTypeConstant.VARCHAR, length = 255, comment = "预警方案名称", isNull = false)
    private String schemeName;

    /**
     * 文章错误预警（0：关闭，1：开启）
     */
    @Schema(description = "文章错误预警：0关闭，1开启")
    @Column(name = "article_error_warn", type = MySqlTypeConstant.TINYINT, length = 1, comment = "文章错误预警：0关闭，1开启", defaultValue = "0")
    private Integer articleErrorWarn;

    /**
     * 附件错误预警（0：关闭，1：开启）
     */
    @Schema(description = "附件错误预警：0关闭，1开启")
    @Column(name = "attachment_error_warn", type = MySqlTypeConstant.TINYINT, length = 1, comment = "附件错误预警：0关闭，1开启", defaultValue = "0")
    private Integer attachmentErrorWarn;

    //===============================巡查预警推送内容：==========================================

    // /**
    //  * 巡查预警（0：关闭，1：开启） 改成true false
    //  */
    // @Column(name = "patrol_warn", type = MySqlTypeConstant.TINYINT, length = 1, comment = "巡查预警：0关闭，1开启", defaultValue = "0")
    // private Boolean patrolWarn;
    //
    // /**
    //  * 历史数据巡查预警（0：关闭，1：开启）
    //  */
    // @Column(name = "history_data_patrol_warn", type = MySqlTypeConstant.TINYINT, length = 1, comment = "历史数据巡查预警：0关闭，1开启", defaultValue = "0")
    // private Boolean historyDataPatrolWarn;


    /**
     * 更新预警（0：关闭，1：开启）
     */
    @Schema(description = "更新预警：0关闭，1开启")
    @Column(name = "update_warn", type = MySqlTypeConstant.TINYINT, length = 1, comment = "更新预警：0关闭，1开启", defaultValue = "0")
    private Boolean updateWarn;

    /**
     * 微信连续未更新天数设置
     */
    @Schema(description = "微信连续未更新天数设置")
    @Column(name = "wechat_unupdate_days", type = MySqlTypeConstant.INT, length = 3, comment = "微信连续未更新天数设置")
    private Integer wechatUnupdateDays;

    /**
     * 微博连续未更新天数设置
     */
    @Schema(description = "微博连续未更新天数设置")
    @Column(name = "weibo_unupdate_days", type = MySqlTypeConstant.INT, length = 3, comment = "微博连续未更新天数设置")
    private Integer weiboUnupdateDays;

    @Schema(description = "头条平台更新天数")
    @Column(name = "toutiao_continuous_days", type = MySqlTypeConstant.INT, length = 3, comment = "头条平台更新天数")
    private Integer toutiaoContinuousDays;

    @Schema(description = "抖音号更新天数")
    @Column(name = "douyin_continuous_days", type = MySqlTypeConstant.INT, length = 3, comment = "抖音号更新天数")
    private Integer douyinContinuousDays;
    /**
     * 快手号更新天数
     */
    @Schema(description = "快手号更新天数")
    @Column(name = "kuaishou_continuous_days", type = MySqlTypeConstant.INT, length = 3, comment = "快手号更新天数")
    private Integer kuaishouContinuousDays;
    /**
     * 微信视频号更新天数
     */
    @Schema(description = "微信视频号更新天数")
    @Column(name = "wxvideonumber_continuous_days", type = MySqlTypeConstant.INT, length = 3, comment = "微信视频号更新天数")
    private Integer wxvideonumberContinuousDays;
    /**
     * 小红书更新天数
     */
    @Schema(description = "小红书更新天数")
    @Column(name = "xiaohongshu_continuous_days", type = MySqlTypeConstant.INT, length = 3, comment = "小红书更新天数")
    private Integer xiaohongshuContinuousDays;
    /**
     * 网站更新天数
     */
    @Schema(description = "网站更新天数")
    @Column(name = "website_continuous_days", type = MySqlTypeConstant.INT, length = 3, comment = "小红书更新天数")
    private Integer websiteContinuousDays;
    /**
     * 首页更新天数设置
     */
    @Schema(description = "首页更新天数设置")
    @Column(name = "home_page_update_days", type = MySqlTypeConstant.INT, length = 3, comment = "首页更新天数设置")
    private Integer homePageUpdateDays;

    /**
     * 栏目分类，多个用逗号分隔
     */
    @Schema(description = "栏目分类，多个用逗号分隔")
    @Column(name = "column_category", type = MySqlTypeConstant.VARCHAR, length = 255, comment = "栏目分类，多个用逗号分隔")
    private String columnCategory;

    /**
     * 栏目信息天数设置
     */
    @Schema(description = "栏目信息天数设置")
    @Column(name = "column_info_days", type = MySqlTypeConstant.INT, length = 3, comment = "栏目信息天数设置")
    private Integer columnInfoDays;

    /**
     * 链接预警（0：关闭，1：开启）
     */
    @Schema(description = "链接预警：0关闭，1开启")
    @Column(name = "link_warn", type = MySqlTypeConstant.TINYINT, length = 1, comment = "链接预警：0关闭，1开启", defaultValue = "0")
    private Boolean linkWarn;

    /**
     * 连通性预警（0：关闭，1：开启）
     */
    @Schema(description = "连通性预警：0关闭，1开启")
    @Column(name = "connectivity_warn", type = MySqlTypeConstant.TINYINT, length = 1, comment = "连通性预警：0关闭，1开启", defaultValue = "0")
    private Boolean connectivityWarn;

    /**
     * 异常访问次数阈值
     */
    @Schema(description = "异常访问次数阈值")
    @Column(name = "abnormal_access_count", type = MySqlTypeConstant.INT, length = 11, comment = "异常访问次数阈值")
    private Integer abnormalAccessCount;

    /**
     * 死链预警（0：关闭，1：开启）
     */
    @Schema(description = "死链预警：0关闭，1开启")
    @Column(name = "dead_link_warn", type = MySqlTypeConstant.TINYINT, length = 1, comment = "死链预警：0关闭，1开启", defaultValue = "0")
    private Boolean deadLinkWarn;

    /**
     * 死链条数阈值
     */
    @Schema(description = "死链条数阈值")
    @Column(name = "dead_link_count", type = MySqlTypeConstant.INT, length = 11, comment = "死链条数阈值")
    private Integer deadLinkCount;

    /**
     * 死链类型：全部、图片、附件、外部链接、文章、其他，多个用逗号分隔
     */
    @Schema(description = "死链类型：全部、图片、附件、外部链接、文章、其他，多个用逗号分隔")
    @Column(name = "dead_link_types", type = MySqlTypeConstant.VARCHAR, length = 255, comment = "死链类型，多个用逗号分隔")
    private String deadLinkTypes;

    /**
     * 外链预警（0：关闭，1：开启）
     */
    @Schema(description = "外链预警：0关闭，1开启")
    @Column(name = "external_link_warn", type = MySqlTypeConstant.TINYINT, length = 1, comment = "外链预警：0关闭，1开启", defaultValue = "0")
    private Boolean externalLinkWarn;

    /**
     * 外链条数阈值
     */
    @Schema(description = "外链条数阈值")
    @Column(name = "external_link_count", type = MySqlTypeConstant.INT, length = 11, comment = "外链条数阈值")
    private Integer externalLinkCount;

    /**
     * 逻辑删除字段（0：未删除，1：已删除）
     */
    @TableLogic
    @TableField(value = "is_deleted")
    @Column(name = "is_deleted", type = MySqlTypeConstant.TINYINT, length = 1, comment = "逻辑删除：0未删除，1已删除", defaultValue = "0")
    private Boolean isDeleted;

    // ===================== 推送方式 =====================
    /**
     * 微信推送开关：0-关闭 1-开启
     */
    @Schema(description = "微信推送开关：0-关闭 1-开启")
    @Column(name = "wechat_push_enable", type = MySqlTypeConstant.TINYINT, length = 1, comment = "微信推送开关：0关闭，1开启", defaultValue = "0")
    private Boolean wechatPushEnable;

    /**
     * 短信推送开关：0-关闭 1-开启
     */
    @Schema(description = "短信推送开关：0-关闭 1-开启")
    @Column(name = "sms_push_enable", type = MySqlTypeConstant.TINYINT, length = 1, comment = "短信推送开关：0关闭，1开启", defaultValue = "0")
    private Boolean smsPushEnable;


    /**
     * 创建时间
     */
    @Column(name = "create_time", type = MySqlTypeConstant.DATETIME, comment = "创建时间", isNull = false)
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time", type = MySqlTypeConstant.DATETIME, comment = "更新时间", isNull = false)
    private Date updateTime;

}
