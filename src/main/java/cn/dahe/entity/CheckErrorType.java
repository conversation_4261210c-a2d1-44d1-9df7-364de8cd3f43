package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.*;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 错误类型实体类
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Data
@TableName("t_check_error_type")
@Schema(name = "错误类型", description = "内容检查错误类型信息")
public class CheckErrorType {

    @IsKey
    @IsAutoIncrement
    @TableId
    @Column("id")
    @ColumnComment("主键ID")
    @Schema(description = "主键ID", example = "1")
    private Long id;

    @Column("type_name")
    @ColumnComment("错误类型名称")
    @Schema(description = "错误类型名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "标题错误")
    private String typeName;

    @Column("parent_id")
    @ColumnComment("父级ID，NULL表示一级类型")
    @Schema(description = "父级ID，NULL表示一级类型", example = "1")
    private Long parentId;

    @Column("level")
    @ColumnComment("层级：1-一级分类 2-二级原因 3-三级细分")
    @Schema(description = "层级：1-一级分类 2-二级原因 3-三级细分", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer level;

    @Column("error_level")
    @ColumnComment("错误等级ID")
    @Schema(description = "错误等级ID", example = "1")
    private Long errorLevel;

    @Column("sort_order")
    @ColumnComment("同级排序")
    @DefaultValue("0")
    @Schema(description = "同级排序", example = "1")
    private Integer sortOrder;

    @Column("remark")
    @ColumnComment("说明")
    @Schema(description = "说明", example = "标题相关的错误类型")
    private String remark;

    @Column(name = "create_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("创建时间")
    @DefaultValue("CURRENT_TIMESTAMP")
    @Schema(description = "创建时间", example = "2025-07-16 10:00:00")
    private Date createTime;

    @Column(name = "update_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("更新时间")
    @IgnoreUpdate
    @DefaultValue("NULL ON UPDATE CURRENT_TIMESTAMP")
    @Schema(description = "更新时间", example = "2025-07-16 10:00:00")
    private Date updateTime;
} 