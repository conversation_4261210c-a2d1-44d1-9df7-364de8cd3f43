package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.*;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 文章检查内任务实体类
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
@Accessors(chain = true)
@TableName("t_check_task")
public class CheckTask {

    @IsKey
    @IsAutoIncrement
    @TableId
    @Column(value = "id", comment = "主键ID")
    private Long id;

    @Column(name = "relation_type", type = MySqlTypeConstant.TINYINT)
    @ColumnComment("关联类型 0 article 第一次检查 1 attachment")
    @DefaultValue("0")
    private Integer relationType;

    @Index(columns = "relation_type,relation_id")
    @Column(name = "relation_id", type = MySqlTypeConstant.BIGINT)
    private Long relationId;

    @Column(name = "check_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("检查时间")
    private Date checkTime;


    @Column(name = "check_status", comment = "检查状态 0未检查 1已检查 2检查失败待重试 3检查失败", type = MySqlTypeConstant.TINYINT)
    @DefaultValue("0")
    private Integer checkStatus;

    @Column(name = "result_status", comment = "检查结果状态 0未检查 1有结果 2无结果", type = MySqlTypeConstant.TINYINT)
    private Integer resultStatus;

    @Column(name = "create_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("创建时间")
    @DefaultValue("CURRENT_TIMESTAMP")
    private Date createTime;

    @Column(name = "update_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("更新时间")
    @IgnoreUpdate
    @DefaultValue("NULL ON UPDATE CURRENT_TIMESTAMP")
    private Date updateTime;

    /**
     * 检查任务失败类型：1-本地异常，2-接口调用失败，3-接口响应失败
     */
    @Column(name = "check_fail_type", comment = "检查任务失败类型： 0-未失败 1-本地异常，2-接口调用失败，3-接口响应失败 4-任务未启动", type = MySqlTypeConstant.TINYINT)
    @DefaultValue("0")
    private Integer checkFailType;

    /**
     * 检查任务失败信息
     */
    @Column(name = "check_fail_msg", comment = "检查任务失败信息", type = MySqlTypeConstant.TEXT)
    private String checkFailMsg;

    /**
     * 检查任务失败原因（包含失败详情、响应时间等信息）
     */
    @Column(name = "check_fail_reason", type = MySqlTypeConstant.TEXT)
    @ColumnComment("检查任务失败原因")
    private String checkFailReason;

    @Column(name = "content_status", type = MySqlTypeConstant.TINYINT)
    @ColumnComment("内容获取状态 0 未获取 1 已获取 2获取失败")
    @DefaultValue("1")
    private Integer contentStatus;

    @Column(name = "content_fail_type", comment = "内容获取失败类型 0-未失败 ", type = MySqlTypeConstant.TINYINT)
    @DefaultValue("0")
    private Integer contentFailType;
    /**
     * 内容获取失败原因
     */
    @Column(name = "content_fail_msg", type = MySqlTypeConstant.VARCHAR, length = 500)
    private String contentFailMsg;
    /**
     * 内容获取失败原因
     */
    @Column(name = "content_fail_reason", type = MySqlTypeConstant.TEXT)
    private String contentFailReason;

    @Column(name = "check_strategy", comment = "检查策略 0 查全 1 查准", type = MySqlTypeConstant.TINYINT)
    @Schema(description = "检查策略 0 查全 1 查准", example = "0")
    @DefaultValue("0")
    private Integer checkStrategy;

    @Column(name = "content_length", comment = "正文字数")
    @DefaultValue("0")
    private Integer contentLength;

} 