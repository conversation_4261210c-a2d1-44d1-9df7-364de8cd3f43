package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.annotation.ColumnComment;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsAutoIncrement;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsKey;
import lombok.Data;


@Data
@TableName("t_dict")
public class Dict {

    @IsKey
    @IsAutoIncrement
    @Column("id")
    @ColumnComment("ID")
    private Integer id;

    /**
     * 字典名称
     */
    @Column("name")
    @ColumnComment("城市名称")
    private String name;

    /**
     * 排序值，默认为id
     */
    @Column(value = "seq", defaultValue = "0")
    @ColumnComment("排序值")
    private Integer seq;

    /**
     * 状态码 -1删除 0未启用 1启用
     */
    @Column(value = "status", defaultValue = "1")
    @ColumnComment("状态码")
    private Integer status;

    /**
     * 父id
     */
    @Column(value = "pid", defaultValue = "0")
    @ColumnComment("父id")
    private Integer pid;

    /**
     * 备注
     */
    @Column("note")
    @ColumnComment("备注")
    private String note;


    /**
     * 类型
     */
    @Column("type")
    @ColumnComment("类型")
    private String type;


    @Column(value = "optional", defaultValue = "1")
    @ColumnComment("是否可选  0否 1是")
    private Integer optional;
}