package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.annotation.DefaultValue;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsAutoIncrement;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsKey;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * SSO用户信息扩展表
 *
 * <AUTHOR> @since 1.0.0 2023-01-31
 */
@Data
@TableName("t_user")
public class User {
    /**
     * 用户id与sso用户id一致
     */
    @Is<PERSON>ey
    @TableId
    @IsAutoIncrement
    @Column(name = "user_id")
    private Integer userId;


    @Column(name = "account", comment = "登陆时的账号")
    private String account;

    /**
     * 用户名
     */
    @Column(name = "user_name")
    private String userName;

    /**
     * 单位名
     */
    @Column(name = "dep_id", defaultValue = "0")
    private Integer depId;

    /**
     * 单位名
     */
    @Column(name = "dep_name")
    private String depName;


    /**
     * 密码
     */
    @Column(name = "password")
    private String password;
    
    /**
     * 地市ID
     */
    @Column(name = "city_id")
    @DefaultValue("0")
    private Integer cityId;

    /**
     * 状态 禁用 0，启用 1， 默认1
     */
    @Column(name = "status")
    @DefaultValue("1")
    private Integer status;


    /**
     * 排序
     */
    @Column(name = "seq", comment = "排序")
    @DefaultValue("0")
    private Integer seq;


    /**
     * 更新人
     */
    @Column(name = "update_user_id", comment = "更新人")
    @DefaultValue("0")
    private Integer updateUserId;


    /**
     * 更新人名称
     */
    @Column(name = "update_user_name", comment = "更新人名称")
    private String updateUserName;


    /**
     * 添加时间
     */
    @Column(name = "create_time", comment = "添加时间", type = MySqlTypeConstant.DATETIME)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;


    /**
     * 更新时间
     */
    @Column(name = "update_time", comment = "更新时间", type = MySqlTypeConstant.DATETIME)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @Column(name = "team_id", comment = "团队id")
    private Integer teamId;
}