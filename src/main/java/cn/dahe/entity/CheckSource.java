package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gitee.sunchenbin.mybatis.actable.annotation.*;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 校对服务来源实体类
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Data
@TableName("t_check_source")
public class CheckSource {

    @IsKey
    @IsAutoIncrement
    @TableId
    @Column("id")
    @ColumnComment("主键ID")
    private Long id;

    @Column("source_code")
    @ColumnComment("来源标识码")
    private String sourceCode;

    @Column("source_name")
    @ColumnComment("来源名称")
    private String sourceName;

    @Column("remark")
    @ColumnComment("说明")
    private String remark;

    @Column(name = "create_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("创建时间")
    private Date createTime;
} 