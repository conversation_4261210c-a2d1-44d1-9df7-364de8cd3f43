package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.*;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 站点下网站栏目更新情况检查
 * <AUTHOR>
 * @date 2025-09-09

 *
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("web_site_channel_check")
public class WebSiteChannelCheck {

    @IsKey
    @IsAutoIncrement
    @Column("id")
    @ColumnComment("编号")
    @Schema(description = "编号", example = "1")
    private Long id;


    @Column("website_id")
    @ColumnComment("站点ID")
    @Schema(description = "站点ID", example = "1")
    private Long websiteId;

    @Column("column_id")
    @ColumnComment("栏目ID")
    @Schema(description = "栏目ID", example = "1")
    private Long columnId;

    @Column("column_name")
    @ColumnComment("栏目名称")
    @Schema(description = "栏目名称", example = "栏目名称")
    private String columnName;

    //更新文章个数
    @Column("update_count")
    @ColumnComment("更新文章个数")
    @Schema(description = "更新文章个数", example = "10")
    @DefaultValue("0")
    private Integer updateCount;

    @Column(name = "last_update_time", comment = "新媒体最后更新时间", type = MySqlTypeConstant.DATETIME)
    @Schema(description = "新媒体最后更新时间")
    private Date lastUpdateTime;

    //检查时间
    @Column("check_time")
    @ColumnComment("检查时间")
    @Schema(description = "检查时间", example = "2020-01-01 00:00:00")
    private Date checkTime;

    //是否更新
    @Column("is_update")
    @ColumnComment("是否更新")
    @Schema(description = "是否更新", example = "0")
    @DefaultValue("0")
    private Integer isUpdate;


}
