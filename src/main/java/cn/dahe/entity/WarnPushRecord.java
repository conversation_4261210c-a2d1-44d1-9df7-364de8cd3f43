package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsAutoIncrement;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsKey;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 预警推送记录实体类
 * 对应页面：预警推送记录列表
 * 数据库表：t_warn_push_record
 * <p>
 * 设计说明：
 * - 与预警方案（WarnPlan）为多对一关系，字段 {@link #warnPlanId}
 * - 列表查询常用筛选项包含：推送时间范围、来源平台、预警方案、接收人、预警类型、推送方式
 * - 记录保存推送的关键元数据，便于后续统计与审计
 * <p>
 * 字段命名与含义均与前端筛选项一一对应，便于直觉理解与联调
 * <p>
 * author: 李云龙
 */
@Data
@Accessors(chain = true)
@TableName("t_warn_push_record")
public class WarnPushRecord {

    // ===================== 主键与基础字段 =====================
    /**
     * 主键ID（自增）
     * 注解说明：
     * - @TableId：MyBatis-Plus 主键注解，指定 ID 生成策略为 AUTO（自增）
     * - @IsKey：mybatis-actable 主键标识
     * - @IsAutoIncrement：mybatis-actable 自增标识
     * - @Column：数据库字段配置，指定类型、注释
     */
    @IsKey
    @IsAutoIncrement
    @Column(name = "id", type = MySqlTypeConstant.INT, length = 11, comment = "预警推送记录ID，自增主键")
    private Integer id;

    /**
     * 实际推送时间（精确到秒）
     * 用于列表的时间范围筛选（pushTimeStart/pushTimeEnd）
     */
    @Column(name = "actual_push_time", type = MySqlTypeConstant.DATETIME, comment = "实际推送时间（精确到秒）")
    private Date actualPushTime;

    /**
     * 来源平台（枚举或字典）
     * 示例：1-微博，2-微信，3-今日头条 ...
     * 列表支持多选过滤（sourcePlatforms）
     */
    /**
     * 来源平台（多选），参照Chaneel表中的process_type字段
     * 页面示例：0：网站  1：微信  2：微博  3：头条号  6335：抖音号  6429：快手号  6452：微信视频号  6460：小红书
     */
    @Column(name = "source_platform", type = MySqlTypeConstant.INT, length = 1, defaultValue = "0")
    private Integer sourcePlatform;

    /**
     * 预警信源名称（模糊匹配）
     */
    /**
     * 预警信源名称
     * 页面示例："请输入信源名称"
     */
    @Column(name = "warn_source_name", type = MySqlTypeConstant.VARCHAR, length = 255, comment = "预警信源名称")
    private String warnSourceName;

    @Column(name = "website_id", comment = "网站ID")
    private Long websiteId;


    /**
     * 预警方案ID（外键，关联 WarnPlan.id）
     * 列表支持多选过滤（warnPlanIds）
     */
    /**
     * 预警方案ID（外键，关联 WarnPlan.id）
     */
    @Column(name = "warn_plan_id", type = MySqlTypeConstant.INT, length = 11, comment = "预警方案ID")
    private Integer warnPlanId;

    /**
     * 预警接收人姓名（冗余）
     */
    /**
     * 预警接收人姓名（多选）
     * 页面示例："李云龙,李云龙"
     * 存储格式：逗号分隔字符串
     */
    @Column(name = "receiver_name", type = MySqlTypeConstant.VARCHAR, length = 255, comment = "预警接收人姓名，多选逗号分隔")
    private String receiverName;

    /**
     * 接收人用户ID（便于精确筛选）
     * 列表支持多选过滤（receiverUserIds）
     */
    /**
     * 预警接收人姓名（多选）
     * 页面示例："李云龙,李云龙"
     * 存储格式：逗号分隔字符串
     */
    @Column(name = "receiver_user_id", type = MySqlTypeConstant.VARCHAR, length = 255, comment = "预警接收人ID")
    private String receiverUserId;

    /**
     * 文章错误预警（0：关闭，1：开启）
     * 该字段可用于标识记录是否属于文章错误类的预警
     */
    /**
     * 文章错误预警（0：关闭，1：开启）
     */
    @Column(name = "article_error_warn", type = MySqlTypeConstant.TINYINT, length = 1, comment = "文章错误预警：0关闭，1开启", defaultValue = "0")
    private Integer articleErrorWarn;

    /**
     * 预警推送方式：0-系统预警，1-人工预警
     * 列表支持单选过滤（warnPushType）
     */
    /**
     * 预警推送方式（0：系统预警，1：人工预警）
     */
    @Column(name = "warn_push_type", type = MySqlTypeConstant.TINYINT, length = 1, comment = "预警推送方式：0系统预警，1人工预警", defaultValue = "0")
    private Integer warnPushType;


    /**
     * 预警类型：0-巡查预警，1-更新预警，2-链接预警
     * 列表支持单选过滤（warnType）
     */
    /**
     * 预警类型（0：巡查预警，1：更新预警 2 链接预警）
     */
    @Column(name = "warn_type", type = MySqlTypeConstant.TINYINT, length = 1, comment = "预警类型：0巡查预警，1更新预警 2 链接预警", defaultValue = "0")
    private Integer warnType;
    // ===================== 列表展示字段 =====================

    /**
     * 预警详情链接（可跳转到具体来源/详情页）
     */
    /**
     * 预警链接
     * 页面示例：http://dhax.dahe.cn/...
     */
    @Column(name = "warn_link", type = MySqlTypeConstant.TEXT, comment = "预警详情链接")
    private String warnLink;


    // ===================== 扩展字段（可选） =====================
    /**
     * 逻辑删除标识：0-未删除，1-已删除
     * 仅作软删除控制，不影响历史查询统计
     */
    /**
     * 逻辑删除标识
     * 0-未删除，1-已删除（mybatis-actable 逻辑删除支持）
     */
    @Column(name = "is_deleted", type = MySqlTypeConstant.TINYINT, length = 1, defaultValue = "0", comment = "逻辑删除标识：0未删除，1已删除")
    private Integer isDeleted;

    /**
     * 推送数据
     */
    @Column(name = "data", type = MySqlTypeConstant.LONGTEXT, comment = "推送数据")
    private String data;

    @Column(name = "msg_id", comment = "消息ID")
    private Long msgId;

}