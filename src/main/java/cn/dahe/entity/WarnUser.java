package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsAutoIncrement;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsKey;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import lombok.Data;

import java.util.Date;

/**
 * 预警接收人 与User不同，User是系统用户，预警接收人是预警方案的接收人
 * 对应数据库表：t_warn_plan
 */
@Data
@TableName("t_warn_user")
public class WarnUser {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    @IsKey
    @IsAutoIncrement
    @Column(name = "id", type = MySqlTypeConstant.INT, length = 11, comment = "主键ID")
    private Integer id;

    /**
     * 用户姓名
     */
    @Column(name = "user_name", type = MySqlTypeConstant.VARCHAR, length = 255, comment = "用户姓名")
    private String userName;

    /**
     * 用户openId
     */
    @Column(name = "user_open_id", type = MySqlTypeConstant.VARCHAR, length = 255, comment = "用户openId")
    private String userOpenId;

    /**
     * 用户手机号
     */
    @Column(name = "user_phone", type = MySqlTypeConstant.VARCHAR, length = 255, comment = "用户手机号")
    private String userPhone;

    /**
     * 创建时间
     */
    @Column(name = "create_time", type = MySqlTypeConstant.DATETIME, length = 255, comment = "创建时间")
    private Date createTime;

    @TableField(value = "is_subscribe")
    @Column(name = "is_subscribe", type = MySqlTypeConstant.TINYINT, length = 1, comment = "订阅状态：0未订阅，1已订阅", defaultValue = "1")
    private Integer isSubscribe;


}
