package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.annotation.DefaultValue;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsAutoIncrement;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsKey;
import lombok.Data;

/**
 * 用户角色关系表
 */
@Data
@TableName("t_user_role")
public class UserRole {
    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    @IsKey
    @IsAutoIncrement
    @Column(name = "id", comment = "id")
    private Integer id;

    /**
     * 用户id
     */
    @Column(name = "user_id", comment = "用户id")
    @DefaultValue("0")
    private Integer userId;

    /**
     * 角色id
     */
    @Column(name = "role_id", comment = "角色id")
    @DefaultValue("0")
    private Integer roleId;

}