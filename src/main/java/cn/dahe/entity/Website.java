package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gitee.sunchenbin.mybatis.actable.annotation.*;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 网站实体类
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
@Data
@Accessors(chain = true)
@TableName("t_website")
public class Website {

    @IsKey
    @IsAutoIncrement
    @Column("id")
    @ColumnComment("ID")
    private Long id;

    @Column(value = "web_name")
    @ColumnComment("网站名称")
    private String webName;

    @Column("web_url")
    @ColumnComment("网站地址")
    private String webUrl;

    @Column("web_code_id")
    @ColumnComment("网站标识码")
    private String webCodeId;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "create_time", comment = "添加时间", type = MySqlTypeConstant.DATETIME)
    private Date createTime;

    @Column(value = "create_user_id", defaultValue = "0", type = MySqlTypeConstant.INT)
    @ColumnComment("创建用户")
    private Integer createUserId;

    @Column(value = "create_user_name")
    @ColumnComment("创建用户")
    private String createUserName;

    /**
     * 状态 -1：删除 0：停用 1：启用
     */
    @Column(name = "status", comment = "状态 0：停用 1：启用", type = MySqlTypeConstant.TINYINT)
    @DefaultValue("1")
    private int status;

    @Column(name = "group_id", comment = "分组ID")
    @DefaultValue("0")
    private Long groupId;

    @Column(name = "check_strategy", comment = "检查策略：(查全/查准)或者其他", type = MySqlTypeConstant.TINYINT)
    @DefaultValue("0")
    private Integer checkStrategy;

    @TableLogic
    @Column(name = "is_del", comment = "是否删除 0：未删除 1：已删除", type = MySqlTypeConstant.TINYINT)
    @DefaultValue("0")
    private Boolean isDel;
    /**
     * 原始ID 存网站站点ID、
     */
    @Column(name = "site_id", comment = "原始ID", type = MySqlTypeConstant.INT)
    @DefaultValue("0")
    private Integer siteId;

    /**
     * 解析处理类型
     * 0：网站 1：微信 2：微博 3：头条号 6335：抖音号 6429：快手号 6452：微信视频号 6460：小红书
     */
    @Unique(columns = {"process_type", "site_id", "channel_id"})
    @Column(name = "process_type", comment = "解析处理类型", type = MySqlTypeConstant.INT)
    @DefaultValue("0")
    private Integer processType;

    /**
     * 新媒体栏目ID
     */
    @Column(name = "channel_id", comment = "解析处理类型", type = MySqlTypeConstant.INT)
    @DefaultValue("0")
    private Integer channelId;

    @Column(name = "tenant_id", comment = "租户id")
    private Integer tenantId;
}
