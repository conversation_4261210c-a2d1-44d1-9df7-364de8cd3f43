package cn.dahe.entity;

import cn.dahe.enums.ChannelOperationTypeEnum;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gitee.sunchenbin.mybatis.actable.annotation.*;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;

import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Entity;
import java.util.Date;

/**
 * 栏目配置
 */
@Entity
@Data
@Accessors(chain = true)
@TableName("t_channel_operation_log")
public class ChannelOperationLog {

    @IsKey
    @IsAutoIncrement
    @Column("id")
    @ColumnComment("ID")
    private Integer id;

    /**
     * 栏目ID
     */
    @Column(value = "channel_id")
    @ColumnComment("栏目ID")
    private Integer channelId;
   
    @Column(name = "create_time", comment = "操作时间", type = MySqlTypeConstant.DATETIME)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 操作人
     */
    @Column(value = "user_name")
    @ColumnComment("操作人")
    private String userName;

    /**
     * 操作内容
     */
    @Column(value = "content")
    @ColumnComment("操作内容")
    private String content;

    /**
     * 操作类型
     */
    @Column(value = "opt_type")
    @ColumnComment("操作类型")
    private Integer optType;
   
    /**
     * 操作结果
     */
    @Column(value = "result")
    @ColumnComment("操作结果")
    private String result;

    /**
     * 操作备注
     */
    @Column(value = "remark")
    @ColumnComment("操作备注")
    private String remark;
//
//    /**
//     * 删除状态 0-正常 1-删除
//     */
//    @Column(value = "del_status")
//    @ColumnComment("删除状态")
//    private Integer delStatus;
}