package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.*;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 文章原文实体类
 * 与t_article表一一对应，存储原始采集内容
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
@Accessors(chain = true)
@TableName("t_article_content")
public class ArticleContent {

    @IsKey
    @TableId(type = IdType.INPUT)
    @Column(value = "id", comment = "主键ID，与t_article表ID一一对应")
    private Long id;

    @Column(name = "title", type = MySqlTypeConstant.TEXT)
    @ColumnComment("标题")
    private String title;

    @Column(name = "content", type = MySqlTypeConstant.LONGTEXT)
    @ColumnComment("正文")
    private String content;

    @Column(name = "web_code", type = MySqlTypeConstant.LONGTEXT)
    private String webCode;

    @Column(name = "create_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("创建时间")
    @DefaultValue("CURRENT_TIMESTAMP")
    private Date createTime;

    @Column(name = "update_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("更新时间")
    @IgnoreUpdate
    @DefaultValue("NULL ON UPDATE CURRENT_TIMESTAMP")
    private Date updateTime;
} 