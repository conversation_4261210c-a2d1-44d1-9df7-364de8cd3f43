package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsAutoIncrement;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsKey;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 用户角色关系表
 */
@Accessors(chain = true)
@Data
@TableName("t_user_website")
public class UserWebsite {
    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    @IsKey
    @IsAutoIncrement
    @Column(name = "id", comment = "id")
    private Integer id;

    /**
     * 用户id
     */
    @Column(name = "user_id", comment = "用户id")
    private Integer userId;

    /**
     * 角色id
     */
    @Column(name = "website_id", comment = "站点id")
    private Integer websiteId;

    @Column(name = "process_type", comment = "平台类型")
    private Integer processType;

}