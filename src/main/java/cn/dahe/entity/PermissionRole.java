package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.annotation.DefaultValue;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsAutoIncrement;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsKey;
import lombok.Data;

/**
 * 权限角色关系表
 */
@Data
@TableName("t_permission_role")
public class PermissionRole {
    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    @IsKey
    @IsAutoIncrement
    @Column(name = "id", comment = "id")
    private Integer id;

    /**
     * 权限id
     */
    @Column(name = "permission_id", comment = "权限id")
    @DefaultValue("0")
    private int permissionId;

    /**
     * 角色id
     */
    @Column(name = "role_id", comment = "角色id")
    @DefaultValue("0")
    private int roleId;

}