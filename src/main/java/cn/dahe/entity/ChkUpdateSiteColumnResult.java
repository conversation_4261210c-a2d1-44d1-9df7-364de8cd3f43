package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gitee.sunchenbin.mybatis.actable.annotation.*;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 栏目更新检查结果
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@TableName("chk_update_site_column_result")
@Schema(name = "栏目更新检查结果", description = "栏目更新检查结果")
public class ChkUpdateSiteColumnResult {

    @IsKey
    @IsAutoIncrement
    @Column("id")
    @ColumnComment("编号")
    @Schema(description = "编号", example = "1")
    private Long id;


    @Column("site_id")
    @ColumnComment("站点ID")
    @Schema(description = "站点ID")
    private int siteId;

    @Column("site_name")
    @ColumnComment("站点名称")
    @Schema(description = "站点名称", example = "站点名称")
    private String siteName;

    @Column("group_id")
    @ColumnComment("分组ID")
    @Schema(description = "分组ID")
    private Long groupId;

    @Column("group_name")
    @ColumnComment("分组名称")
    @Schema(description = "分组名称", example = "分组名称")
    private String groupName;

    @Column("column_id")
    @ColumnComment("栏目ID")
    @Schema(description = "栏目ID", example = "1")
    private Long columnId;

    @Column("column_name")
    @ColumnComment("栏目名称")
    @Schema(description = "栏目名称", example = "栏目名称")
    private String columnName;

    /**
     * 解析处理类型
     * 0：网站  1：微信  2：微博  3：头条号  6335：抖音号  6429：快手号  6452：微信视频号  6460：小红书
     */
    @Column(name = "process_type", comment = "解析处理类型")
    private int processType;

    @Column("check_status")
    @ColumnComment("检测状态")
    @DefaultValue("0")
    @Schema(description = "检测状态(1:正常,2:异常)", example = "1")
    private Integer checkStatus;

    @Column("check_status_desc")
    @ColumnComment("检测状态描述")
    @DefaultValue("0")
    @Schema(description = "检测状态描述：正常，异常", example = "正常")
    private String checkStatusDesc;

    @Column("continuous_not_update_days")
    @ColumnComment("连续不更新天数")
    @DefaultValue("0")
    @Schema(description = "连续不更新天数", example = "1")
    private Integer continuousNotUpdateDays;

    @Column(name = "continuous_not_update_days_detail",type = MySqlTypeConstant.LONGTEXT)
    @ColumnComment("连续未更新天数详情")
    @Schema(description = "连续未更新天数详情", example = "2025-06-19,2025-06-20,2025-06-21")
    private String continuousNotUpdateDaysDetail;

    @Column("update_days")
    @ColumnComment("更新天数")
    @DefaultValue("0")
    @Schema(description = "更新天数", example = "1")
    private Integer updateDays;

    @Column(name = "update_days_detail",type = MySqlTypeConstant.LONGTEXT)
    @ColumnComment("更新天数详情")
    @Schema(description = "更新天数详情", example = "2025-06-19,2025-06-20,2025-06-21")
    private String updateDaysDetail;

    @Column("update_num")
    @ColumnComment("更新新闻数量")
    @DefaultValue("0")
    @Schema(description = "更新新闻数量", example = "1")
    private Integer updateNum;

    @Column(name = "last_update_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("最后一次更新时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "最后一次更新时间", example = "2025-07-30 10:30:00")
    private LocalDateTime lastUpdateTime;

    @Column(name = "update_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("更新时间，每天3:00更新")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间", example = "2025-07-30 10:30:00")
    private LocalDateTime updateTime;
}