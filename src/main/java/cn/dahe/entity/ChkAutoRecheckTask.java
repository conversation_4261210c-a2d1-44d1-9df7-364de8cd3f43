package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gitee.sunchenbin.mybatis.actable.annotation.*;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 自动复查任务
 *
 * <AUTHOR>
 * @date 2025-08-21
 */
@Data
@TableName("chk_auto_recheck_task")
@Schema(name = "自动复查任务", description = "自动复查任务")
public class ChkAutoRecheckTask {

    @IsKey
    @IsAutoIncrement
    @Column("id")
    @ColumnComment("编号")
    @Schema(description = "编号", example = "1")
    private Long id;

    @Column("task_name")
    @ColumnComment("任务名称")
    @Schema(description = "任务名称", example = "xxx")
    private String taskName;

    @Column(name = "site_ids", type = MySqlTypeConstant.TEXT)
    @ColumnComment("信源ID集合")
    @Schema(description = "信源ID", example = "1")
    private String siteIds;
    @Column(name = "site_names", type = MySqlTypeConstant.TEXT)
    @ColumnComment("信源名称集合")
    @Schema(description = "信源名称", example = "河南省人民政府")
    private String siteNames;
    @Column(name = "article_titles", type = MySqlTypeConstant.TEXT)
    @ColumnComment("文章标题集合")
    @Schema(description = "文章标题集合", example = "xxx")
    private String articleTitles;
    @Column(name = "article_ids", type = MySqlTypeConstant.TEXT)
    @ColumnComment("文章ID集合")
    @Schema(description = "文章ID集合", example = "xxx")
    private String articleIds;
    @Column(name = "article_urls", type = MySqlTypeConstant.TEXT)
    @ColumnComment("文章URL集合")
    @Schema(description = "文章URL集合", example = "xxx")
    private String articleUrls;

    @Column("check_error_type")
    @ColumnComment("复查错误类型")
    @Schema(description = "复查错误类型", example = "多个错误类型逗号分割")
    private String checkErrorType;

    @Column(name = "check_begin_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("复查起始时间")
    @DefaultValue("CURRENT_TIMESTAMP")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "复查开始时间", example = "2025-07-30 10:30:00")
    private Date checkBeginTime;

    @Column(name = "check_end_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("复查结束时间")
    @DefaultValue("CURRENT_TIMESTAMP")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "复查结束时间", example = "2025-07-30 10:30:00")
    private Date checkEndTime;

    //已整改	未整改  新增	疑似已删除
    @Column(name = "rectify_problem_count",defaultValue = "0")
    @ColumnComment("已整改问题数")
    @Schema(description = "已整改问题数", example = "xxx")
    private int rectifyProblemCount;
    @Column(name = "not_rectify_problem_count",defaultValue = "0")
    @ColumnComment("未整改问题数")
    @Schema(description = "未整改问题数", example = "xxx")
    private int notRectifyProblemCount;
    @Column(name = "increase_problem_count",defaultValue = "0")
    @ColumnComment("新增问题数")
    @Schema(description = "新增问题数", example = "xxx")
    private int increaseProblemCount;
    @Column(name = "maybe_del_problem_count",defaultValue = "0")
    @ColumnComment("疑似已删除问题数")
    @Schema(description = "疑似已删除问题数", example = "xxx")
    private int maybeDelProblemCount;

    @Column(name = "status",defaultValue = "0")
    @ColumnComment("复查状态 0未开始 100进行中 200已完成 500任务失败")
    @Schema(description = "复查状态 0未开始 100进行中 200已完成 500任务失败", example = "xxx")
    private int status;

    @Column("filter_title")
    @ColumnComment("复查标题过滤：0未开启，1开启")
    @Schema(description = "复查标题过滤：0未开启，1开启", example = "xxx")
    private int filterTitle;

    @Column(name = "create_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("创建时间")
    @DefaultValue("CURRENT_TIMESTAMP")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间", example = "2025-07-30 10:30:00")
    private Date createTime;

    @Column("create_user_id")
    @ColumnComment("创建人id")
    @Schema(description = "创建人id", example = "1")
    private Integer createUserId;

    @Column("create_user_name")
    @ColumnComment("创建人名称")
    @Schema(description = "创建人名称", example = "xxx")
    private String createUserName;

    @Column("create_dep_id")
    @ColumnComment("创建人单位Id")
    @Schema(description = "创建人单位Id", example = "1")
    private Integer createDepId;
    @Column("create_dep_name")
    @ColumnComment("创建人单位名称")
    @Schema(description = "创建人单位名称", example = "xxx")
    private String createDepName;

    @Column(name = "is_del", type = MySqlTypeConstant.TINYINT)
    @ColumnComment("是否删除")
    @DefaultValue("0")
    private Boolean isDel;

}
