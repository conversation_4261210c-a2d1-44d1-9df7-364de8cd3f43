package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gitee.sunchenbin.mybatis.actable.annotation.*;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 首页更新主表实体类
 * 数据组推送解析后的数据
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@TableName("chk_update_site_index")
@Schema(name = "首页更新主表", description = "网站首页更新检查信息")
public class ChkUpdateSiteIndex {

    @IsKey
    @IsAutoIncrement
    @Column("id")
    @ColumnComment("编号")
    @Schema(description = "编号", example = "1")
    private Long id;

    @Column("site_id")
    @ColumnComment("网站ID")
    @Schema(description = "网站ID", example = "1")
    private Integer siteId;

    @Column("site_name")
    @ColumnComment("网站名称")
    @Schema(description = "网站名称", example = "河南省人民政府")
    private String siteName;

    @Column("site_url")
    @ColumnComment("网站URL")
    @Schema(description = "网站URL", example = "https://www.henan.gov.cn")
    private String siteUrl;

    @Column("status")
    @ColumnComment("更新状态")
    @Schema(description = "更新状态：updated-已更新，unchanged-未更新", example = "updated")
    private String status;

    @Column(name = "last_check" , type = MySqlTypeConstant.DATETIME)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ColumnComment("最后检查时间")
    @Schema(description = "最后检查时间", example = "2025-08-04T09:30:00Z")
    private Date lastCheck;

    @Column("latency_ms")
    @ColumnComment("响应时间(毫秒)")
    @Schema(description = "响应时间(毫秒)", example = "320")
    private Integer latencyMs;

    @Column(name = "error", type = MySqlTypeConstant.TEXT)
    @ColumnComment("错误信息")
    @Schema(description = "错误信息")
    private String error;

    // ==================== 扩展字段（用于兼容现有业务逻辑） ====================

    @Column("group_id")
    @ColumnComment("分组ID")
    @Schema(description = "分组ID", example = "1")
    private Long groupId;

    @Column("group_name")
    @ColumnComment("分组名称")
    @Schema(description = "分组名称", example = "政府网站")
    private String groupName;

    @TableField(exist = false)
    @Schema(description = "网站ID（兼容字段）", example = "1")
    private Long websiteId;

    @TableField(exist = false)
    @Schema(description = "网站名称（兼容字段）", example = "河南省人民政府")
    private String websiteName;

    @TableField(exist = false)
    @Schema(description = "网站首页地址", example = "https://www.henan.gov.cn")
    private String websiteIndexUrl;

    @TableField(exist = false)
    @Schema(description = "文章标题")
    private String articleTitle;

    @TableField(exist = false)
    @Schema(description = "文章内容")
    private String articleContent;

    @TableField(exist = false)
    @Schema(description = "文章链接")
    private String articleUrl;

    @TableField(exist = false)
    @ColumnComment("文章发布时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "文章发布时间", example = "2025-07-30 10:30:00")
    private Date articlePublishTime;

    @TableField(exist = false)
    @ColumnComment("解析时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "解析时间", example = "2025-07-30 10:30:00")
    private Date parseTime;

    @Column("sign")
    @ColumnComment("数据签名（用于去重）")
    @Schema(description = "数据签名（用于去重）")
    private String sign;

    @Column(name = "create_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("创建时间")
    @DefaultValue("CURRENT_TIMESTAMP")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间", example = "2025-07-30 10:30:00")
    private Date createTime;

    @Column(name = "is_del", type = MySqlTypeConstant.TINYINT)
    @ColumnComment("是否删除")
    @DefaultValue("0")
    private Boolean isDel;
}
