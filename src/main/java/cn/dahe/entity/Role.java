package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.annotation.DefaultValue;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsAutoIncrement;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsKey;
import lombok.Data;

import java.util.ArrayList;

/**
 * 角色表
 */
@Data
@TableName("t_role")
public class Role {
    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    @IsKey
    @IsAutoIncrement
    @Column(name = "id", comment = "id")
    private Integer id;

    /**
     * 角色名称
     */
    @Column(name = "name", comment = "角色名称")
    private String name;

    /**
     * 角色标识
     */
    @Column(name = "sn", comment = "角色标识")
    private String sn;

    /**
     * 备注
     */
    @Column(name = "note", comment = "备注")
    private String note;

    /**
     * 状态 -1：删除 0：停用 1：启用
     */
    @Column(name = "status", comment = "状态 0：停用 1：启用")
    @DefaultValue("0")
    private Integer status;


    @TableField(exist = false)
    private ArrayList<Permission> permissionArrayList;

}