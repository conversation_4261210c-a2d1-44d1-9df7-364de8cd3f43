package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.annotation.DefaultValue;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsAutoIncrement;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsKey;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import lombok.Data;

import java.util.Date;


@Data
@TableName("t_warn_push_msg")
public class WarnPushMsg {


    @TableId(type = IdType.AUTO)
    @IsKey
    @IsAutoIncrement
    @Column(name = "id", comment = "预警消息体ID，自增主键")
    private Long id;

    @Column(name = "time", comment = "预警时间")
    private String time;
    /**
     * 字段不够 暂时预留
     */
    @Column(name = "type", comment = "预警类型")
    private String type;
    /**
     * 长度不超过20字
     */
    @Column(name = "content", comment = "预警内容")
    private String content;

    @Column(name = "link", comment = "预警链接")
    private String link;

    @Column(name = "detail_type", comment = "关联类型")
    private Integer detailType;
    @Column(name = "detail_id", comment = "关联ID")
    private Long detailId;

    @Column(name = "create_time", comment = "创建时间", type = MySqlTypeConstant.DATETIME)
    @DefaultValue("CURRENT_TIMESTAMP")
    private Date createTime;



}