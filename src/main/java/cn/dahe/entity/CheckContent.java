package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.*;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 文章检查内容实体类
 * 与检查任务id保持一致，一个任务对应一个检查内容
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
@Accessors(chain = true)
@TableName("t_check_content")
public class CheckContent {

    @IsKey
    @TableId(type = IdType.INPUT)
    @Column("id")
    @ColumnComment("主键ID")
    private Long id;

    @Column(name = "compressed_title", type = MySqlTypeConstant.TEXT)
    @ColumnComment("压缩的html标题")
    private String compressedTitle;

    @Column(name = "compressed_content", type = MySqlTypeConstant.LONGTEXT)
    @ColumnComment("压缩的html内容")
    private String compressedContent;


    @Column(name = "cleaned_title", type = MySqlTypeConstant.TEXT)
    @ColumnComment("纯文本的标题")
    private String cleanedTitle;

    @Column(name = "cleaned_content", type = MySqlTypeConstant.LONGTEXT)
    @ColumnComment("纯文本的内容")
    private String cleanedContent;

    @Column(name = "compressed_code", type = MySqlTypeConstant.LONGTEXT)
    @ColumnComment("压缩的代码")
    private String compressedCode;
    @Column(name = "cleaned_code", type = MySqlTypeConstant.LONGTEXT)
    @ColumnComment("纯文本源代码")
    private String cleanedCode;


    @Column(name = "create_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("创建时间")
    @DefaultValue("CURRENT_TIMESTAMP")
    private Date createTime;

    @Column(name = "update_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("更新时间")
    @IgnoreUpdate
    @DefaultValue("NULL ON UPDATE CURRENT_TIMESTAMP")
    private Date updateTime;

} 