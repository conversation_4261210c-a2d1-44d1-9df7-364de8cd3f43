package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.annotation.ColumnComment;
import com.gitee.sunchenbin.mybatis.actable.annotation.DefaultValue;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsAutoIncrement;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsKey;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Entity;
import java.util.Date;

/**
 * 栏目配置
 */
@Entity
@Data
@Accessors(chain = true)
@TableName("t_channel")
public class Channel {

    @IsKey
    @IsAutoIncrement
    @Column("id")
    @ColumnComment("ID")
    private Integer id;

    /**
     * 栏目名称
     */
    @Column(value = "name")
    @ColumnComment("栏目名称")
    private String name;

    /**
     * 栏目备注名称
     */
    @Column(value = "remark")
    @ColumnComment("栏目备注名称")
    private String remark;
     
    /**
     * 链接
     */
    @Column(value = "url")
    @ColumnComment("链接")
    private String url;

    /**
     * 抓取状态 0：禁用  1：开启
     */
    @Column(value = "enable", defaultValue = "1")
    @ColumnComment("抓取状态")
    private int enable;

    /**
     * 是否异步（动态js渲染）  0：非异步  1：异步
     */
    @Column(name = "async", comment = "是否异步（动态js渲染）  0：非异步  1：异步")
    @DefaultValue("1")
    private int async;

    /**
     * 网站站点id
     */
    @Column(name = "site_id", comment = "网站站点id，外键，website的id")
    private int siteId;

    /**
     * 网站名称/站点名称
     */
    @Column(value = "site_name", length = 500)
    @ColumnComment("网站名称/站点名称")
    private String siteName;


    /**
     * 解析处理类型
     * 0：网站  1：微信  2：微博  3：头条号  6335：抖音号  6429：快手号  6452：微信视频号  6460：小红书
     */
    @Column(name = "process_type", comment = "解析处理类型")
    private Integer processType;


    @Column(value = "create_user_id", defaultValue = "0")
    @ColumnComment("创建用户")
    private Integer createUserId;


    @Column(value = "create_user_name")
    @ColumnComment("创建用户")
    private String createUserName;


    @Column(name = "create_time", comment = "创建时间", type = MySqlTypeConstant.DATETIME)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;


    @Column(name = "last_modify_time", comment = "最后修改时间", type = MySqlTypeConstant.DATETIME)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastModifyTime;

    /**
     * 栏目类型 与ChannelType的id关联
     */
    @Column(name = "channel_type_id", comment = "栏目类型")
    @DefaultValue("0")
    private Long channelTypeId;

    /**
     * 更新期限 单位：天 与ChannelType的updatePeriod保持一致
     */
    @Column(name = "update_period", comment = "更新期限 单位：天")
    @DefaultValue("0")
    private Integer updatePeriod;
}