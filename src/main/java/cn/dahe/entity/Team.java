package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsAutoIncrement;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsKey;
import com.gitee.sunchenbin.mybatis.actable.annotation.TableComment;
import com.gitee.sunchenbin.mybatis.actable.annotation.Unique;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@TableName("t_team")
@TableComment("团队")
@Accessors(chain = true)
@Data
public class Team {
    @Unique(columns = {"name", "tenant_id", "delete_time"})
    @IsKey
    @IsAutoIncrement
    @Column(name = "id")
    private Integer id;

    @Column(name = "name", comment = "名称")
    private String name;

    @Column(name = "tenant_id", comment = "租户id")
    private Integer tenantId;

    @Column(name = "remark", comment = "备注")
    private String remark;

    @Column(name = "create_user_id", comment = "创建人id")
    private Integer createUserId;

    @Column(name = "last_update_user_id", comment = "上次更新人id")
    private Integer lastUpdateUserId;

    @Column(name = "delete_user_id", comment = "删除人id")
    private Integer deleteUserId;

    @Column(name = "create_time", comment = "创建时间", defaultValue = "current_timestamp")
    private LocalDateTime createTime;

    @Column(name = "last_update_time", comment = "上次更新时间", defaultValue = "null on update current_timestamp")
    private LocalDateTime lastUpdateTime;

    @Column(name = "delete_time", comment = "删除时间", defaultValue = "0")
    private Long deleteTime;

    @TableField(exist = false)
    private String createUserName;

    @TableField(exist = false)
    private String lastUpdateUserName;

    @TableField(exist = false)
    private String tenantName;
}
