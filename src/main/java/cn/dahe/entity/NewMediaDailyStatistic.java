package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsAutoIncrement;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsKey;
import com.gitee.sunchenbin.mybatis.actable.annotation.TableComment;
import com.gitee.sunchenbin.mybatis.actable.annotation.Unique;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

@TableName("t_new_media_daily_statistic")
@TableComment("新媒体每日统计")
@Accessors(chain = true)
@Data
public class NewMediaDailyStatistic {
    @Unique(columns = {"new_media_id", "create_time"})
    @IsKey
    @IsAutoIncrement
    @Column(name = "id")
    private Integer id;

    @Column(name = "new_media_id", comment = "website表id")
    private Long newMediaId;

    @Column(name = "article_total", comment = "文章总数")
    private Integer articleTotal;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "new_media_last_update_time", comment = "新媒体最后更新时间")
    private LocalDateTime newMediaLastUpdateTime;

    @Column(name = "create_time", comment = "创建日期")
    private LocalDate createTime;
}
