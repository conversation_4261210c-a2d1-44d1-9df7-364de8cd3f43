package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.annotation.ColumnComment;
import com.gitee.sunchenbin.mybatis.actable.annotation.DefaultValue;
import com.gitee.sunchenbin.mybatis.actable.annotation.IgnoreUpdate;
import com.gitee.sunchenbin.mybatis.actable.annotation.Index;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsKey;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 采集文章实体类
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
@TableName("t_article")
@Schema(name = "文章信息", description = "采集的文章基本信息")
public class Article {

    @IsKey
    @TableId(type = IdType.INPUT)
    @Column(value = "id", comment = "主键ID")
    private Long id;

    @Column(value = "title", type = MySqlTypeConstant.TEXT)
    @ColumnComment("文章标题")
    private String title;

    @Column(name = "create_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("创建时间")
    @DefaultValue("CURRENT_TIMESTAMP")
    private Date createTime;

    @Index
    @Column(name = "check_id")
    @ColumnComment("检测正文任务id")
    private Long checkId;

    @Column(name = "update_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("更新时间")
    @IgnoreUpdate
    @DefaultValue("NULL ON UPDATE CURRENT_TIMESTAMP")
    private Date updateTime;

    @Index
    @Column("website_id")
    @ColumnComment("website表id")
    private Long websiteId;

    @Index
    @Column("site_id")
    @ColumnComment("推送站点id")
    private Long siteId;

    @Index
    @Column("channel_id")
    @ColumnComment("channel表id")
    private Long channelId;

    @Column(name = "process_type", comment = "解析处理类型，和栏目值保持一致", type = MySqlTypeConstant.INT)
    private Integer processType;

    @Column(name = "focus_img_url", type = MySqlTypeConstant.TEXT)
    @ColumnComment("焦点图链接")
    @Schema(description = "焦点图链接")
    private String focusImgUrl;

    /**
     * 文章的发布时间
     */
    @Column(name = "pub_time", comment = "文章的发布时间", type = MySqlTypeConstant.DATETIME)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date pubTime;

    @Column(name = "collect_time", comment = "采集时间", type = MySqlTypeConstant.DATETIME)
    private Date collectTime;


    /**
     * 文章访问链接 TODO 如何针对http、https合并？
     */
    @Column(name = "article_url", type = MySqlTypeConstant.TEXT, comment = "访问链接")
    private String articleUrl;


    /**
     * 作者
     */
    @Column(name = "author", type = MySqlTypeConstant.TEXT, comment = "作者")
    private String author;

    @Index
    @Column(name = "reprint_source_id", type = MySqlTypeConstant.BIGINT, comment = "转载信源id")
    private Long reprintSourceId;

    @Column(name = "reprint_source", type = MySqlTypeConstant.TEXT, comment = "转载信源")
    private String reprintSource;

    /**
     * 审核状态
     */
    @Column(name = "audit_status", comment = "审核状态 0未审核 1审核通过 2审核驳回", type = MySqlTypeConstant.TINYINT)
    @DefaultValue("0")
    private Integer auditStatus;

    @Column(name = "audit_user_id")
    @ColumnComment("审核人ID")
    private Long auditUserId;

    @Column(name = "audit_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("审核时间")
    private Date auditTime;

    /**
     * 处置状态
     */
    @Column(name = "disposal_status", comment = "处置状态 0未处置 1已约谈整改 2已上报线索 3已关停站点", type = MySqlTypeConstant.TINYINT)
    @DefaultValue("0")
    private Integer disposalStatus;

    /**
     * 处置备注（最多200字）
     */
    @Column(name = "disposal_remark", comment = "处置备注", length = 200)
    @Schema(description = "处置备注 最多200字", example = "已与网站负责人沟通，要求立即整改")
    private String disposalRemark;

    /**
     * 整改状态（0未整改，1整改中，2已整改，3无需整改）
     */
    @Column(name = "rectify_status", comment = "整改状态 0未下发 1待整改 2已整改 3无需整改", type = MySqlTypeConstant.TINYINT)
    @DefaultValue("0")
    private Integer rectifyStatus;

    @Column(name = "summary", comment = "摘要", type = MySqlTypeConstant.TEXT)
    private String summary;

    @TableField(exist = false)
    private String platform;

    /**
     * 是否是首页文章
     */
    @Column(name = "is_index", comment = "是否是首页文章", type = MySqlTypeConstant.TINYINT)
    @DefaultValue("0")
    private Integer isIndex;

}