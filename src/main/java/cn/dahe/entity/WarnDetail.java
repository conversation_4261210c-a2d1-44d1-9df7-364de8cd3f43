package cn.dahe.entity;

import cn.dahe.utils.SmUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsAutoIncrement;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsKey;
import com.gitee.sunchenbin.mybatis.actable.annotation.Unique;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import lombok.Data;


@Data
@TableName("t_warn_detail")
public class WarnDetail {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    @IsAutoIncrement
    @IsKey
    @Column(name = "id", comment = "主键ID")
    private Long id;

    @Unique(columns = {"relate_type", "relate_id"})
    @Column(name = "relate_id", comment = "关联业务id")
    private Long relateId;
    @Column(name = "relate_type", comment = "关联业务类型")
    private Integer relateType;

    @Column(name = "content", type = MySqlTypeConstant.LONGTEXT)
    private String content;

    public String getSign() {
        return SmUtil.encrypt(StrUtil.format("{}~{}~{}", relateType, relateId, id));
    }

    public static Long getIdFromSign(String sign) {
        String idStr = StrUtil.split(SmUtil.decrypt(sign), "~").get(2);
        return Long.parseLong(idStr);
    }


}
