package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.*;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 文章检查错误实体类
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
@TableName("t_check_result")
@Schema(name = "文章检查错误", description = "文章内容检查的错误信息记录")
public class CheckResult {

    @IsKey
    @IsAutoIncrement
    @TableId
    @Column("id")
    @ColumnComment("主键ID")
    private Long id;

    @Index
    @Column("check_id")
    @ColumnComment("关联的检测内容ID")
    private Long checkId;

    @Index
    @Column("word_id")
    @ColumnComment("关联的错误词库ID")
    private Long wordId;

    @Column(name = "error_word")
    private String errorWord;

    @Column(name = "suggest_word")
    private String suggestWord;

    @Column("position")
    @ColumnComment("在文本中的位置")
    private Integer position;

    @Column("error_type")
    private Integer errorType;

    @Column("error_level")
    private Integer errorLevel;

    @Column(name = "html_error_word", type = MySqlTypeConstant.TEXT)
    @ColumnComment("html错误词")
    private String htmlErrorWord;

    @Column("html_position")
    @ColumnComment("在HTML文本中的位置")
    private Integer htmlPosition;

    @Column(name = "article_location", type = MySqlTypeConstant.TINYINT)
    @ColumnComment("错误位置：0正文 1标题 2源码")
    @DefaultValue("0")
    private Integer articleLocation;

    @Column(value = "context", type = MySqlTypeConstant.TEXT)
    @ColumnComment("错误上下文，纯文本句子，但长度有限制")
    private String context;

    @Column("context_position")
    private Integer contextPosition;

    @Column(name = "audit_status", type = MySqlTypeConstant.TINYINT)
    @ColumnComment("审核状态：0未审核 1审核通过 2审核驳回")
    @DefaultValue("0")
    private Integer auditStatus;

    @Column(name = "create_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("创建时间")
    @DefaultValue("CURRENT_TIMESTAMP")
    private Date createTime;

    @Column(name = "update_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("更新时间")
    @IgnoreUpdate
    @DefaultValue("NULL ON UPDATE CURRENT_TIMESTAMP")
    private Date updateTime;
} 