package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gitee.sunchenbin.mybatis.actable.annotation.*;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 转办督办
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Data
@TableName("chk_transfer_task")
@Schema(name = "转办督办", description = "转办督办")
public class ChkTransferTask {

    @IsKey
    @IsAutoIncrement
    @Column("id")
    @ColumnComment("编号")
    @Schema(description = "编号", example = "1")
    private Long id;

    @Column(name = "dep_id", type = MySqlTypeConstant.TEXT)
    @ColumnComment("整改单位Id，兼容考虑多用户情况")
    @Schema(description = "整改单位Id", example = "1")
    private String depId;
    @Column(name = "dep_name", type = MySqlTypeConstant.TEXT)
    @ColumnComment("整改单位名称，兼容考虑多用户情况")
    @Schema(description = "整改单位名称", example = "xxx")
    private String depName;
    @Column(name = "transfer_user_id", type = MySqlTypeConstant.TEXT)
    @ColumnComment("整改账号ID")
    @Schema(description = "整改账号ID，兼容考虑多用户情况", example = "1")
    private String transferUserId;
    @Column(name = "transfer_user_name", type = MySqlTypeConstant.TEXT)
    @ColumnComment("整改账号名称")
    @Schema(description = "整改账号名称，兼容考虑多用户情况", example = "xxx")
    private String transferUserName;

    @Column("site_id")
    @ColumnComment("网站ID")
    @Schema(description = "网站ID", example = "1")
    private Integer siteId;
    @Column("site_name")
    @ColumnComment("网站名称")
    @Schema(description = "网站名称", example = "河南省人民政府")
    private String siteName;
    @Column("site_url")
    @ColumnComment("网站URL")
    @Schema(description = "网站URL", example = "https://www.henan.gov.cn")
    private String siteUrl;

    @Column("article_title")
    @ColumnComment("文章标题")
    @Schema(description = "文章标题", example = "xxx")
    private String articleTitle;
    @Column("article_id")
    @ColumnComment("文章ID")
    @Schema(description = "文章ID", example = "xxx")
    private Long articleId;
    @Column("article_url")
    @ColumnComment("文章URL")
    @Schema(description = "文章URL", example = "xxx")
    private String articleUrl;

    @Column(name = "serious_error_count",defaultValue = "0")
    @ColumnComment("严重错误数")
    @Schema(description = "严重错误数", example = "xxx")
    private int seriousErrorCount;
    @Column(name = "common_error_count",defaultValue = "0")
    @ColumnComment("一般错误数")
    @Schema(description = "一般错误数", example = "xxx")
    private int commonErrorCount;
    @Column(name = "maybe_error_count",defaultValue = "0")
    @ColumnComment("疑错词数")
    @Schema(description = "疑错词数", example = "xxx")
    private int maybeErrorCount;
    @Column(name = "diy_error_count",defaultValue = "0")
    @ColumnComment("自定义错误数")
    @Schema(description = "自定义错误数", example = "xxx")
    private int diyErrorCount;
    @Column(name = "risk_tip_count",defaultValue = "0")
    @ColumnComment("风险提示词数")
    @Schema(description = "风险提示词数", example = "0")
    private Integer riskTipCount;

    @Column(name = "select_result_ids", type = MySqlTypeConstant.LONGTEXT)
    @ColumnComment("选择的检测结果ID")
    @Schema(description = "选择的检测结果ID", example = "1,2,3")
    private String selectResultIds;

    @Column(name = "send_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("任务下发时间")
    @DefaultValue("CURRENT_TIMESTAMP")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "任务下发时间", example = "2025-07-30 10:30:00")
    private Date sendTime;

    @Column(name = "create_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("创建时间")
    @DefaultValue("CURRENT_TIMESTAMP")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间", example = "2025-07-30 10:30:00")
    private Date createTime;

    @Column(name = "is_del", type = MySqlTypeConstant.TINYINT)
    @ColumnComment("是否删除")
    @DefaultValue("0")
    private Boolean isDel;

    @Column("create_user_id")
    @ColumnComment("创建账号ID")
    @Schema(description = "创建账号ID", example = "1")
    private Integer createUserId;

    @Column("create_user_name")
    @ColumnComment("创建账号名称")
    @Schema(description = "创建账号名称", example = "xxx")
    private String createUserName;
}
