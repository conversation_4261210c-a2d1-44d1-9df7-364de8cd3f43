package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gitee.sunchenbin.mybatis.actable.annotation.*;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 转办督办记录
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Data
@TableName("chk_transfer_replay")
@Schema(name = "转办督办记录", description = "转办督办记录")
public class ChkTransferReplay {

    @IsKey
    @IsAutoIncrement
    @Column("id")
    @ColumnComment("编号")
    @Schema(description = "编号", example = "1")
    private Long id;

    @Column("transfer_task_id")
    @ColumnComment("转办督办任务Id")
    @Schema(description = "转办督办任务Id", example = "1")
    private Long transferTaskId;

    @Column("transfer_result_img_url")
    @ColumnComment("整改结果截图")
    @Schema(description = "整改结果截图", example = "xxx")
    private String transferResultImgUrl;

    @Column("transfer_article_url")
    @ColumnComment("整改链接URL")
    @Schema(description = "整改链接URL", example = "xxx")
    private String transferArticleUrl;

    @Column("transfer_msg")
    @ColumnComment("整改留言")
    @Schema(description = "整改留言", example = "xxx")
    private String transferMsg;

    @Column(name = "transfer_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("整改提交时间")
    @DefaultValue("CURRENT_TIMESTAMP")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "整改提交时间", example = "2025-07-30 10:30:00")
    private Date transferTime;

    @Column(name = "create_time", type = MySqlTypeConstant.DATETIME)
    @DefaultValue("CURRENT_TIMESTAMP")
    @ColumnComment("创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间", example = "2025-07-30 10:30:00")
    private Date createTime;

    @Column("create_user_id")
    @ColumnComment("创建账号ID")
    @Schema(description = "创建账号ID", example = "1")
    private Integer createUserId;

    @Column("create_user_name")
    @ColumnComment("创建账号名称")
    @Schema(description = "创建账号名称", example = "xxx")
    private String createUserName;


    @Column(name = "update_time", type = MySqlTypeConstant.DATETIME)
    @IgnoreUpdate
    @DefaultValue("NULL ON UPDATE CURRENT_TIMESTAMP")
    @ColumnComment("更新时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间", example = "2025-07-30 10:30:00")
    private Date updateTime;

    @Column("update_user_id")
    @ColumnComment("更新账号ID")
    @Schema(description = "更新账号ID", example = "1")
    private Integer updateUserId;

    @Column("update_user_name")
    @ColumnComment("更新账号名称")
    @Schema(description = "更新账号名称", example = "xxx")
    private String updateUserName;

    @Column(name = "is_del", type = MySqlTypeConstant.TINYINT)
    @ColumnComment("是否删除")
    @DefaultValue("0")
    private Boolean isDel;

}
