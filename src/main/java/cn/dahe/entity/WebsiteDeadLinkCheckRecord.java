package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.annotation.ColumnComment;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsAutoIncrement;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsKey;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
@TableName("t_website_dead_link_check_record")
public class WebsiteDeadLinkCheckRecord {

    @IsKey
    @IsAutoIncrement
    @Column("id")
    @ColumnComment("ID")
    private Integer id;

    @Column(value = "web_id", defaultValue = "0")
    @ColumnComment("网站Id")
    private Integer webId;

    @Column(value = "web_name")
    @ColumnComment("网站名称")
    private String webName;

    @Column("web_url")
    @ColumnComment("网站地址")
    private String webUrl;

    @Column(value = "link_url", type = MySqlTypeConstant.TEXT)
    @ColumnComment("死链地址")
    private String linkUrl;

    @Column(value = "snapshot", type = MySqlTypeConstant.LONGTEXT)
    @ColumnComment("死链快照")
    private String snapshot;

    @Column(value = "source_page", type = MySqlTypeConstant.TEXT)
    @ColumnComment("来源页面")
    private String sourcePage;

    @Column(name = "http_code", comment = "状态码，200表示成功", defaultValue = "200")
    private int httpCode;

    @Column(value = "http_code_desc", type = MySqlTypeConstant.TEXT)
    @ColumnComment("状态码，解释")
    private String httpCodeDesc;

    @Column(name = "type", comment = "类型 0其他 1图片 2附件 3外部链接 4文章", defaultValue = "0")
    private int type;

    @Column(name = "latest", comment = "是否为最新的", defaultValue = "1")
    private int latest;

    @Column(name = "check_time", comment = "访问时间/检查时间", type = MySqlTypeConstant.DATETIME)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date checkTime;

    @Column(name = "create_time", comment = "添加时间", type = MySqlTypeConstant.DATETIME)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @Column(name = "pub_time", comment = "发布时间", type = MySqlTypeConstant.DATETIME)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date pubTime;

    @Column("sign")
    @ColumnComment("唯一签名")
    private String sign;

    @Column(name = "process_type", comment = "解析处理类型，和栏目值保持一致", type = MySqlTypeConstant.INT)
    private Integer processType;

}
