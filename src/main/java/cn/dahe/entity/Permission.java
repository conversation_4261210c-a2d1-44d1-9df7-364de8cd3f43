package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.annotation.DefaultValue;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsAutoIncrement;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsKey;
import lombok.Data;

/**
 * 权限表
 */
@Data
@TableName("t_permission")
public class Permission {
    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    @IsKey
    @IsAutoIncrement
    @Column(name = "id", comment = "id")
    private Integer id;

    /**
     * 权限名称
     */
    @Column(name = "name", comment = "权限名称")
    private String name;

    /**
     * 授权字符
     */
    @Column(name = "sn", comment = "授权字符")
    private String sn;

    /**
     * 备注
     */
    @Column(name = "note", comment = "备注")
    private String note;

    /**
     * 状态 -1：删除 0：停用 1：启用
     */
    @Column(name = "status", comment = "状态 0：停用 1：启用")
    @DefaultValue("0")
    private int status;

    /**
     * 父级权限ID
     */
    @Column(name = "pid", comment = "父级权限ID")
    @DefaultValue("0")
    private int pid;

    /**
     * 权限分类 0 按钮 1 列表
     */
    @Column(name = "type", comment = "权限分类")
    @DefaultValue("1")
    private int type;
}