package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.*;
import lombok.Data;

/**
 * 内容错误等级实体类
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
@TableName("t_check_error_level")
public class CheckErrorLevel {

    @IsKey
    @IsAutoIncrement
    @TableId
    @Column("id")
    @ColumnComment("主键ID")
    private Integer id;

    @Column("level_name")
    @ColumnComment("错误等级名称")
    private String levelName;

    @Column("remark")
    @ColumnComment("错误等级说明")
    private String remark;
} 