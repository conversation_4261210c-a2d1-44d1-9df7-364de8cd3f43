package cn.dahe.service;

import cn.dahe.entity.WarnPlanPushUser;
import cn.dahe.entity.WarnPushMsg;
import cn.dahe.entity.WarnUser;
import cn.dahe.model.dto.WarnPushMsgDto;
import cn.dahe.service.impl.NewMediaService;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PushService {
    @Resource
    private NewMediaService newMediaService;
    @Resource
    private WarnUserService warnUserService;
    @Value("${anxun.base-url}")
    private String warnBaseUrl;

    public void pushWithUsers(Collection<WarnPlanPushUser> pushUsers, WarnPushMsg warnPushMsg) {
        WarnPushMsgDto dto = new WarnPushMsgDto();
        dto.setType(warnPushMsg.getType());
        dto.setTime(warnPushMsg.getTime());
        String link = warnPushMsg.getLink();
        if (!StrUtil.startWith(link, "http")) {
            link = warnBaseUrl + link;
        }
        dto.setLink(link);
        dto.setContent(warnPushMsg.getContent());
        pushWithUsers(pushUsers, dto);
    }

    public void pushWithUsers(Collection<WarnPlanPushUser> pushUsers, WarnPushMsgDto warnPushMsg) {
        if (CollUtil.isNotEmpty(pushUsers)) {
            //  TODO 按照userId和推送类型分组并去重
            Map<Integer, Set<Integer>> pushTypeMap = pushUsers.stream().collect(Collectors.groupingBy(
                    WarnPlanPushUser::getPushType,
                    // 收集每个组的userId并去重（使用Set实现去重）
                    Collectors.mapping(
                            WarnPlanPushUser::getUserId,
                            Collectors.toSet()
                    )
            ));
            //  微信推送
            Set<Integer> wxUserIds = pushTypeMap.get(0);
            if (CollUtil.isNotEmpty(wxUserIds)) {
                //  微信推送
                log.info("微信推送：{}", warnPushMsg.getContent());
                List<WarnUser> warnUsers = warnUserService.listByIds(wxUserIds);
                for (WarnUser warnUser : warnUsers) {
                    String openId = warnUser.getUserOpenId();
                    JsonNode jsonNode = newMediaService.sendWarn(warnPushMsg, openId);
                    log.info("{} {} 推送结果：{}", warnUser.getId(), warnUser.getUserName(), jsonNode);
                }
            }
            //  TODO 短信推送
        }
    }
}
