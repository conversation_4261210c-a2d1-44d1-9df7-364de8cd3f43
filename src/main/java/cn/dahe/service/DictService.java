package cn.dahe.service;

import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.Result;
import cn.dahe.model.dto.TreeNode;
import cn.dahe.entity.Dict;
import cn.dahe.model.query.DictQuery;
import cn.dahe.model.vo.LoginUserVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;


/**
 *
 */
public interface DictService extends IService<Dict> {


    /**
     * 分页查询
     *
     * @param query
     * @return
     */
    PageResult<Dict> page(DictQuery query);


    /**
     * 权限树
     *
     * @return
     */
    Result listMediaTreelByProject(String dictIdProject);

    /**
     * 权限树
     *
     * @return
     */
    Result listMediaTreelByProjectSpecial(String dictIdGenre);

    /**
     * 权限树
     *
     * @return
     */
    Result listDictTreelById(String id);

    /**
     * 权限树
     *
     * @return
     */
    List<TreeNode> listDictTreelProject(LoginUserVO loginUserVO);

    /**
     * 权限树
     *
     * @return
     */
    List<TreeNode> listDictTreelProjectByType(String type);


    /**
     * 添加
     *
     * @param dict
     * @return
     */
    Result<String> save(Dict dict, LoginUserVO user);


    /**
     * 编辑
     *
     * @param id
     * @param dict
     * @return
     */
    Result<String> update(String id, Dict dict, LoginUserVO user);


    /**
     * 修改状态
     *
     * @param id
     * @param status
     * @return
     */
    Result<String> updateStatus(String id, int status);


    List<String> getByType(String type);

}