package cn.dahe.service.impl;

import cn.dahe.dao.CheckWordDao;
import cn.dahe.entity.CheckWord;
import cn.dahe.model.dto.CheckResultDto;
import cn.dahe.service.CheckErrorTypeService;
import cn.dahe.service.CheckWordService;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 文章检查错误词库Service实现类
 */
@Service
public class CheckWordServiceImpl extends ServiceImpl<CheckWordDao, CheckWord> implements CheckWordService {


    @Override
    public boolean updateFilterStatus(Long wordId, Boolean filterStatus) {
        return this.lambdaUpdate()
                .eq(CheckWord::getId, wordId)
                .set(CheckWord::getFilterStatus, filterStatus)
                .set(CheckWord::getUpdateTime, new Date())
                .update();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, CheckWord> batchGetOrCreateCheckWords(List<CheckResultDto> checkDtos) {
        if (CollUtil.isEmpty(checkDtos)) {
            return new HashMap<>();
        }

        // 1. 收集所有错误词
        Set<String> allErrorWords = checkDtos.stream()
                .map(CheckResultDto::getErrorWord)
                .collect(Collectors.toSet());

        // 2. 批量查询已存在的错误词
        Map<String, CheckWord> resultMap = this.getBaseMapper().selectBatchDistinctWord(allErrorWords).stream()
                .collect(Collectors.toMap(CheckWord::getErrorWord, Function.identity()));

        // 筛选需要新增的词（不在已有结果中）
        List<CheckWord> newWords = checkDtos.stream()
                // 只处理不在已有结果中的词，且每个词只处理一次
                .filter(dto -> !resultMap.containsKey(dto.getErrorWord()))
                .collect(Collectors.toMap(
                        CheckResultDto::getErrorWord,
                        Function.identity(),
                        (existing, replacement) -> existing // 重复时保留第一个
                ))
                .values().stream()
                // 转换为实体对象
                .map(this::transfer)
                .collect(Collectors.toList());

        // 批量保存并更新结果映射
        if (!newWords.isEmpty()) {
            this.saveBatch(newWords);
            newWords.forEach(word -> resultMap.put(word.getErrorWord(), word));
        }

        return resultMap;
    }

    private CheckWord transfer(CheckResultDto dto) {
        CheckWord word = new CheckWord();
        word.setErrorWord(dto.getErrorWord());
        word.setSuggestWord(dto.getSuggestWord());
        Long[] typeHierarchy = CheckErrorTypeService.getTypeHierarchy((long) dto.getErrorType().getValue());
        word.setFirstErrorType(typeHierarchy[0]);
        word.setSecondErrorType(typeHierarchy[1]);
        word.setThirdErrorType(typeHierarchy[2]);
        word.setErrorLevel(dto.getErrorLevel().getValue());
        word.setFilterStatus(false);
        Date date = new Date();
        word.setCreateTime(date);
        word.setUpdateTime(date);
        return word;
    }
} 