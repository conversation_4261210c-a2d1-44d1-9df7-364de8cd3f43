package cn.dahe.service.impl;

import cn.dahe.common.constants.StatusConstants;
import cn.dahe.dao.SystemNoticeDao;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.Result;
import cn.dahe.entity.SystemNotice;
import cn.dahe.model.query.SystemNoticeQuery;
import cn.dahe.service.SystemNoticeService;
import cn.dahe.utils.StringUtils;
import cn.dahe.model.vo.LoginUserVO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2023-08-10
 */
@Service
@AllArgsConstructor
public class SystemNoticeServiceImpl extends BaseServiceImpl<SystemNoticeDao, SystemNotice> implements SystemNoticeService {
    @Override
    public PageResult<SystemNotice> page(SystemNoticeQuery query) {
        IPage<SystemNotice> dataPage = baseMapper.selectPage(new Page(query.getPage(), query.getLimit()), getWrapper(query));
        return new PageResult<>(dataPage.getRecords(), dataPage);
    }

    @Override
    public List<SystemNotice> listAll(SystemNoticeQuery query) {
        query.setStatus(StatusConstants.COMMON_NORMAL + "");
        return list(getWrapper(query));
    }

    @Override
    public Result<String> save(SystemNotice systemNotice, LoginUserVO user) {
        String errorMsg = checkSystemNotice(systemNotice);
        if (StringUtils.isNotBlank(errorMsg)) {
            return Result.error(errorMsg);
        }
        SystemNotice entity = new SystemNotice();
        entity.setCreateTime(new Date());
        entity.setCreateUserId(user.getUserId());
        entity.setCreateUserName(user.getUsername());
        entity.setUpdateUserId(user.getUserId());
        entity.setUpdateUserName(user.getUsername());
        entity.setUpdateTime(entity.getCreateTime());
        entity.setTitle(systemNotice.getTitle());
        entity.setContent(systemNotice.getContent());
        if (!save(entity)) {
            return Result.error();
        }
        return Result.ok();
    }

    @Override
    public Result<String> update(SystemNotice systemNotice, LoginUserVO user) {
        SystemNotice oldSystemNotice = this.getById(systemNotice.getId());
        if (oldSystemNotice == null) {
            return Result.error("该网站不存在，请重新选择");
        }
        String errorMsg = checkSystemNotice(systemNotice);
        if (StringUtils.isNotBlank(errorMsg)) {
            return Result.error(errorMsg);
        }
        oldSystemNotice.setUpdateUserId(user.getUserId());
        oldSystemNotice.setUpdateUserName(user.getUsername());
        oldSystemNotice.setUpdateTime(new Date());
        oldSystemNotice.setTitle(systemNotice.getTitle());
        oldSystemNotice.setContent(systemNotice.getContent());
        if (!updateById(oldSystemNotice)) {
            return Result.error();
        }
        return Result.ok();
    }

    @Override
    public Result<String> updateStatus(String id, LoginUserVO user) {
        SystemNotice systemNotice = this.getById(id);
        if (systemNotice == null) {
            return Result.error("该公告不存在，请重新选择");
        }
        systemNotice.setStatus(systemNotice.getStatus().equals(StatusConstants.COMMON_NORMAL) ? StatusConstants.COMMON_DISABLE : StatusConstants.COMMON_NORMAL);
        systemNotice.setUpdateUserId(user.getUserId());
        systemNotice.setUpdateUserName(user.getUsername());
        systemNotice.setUpdateTime(new Date());
        if (!updateById(systemNotice)) {
            return Result.error();
        }
        return Result.ok();
    }


    private String checkSystemNotice(SystemNotice systemNotice) {
        if (StringUtils.isBlank(systemNotice.getTitle())) {
            return "标题不能为空";
        }
        if (StringUtils.isBlank(systemNotice.getContent())) {
            return "内容不能为空";
        }
        return "";
    }


    private QueryWrapper<SystemNotice> getWrapper(SystemNoticeQuery query) {
        QueryWrapper<SystemNotice> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(query.getStatus())) {
            queryWrapper.eq("status", query.getStatus());

        }
        queryWrapper.orderByDesc("id");
        return queryWrapper;
    }
}
