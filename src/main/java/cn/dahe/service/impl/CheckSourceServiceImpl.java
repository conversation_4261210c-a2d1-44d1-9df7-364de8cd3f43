package cn.dahe.service.impl;

import cn.dahe.dao.CheckSourceDao;
import cn.dahe.entity.CheckSource;
import cn.dahe.service.CheckSourceService;
import org.springframework.stereotype.Service;

/**
 * 校对服务来源Service实现类
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Service
public class CheckSourceServiceImpl extends BaseServiceImpl<CheckSourceDao, CheckSource> implements CheckSourceService {

    @Override
    public CheckSource getBySourceCode(String sourceCode) {
        return this.lambdaQuery()
                .eq(CheckSource::getSourceCode, sourceCode)
                .one();
    }
} 