package cn.dahe.service.impl;

import cn.dahe.dao.ChkUpdateSiteColumnDao;
import cn.dahe.dao.ChkUpdateSiteColumnResultDao;
import cn.dahe.dao.WebsiteDao;
import cn.dahe.dao.WebsiteGroupDao;
import cn.dahe.entity.ChkUpdateSiteColumnResult;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.query.ChkUpdateSiteColumnQuery;
import cn.dahe.model.vo.ChkUpdateSiteColumnCountVO;
import cn.dahe.model.vo.ChkUpdateSiteColumnDetailVO;
import cn.dahe.model.vo.ChkUpdateSiteColumnVO;
import cn.dahe.model.vo.LoginUserVO;
import cn.dahe.service.ChannelService;
import cn.dahe.service.ChkUpdateSiteColumnService;
import cn.dahe.utils.excel.ExcelExportUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 栏目更新检查Service实现类 - 对应原型图功能
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@Service
public class ChkUpdateSiteColumnServiceImpl implements ChkUpdateSiteColumnService {

    @Resource
    private WebsiteDao websiteDao;
    @Resource
    private WebsiteGroupDao websiteGroupDao;
    @Resource
    private ChkUpdateSiteColumnDao chkUpdateSiteColumnDao;
    @Resource
    private ChannelService channelService;
    @Resource
    private ChkUpdateSiteColumnResultDao chkUpdateSiteColumnResultDao;
    // 定义锁对象
    private final Lock lock = new ReentrantLock();

    // ==================== 栏目更新检查概览 ====================

    @Override
    public ChkUpdateSiteColumnCountVO getOverviewStatistics(ChkUpdateSiteColumnQuery query) {
        try {
            ChkUpdateSiteColumnCountVO result = chkUpdateSiteColumnDao.getOverviewStatistics(query);
            result.setNormalCheckCount(result.getTotalCheckResultCount() - result.getSeriousOverdueCount() - result.getAboutToOverdueCount());
            return result;
        } catch (Exception e) {
            log.error("获取栏目更新检查概览统计失败，返回兜底数据", e);
        }
        return new ChkUpdateSiteColumnCountVO();
    }

    // ==================== 栏目更新检查记录 ====================

    @Override
    public PageResult<ChkUpdateSiteColumnVO> page(ChkUpdateSiteColumnQuery query) {
        try {
            Page<ChkUpdateSiteColumnVO> page = new Page<>(query.getPage(), query.getLimit());
            IPage<ChkUpdateSiteColumnVO> result = chkUpdateSiteColumnDao.selectPageWithExtInfo(page, query);
            return PageResult.page(result);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("查询栏目更新检查记录失败，返回兜底数据", e);
        }
        return new PageResult<>();
    }

    @Override
    public PageResult<ChkUpdateSiteColumnDetailVO> get(Long id, String updateTime, int page, int limit) {
        try {
            Page<ChkUpdateSiteColumnDetailVO> pageReq = new Page<>(page, limit);
            IPage<ChkUpdateSiteColumnDetailVO> chkUpdateSiteColumnDetailVOIPage = chkUpdateSiteColumnDao.selectDetailById(pageReq, id, updateTime);
            return PageResult.page(chkUpdateSiteColumnDetailVOIPage);
        } catch (Exception e) {
            log.error("获取栏目更新检查详情失败，返回兜底数据", e);
        }
        return new PageResult<>();
    }

    // ==================== 数据导出 ====================

    @Override
    public void export(ChkUpdateSiteColumnQuery query, LoginUserVO user, HttpServletResponse response) throws IOException {
        try {
            log.info("用户{}导出栏目更新检查记录", user.getUserId());
            // 1. 查询数据
            List<Map<String, Object>> dataList = chkUpdateSiteColumnDao.selectExportData(query);
            // 2. 导出
            ExcelExportUtil.commonExport("栏目更新检查记录", new String[]{"序号", "栏目名称", "栏目分类", "更新期限", "检测状态", "连续不更新天数", "检测结果"},
                    new String[]{"id", "column_name", "column_category", "update_time", "check_status_desc", "continuous_not_update_days", "check_result"}, dataList, response);
        } catch (Exception e) {
            log.error("导出栏目更新检查记录失败", e);
            throw new IOException("导出失败：" + e.getMessage());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delUpdateSiteColumnData(List<Long> ids, List<ChkUpdateSiteColumnResult> updateSiteColumnResults) {
        // 加锁
        lock.lock();
        try {
            if (updateSiteColumnResults != null && updateSiteColumnResults.size() > 0) {
                chkUpdateSiteColumnResultDao.insertBatch(updateSiteColumnResults);
            }
            if (ids != null && ids.size() == 2) {
                chkUpdateSiteColumnDao.deleteSiteColumnTaskDeleteTodayData(ids.get(0), ids.get(1));
            }
        } finally {
            // 确保释放锁
            lock.unlock();
        }
    }

    @Override
    public List<Map<String, Object>> selectSiteColumnTaskInsertTodayData() {
        return chkUpdateSiteColumnDao.selectSiteColumnTaskInsertTodayData();
    }

    @Override
    public List<Long> getWaitDeleteSiteColumnIds() {
        return chkUpdateSiteColumnDao.selectWaitDeleteResultIds();
    }


}
