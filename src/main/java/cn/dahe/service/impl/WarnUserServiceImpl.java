package cn.dahe.service.impl;

import cn.dahe.dao.WarnUserDao;
import cn.dahe.entity.WarnUser;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.Result;
import cn.dahe.model.dto.WarnUserDTO;
import cn.dahe.model.query.WarnUserQuery;
import cn.dahe.service.WarnUserService;
import cn.dahe.utils.WarnMaskUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class WarnUserServiceImpl extends BaseServiceImpl<WarnUserDao, WarnUser> implements WarnUserService {

    @Resource
    private WarnUserDao warnUserDao;

    @Override
    public Integer create(WarnUser user) {
        user.setCreateTime(new Date());
        this.baseMapper.insert(user);
        return user.getId();
    }

    @Override
    public Result<Boolean> updateForSubscribe(String openId, Integer subscribeStatus) {
        WarnUser user = warnUserDao.getByOpenId(openId);
        boolean isExist = true;
        if (user == null) {
            isExist = false;
        } else {
            user.setIsSubscribe(subscribeStatus);
            this.baseMapper.updateById(user);
        }
        return Result.ok(isExist);
    }

    @Override
    public Boolean deleteById(Integer id) {
        WarnUser user = this.baseMapper.selectById(id);
        if (user == null) {
            return false;
        }
        return this.baseMapper.deleteById(id) > 0;
    }

    @Override
    public WarnUserDTO getByIdMasked(Integer id) {
        WarnUser user = this.baseMapper.selectById(id);
        if (user == null) {
            return null;
        }
        return toDTO(user);
    }

    @Override
    public PageResult<WarnUserDTO> pageMasked(WarnUserQuery query) {
        if (query.getPage() == null || query.getPage() < 1) {
            query.setPage(1);
        }
        if (query.getLimit() == null || query.getLimit() < 1) {
            query.setLimit(10);
        }
        PageHelper.startPage(query.getPage(), query.getLimit());
        List<WarnUser> list = warnUserDao.list(query);
        PageInfo<WarnUser> pageInfo = new PageInfo<>(list);
        List<WarnUserDTO> dtoList = list.stream().map(this::toDTO).collect(Collectors.toList());
        PageResult<WarnUserDTO> result = new PageResult<>();
        result.setTotal(pageInfo.getTotal());
        result.setPageSize(pageInfo.getSize());
        result.setPage(pageInfo.getPageNum());
        result.setTotalPages(pageInfo.getPages());
        result.setList(dtoList);
        return result;
    }

    @Override
    public Boolean updateUsername(Integer id, String userName) {
        // 检查用户是否存在
        WarnUser user = this.baseMapper.selectById(id);
        if (user == null) {
            return false;
        }
        // 更新用户名
        user.setUserName(userName);
        // 更新到数据库
        return this.baseMapper.updateById(user) > 0;
    }

    private WarnUserDTO toDTO(WarnUser user) {
        WarnUserDTO dto = new WarnUserDTO();
        dto.setId(user.getId());
        dto.setUserName(user.getUserName());
        dto.setUserOpenId(WarnMaskUtil.openIdEncrypt(user.getUserOpenId()));
        dto.setUserPhone(WarnMaskUtil.phoneEncrypt(user.getUserPhone()));
        dto.setCreateTime(user.getCreateTime());
        return dto;
    }
}
