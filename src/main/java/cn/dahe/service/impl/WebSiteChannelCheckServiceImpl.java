package cn.dahe.service.impl;

import cn.dahe.controller.check.CheckErrorLevelController;
import cn.dahe.dao.ArticleDao;
import cn.dahe.dao.ChannelDao;
import cn.dahe.dao.WebSiteChannelCheckDao;
import cn.dahe.entity.*;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.query.WebSiteChannelCheckQuery;
import cn.dahe.model.vo.WebSiteChannelCheckDetailVO;
import cn.dahe.model.vo.WebSiteChannelCheckExportVO;
import cn.dahe.model.vo.WebSiteChannelStatisticsVO;
import cn.dahe.service.ChannelService;
import cn.dahe.service.ChannelTypeService;
import cn.dahe.service.WebSiteChannelCheckService;
import cn.dahe.service.WebsiteService;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 网站栏目更新检查Service实现类
 *
 * <AUTHOR>
 * @date 2025-09-09
 */
@Slf4j
@Service
public class WebSiteChannelCheckServiceImpl extends ServiceImpl<WebSiteChannelCheckDao, WebSiteChannelCheck> implements WebSiteChannelCheckService {

    @Resource
    private WebSiteChannelCheckDao webSiteChannelCheckDao;

    @Resource
    private ChannelService channelService;

    @Resource
    private ArticleDao articleDao;

    @Resource
    private WebsiteService websiteService;
    
    @Resource
    private Executor asyncServiceExecutor;

    @Resource
    private ChannelTypeService channelTypeService;

    @Override
    public Integer generateChannelCheckData() {
        log.info("开始生成栏目检查数据");
        long startTime = System.currentTimeMillis();
        
        // 1、查询所有栏目(Channel) 状态enable为1且processType为0的
        List<Channel> enabledChannels = channelService.lambdaQuery()
                .eq(Channel::getEnable, 1)
                .eq(Channel::getProcessType, 0)
                .list();
        
        if (CollUtil.isEmpty(enabledChannels)) {
            log.info("没有找到启用且processType为0的栏目");
            return 0;
        }
        
        // 2、设置时间范围：从昨天开始往前推2天，查询前3天的数据
        LocalDate today = LocalDate.now();
        LocalDate endDate = today.minusDays(1); // 昨天
        LocalDate startDate = today.minusDays(3); // 前天往前推1天，总共3天（day-3, day-2, day-1）
        log.info("生成栏目检查数据，时间范围：{} 到 {}，栏目数量：{}", startDate, endDate, enabledChannels.size());
        
        // 3、使用多线程并行处理栏目，每批处理20个栏目
        int batchSize = 20;
        List<List<Channel>> channelBatches = CollUtil.split(enabledChannels, batchSize);
        List<CompletableFuture<Integer>> futures = new ArrayList<>();
        
        for (List<Channel> batch : channelBatches) {
            CompletableFuture<Integer> future = CompletableFuture.supplyAsync(() -> {
                return procesChannelBatch(batch, startDate, endDate);
            }, asyncServiceExecutor);
            futures.add(future);
        }
        
        // 4、等待所有批次完成并统计结果
        int totalGenerated = futures.stream()
                .mapToInt(CompletableFuture::join)
                .sum();
        
        long endTime = System.currentTimeMillis();
        log.info("栏目检查数据生成完成，共生成{}条记录，耗时{}ms", totalGenerated, endTime - startTime);
        return totalGenerated;
    }
    
    @Async("asyncServiceExecutor")
    @Override
    public CompletableFuture<Integer> generateChannelCheckDataAsync() {
        log.info("异步开始生成栏目检查数据");
        Integer result = generateChannelCheckData();
        log.info("异步生成栏目检查数据完成，共生成{}条记录", result);
        return CompletableFuture.completedFuture(result);
    }

    @Override
    public PageResult<WebSiteChannelCheckDetailVO> pageChannelCheckDetail(WebSiteChannelCheckQuery query) {
        // 设置默认分页参数
        if (query.getPage() == null || query.getPage() < 1) {
            query.setPage(1);
        }
        if (query.getLimit() == null || query.getLimit() < 1) {
            query.setLimit(10);
        }
        // 优化：使用数据库分页查询
        return pageChannelCheckDetailOptimized(query);
    }
    @Override
    public List<WebSiteChannelCheck> listByColumnIdAndDateRange(Long columnId, LocalDate startDate, LocalDate endDate) {
        return webSiteChannelCheckDao.selectByColumnIdAndDateRange(columnId, startDate, endDate);
    }
    
    /**
     * 批量处理栏目数据生成（多线程使用）
     */
    private Integer procesChannelBatch(List<Channel> channels, LocalDate startDate, LocalDate endDate) {
        int batchGenerated = 0;
        String threadName = Thread.currentThread().getName();
        log.debug("线程{}开始处理{}个栏目", threadName, channels.size());
        
        for (Channel channel : channels) {
            try {
                // 遍历每一天
                LocalDate currentDate = startDate;
                while (!currentDate.isAfter(endDate)) {
                    // 检查是否已存在该栏目该日期的记录
                    WebSiteChannelCheck existingRecord = webSiteChannelCheckDao.selectByColumnIdAndDate(
                            channel.getId().longValue(), currentDate);
                    
                    if (existingRecord == null) {
                        // 生成新记录
                        WebSiteChannelCheck newRecord = generateDailyRecord(channel, currentDate);
                        if (newRecord != null) {
                            save(newRecord);
                            batchGenerated++;
                        }
                    }
                    currentDate = currentDate.plusDays(1);
                }
            } catch (Exception e) {
                log.error("处理栏目{}失败", channel.getId(), e);
            }
        }
        
        log.debug("线程{}处理完成，生成{}条记录", threadName, batchGenerated);
        return batchGenerated;
    }
    /**
     * 根据查询条件获取栏目列表（缓存优化）
     */
    private List<Channel> getChannelsByQuery(WebSiteChannelCheckQuery query) {
        log.info("开始查询栏目列表，查询条件: channelTypeId={}, channelName={}, websiteIds={}", 
                query.getChannelTypeId(), query.getChannelName(), query.getWebsiteIds());
        
        LambdaQueryWrapper<Channel> wrapper = new LambdaQueryWrapper<Channel>()
                .eq(Channel::getEnable, 1) // 只查询启用的栏目
                .eq(Channel::getProcessType, 0); // 只查询processType为0的栏目
        // 根据栏目名称模糊查询
        if (StrUtil.isNotBlank(query.getChannelName())) {
            wrapper.like(Channel::getName, query.getChannelName());
        }
        // 根据站点ID列表过滤
        if (CollUtil.isNotEmpty(query.getWebsiteIds())) {
            wrapper.in(Channel::getSiteId, query.getWebsiteIds());
        }
        // 根据栏目类型ID过滤
        if (query.getChannelTypeId() != null) {
            wrapper.eq(Channel::getChannelTypeId, query.getChannelTypeId());
            log.info("添加栏目类型ID过滤条件: {}", query.getChannelTypeId());
        }
        
        List<Channel> channels = channelService.list(wrapper);
        log.info("查询到{}个栏目，其中栏目类型ID为{}的有{}个", 
                channels.size(), 
                query.getChannelTypeId(),
                channels.stream().filter(c -> query.getChannelTypeId() == null || 
                        query.getChannelTypeId().equals(c.getChannelTypeId())).count());
        
        return channels;
    }

    /**
     * 判断栏目是否应该包含在结果中
     */
    private boolean shouldIncludeChannel(Channel channel, WebSiteChannelCheckQuery query) {
        // 根据栏目类型ID过滤
        if (query.getChannelTypeId() != null && !query.getChannelTypeId().equals(channel.getChannelTypeId())) {
            return false;
        }
        
        // 根据isUpdate过滤（需要查询检查记录来判断）
        if (query.getIsUpdate() != null) {
            // 查询最近的检查记录来判断更新状态
            LocalDate endDate = LocalDate.now().minusDays(1);
            LocalDate startDate = endDate.minusDays(29);
            
            List<WebSiteChannelCheck> checkRecords = listByColumnIdAndDateRange(
                    channel.getId().longValue(), startDate, endDate);
            
            if (CollUtil.isEmpty(checkRecords)) {
                return query.getIsUpdate() == 0; // 没有记录视为未更新
            }
            
            // 找到最新一天的记录，根据最新一天的状态进行过滤
            WebSiteChannelCheck latestRecord = checkRecords.stream()
                    .filter(r -> r.getCheckTime() != null)
                    .max(Comparator.comparing(WebSiteChannelCheck::getCheckTime))
                    .orElse(null);
                    
            if (latestRecord == null) {
                return query.getIsUpdate() == 0; // 没有有效记录视为未更新
            }
            
            boolean latestDayUpdate = (latestRecord.getIsUpdate() == 1);
            if (query.getIsUpdate() == 1 && !latestDayUpdate) {
                return false;
            }
            if (query.getIsUpdate() == 0 && latestDayUpdate) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * 构建栏目检查详情VO
     */
    private WebSiteChannelCheckDetailVO buildChannelCheckDetailVO(Channel channel, WebSiteChannelCheckQuery query) {
        try {
            WebSiteChannelCheckDetailVO vo = new WebSiteChannelCheckDetailVO();
            
            // 设置栏目基本信息
            vo.setColumnId(channel.getId().longValue());
            vo.setColumnName(channel.getName());
            vo.setColumnUrl(channel.getUrl());
            vo.setChannelTypeId(channel.getChannelTypeId());
            // 设置栏目类型名称
            ChannelType channelType = channelTypeService.getById(channel.getChannelTypeId());
            if (channelType != null) {
                vo.setChannelTypeName(channelType.getName());
                vo.setUpdatePeriod(channelType.getUpdatePeriod());
            }
            // 设置网站信息
            Website website = websiteService.getWebsiteBySiteId(channel.getSiteId());
            if (website != null) {
                vo.setWebsiteId(website.getId());
                vo.setWebsiteName(website.getWebName());
                vo.setWebsiteUrl(website.getWebUrl());
            }
            // 查询检查记录
            LocalDate endDate = LocalDate.now().minusDays(1);
            LocalDate startDate = endDate.minusDays(29);
            // 应用查询时间条件
            if (query.getBeginTime() != null) {
                LocalDate queryStartDate = query.getBeginTime().toInstant()
                        .atZone(ZoneId.systemDefault()).toLocalDate();
                if (queryStartDate.isAfter(startDate)) {
                    startDate = queryStartDate;
                }
            }
            if (query.getEndTime() != null) {
                LocalDate queryEndDate = query.getEndTime().toInstant()
                        .atZone(ZoneId.systemDefault()).toLocalDate();
                if (queryEndDate.isBefore(endDate)) {
                    endDate = queryEndDate;
                }
            }
            List<WebSiteChannelCheck> checkRecords = listByColumnIdAndDateRange(
                    channel.getId().longValue(), startDate, endDate);
            // 构建详细信息
            buildDetailInfo(vo, checkRecords);
            return vo;
            
        } catch (Exception e) {
            log.error("构建栏目{}检查详情失败", channel.getId(), e);
            return null;
        }
    }
    
    /**
     * 构建详细信息
     */
    private void buildDetailInfo(WebSiteChannelCheckDetailVO vo, List<WebSiteChannelCheck> checkRecords) {
        log.debug("开始构建栏目{}的详细信息，检查记录数量: {}", vo.getColumnId(), 
                CollUtil.isNotEmpty(checkRecords) ? checkRecords.size() : 0);
        if (CollUtil.isEmpty(checkRecords)) {
            vo.setIsUpdate(0);
            vo.setUpdateCount(0);
            vo.setContinuousNotUpdateDays(30);
            vo.setContinuousNotUpdateCount(30);
            vo.setContinuousUpdateCount(0);
            vo.setCheckDays(Collections.emptyList());
            vo.setCheckNotUpdateDays(Collections.emptyList());
            vo.setCheckUpdateDays(Collections.emptyList());
            return;
        }
        
        // 构建更新天数列表
        List<WebSiteChannelCheckDetailVO.UpdateDayInfo> checkDays = new ArrayList<>();
        List<String> checkNotUpdateDays = new ArrayList<>();
        List<String> checkUpdateDays = new ArrayList<>();
        
        // 按日期排序检查记录，从最新到最旧
        List<WebSiteChannelCheck> sortedRecords = checkRecords.stream()
                .filter(r -> r.getCheckTime() != null)
                .sorted(Comparator.comparing(WebSiteChannelCheck::getCheckTime).reversed())
                .collect(Collectors.toList());
        
        boolean hasUpdate = false; // 整个时间段内是否有过更新
        boolean latestDayUpdate = false; // 最新一天是否有更新
        Date latestUpdateTime = null;
        
        // 获取最新一天的状态（sortedRecords的第一条记录就是最新的）
        WebSiteChannelCheck latestRecord = sortedRecords.get(0);
        Integer latestUpdateCount = latestRecord.getUpdateCount();
        latestDayUpdate = (latestRecord.getIsUpdate() == 1);
        log.debug("栏目{}最新一天的更新状态：isUpdate={}, updateCount={}", 
                vo.getColumnId(), latestRecord.getIsUpdate(), latestRecord.getUpdateCount());
        
        // 遍历检查记录，构建更新天数列表
        for (WebSiteChannelCheck record : sortedRecords) {
            LocalDate recordDate = record.getCheckTime().toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDate();
            
            // 构建更新天数信息
            WebSiteChannelCheckDetailVO.UpdateDayInfo updateDay = new WebSiteChannelCheckDetailVO.UpdateDayInfo();
            updateDay.setUpdateDate(recordDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            updateDay.setIsUpdate(record.getIsUpdate());
            updateDay.setUpdateCount(record.getUpdateCount());
            checkDays.add(updateDay);
            
            if (record.getIsUpdate() == 1) {
                hasUpdate = true;
                checkUpdateDays.add(recordDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                if (latestUpdateTime == null || record.getLastUpdateTime().after(latestUpdateTime)) {
                    latestUpdateTime = record.getLastUpdateTime();
                }
            } else {
                checkNotUpdateDays.add(recordDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            }
        }
        
        // 设置基本信息 - 外层isUpdate应该表示最新一天的更新状态
        log.debug("栏目{}设置外层isUpdate: latestDayUpdate={}, hasUpdate={}", vo.getColumnId(), latestDayUpdate, hasUpdate);
        vo.setIsUpdate(latestDayUpdate ? 1 : 0);
        vo.setUpdateCount(latestUpdateCount); // 使用最新一天的更新数量
        vo.setLastUpdateTime(latestUpdateTime);
        vo.setCheckTime(new Date());
        vo.setCheckDays(checkDays);
        vo.setCheckNotUpdateDays(checkNotUpdateDays);
        vo.setCheckUpdateDays(checkUpdateDays);
        
        // 计算连续天数
        calculateContinuousDays(vo, sortedRecords);
    }
    
    /**
     * 计算连续更新和未更新天数
     */
    private void calculateContinuousDays(WebSiteChannelCheckDetailVO vo, List<WebSiteChannelCheck> sortedRecords) {
        if (CollUtil.isEmpty(sortedRecords)) {
            vo.setContinuousUpdateCount(0);
            vo.setContinuousNotUpdateCount(0);
            vo.setContinuousNotUpdateDays(0);
            return;
        }
        
        int continuousUpdateCount = 0;
        int continuousNotUpdateCount = 0;
        
        // 从最新记录开始计算连续天数
        for (WebSiteChannelCheck record : sortedRecords) {
            if (record.getIsUpdate() == 1) {
                if (continuousNotUpdateCount == 0) {
                    continuousUpdateCount++;
                } else {
                    break; // 遇到未更新记录，停止计算连续更新天数
                }
            } else {
                if (continuousUpdateCount == 0) {
                    continuousNotUpdateCount++;
                } else {
                    break; // 遇到更新记录，停止计算连续未更新天数
                }
            }
        }
        
        vo.setContinuousUpdateCount(continuousUpdateCount);
        vo.setContinuousNotUpdateCount(continuousNotUpdateCount);
        vo.setContinuousNotUpdateDays(continuousNotUpdateCount);
    }

    /**
     * 生成指定日期的栏目更新检查记录
     */
    private WebSiteChannelCheck generateDailyRecord(Channel channel, LocalDate checkDate) {
        try {
            // 查询该栏目在指定日期的文章
            List<Article> articles = articleDao.selectByChannelIdAndPubDate(channel.getId(), checkDate);
            
            // 创建检查记录
            WebSiteChannelCheck record = new WebSiteChannelCheck();
            record.setWebsiteId((long) channel.getSiteId());
            record.setColumnId(channel.getId().longValue());
            record.setColumnName(channel.getName());
            record.setUpdateCount(articles.size());
            record.setIsUpdate(articles.size() > 0 ? 1 : 0);
            // checkTime设置为检查的具体日期（当天的开始时间）
            record.setCheckTime(Date.from(checkDate.atStartOfDay()
                    .atZone(ZoneId.systemDefault()).toInstant()));
            
            // 设置最后更新时间为当天最后一篇文章的发布时间
            if (!articles.isEmpty()) {
                Date lastPubTime = articles.stream()
                        .map(Article::getPubTime)
                        .filter(Objects::nonNull)
                        .max(Date::compareTo)
                        .orElse(null);
                record.setLastUpdateTime(lastPubTime);
            }
            
            log.debug("生成栏目{}在{}的检查记录，文章数量：{}，是否更新：{}", 
                    channel.getId(), checkDate, articles.size(), record.getIsUpdate());
            
            return record;
            
        } catch (Exception e) {
            log.error("生成栏目{}在{}的检查记录失败", channel.getId(), checkDate, e);
            return null;
        }
    }
    
    /**
     * 优化的分页查询方法（多线程优化）
     */
    private PageResult<WebSiteChannelCheckDetailVO> pageChannelCheckDetailOptimized(WebSiteChannelCheckQuery query) {
        try {
            long startTime = System.currentTimeMillis();
            // 如果有isUpdate过滤条件，需要特殊处理，因为过滤条件需要查询检查记录
            if (query.getIsUpdate() != null) {
                return pageChannelCheckDetailWithUpdateFilter(query);
            }
            // 1. 使用ChannelDao的方法（包含与website表的联查逻辑）
            // 并行查询总数和分页数据
            CompletableFuture<Integer> totalFuture = CompletableFuture.supplyAsync(() -> {
                return ((ChannelDao) channelService.getBaseMapper()).getCountChannelsByWebsiteAndProcessType(query);
            }, asyncServiceExecutor);
            
            CompletableFuture<List<Channel>> channelsFuture = CompletableFuture.supplyAsync(() -> {
                // 计算分页参数
                int offset = (query.getPage() - 1) * query.getLimit();
                // 创建新的查询对象，设置分页参数
                WebSiteChannelCheckQuery pageQuery = new WebSiteChannelCheckQuery();
                pageQuery.setWebsiteIds(query.getWebsiteIds());
                pageQuery.setChannelName(query.getChannelName());
                pageQuery.setChannelTypeId(query.getChannelTypeId()); // 添加channelTypeId过滤条件
                pageQuery.setPage(offset); // 注意：这里传的是offset，不是page
                pageQuery.setLimit(query.getLimit());
                return ((ChannelDao) channelService.getBaseMapper()).getChannelsByWebsiteAndProcessType(pageQuery);
            }, asyncServiceExecutor);
            
            // 2. 等待基础数据查询完成
            long total = totalFuture.join().longValue();
            List<Channel> channels = channelsFuture.join();
            
            log.info("分页查询结果 - 总数: {}, 当前页数据量: {}, 查询条件: channelTypeId={}", 
                    total, channels.size(), query.getChannelTypeId());
            
            if (total == 0 || CollUtil.isEmpty(channels)) {
                return new PageResult<>(Collections.emptyList(), total, query.getLimit(), query.getPage());
            }
            
            // 3. 并行查询网站信息和检查记录
            Set<Integer> siteIds = channels.stream()
                    .map(Channel::getSiteId)
                    .collect(Collectors.toSet());
            List<Long> columnIds = channels.stream()
                    .map(channel -> channel.getId().longValue())
                    .collect(Collectors.toList());
            
            CompletableFuture<Map<Integer, Website>> websiteMapFuture = CompletableFuture.supplyAsync(() -> {
                return websiteService.listBySiteIds(siteIds).stream()
                        .collect(Collectors.toMap(
                                Website::getSiteId, 
                                Function.identity(),
                                (existing, replacement) -> {
                                    // 如果存在重复的siteId，优先保留ID较小的记录（通常是更早创建的）
                                    log.warn("发现重复的siteId: {}，保留ID较小的记录。existing: {}, replacement: {}", 
                                            existing.getSiteId(), existing.getId(), replacement.getId());
                                    return existing.getId() < replacement.getId() ? existing : replacement;
                                }
                        ));
            }, asyncServiceExecutor);
            CompletableFuture<Map<Long, List<WebSiteChannelCheck>>> checkRecordsMapFuture = CompletableFuture.supplyAsync(() -> {
                return batchQueryCheckRecords(columnIds, query);
            }, asyncServiceExecutor);
            
            // 4. 等待关联数据查询完成
            Map<Integer, Website> websiteMap = websiteMapFuture.join();
            Map<Long, List<WebSiteChannelCheck>> checkRecordsMap = checkRecordsMapFuture.join();
            // 5. 分批并行构建结果（避免创建过多线程）
            int batchSize = Math.min(20, channels.size()); // 控制批量大小
            List<List<Channel>> channelBatches = CollUtil.split(channels, batchSize);
            List<CompletableFuture<List<WebSiteChannelCheckDetailVO>>> batchFutures = channelBatches.stream()
                    .map(batch -> CompletableFuture.supplyAsync(() -> {
                        return batch.stream()
                                .map(channel -> buildChannelCheckDetailVOOptimized(channel, query, websiteMap, checkRecordsMap))
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList());
                    }, asyncServiceExecutor))
                    .collect(Collectors.toList());
            
            List<WebSiteChannelCheckDetailVO> result = batchFutures.stream()
                    .map(CompletableFuture::join)
                    .flatMap(List::stream)
                    .collect(Collectors.toList());
            long endTime = System.currentTimeMillis();
            log.debug("优化查询栏目检查结果完成，耗时{}ms，返回{}条记录", endTime - startTime, result.size());
            return new PageResult<>(result, total, query.getLimit(), query.getPage());
        } catch (Exception e) {
            log.error("优化查询栏目检查结果失败", e);
            // 降级：使用原有的channelService.page方法
            return pageChannelCheckDetailFallbackOptimized(query);
        }
    }
    
    /**
     * 带有isUpdate过滤条件的分页查询
     */
    private PageResult<WebSiteChannelCheckDetailVO> pageChannelCheckDetailWithUpdateFilter(WebSiteChannelCheckQuery query) {
        try {
            long startTime = System.currentTimeMillis();
            log.debug("使用isUpdate过滤条件分页查询，isUpdate={}", query.getIsUpdate());
            
            // 1. 先查询所有符合基本条件的栏目
            List<Channel> allChannels = getChannelsByQuery(query);
            if (CollUtil.isEmpty(allChannels)) {
                return new PageResult<>(Collections.emptyList(), 0L, query.getLimit(), query.getPage());
            }
            
            // 2. 并行查询所有栏目的检查记录
            List<Long> allColumnIds = allChannels.stream().map(channel -> channel.getId().longValue()).collect(Collectors.toList());
            Map<Long, List<WebSiteChannelCheck>> allCheckRecordsMap = batchQueryCheckRecords(allColumnIds, query);
            
            // 3. 根据isUpdate条件过滤栏目
            List<Channel> filteredChannels = allChannels.stream()
                    .filter(channel -> shouldIncludeChannelOptimized(channel, query, allCheckRecordsMap))
                    .collect(Collectors.toList());
            
            // 4. 手动分页过滤后的结果
            long filteredTotal = filteredChannels.size();
            int fromIndex = (query.getPage() - 1) * query.getLimit();
            int toIndex = Math.min(fromIndex + query.getLimit(), filteredChannels.size());
            
            if (fromIndex >= filteredChannels.size()) {
                return new PageResult<>(Collections.emptyList(), filteredTotal, query.getLimit(), query.getPage());
            }
            
            List<Channel> pagedChannels = filteredChannels.subList(fromIndex, toIndex);
            
            // 5. 并行查询分页栏目的网站信息
            Set<Integer> siteIds = pagedChannels.stream().map(Channel::getSiteId).collect(Collectors.toSet());
            CompletableFuture<Map<Integer, Website>> websiteMapFuture = CompletableFuture.supplyAsync(() -> {
                return websiteService.listBySiteIds(siteIds).stream()
                        .collect(Collectors.toMap(
                                Website::getSiteId, 
                                Function.identity(),
                                (existing, replacement) -> {
                                    log.warn("发现重复的siteId: {}，保留ID较小的记录。existing: {}, replacement: {}", 
                                            existing.getSiteId(), existing.getId(), replacement.getId());
                                    return existing.getId() < replacement.getId() ? existing : replacement;
                                }
                        ));
            }, asyncServiceExecutor);
            
            // 6. 等待网站信息查询完成
            Map<Integer, Website> websiteMap = websiteMapFuture.join();
            
            // 7. 并行构建结果（使用已有的检查记录缓存）
            List<CompletableFuture<WebSiteChannelCheckDetailVO>> voFutures = pagedChannels.stream()
                    .map(channel -> CompletableFuture.supplyAsync(() -> {
                        return buildChannelCheckDetailVOOptimized(channel, query, websiteMap, allCheckRecordsMap);
                    }, asyncServiceExecutor))
                    .collect(Collectors.toList());
            
            List<WebSiteChannelCheckDetailVO> result = voFutures.stream()
                    .map(CompletableFuture::join)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            
            long endTime = System.currentTimeMillis();
            log.debug("带isUpdate过滤条件的分页查询完成，耗时{}ms，过滤后总数：{}，返回{}条记录", 
                    endTime - startTime, filteredTotal, result.size());
            
            return new PageResult<>(result, filteredTotal, query.getLimit(), query.getPage());
            
        } catch (Exception e) {
            log.error("带isUpdate过滤条件的分页查询失败", e);
            // 降级到原有方法
            return pageChannelCheckDetailFallback(query);
        }
    }
    
    /**
     * 构建栏目查询条件
     */
    private LambdaQueryWrapper<Channel> buildChannelQueryWrapper(WebSiteChannelCheckQuery query) {
        LambdaQueryWrapper<Channel> wrapper = new LambdaQueryWrapper<Channel>()
                .eq(Channel::getEnable, 1) // 只查询启用的栏目
                .eq(Channel::getProcessType, 0); // 只查询processType为0的栏目
        
        // 根据栏目名称模糊查询
        if (StrUtil.isNotBlank(query.getChannelName())) {
            wrapper.like(Channel::getName, query.getChannelName());
        }
        
        // 根据站点ID列表过滤
        if (CollUtil.isNotEmpty(query.getWebsiteIds())) {
            wrapper.in(Channel::getSiteId, query.getWebsiteIds());
        }
        
        return wrapper;
    }
    
    /**
     * 批量查询检查记录
     */
    private Map<Long, List<WebSiteChannelCheck>> batchQueryCheckRecords(List<Long> columnIds, WebSiteChannelCheckQuery query) {
        if (CollUtil.isEmpty(columnIds)) {
            return Collections.emptyMap();
        }
        
        // 计算时间范围
        LocalDate endDate = LocalDate.now().minusDays(1);
        LocalDate startDate = endDate.minusDays(29);
        
        // 应用查询时间条件
        if (query.getBeginTime() != null) {
            LocalDate queryStartDate = query.getBeginTime().toInstant()
                    .atZone(ZoneId.systemDefault()).toLocalDate();
            if (queryStartDate.isAfter(startDate)) {
                startDate = queryStartDate;
            }
        }
        if (query.getEndTime() != null) {
            LocalDate queryEndDate = query.getEndTime().toInstant()
                    .atZone(ZoneId.systemDefault()).toLocalDate();
            if (queryEndDate.isBefore(endDate)) {
                endDate = queryEndDate;
            }
        }
        
        // 使用优化的批量查询
        List<WebSiteChannelCheck> allRecords = webSiteChannelCheckDao.selectByColumnIdsAndDateRange(
                columnIds, startDate, endDate);
        
        // 按栏目ID分组
        return allRecords.stream().collect(Collectors.groupingBy(WebSiteChannelCheck::getColumnId));
    }
    
    /**
     * 优化的判断栏目是否包含在结果中
     */
    private boolean shouldIncludeChannelOptimized(Channel channel, WebSiteChannelCheckQuery query, 
                                                  Map<Long, List<WebSiteChannelCheck>> checkRecordsMap) {
        // 根据isUpdate过滤
        if (query.getIsUpdate() != null) {
            List<WebSiteChannelCheck> checkRecords = checkRecordsMap.get(channel.getId().longValue());
            
            if (CollUtil.isEmpty(checkRecords)) {
                return query.getIsUpdate() == 0; // 没有记录视为未更新
            }
            
            // 找到最新一天的记录，根据最新一天的状态进行过滤
            WebSiteChannelCheck latestRecord = checkRecords.stream()
                    .filter(r -> r.getCheckTime() != null)
                    .max(Comparator.comparing(WebSiteChannelCheck::getCheckTime))
                    .orElse(null);
            
            if (latestRecord == null) {
                return query.getIsUpdate() == 0; // 没有有效记录视为未更新
            }
            
            boolean latestDayUpdate = (latestRecord.getIsUpdate() == 1);
            if (query.getIsUpdate() == 1 && !latestDayUpdate) {
                return false;
            }
            if (query.getIsUpdate() == 0 && latestDayUpdate) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 优化的构建栏目检查详情VO
     */
    private WebSiteChannelCheckDetailVO buildChannelCheckDetailVOOptimized(
            Channel channel, WebSiteChannelCheckQuery query, 
            Map<Integer, Website> websiteMap,
            Map<Long, List<WebSiteChannelCheck>> checkRecordsMap) {
        try {
            WebSiteChannelCheckDetailVO vo = new WebSiteChannelCheckDetailVO();
            // 设置栏目基本信息
            vo.setColumnId(channel.getId().longValue());
            vo.setColumnName(channel.getName());
            vo.setColumnUrl(channel.getUrl());
            vo.setChannelTypeId(channel.getChannelTypeId());
            // 设置栏目类型
            ChannelType channelType = channelTypeService.getById(channel.getChannelTypeId());
            if (channelType != null) {
                vo.setUpdatePeriod(channelType.getUpdatePeriod());
                vo.setChannelTypeName(channelType.getName());
            }
            // 从缓存中获取网站信息
            Website website = websiteMap.get(channel.getSiteId());
            if (website != null) {
                vo.setWebsiteId(website.getId());
                vo.setWebsiteName(website.getWebName());
                vo.setWebsiteUrl(website.getWebUrl());
            }
            // 从缓存中获取检查记录
            List<WebSiteChannelCheck> checkRecords = checkRecordsMap.get(channel.getId().longValue());
            if (checkRecords == null) {
                checkRecords = Collections.emptyList();
            }
            // 构建详细信息
            buildDetailInfo(vo, checkRecords);
            
            return vo;
            
        } catch (Exception e) {
            log.error("优化构建栏目{}检查详情失败", channel.getId(), e);
            return null;
        }
    }
    
    /**
     * 降级方法：使用原有逻辑
     */
    private PageResult<WebSiteChannelCheckDetailVO> pageChannelCheckDetailFallback(WebSiteChannelCheckQuery query) {
        log.warn("使用降级方法查询栏目检查结果");
        
        // 查询启用的栏目
        List<Channel> channels = getChannelsByQuery(query);
        if (CollUtil.isEmpty(channels)) {
            return new PageResult<>(Collections.emptyList(), 0L, query.getLimit(), query.getPage());
        }
        
        // 构建详细信息
        List<WebSiteChannelCheckDetailVO> result = new ArrayList<>();
        for (Channel channel : channels) {
            if (shouldIncludeChannel(channel, query)) {
                WebSiteChannelCheckDetailVO vo = buildChannelCheckDetailVO(channel, query);
                if (vo != null) {
                    result.add(vo);
                }
            }
        }
        
        // 手动分页
        int total = result.size();
        int fromIndex = (query.getPage() - 1) * query.getLimit();
        int toIndex = Math.min(fromIndex + query.getLimit(), total);
        
        List<WebSiteChannelCheckDetailVO> pagedResult = total > 0 && fromIndex < total ?
                result.subList(fromIndex, toIndex) : Collections.emptyList();
        
        return new PageResult<>(pagedResult, (long) total, query.getLimit(), query.getPage());
    }
    
    /**
     * 优化的降级方法：保持数据查询优化但使用同步处理
     */
    private PageResult<WebSiteChannelCheckDetailVO> pageChannelCheckDetailFallbackOptimized(WebSiteChannelCheckQuery query) {
        log.warn("使用优化的降级方法查询栏目检查结果");
        
        try {
            // 1. 使用原有的channelService.page方法（保持与website表的联查逻辑）
            PageResult<Channel> channelPage = channelService.page(query);
            List<Channel> channels = channelPage.getList();
            long total = channelPage.getTotal();
            
            if (total == 0 || CollUtil.isEmpty(channels)) {
                return new PageResult<>(Collections.emptyList(), total, query.getLimit(), query.getPage());
            }
            
            // 2. 批量查询关联数据
            Set<Integer> siteIds = channels.stream()
                    .map(Channel::getSiteId)
                    .collect(Collectors.toSet());
            List<Long> columnIds = channels.stream()
                    .map(channel -> channel.getId().longValue())
                    .collect(Collectors.toList());
            
            Map<Integer, Website> websiteMap = websiteService.listBySiteIds(siteIds).stream()
                    .collect(Collectors.toMap(
                            Website::getSiteId, 
                            Function.identity(),
                            (existing, replacement) -> existing.getId() < replacement.getId() ? existing : replacement
                    ));
            
            Map<Long, List<WebSiteChannelCheck>> checkRecordsMap = batchQueryCheckRecords(columnIds, query);
            
            // 3. 同步构建结果
            List<WebSiteChannelCheckDetailVO> result = channels.stream()
                    .map(channel -> buildChannelCheckDetailVOOptimized(channel, query, websiteMap, checkRecordsMap))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            
            return new PageResult<>(result, total, query.getLimit(), query.getPage());
            
        } catch (Exception e) {
            log.error("优化的降级方法也失败，使用最基础的方法", e);
            return pageChannelCheckDetailFallback(query);
        }
    }
    
    @Override
    public List<WebSiteChannelStatisticsVO> getChannelCheckStatistics(WebSiteChannelCheckQuery query) {
        try {
            long startTime = System.currentTimeMillis();
            log.info("开始查询栏目检查统计结果");
            // 1. 查询符合条件的栏目，与/page-detail保持一致
            List<Channel> channels = getChannelsByQuery(query);
            if (CollUtil.isEmpty(channels)) {
                return Collections.emptyList();
            }
            // 2. 按站点分组
            Map<Integer, List<Channel>> channelsByWebsite = channels.stream()
                    .collect(Collectors.groupingBy(Channel::getSiteId));
            // 3. 并行查询站点信息和检查记录
            Set<Integer> siteIds = channelsByWebsite.keySet();
            List<Long> allColumnIds = channels.stream().map(channel -> channel.getId().longValue()).collect(Collectors.toList());
            CompletableFuture<Map<Integer, Website>> websiteMapFuture = CompletableFuture.supplyAsync(() -> {
                //查询Website的siteId，处理重复siteId的情况
                return websiteService.listBySiteIds(siteIds).stream()
                        .collect(Collectors.toMap(
                                Website::getSiteId, 
                                Function.identity(),
                                (existing, replacement) -> {
                                    // 如果存在重复的siteId，优先保留ID较小的记录（通常是更早创建的）
                                    log.warn("发现重复的siteId: {}，保留ID较小的记录。existing: {}, replacement: {}", 
                                            existing.getSiteId(), existing.getId(), replacement.getId());
                                    return existing.getId() < replacement.getId() ? existing : replacement;
                                }
                        ));
            }, asyncServiceExecutor);
            CompletableFuture<Map<Long, List<WebSiteChannelCheck>>> checkRecordsMapFuture = CompletableFuture.supplyAsync(() -> {
                return batchQueryCheckRecords(allColumnIds, query);
            }, asyncServiceExecutor);
            // 4. 等待关联数据查询完成
            Map<Integer, Website> websiteMap = websiteMapFuture.join();
            Map<Long, List<WebSiteChannelCheck>> checkRecordsMap = checkRecordsMapFuture.join();
            
            // 5. 按站点统计
            List<WebSiteChannelStatisticsVO> result = new ArrayList<>();
            
            for (Map.Entry<Integer, List<Channel>> entry : channelsByWebsite.entrySet()) {
                Integer siteId = entry.getKey();
                List<Channel> siteChannels = entry.getValue();
                Website website = websiteMap.get(siteId);
                if (website == null) {
                    log.warn("站点ID: {} 在Website表中不存在，跳过该站点统计", siteId);
                    continue;
                }
                // 构建站点统计VO
                WebSiteChannelStatisticsVO vo = buildWebsiteStatisticsVO(website, siteChannels, checkRecordsMap, query);
                if (vo != null) {
                    result.add(vo);
                }
            }
            
            long endTime = System.currentTimeMillis();
            log.info("查询栏目检查统计结果完成，共统计{}个站点，耗时{}ms", result.size(), endTime - startTime);
            
            return result;
            
        } catch (Exception e) {
            log.error("查询栏目检查统计结果失败", e);
            return Collections.emptyList();
        }
    }
    
    /**
     * 构建站点统计VO
     */
    private WebSiteChannelStatisticsVO buildWebsiteStatisticsVO(
            Website website, 
            List<Channel> siteChannels, 
            Map<Long, List<WebSiteChannelCheck>> checkRecordsMap,
            WebSiteChannelCheckQuery query) {
        try {
            WebSiteChannelStatisticsVO vo = new WebSiteChannelStatisticsVO();
            
            // 设置站点基本信息
            vo.setWebsiteId(Long.valueOf(website.getSiteId()));
            vo.setWebsiteName(website.getWebName());
            vo.setWebsiteUrl(website.getWebUrl());
            
            // 统计所有栏目的检查记录
            List<WebSiteChannelCheck> allSiteCheckRecords = new ArrayList<>();
            int totalColumns = 0;
            int updatedColumns = 0;
            int totalUpdateCount = 0;
            Date latestUpdateTime = null;
            Date latestCheckTime = null;
            
            // 遍历该站点下的所有栏目
            for (Channel channel : siteChannels) {
                List<WebSiteChannelCheck> channelRecords = checkRecordsMap.get(channel.getId().longValue());
                if (CollUtil.isNotEmpty(channelRecords)) {
                    allSiteCheckRecords.addAll(channelRecords);
                    
                    // 统计该栏目是否有更新（基于最新一天的状态）
                    WebSiteChannelCheck latestChannelRecord = channelRecords.stream()
                            .filter(r -> r.getCheckTime() != null)
                            .max(Comparator.comparing(WebSiteChannelCheck::getCheckTime))
                            .orElse(null);
                    
                    if (latestChannelRecord != null && latestChannelRecord.getIsUpdate() == 1) {
                        updatedColumns++;
                    }
                    
                    // 统计更新数量（取最新一天的数据）
                    WebSiteChannelCheck latestRecord = channelRecords.stream()
                            .filter(r -> r.getCheckTime() != null)
                            .max(Comparator.comparing(WebSiteChannelCheck::getCheckTime))
                            .orElse(null);
                    
                    if (latestRecord != null) {
                        totalUpdateCount += latestRecord.getUpdateCount();
                        
                        if (latestRecord.getLastUpdateTime() != null && 
                            (latestUpdateTime == null || latestRecord.getLastUpdateTime().after(latestUpdateTime))) {
                            latestUpdateTime = latestRecord.getLastUpdateTime();
                        }
                        
                        if (latestCheckTime == null || latestRecord.getCheckTime().after(latestCheckTime)) {
                            latestCheckTime = latestRecord.getCheckTime();
                        }
                    }
                }
                totalColumns++;
            }
            
            // 设置统计信息
            vo.setTotalColumns(totalColumns);
            vo.setUpdatedColumns(updatedColumns);
            vo.setNotUpdatedColumns(totalColumns - updatedColumns);
            
            // 构建检查天数信息（按日期聚合）
            buildStatisticsDetailInfoForStatisticsVO(vo, allSiteCheckRecords);
            
            return vo;
            
        } catch (Exception e) {
            log.error("构建站点{}统计VO失败", website.getSiteId(), e);
            return null;
        }
    }
    
    /**
     * 构建统计详细信息专用于 WebSiteChannelStatisticsVO
     */
    private void buildStatisticsDetailInfoForStatisticsVO(WebSiteChannelStatisticsVO vo, List<WebSiteChannelCheck> allRecords) {
        Map<LocalDate, List<WebSiteChannelCheck>> recordsByDate = allRecords.stream()
                .filter(r -> r.getCheckTime() != null)
                .collect(Collectors.groupingBy(record -> 
                    record.getCheckTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate()
                ));

        List<String> checkNotUpdateDays = new ArrayList<>();
        List<String> checkUpdateDays = new ArrayList<>();
        
        // 按日期排序（从最新到最旧）
        List<LocalDate> sortedDates = recordsByDate.keySet().stream()
                .sorted(Comparator.reverseOrder())
                .collect(Collectors.toList());
        
        for (LocalDate date : sortedDates) {
            List<WebSiteChannelCheck> dayRecords = recordsByDate.get(date);
            
            // 统计当天的更新情况
            int dayUpdateCount = dayRecords.stream().mapToInt(WebSiteChannelCheck::getUpdateCount).sum();
            boolean dayHasUpdate = dayRecords.stream().anyMatch(record -> record.getIsUpdate() == 1);
            
            String dateStr = date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            if (dayHasUpdate) {
                checkUpdateDays.add(dateStr);
            } else {
                checkNotUpdateDays.add(dateStr);
            }
        }
    }

    
    /**
     * 构建统计详细信息
     */
    private void buildStatisticsDetailInfo(WebSiteChannelCheckDetailVO vo, List<WebSiteChannelCheck> allRecords) {
        if (CollUtil.isEmpty(allRecords)) {
            vo.setCheckDays(Collections.emptyList());
            vo.setCheckNotUpdateDays(Collections.emptyList());
            vo.setCheckUpdateDays(Collections.emptyList());
            vo.setContinuousUpdateCount(0);
            vo.setContinuousNotUpdateCount(0);
            vo.setContinuousNotUpdateDays(0);
            return;
        }
        
        // 按日期聚合数据
        Map<LocalDate, List<WebSiteChannelCheck>> recordsByDate = allRecords.stream()
                .filter(r -> r.getCheckTime() != null)
                .collect(Collectors.groupingBy(record -> 
                    record.getCheckTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate()
                ));
        
        // 构建检查天数列表
        List<WebSiteChannelCheckDetailVO.UpdateDayInfo> checkDays = new ArrayList<>();
        List<String> checkNotUpdateDays = new ArrayList<>();
        List<String> checkUpdateDays = new ArrayList<>();
        
        // 按日期排序（从最新到最旧）
        List<LocalDate> sortedDates = recordsByDate.keySet().stream()
                .sorted(Comparator.reverseOrder())
                .collect(Collectors.toList());
        
        for (LocalDate date : sortedDates) {
            List<WebSiteChannelCheck> dayRecords = recordsByDate.get(date);
            
            // 统计当天的更新情况
            int dayUpdateCount = dayRecords.stream().mapToInt(WebSiteChannelCheck::getUpdateCount).sum();
            boolean dayHasUpdate = dayRecords.stream().anyMatch(record -> record.getIsUpdate() == 1);
            
            WebSiteChannelCheckDetailVO.UpdateDayInfo dayInfo = new WebSiteChannelCheckDetailVO.UpdateDayInfo();
            dayInfo.setUpdateDate(date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            dayInfo.setIsUpdate(dayHasUpdate ? 1 : 0);
            dayInfo.setUpdateCount(dayUpdateCount);
            checkDays.add(dayInfo);
            
            String dateStr = date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            if (dayHasUpdate) {
                checkUpdateDays.add(dateStr);
            } else {
                checkNotUpdateDays.add(dateStr);
            }
        }
        
        vo.setCheckDays(checkDays);
        vo.setCheckNotUpdateDays(checkNotUpdateDays);
        vo.setCheckUpdateDays(checkUpdateDays);
        
        // 计算连续天数（简化版，按整体站点统计）
        calculateStatisticsContinuousDays(vo, checkDays);
    }
    
    /**
     * 计算统计连续天数
     */
    private void calculateStatisticsContinuousDays(WebSiteChannelCheckDetailVO vo, List<WebSiteChannelCheckDetailVO.UpdateDayInfo> checkDays) {
        if (CollUtil.isEmpty(checkDays)) {
            vo.setContinuousUpdateCount(0);
            vo.setContinuousNotUpdateCount(0);
            vo.setContinuousNotUpdateDays(0);
            return;
        }
        
        int continuousUpdateCount = 0;
        int continuousNotUpdateCount = 0;
        
        // 从最新日期开始计算连续天数
        for (WebSiteChannelCheckDetailVO.UpdateDayInfo dayInfo : checkDays) {
            if (dayInfo.getIsUpdate() == 1) {
                if (continuousNotUpdateCount == 0) {
                    continuousUpdateCount++;
                } else {
                    break;
                }
            } else {
                if (continuousUpdateCount == 0) {
                    continuousNotUpdateCount++;
                } else {
                    break;
                }
            }
        }
        
        vo.setContinuousUpdateCount(continuousUpdateCount);
        vo.setContinuousNotUpdateCount(continuousNotUpdateCount);
        vo.setContinuousNotUpdateDays(continuousNotUpdateCount);
    }
    
    @Override
    public List<WebSiteChannelCheckExportVO> exportChannelCheckData(WebSiteChannelCheckQuery query) {
        try {
            log.info("开始导出栏目检查结果数据");
            long startTime = System.currentTimeMillis();
            
            // 1. 查询所有符合条件的栏目和检查记录（不分页）
            List<Channel> channels = getChannelsByQuery(query);
            if (CollUtil.isEmpty(channels)) {
                log.warn("没有找到符合条件的栏目数据");
                return Collections.emptyList();
            }
            
            // 2. 获取检查记录数据
            List<Long> columnIds = channels.stream().map(channel -> channel.getId().longValue()).collect(Collectors.toList());
            Map<Long, List<WebSiteChannelCheck>> checkRecordsMap = batchQueryCheckRecords(columnIds, query);
            
            // 3. 获取站点信息
            Set<Integer> siteIds = channels.stream().map(Channel::getSiteId).collect(Collectors.toSet());
            Map<Integer, Website> websiteMap = websiteService.listBySiteIds(siteIds).stream()
                    .collect(Collectors.toMap(
                            Website::getSiteId, 
                            Function.identity(),
                            (existing, replacement) -> {
                                log.warn("发现重复的siteId: {}，保留ID较小的记录。existing: {}, replacement: {}", 
                                        existing.getSiteId(), existing.getId(), replacement.getId());
                                return existing.getId() < replacement.getId() ? existing : replacement;
                            }
                    ));
            
            // 4. 过滤符合isUpdate条件的栏目（如果有的话）
            List<Channel> filteredChannels = channels;
            if (query.getIsUpdate() != null) {
                filteredChannels = channels.stream()
                        .filter(channel -> shouldIncludeChannelOptimized(channel, query, checkRecordsMap))
                        .collect(Collectors.toList());
            }
            
            // 5. 构建导出数据
            List<WebSiteChannelCheckExportVO> exportList = new ArrayList<>();
            for (Channel channel : filteredChannels) {
                try {
                    WebSiteChannelCheckExportVO exportVO = buildExportVO(channel, websiteMap, checkRecordsMap);
                    if (exportVO != null) {
                        exportList.add(exportVO);
                    }
                } catch (Exception e) {
                    log.error("构建栏目{}导出数据失败", channel.getId(), e);
                }
            }
            
            long endTime = System.currentTimeMillis();
            log.info("导出栏目检查结果数据完成，共导出{}条记录，耗时{}ms", exportList.size(), endTime - startTime);
            return exportList;
            
        } catch (Exception e) {
            log.error("导出栏目检查结果数据失败", e);
            return Collections.emptyList();
        }
    }
    
    /**
     * 构建导出数据VO
     */
    private WebSiteChannelCheckExportVO buildExportVO(
            Channel channel, 
            Map<Integer, Website> websiteMap,
            Map<Long, List<WebSiteChannelCheck>> checkRecordsMap) {
        try {
            WebSiteChannelCheckExportVO exportVO = new WebSiteChannelCheckExportVO();
            
            // 设置栏目信息
            exportVO.setColumnName(channel.getName());
            exportVO.setColumnUrl(channel.getUrl());
            
            // 设置站点信息
            Website website = websiteMap.get(channel.getSiteId());
            if (website != null) {
                exportVO.setWebsiteName(website.getWebName());
                exportVO.setWebsiteUrl(website.getWebUrl());
            } else {
                exportVO.setWebsiteName("未知站点");
                exportVO.setWebsiteUrl("");
            }
            
            // 获取检查记录并计算状态
            List<WebSiteChannelCheck> checkRecords = checkRecordsMap.get(channel.getId().longValue());
            if (CollUtil.isNotEmpty(checkRecords)) {
                // 按时间排序，获取最新记录
                List<WebSiteChannelCheck> sortedRecords = checkRecords.stream()
                        .filter(r -> r.getCheckTime() != null)
                        .sorted(Comparator.comparing(WebSiteChannelCheck::getCheckTime).reversed())
                        .collect(Collectors.toList());
                
                if (CollUtil.isNotEmpty(sortedRecords)) {
                    // 获取最新一天的状态
                    WebSiteChannelCheck latestRecord = sortedRecords.get(0);
                    boolean isUpdate = (latestRecord.getIsUpdate() == 1);
                    exportVO.setUpdateStatus(isUpdate ? "已更新" : "未更新");
                    
                    // 计算连续未更新天数
                    int continuousNotUpdateDays = calculateContinuousNotUpdateDays(sortedRecords);
                    exportVO.setContinuousNotUpdateDays(continuousNotUpdateDays);
                } else {
                    exportVO.setUpdateStatus("未更新");
                    exportVO.setContinuousNotUpdateDays(30); // 默认倰30天都未更新
                }
            } else {
                exportVO.setUpdateStatus("未更新");
                exportVO.setContinuousNotUpdateDays(30); // 默认倰30天都未更新
            }
            
            return exportVO;
            
        } catch (Exception e) {
            log.error("构建栏目{}导出数据失败", channel.getId(), e);
            return null;
        }
    }
    
    /**
     * 计算连续未更新天数
     */
    private int calculateContinuousNotUpdateDays(List<WebSiteChannelCheck> sortedRecords) {
        int continuousNotUpdateDays = 0;
        
        // 从最新记录开始计算连续未更新天数
        for (WebSiteChannelCheck record : sortedRecords) {
            if (record.getIsUpdate() == 0) {
                continuousNotUpdateDays++;
            } else {
                break; // 遇到更新记录，停止计算
            }
        }
        
        return continuousNotUpdateDays;
    }
}