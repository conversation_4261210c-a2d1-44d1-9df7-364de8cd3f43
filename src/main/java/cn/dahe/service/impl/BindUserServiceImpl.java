package cn.dahe.service.impl;

import cn.dahe.dao.WarnUserDao;
import cn.dahe.entity.WarnUser;
import cn.dahe.model.dto.Result;
import cn.dahe.service.BindUserService;
import cn.dahe.utils.AESUtils;
import cn.dahe.utils.DateUtil;
import cn.dahe.utils.SmsUtil;

/**
 * 绑定预警人员服务实现类
 * <AUTHOR>
 */
import cn.dahe.utils.StringUtils;
import com.alibaba.druid.sql.visitor.functions.Bin;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringEscapeUtils;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 绑定预警人员服务实现类
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class BindUserServiceImpl implements BindUserService {
    private final WarnUserDao warnUserDao;
@Override
public Result<String> bind(String token,String userName, String phone, String code) {
    //xss过滤 token、userName、
    // 使用注解自动过滤，无需手动调用
    // token = StringEscapeUtils.escapeHtml4(token);
    // userName = StringEscapeUtils.escapeHtml4(userName);
    
    // 业务逻辑保持不变
    //校验code
    if (StringUtils.isBlank(code)) {
        return Result.error("验证码不能为空");
    }
    //校验code为6位数字
    if (!code.matches("\\d{6}")) {
        return Result.error("验证码格式错误");
    }
    //校验手机号
    if (!StringUtils.isMobileNum(phone)) {
        return Result.error("手机号格式错误");
    }
    //校验用户名
    if (StringUtils.isBlank(userName)) {
        return Result.error("用户名不能为空");
    }
    //校验用户名长度
    if (userName.length() > 10) {
        return Result.error("用户名长度不能超过10个字符");
    }
    // 校验验证码
    Result<String> result = SmsUtil.checkSmsCode(phone, code);
    if (!result.isSuccess()) {
        return result;
    }
    // openId解密
    String tokenDecrypt = AESUtils.decryptFromAuthCenter(token);
    // 校验手机号是否存在账户
    WarnUser warnUser = warnUserDao.getByUserPhoneWarnUser(phone);
//    if (warnUser != null) {
//        return Result.error("已存在该手机号");
//    }
    // 检查openId是否已经绑定用户
    WarnUser existingUser = warnUserDao.getByOpenId(tokenDecrypt);
    // 创建新的预警用户对象
    warnUser = new WarnUser();
    warnUser.setUserPhone(phone);
    warnUser.setUserName(userName);
    warnUser.setCreateTime(new Date());
    warnUser.setIsSubscribe(1);
    if (existingUser != null) {
        // 如果openId已经与用户绑定 参考蜜度的逻辑 只新增短信接收人员信息
        warnUser.setUserOpenId("");
    } else {
        // 新用户绑定
        warnUser.setUserOpenId(tokenDecrypt);
    }
    int insert = warnUserDao.insert(warnUser);
    if (insert <= 0) {
        return Result.error("绑定失败");
    }

    // 绑定成功 移除验证码
    SmsUtil.removeSmsCode(phone);
    return Result.ok("绑定成功");
}


    @Override
    public Result<String> sendSmsCode(String phone) {
        // 发送短信验证码
        return SmsUtil.sendSmsAccount(phone);
    }
}
