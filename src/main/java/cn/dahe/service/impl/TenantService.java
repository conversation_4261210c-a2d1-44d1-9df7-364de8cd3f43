package cn.dahe.service.impl;

import cn.dahe.dao.TeamDao;
import cn.dahe.dao.TenantDao;
import cn.dahe.entity.Team;
import cn.dahe.entity.Tenant;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.Result;
import cn.dahe.model.dto.TeamDto;
import cn.dahe.model.dto.TenantDto;
import cn.dahe.model.vo.LoginUserVO;
import cn.dahe.utils.StringUtils;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.var;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
public class TenantService {
    final TenantDao tenantDao;
    final TeamDao teamDao;
    @Value("${spring.profiles.active}")
    String profilesActive;

    @XxlJob("expireTenant")
    void expireTenant() {
        tenantDao.expire();
    }

    public void add(Integer userCount, Integer siteCount, Integer newMediaCount, Integer groupCount, Integer teamCount, Integer warnPlanCount,
                    String name, String remark,
                    LocalDate expireDate,
                    LoginUserVO loginUser) {
        if (tenantDao.selectOneByName(name = name.trim()) != null) throw new IllegalArgumentException("单位名称已存在");
        tenantDao.insert(new Tenant().setName(name)
                                     .setRemark(remark)
                                     .setExpireDate(expireDate)
                                     .setUserCount(userCount)
                                     .setSiteCount(siteCount)
                                     .setNewMediaCount(newMediaCount)
                                     .setGroupCount(groupCount)
                                     .setTeamCount(teamCount)
                                     .setWarnPlanCount(warnPlanCount)
                                     .setCreateUserId(loginUser.getUserId()));
    }

    public void delete(int id, LoginUserVO loginUser) {
        tenantDao.delete(id, loginUser.getUserId());
    }

    public void update(int id, Integer status,
                       Integer userCount, Integer siteCount, Integer newMediaCount, Integer groupCount, Integer teamCount, Integer warnPlanCount,
                       String name, String remark,
                       LocalDate expireDate,
                       LoginUserVO loginUser) {
        if (StringUtils.hasText(name) && tenantDao.selectOneByIdNotAndName(id, name = name.trim()) != null)
            throw new IllegalArgumentException("单位名称已存在");
        tenantDao.update(new Tenant().setId(id)
                                     .setStatus(status)
                                     .setName(StringUtils.hasText(name) ? name.trim() : name)
                                     .setRemark(remark)
                                     .setExpireDate(expireDate)
                                     .setUserCount(userCount)
                                     .setSiteCount(siteCount)
                                     .setNewMediaCount(newMediaCount)
                                     .setGroupCount(groupCount)
                                     .setTeamCount(teamCount)
                                     .setWarnPlanCount(warnPlanCount)
                                     .setLastUpdateUserId(loginUser.getUserId()));
    }

    public Result<PageResult<TenantDto>> page(String name, Integer status, int page, int limit) {
        try (Page<Tenant> tenantPage = PageHelper.startPage(page, limit == 0 ? Integer.MAX_VALUE : limit)) {
            tenantDao.selectNotDeletePage(status, name);
            var result = Result.ok(new PageResult<>(tenantPage.stream().map(this::toDto).collect(Collectors.toList()), tenantPage.toPageInfo()));
            return "dev".equals(profilesActive) ? result.setExplain(explain(Tenant.class)) : result;
        }
    }

    public void teamAdd(int tenantId, String name, String remark, LoginUserVO loginUser) {
        if (teamDao.selectOneByNameAndTenantId(name = name.trim(), tenantId) != null) throw new IllegalArgumentException("子单位名称已存在");
        teamDao.insert(new Team().setName(name).setTenantId(tenantId).setRemark(remark).setCreateUserId(loginUser.getUserId()));
    }

    public void teamDelete(int id, LoginUserVO loginUser) {
        teamDao.delete(id, loginUser.getUserId());
    }

    public void teamUpdate(int id, int tenantId, String name, String remark, LoginUserVO loginUser) {
        if (StringUtils.hasText(name) && teamDao.selectOneByIdNotAndNameAndTenantId(id, name = name.trim(), tenantId) != null)
            throw new IllegalArgumentException("子单位名称已存在");
        teamDao.update(new Team().setId(id).setName(name).setRemark(remark).setLastUpdateUserId(loginUser.getUserId()));
    }

    public Result<PageResult<TeamDto>> teamPage(String name, int tenantId, int page, int limit) {
        try (Page<Team> teamPage = PageHelper.startPage(page, limit == 0 ? Integer.MAX_VALUE : limit)) {
            teamDao.selectPage(name, tenantId);
            var result = Result.ok(new PageResult<>(teamPage.stream().map(this::toDto).collect(Collectors.toList()), teamPage.toPageInfo()));
            return "dev".equals(profilesActive) ? result.setExplain(explain(Team.class)) : result;
        }
    }

    TenantDto toDto(Tenant tenant) {
        if (tenant == null) return null;
        TenantDto dto = new TenantDto();
        BeanUtils.copyProperties(tenant, dto);
        return dto;
    }

    TeamDto toDto(Team team) {
        if (team == null) return null;
        TeamDto dto = new TeamDto();
        BeanUtils.copyProperties(team, dto);
        return dto;
    }

    Map<String, String> explain(Class<?> c) {
        Map<String, String> map = new HashMap<>();
        for (Field field : c.getDeclaredFields()) {
            Column col = field.getAnnotation(Column.class);
            if (col != null) {
                String comment = col.comment();
                if (StringUtils.hasText(comment)) map.put(field.getName(), comment);
            }
        }
        return map;
    }
}
