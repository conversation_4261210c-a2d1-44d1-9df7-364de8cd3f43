package cn.dahe.service.impl;

import cn.dahe.common.auth.SecurityUtils;
import cn.dahe.dao.ArticleDao;
import cn.dahe.entity.Article;
import cn.dahe.entity.Channel;
import cn.dahe.entity.Website;
import cn.dahe.enums.ArticleDisposalStatusEnum;
import cn.dahe.enums.AuditStatusEnum;
import cn.dahe.enums.ProcessTypeEnum;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.Result;
import cn.dahe.model.dto.WarnCheckDetailDto;
import cn.dahe.model.query.AllSiteSearchQuery;
import cn.dahe.model.query.ArticleCheckQuery;
import cn.dahe.model.query.ChannelArticleQuery;
import cn.dahe.model.query.IndexArticleQuery;
import cn.dahe.model.request.LingcaiPushDataDto;
import cn.dahe.model.vo.ArticleAllSiteSearchVO;
import cn.dahe.model.vo.ArticleCheckResultVO;
import cn.dahe.model.vo.ArticleCheckVO;
import cn.dahe.model.vo.ArticleVO;
import cn.dahe.model.vo.CheckResultVO;
import cn.dahe.model.vo.CheckSnapshotVO;
import cn.dahe.model.vo.IndexArticleExportVO;
import cn.dahe.model.vo.LoginUserVO;
import cn.dahe.service.ArticleContentService;
import cn.dahe.service.ArticleService;
import cn.dahe.service.ChannelService;
import cn.dahe.service.CheckResultService;
import cn.dahe.service.CheckTaskService;
import cn.dahe.service.WebsiteService;
import cn.dahe.utils.ListUtils;
import cn.dahe.utils.StringUtils;
import cn.dahe.utils.caiji.WebSiteGetHtmlUtils;
import cn.dahe.utils.excel.ExcelExportUtil;
import cn.dahe.utils.excel.FieldMapping;
import cn.dahe.utils.excel.SheetConfig;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 采集文章Service实现类
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Slf4j
@Service
public class ArticleServiceImpl extends BaseServiceImpl<ArticleDao, Article> implements ArticleService {

    @Resource
    private CheckResultService checkResultService;

    @Resource
    private ChannelService channelService;
    @Resource
    private CheckTaskService checkTaskService;
    @Resource
    private NewMediaService newMediaService;

    @Override
    public IPage<ArticleCheckVO> pageArticleAndCheckResults(ArticleCheckQuery query) {
        //  TODO 限制分页查询的单页最大长度
        IPage<ArticleCheckVO> result = this.getBaseMapper().listArticleInfo(Page.of(query.getPage(), query.getLimit()), query);
        result.getRecords().forEach(vo -> checkResultService.markCheckContentWithHtml(vo, query));
        return result;
    }

    @Value("${anxun.base-url}")
    private String baseUrl;

    public List<ArticleCheckResultVO> listCheckResultsForExport(ArticleCheckQuery query) {
        List<ArticleCheckVO> records = this.getBaseMapper().listArticleInfo(query);
        List<ArticleCheckResultVO> result = new ArrayList<>();
        if (CollUtil.isNotEmpty(records)) {
            for (ArticleCheckVO record : records) {
                List<CheckResultVO> checkResults = checkResultService.listCheckResultByTaskAndCheckQuery(record.getTaskId(), query);
                for (CheckResultVO checkResult : checkResults) {
                    ArticleCheckResultVO vo = new ArticleCheckResultVO();
                    vo.setGroupName(record.getGroupName());
                    vo.setWebsiteName(record.getWebsiteName());
                    vo.setProcessTypeName(record.getProcessTypeName());
                    vo.setArticleTitle(record.getCleanedTitle());
                    vo.setArticleUrl(record.getUrl());
                    AuditStatusEnum auditStatusEnum = AuditStatusEnum.getByValue(checkResult.getAuditStatus());
                    vo.setAuditStatusName(auditStatusEnum == null ? StrUtil.EMPTY : auditStatusEnum.getDesc());
                    vo.setSnapshotUrl(StrUtil.format("{}/#/snapshoot/{}/0", baseUrl, record.getTaskId()));
                    vo.setErrorWord(checkResult.getErrorWord());
                    vo.setSuggestWord(checkResult.getSuggestWord());
                    vo.setContext(checkResult.getContext());
                    vo.setErrorLevelName(checkResult.getErrorLevelName());
                    vo.setErrorTypeName(checkResult.getErrorTypeName());
                    vo.setPubTime(record.getPubTime());
                    result.add(vo);
                }
            }
        }
        return result;
    }

    /**
     * 获取预警信息详情，暂时不需要判空
     */
    @Override
    public WarnCheckDetailDto getWarnArticleInfo(Long articleId) {
        return this.getBaseMapper().getWarnArticleInfo(articleId);
    }

    @Override
    public ArticleCheckVO getArticleAndCheckResults(Long articleId, ArticleCheckQuery query) {
        ArticleCheckVO result = this.getBaseMapper().getArticleInfo(articleId, query);
        if (result == null) {
            return null;
        }
        checkResultService.markCheckContentWithHtml(result, query);
        return result;
    }

    @Override
    public ArticleCheckVO getArticleAndSnapshotResults(Long articleId, ArticleCheckQuery query) {
        ArticleCheckVO result = this.getBaseMapper().getArticleInfo(articleId, query);
        if (result == null) {
            return null;
        }
        checkResultService.markCheckContentWithHtmlCode(result, query);
        return result;
    }

    @Override
    public void relateWithCheckTask(Long articleId, Long checkTaskId) {
        this.lambdaUpdate()
                .eq(Article::getId, articleId)
                .set(Article::getCheckId, checkTaskId)
                .update();
    }


    @Override
    public boolean updateAuditStatus(List<Long> articleIds, AuditStatusEnum auditStatus) {
        //  TODO 日志
        LoginUserVO loginUser = SecurityUtils.getLoginUser();
        LambdaUpdateChainWrapper<Article> updateWrapper = this.lambdaUpdate();
        updateWrapper.set(Article::getAuditStatus, auditStatus.getValue());
        if (auditStatus != AuditStatusEnum.WAITING_FOR_REVIEW) {
            updateWrapper.set(Article::getAuditUserId, loginUser.getUserId());
            updateWrapper.set(Article::getAuditTime, new Date());
        }
        updateWrapper.in(Article::getId, articleIds);
        return updateWrapper.update();
    }

    @Override
    public Result<String> updateDisposalStatus(String articleIds,
                                               int disposalStatus,
                                               String remark,
                                               LoginUserVO loginUserVO) {
        if (StringUtils.isBlank(articleIds)) {
            return Result.error("文章ID不能为空");
        }
        ArticleDisposalStatusEnum enumByType = ArticleDisposalStatusEnum.getEnumByType(disposalStatus);
        if (enumByType == null) {
            return Result.error("处置状态选择错误");
        }
        boolean b = updateDisposalStatusByIdsAndStatus(articleIds, enumByType.getType(), remark);
        if (!b) {
            return Result.error();
        }
        return Result.ok();
    }


    private boolean updateDisposalStatusByIdsAndStatus(String articleIds, int status, String remark) {
        UpdateWrapper<Article> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("disposal_status", status);
        updateWrapper.set("disposal_remark", remark);
        updateWrapper.in("id", ListUtils.transferIdsToList(articleIds));
        return this.update(updateWrapper);
    }

    @Resource
    private ArticleContentService articleContentService;
    @Resource
    private WebsiteService websiteService;


    @Override
    public Article saveByPushData(LingcaiPushDataDto pushData) {
        Article article = new Article();
        article.setIsIndex(pushData.getIsIndex());
        article.setId(pushData.getId());
        article.setArticleUrl(pushData.getUrl());
        article.setTitle(pushData.getTitle());
        log.info("pushData.getPub_time() = {}", pushData.getPub_time());
        article.setPubTime(pushData.getPub_time());
        //  采集时间
        log.info("pushData.getCreate_time() = {}", pushData.getCreate_time());
        article.setCollectTime(pushData.getCreate_time());
        article.setAuthor(pushData.getEditor());
        article.setSummary(pushData.getSummary());
        Long channelId = pushData.getColumn_id();
        //  TODO 不考虑查空?
        article.setChannelId(channelId);
        Channel channel = channelService.getById(channelId);
        Integer processType = channel.getProcessType();
        ProcessTypeEnum processTypeEnum = ProcessTypeEnum.getByValue(processType);
        article.setProcessType(processTypeEnum.getValue());
        Website website = websiteService.getWebsiteByCondition(processTypeEnum, channel.getSiteId(), channelId);
        article.setSiteId((long) channel.getSiteId());
        article.setWebsiteId(website.getId());

        article.setFocusImgUrl(pushData.getFocus_img());
        //  转载信源，先记录文本，后续根据媒体类型更新id
        String reprintSource = StrUtil.trim(pushData.getSource());
        if (StrUtil.isNotBlank(reprintSource)) {
            article.setReprintSource(reprintSource);
        }
        // 判断是否是首页文章
        String webUrl = website.getWebUrl();
        // 捕获异常
        String html = WebSiteGetHtmlUtils.getWebSourceCode(webUrl);
        // 判断是否在源码重
        if (html.contains(article.getTitle())) {
            article.setIsIndex(1);
        }
        this.save(article);
        CompletableFuture.runAsync(() -> newMediaService.statistic(article));
        Long articleId = article.getId();
        //  保存正文
        articleContentService.saveByPushData(articleId, pushData);
        return article;
    }


    @Resource
    private HttpServletResponse httpServletResponse;


    @Override
    public void exportByQuery(ArticleCheckQuery query) {
        try {
            LoginUserVO user = SecurityUtils.getLoginUser();
            log.info("用户{}导出附件检查记录", user.getUserId());
            List<ArticleCheckResultVO> results = this.listCheckResultsForExport(query);

            // 创建字段映射
            List<FieldMapping<ArticleCheckResultVO, ?>> fieldMappings = Arrays.asList(
                    FieldMapping.of(ArticleCheckResultVO::getGroupName, "分组名称"),
                    FieldMapping.of(ArticleCheckResultVO::getWebsiteName, "站点名称"),
                    FieldMapping.of(ArticleCheckResultVO::getProcessTypeName, "平台类型"),
                    FieldMapping.of(ArticleCheckResultVO::getArticleTitle, "文章标题"),
                    FieldMapping.of(ArticleCheckResultVO::getArticleUrl, "文章地址"),
                    FieldMapping.of(ArticleCheckResultVO::getErrorWord, "错误词"),
                    FieldMapping.of(ArticleCheckResultVO::getSuggestWord, "建议词"),
                    FieldMapping.of(ArticleCheckResultVO::getContext, ExcelExportUtil::safeStringWithoutHtml, "上下文"),
                    FieldMapping.of(ArticleCheckResultVO::getErrorLevelName, "错误级别"),
                    FieldMapping.of(ArticleCheckResultVO::getErrorTypeName, "错误类型"),
                    FieldMapping.of(ArticleCheckResultVO::getAuditStatusName, "审核状态"),
                    FieldMapping.of(ArticleCheckResultVO::getPubTime, "发布时间")
            );

            // 创建Sheet配置
            SheetConfig<ArticleCheckResultVO> sheetConfig = SheetConfig.of("内容检查记录", fieldMappings, results);

            // 导出Excel
            ExcelExportUtil.exportSingleSheetExcel(httpServletResponse, "内容检查记录", sheetConfig);

        } catch (Exception e) {
            log.error("导出内容检查记录失败", e);
            throw new RuntimeException("导出失败：" + e.getMessage());
        }
    }

    @Override
    public IPage<ArticleAllSiteSearchVO> pageAllSiteSearch(AllSiteSearchQuery query) {
        IPage<ArticleAllSiteSearchVO> result = this.getBaseMapper().pageAllSiteSearch(Page.of(query.getPage(), query.getLimit()), query);
        return result;
    }

    @Override
    public void allSiteSearchExport(AllSiteSearchQuery query, LoginUserVO user, HttpServletResponse response) throws IOException {
        //  获取数据
        try {
            log.info("用户{}导出全站检索记录", user.getUserId());
            // 1. 查询数据
            List<Map<String, Object>> dataList = this.getBaseMapper().allSiteSearchExport(query);
            // 2. 导出
            ExcelExportUtil.commonExport("全站检索记录", new String[]{"序号", "标题", "链接", "发布时间", "检测时间"},
                    new String[]{"id", "html_title", "url", "pub_time", "check_time"}, dataList, response);
        } catch (Exception e) {
            log.error("导出全站检索记录失败", e);
            throw new IOException("导出失败：" + e.getMessage());
        }
    }


    @Override
    public List<Article> listUncheckedArticle() {
        return this.getBaseMapper().listUncheckedArticle();
    }

    @Override
    public IPage<ArticleVO> pageChannelArticles(ChannelArticleQuery query) {
        return this.getBaseMapper().pageChannelArticles(Page.of(query.getPage(), query.getLimit()), query);
    }

    @Override
    public List<Article> listUnrelatedReprintSourceArticles() {
        //  检查有转载信源但没有对应id的
        return this.getBaseMapper().listUnrelatedReprintSourceArticles();
    }

    @Override
    public void batchUpdateReprintSourceId(List<Long> articleIds, Long reprintSourceId) {
        if (CollUtil.isEmpty(articleIds) || reprintSourceId == null) {
            return;
        }
        this.lambdaUpdate()
                .in(Article::getId, articleIds)
                .set(Article::getReprintSourceId, reprintSourceId)
                .update();
    }

    @Override
    public List<Article> listByWebsiteId(Long websiteId) {
        if (websiteId == null) {
            return Collections.emptyList();
        }
        return this.getBaseMapper().listByWebsiteId(websiteId);
    }

    @Override
    public List<Article> listByIsIndex(int i, String webSiteId) {
        if (webSiteId == null) {
            return Collections.emptyList();
        }
        return this.getBaseMapper().listByIsIndex(i, webSiteId);
    }

    @Override
    public PageResult<Article> pageByWebsiteId(Long websiteId, Integer pageNum, Integer pageSize) {
        if (websiteId == null) {
            return new PageResult<>();
        }
        // 设置默认截止时间为昨日23:59:59秒
        LocalDateTime yesterday = LocalDate.now().minusDays(1).atTime(LocalTime.MAX);
        Date endTime = Date.from(yesterday.atZone(ZoneId.systemDefault()).toInstant());

        // 需要Article的isIndex为1 根据pub_time 倒序，并且pub_time小于等于截止时间
        IPage<Article> page = this.getBaseMapper()
                .selectPage(Page.of(pageNum, pageSize),
                        new LambdaQueryWrapper<Article>().eq(Article::getWebsiteId, websiteId)
                                .eq(Article::getIsIndex, 1)
                                .le(Article::getPubTime, endTime)
                                .orderByDesc(Article::getPubTime));
        return PageResult.page(page);
    }

    @Override
    public PageResult<Article> pageIndexArticles(IndexArticleQuery query) {
        if (query.getWebsiteId() == null) {
            return new PageResult<>();
        }
        // 构建查询条件
        LambdaQueryWrapper<Article> wrapper = new LambdaQueryWrapper<Article>()
                .eq(Article::getWebsiteId, query.getWebsiteId())
                .eq(Article::getIsIndex, 1);
        // 处理开始时间
        if (query.getBeginTime() != null) {
            wrapper.ge(Article::getPubTime, query.getBeginTime());
        }
        // 处理结束时间，根据用户偏好记忆，默认设置为昨日23:59:59秒
        if (query.getEndTime() != null) {
            wrapper.le(Article::getPubTime, query.getEndTime());
        } else {
            // 如果没有指定截止时间，默认设置为昨日23:59:59秒
            LocalDateTime yesterday = LocalDate.now().minusDays(1).atTime(LocalTime.MAX);
            Date defaultEndTime = Date.from(yesterday.atZone(ZoneId.systemDefault()).toInstant());
            wrapper.le(Article::getPubTime, defaultEndTime);
        }

        // 按发布时间倒序
        wrapper.orderByDesc(Article::getPubTime);

        // 分页查询
        IPage<Article> page = this.getBaseMapper().selectPage(Page.of(query.getPage(), query.getLimit()), wrapper);
        return PageResult.page(page);
    }

    @Override
    public void exportIndexArticlesToExcel(IndexArticleQuery query, HttpServletResponse response) throws IOException {
        try {
            log.info("开始使用EasyExcel导出首页文章数据，导出类型: {}", query.getExportType());

            List<Article> articles;

            // 根据导出类型选择不同的查询方式
            if (query.getExportType() != null && query.getExportType() == 1) {
                // 按ID批量导出
                articles = exportByArticleIds(query.getArticleIds());
            } else {
                // 按条件导出（全部导出）
                articles = exportByCondition(query);
            }

            log.info("查询到{}条文章数据", articles.size());

            // 转换为导出VO
            List<IndexArticleExportVO> exportDataList = convertToExportVO(articles);

            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = java.net.URLEncoder.encode("首页文章数据_" + System.currentTimeMillis(), "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

            // 使用EasyExcel导出
            EasyExcel.write(response.getOutputStream(), IndexArticleExportVO.class)
                    .sheet("首页文章数据")
                    .doWrite(exportDataList);

            log.info("导出完成，共导出{}条数据", exportDataList.size());

        } catch (Exception e) {
            log.error("使用EasyExcel导出首页文章数据失败", e);
            throw new IOException("导出失败：" + e.getMessage());
        }
    }

    @Override
    public List<Article> listNoContentArticle() {
        return this.getBaseMapper().listNoContentArticle();
    }

    @Override
    public CheckSnapshotVO getSnapshotInfo(Long articleId) {
        return this.getBaseMapper().getSnapshotInfo(articleId);
    }

    /**
     * 按条件导出文章
     */
    private List<Article> exportByCondition(IndexArticleQuery query) {
        if (query.getWebsiteId() == null) {
            throw new IllegalArgumentException("站点ID不能为空");
        }

        // 构建查询条件，复用pageIndexArticles的逻辑
        LambdaQueryWrapper<Article> wrapper = new LambdaQueryWrapper<Article>()
                .eq(Article::getWebsiteId, query.getWebsiteId())
                .eq(Article::getIsIndex, 1);

        // 处理开始时间
        if (query.getBeginTime() != null) {
            wrapper.ge(Article::getPubTime, query.getBeginTime());
        }

        // 处理结束时间，根据用户偏好记忆，默认设置为昨日23:59:59秒
        if (query.getEndTime() != null) {
            wrapper.le(Article::getPubTime, query.getEndTime());
        } else {
            // 如果没有指定截止时间，默认设置为昨日23:59:59秒
            LocalDateTime yesterday = LocalDate.now().minusDays(1).atTime(LocalTime.MAX);
            Date defaultEndTime = Date.from(yesterday.atZone(ZoneId.systemDefault()).toInstant());
            wrapper.le(Article::getPubTime, defaultEndTime);
        }

        // 按发布时间倒序
        wrapper.orderByDesc(Article::getPubTime);

        // 查询所有数据（不分页）
        return this.getBaseMapper().selectList(wrapper);
    }

    /**
     * 按文章ID批量导出
     */
    private List<Article> exportByArticleIds(List<Long> articleIds) {
        if (CollUtil.isEmpty(articleIds)) {
            throw new IllegalArgumentException("文章ID列表不能为空");
        }

        // 按指定的文章ID查询，保留首页文章条件
        LambdaQueryWrapper<Article> wrapper = new LambdaQueryWrapper<Article>()
                .in(Article::getId, articleIds)
                .eq(Article::getIsIndex, 1)
                .orderByDesc(Article::getPubTime);

        return this.getBaseMapper().selectList(wrapper);
    }

    /**
     * 将文章列表转换为导出VO列表
     */
    private List<IndexArticleExportVO> convertToExportVO(List<Article> articles) {
        List<IndexArticleExportVO> exportDataList = new ArrayList<>();

        for (Article article : articles) {
            IndexArticleExportVO exportVO = new IndexArticleExportVO();
            exportVO.setTitle(article.getTitle());
            exportVO.setArticleUrl(article.getArticleUrl());
            exportVO.setPubTime(article.getPubTime());

            // 设置来源，优先使用转载信源，没有的话使用网站名称
            String source = article.getReprintSource();
            if (StrUtil.isBlank(source)) {
                // 获取网站名称作为来源
                try {
                    Website website = websiteService.getById(article.getWebsiteId());
                    if (website != null) {
                        source = website.getWebName();
                    }
                } catch (Exception e) {
                    log.warn("获取网站{}信息失败", article.getWebsiteId(), e);
                    source = "未知来源";
                }
            }
            exportVO.setSource(source);
            exportDataList.add(exportVO);
        }

        return exportDataList;
    }


}