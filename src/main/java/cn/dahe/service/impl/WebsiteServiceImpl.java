package cn.dahe.service.impl;

import cn.dahe.common.constants.StatusConstants;
import cn.dahe.dao.ChannelDao;
import cn.dahe.dao.UserDao;
import cn.dahe.dao.UserWebsiteDao;
import cn.dahe.dao.WebsiteDao;
import cn.dahe.entity.Channel;
import cn.dahe.entity.User;
import cn.dahe.entity.UserWebsite;
import cn.dahe.entity.Website;
import cn.dahe.enums.CheckStrategyEnum;
import cn.dahe.enums.ProcessTypeEnum;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.Result;
import cn.dahe.model.query.AppendWebsiteQuery;
import cn.dahe.model.query.WebsiteQuery;
import cn.dahe.model.vo.LoginUserVO;
import cn.dahe.model.vo.WebsiteVO;
import cn.dahe.service.WebsiteService;
import cn.dahe.task.DataPullScheduleTask;
import cn.dahe.utils.ListUtils;
import cn.dahe.utils.StringUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 *
 */
@Service
public class WebsiteServiceImpl extends BaseServiceImpl<WebsiteDao, Website> implements WebsiteService {
    @Resource
    private UserDao userDao;
    @Resource
    private UserWebsiteDao userWebsiteDao;

    @Resource
    private WebsiteDao websiteDao;

    @Resource
    private ChannelDao channelDao;

    @Override
    public PageResult<WebsiteVO> pageList(AppendWebsiteQuery query) {
        Page<WebsiteVO> page = Page.of(query.getPage(), query.getLimit());
        IPage<WebsiteVO> pageResult = this.getBaseMapper().pageList(page, query);
        return PageResult.page(pageResult);
    }

    @Override
    public List<WebsiteVO> listSelectByQuery(AppendWebsiteQuery query) {
        return this.getBaseMapper().listSelectByQuery(query);
    }

    @Override
    public List<Website> listBySiteIds(Set<Integer> siteIds) {
        if (CollUtil.isEmpty(siteIds)) {
            return Collections.emptyList();
        }
        return websiteDao.selectBySiteIds(siteIds);
    }

    @Override
    public Website getWebsiteBySiteId(Integer id) {
        return this.lambdaQuery()
                   .eq(Website::getSiteId, id)
                   .eq(Website::getProcessType, ProcessTypeEnum.WEBSITE.getValue())
                   .eq(Website::getIsDel, 0)
                   .one();
    }

    @Override
    public List<Website> listWebsiteBySiteIds(Collection<Integer> siteIds) {
        return this.lambdaQuery()
                   .in(Website::getSiteId, siteIds)
                   .eq(Website::getProcessType, ProcessTypeEnum.WEBSITE.getValue())
                   .eq(Website::getIsDel, 0)
                   .list();
    }

    @Override
    public List<User> userList(int id, int tenantId, int option) {
        return userDao.selectEnableByWebsiteIdAndTenantId(id, tenantId, option);
    }

    @Override
    public PageResult<Website> page(WebsiteQuery query) {
        IPage<Website> dataPage = baseMapper.selectPage(new Page(query.getPage(), query.getLimit()), getWrapper(query));
        return new PageResult<>(dataPage.getRecords(), dataPage);
    }

    @Override
    public PageResult<Website> pageByWebIds(String webIds, int page, int limit) {
        if (StrUtil.isBlank(webIds)) {
            return new PageResult<>(new ArrayList<>(), new PageInfo<>());
        }
        WebsiteQuery query = new WebsiteQuery();
        query.setWebId(webIds);
        query.setPage(page);
        query.setLimit(limit);
        IPage<Website> dataPage = baseMapper.selectPage(new Page(query.getPage(), query.getLimit()), getWrapper(query));
        return new PageResult<>(dataPage.getRecords(), dataPage);
    }

    @Override
    public List<Website> listByWebIds(String webIds, String ids) {
        WebsiteQuery query = new WebsiteQuery();
        query.setWebId(webIds);
        query.setIds(ids);
        List<Website> websites = baseMapper.selectList(getWrapper(query));
        return websites;
    }

    @Override
    public PageResult<Website> pageByWebIds(List<Long> websiteIds, int page, int limit) {
        Page<Website> pageResult = this.query().in(CollUtil.isNotEmpty(websiteIds), "id", websiteIds).page(Page.of(page, limit));
        return PageResult.page(pageResult);
    }


    @Override
    public Result<String> save(Website vo, LoginUserVO user) {
        if (StringUtils.isBlank(vo.getWebUrl())) {
            return Result.error("网站URL不能为空");
        }
        if (StringUtils.isBlank(vo.getWebName())) {
            return Result.error("网站名称不能为空");
        }
        Website entity = new Website();
        entity.setWebUrl(vo.getWebUrl());
        entity.setWebName(vo.getWebName());
        entity.setWebCodeId(vo.getWebCodeId());
        entity.setCreateTime(new Date());
        entity.setCreateUserId(user.getUserId());
        entity.setCreateUserName(user.getUsername());
        entity.setStatus(StatusConstants.COMMON_NORMAL);
        boolean save = this.save(entity);
        if (!save) {
            return Result.error();
        }
        return Result.ok();
    }

    @Override
    public Result<String> saveNewMedia(Website vo, LoginUserVO user) {
        if (StringUtils.isBlank(vo.getWebUrl())) {
            return Result.error("网站URL不能为空");
        }
        if (StringUtils.isBlank(vo.getWebName())) {
            return Result.error("网站名称不能为空");
        }
        Website entity = new Website();
        // 新媒体在website存放时id按数据库来，siteId从架构组那来，目前取值上和channelId一样
        entity.setWebUrl(vo.getWebUrl());
        entity.setWebName(vo.getWebName());
        entity.setWebCodeId(vo.getWebCodeId());
        entity.setCreateTime(new Date());
        entity.setCreateUserId(user.getUserId());
        entity.setCreateUserName(user.getUsername());
        entity.setStatus(StatusConstants.COMMON_NORMAL);
        entity.setSiteId(vo.getSiteId());
        entity.setChannelId(vo.getChannelId());
        entity.setProcessType(vo.getProcessType());
        entity.setGroupId(vo.getGroupId());
        boolean save = this.save(entity);
        if (!save) {
            return Result.error();
        }
        Channel newChannel = new Channel();
        newChannel.setId(vo.getChannelId());
        newChannel.setName(vo.getWebName());
        newChannel.setUrl(vo.getWebUrl());
        newChannel.setSiteId(vo.getSiteId());
        newChannel.setSiteName(vo.getWebName());
        newChannel.setEnable(1);
        newChannel.setAsync(1);
        newChannel.setProcessType(vo.getProcessType());
//        newChannel.setSchedulerInterval(60);
        newChannel.setCreateTime(new Date());
        newChannel.setLastModifyTime(newChannel.getCreateTime());
        channelDao.insert(newChannel);
        DataPullScheduleTask.pushLingCai(Collections.singletonList(vo.getChannelId()));
        return Result.ok();
    }

    @Override
    public Result<String> update(Website vo, LoginUserVO user) {
        Website oldWebsite = this.getById(vo.getId());
        if (oldWebsite == null) {
            return Result.error("Id传递错误");
        }
        if (StringUtils.isBlank(vo.getWebUrl())) {
            return Result.error("网站URL不能为空");
        }
        if (StringUtils.isBlank(vo.getWebName())) {
            return Result.error("网站名称不能为空");
        }
        oldWebsite.setWebUrl(vo.getWebUrl());
        oldWebsite.setWebName(vo.getWebName());
        oldWebsite.setWebCodeId(vo.getWebCodeId());
        boolean saveOrUpdate = this.saveOrUpdate(oldWebsite);
        if (!saveOrUpdate) {
            return Result.error();
        }
        return Result.ok();
    }

    @Override
    public Result<String> updateStatus(String id, LoginUserVO user) {
        // 禁用启用
        Website oldWebsite = this.getById(id);
        if (oldWebsite == null) {
            return Result.error("Id传递错误");
        }
        oldWebsite.setStatus(oldWebsite.getStatus() == StatusConstants.COMMON_NORMAL ? StatusConstants.COMMON_DISABLE : StatusConstants.COMMON_NORMAL);
        boolean update = updateById(oldWebsite);
        if (!update) {
            return Result.error();
        }
        return Result.ok();
    }

    @Override
    public List<Website> listByStatus(int status) {
        QueryWrapper<Website> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("process_type", 0);
        queryWrapper.eq("is_del", 0);
        queryWrapper.eq("status", status);
        return list(queryWrapper);
    }

    @Override
    public List<Website> listByStatusAndProcessTypeIn(int status, List<Integer> processTypeList) {
        QueryWrapper<Website> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", status);
        if (processTypeList != null && !processTypeList.isEmpty()) {
            queryWrapper.in("process_type", processTypeList);
        }
        return list(queryWrapper);
    }


    @Override
    public CheckStrategyEnum getWebsiteCheckStrategy(Long websiteId) {
        Optional<Website> websiteOptional = this.lambdaQuery().eq(Website::getId, websiteId)
                                                .select(Website::getCheckStrategy)
                                                .oneOpt();
        return CheckStrategyEnum.getByValue(websiteOptional.map(Website::getCheckStrategy).orElse(null));
    }

    @Override
    public boolean updateCheckStrategy(List<Long> websiteIds, CheckStrategyEnum checkStrategy) {
        return this.lambdaUpdate()
                   .set(Website::getCheckStrategy, checkStrategy.getValue())
                   .in(Website::getSiteId, websiteIds)
                   .update();
    }

    @Override
    public boolean updateId(int oldId, int newId, String newUrl) {
        return baseMapper.updateId(oldId, newId, newUrl) > 0;
    }

    @Override
    public List<Website> listByUserId(Integer userId) {
        ArrayList<Website> list = new ArrayList<>();
        List<Integer> siteIds = userWebsiteDao.listByUserId(userId);
        if (siteIds != null && !siteIds.isEmpty()) {
            List<Website> websites = websiteDao.selectList(new QueryWrapper<Website>().lambda().in(Website::getSiteId, siteIds));
            if (websites != null && !websites.isEmpty()) {
                list.addAll(websites);
            }
        }
        return list;
    }

    @Override
    public boolean assignSite(String userId, String siteIds) {
        if (StringUtils.isNotBlank(userId) && StringUtils.isNotBlank(siteIds)) {
            List<Integer> siteIdList = ListUtils.transferIdsToIntegerList(siteIds);
            if (siteIdList != null && !siteIdList.isEmpty()) {
                userWebsiteDao.delete(new QueryWrapper<UserWebsite>().lambda().eq(UserWebsite::getUserId, userId));
                List<UserWebsite> userSites = new ArrayList<>();
                for (Integer siteId : siteIdList) {
                    UserWebsite userSite = new UserWebsite();
                    userSite.setUserId(Integer.parseInt(userId));
                    userSite.setWebsiteId(siteId);
                    userSites.add(userSite);
                }
                int i = userWebsiteDao.insertBatch(userSites);
                return i > 0;
            }
        }
        return false;
    }

    @Override
    public Website getWebsiteByCondition(ProcessTypeEnum processTypeEnum, Integer siteId, Long channelId) {
        switch (processTypeEnum) {
            case WEBSITE:
                return this.lambdaQuery().eq(Website::getSiteId, siteId).eq(Website::getProcessType, processTypeEnum.getValue()).one();
            default:
                return this.lambdaQuery().eq(Website::getChannelId, channelId).eq(Website::getProcessType, processTypeEnum.getValue()).eq(Website::getChannelId, channelId).one();
        }
    }

    @Override
    public long getWebsiteCount() {
        return websiteDao.getWebsiteCount();
    }


    private QueryWrapper<Website> getWrapper(WebsiteQuery query) {
        QueryWrapper<Website> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(query.getName())) {
            queryWrapper.like("web_name", query.getName());
        }
        if (StringUtils.isNotBlank(query.getUrl())) {
            queryWrapper.like("web_url", query.getUrl());
        }
        if (StringUtils.isNotBlank(query.getStatus())) {
            queryWrapper.eq("status", query.getStatus());
        }
        if (StringUtils.isNotBlank(query.getWebId())) {
            queryWrapper.in("site_id", ListUtils.transferIdsToIntegerList(query.getWebId()));
            queryWrapper.eq("process_type", 0);
        }
        if (StringUtils.isNotBlank(query.getIds())) {
            queryWrapper.in("id", ListUtils.transferIdsToIntegerList(query.getIds()));
        }
        return queryWrapper;
    }
}
