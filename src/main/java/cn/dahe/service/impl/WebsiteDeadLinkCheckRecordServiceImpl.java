package cn.dahe.service.impl;

import cn.dahe.dao.WebsiteDao;
import cn.dahe.dao.WebsiteDeadLinkCheckRecordDao;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.WebsiteAccessOverviewDto;
import cn.dahe.model.dto.WebsiteDeadLinkCheckRecordDto;
import cn.dahe.model.dto.WebsiteUpdateStatsDto;
import cn.dahe.entity.Website;
import cn.dahe.entity.WebsiteDeadLinkCheckRecord;
import cn.dahe.model.query.WebsiteDeadLinkCheckRecordQuery;
import cn.dahe.model.vo.LoginUserVO;
import cn.dahe.service.WebsiteDeadLinkCheckRecordService;
import cn.dahe.service.WebsiteService;
import cn.dahe.utils.SpringUtils;
import cn.dahe.utils.excel.ExcelExportUtil;
import cn.dahe.utils.ListUtils;
import cn.dahe.utils.NumberUtils;
import cn.dahe.utils.StringUtils;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;


/**
 *
 */
@Slf4j
@Service
@AllArgsConstructor
public class WebsiteDeadLinkCheckRecordServiceImpl extends BaseServiceImpl<WebsiteDeadLinkCheckRecordDao, WebsiteDeadLinkCheckRecord> implements WebsiteDeadLinkCheckRecordService {

    @Resource
    private WebsiteService websiteService;


    @Override
    public PageResult<WebsiteDeadLinkCheckRecord> page(WebsiteDeadLinkCheckRecordQuery query) {
        IPage<WebsiteDeadLinkCheckRecord> dataPage = baseMapper.selectPage(new Page(query.getPage(), query.getLimit()), getWrapper(query));
        return new PageResult<>(dataPage.getRecords(), dataPage);
    }

    @Override
    public PageResult<WebsiteDeadLinkCheckRecordDto> pageDeadLinkDetail(WebsiteDeadLinkCheckRecordQuery query) {
        IPage<WebsiteDeadLinkCheckRecord> dataPage = baseMapper.selectPage(new Page(query.getPage(), query.getLimit()), getWrapper(query));
        return new PageResult<>(converToDto(dataPage.getRecords(), query), dataPage);
    }

    @Override
    public PageResult<WebsiteDeadLinkCheckRecord> pageCheckRecordDetail(WebsiteDeadLinkCheckRecordQuery query) {
        IPage<WebsiteDeadLinkCheckRecord> dataPage = baseMapper.selectPage(new Page(query.getPage(), query.getLimit()), getWrapper(query));
        return new PageResult<>(dataPage.getRecords(), dataPage);
    }


    @Override
    public WebsiteUpdateStatsDto totalOverview(WebsiteDeadLinkCheckRecordQuery query) {
        List<Website> websiteList = getNewSiteList(query.getWebId());
        List<WebsiteAccessOverviewDto> overviewDtoList = new ArrayList<>();

        for (Website website : websiteList) {
            WebsiteAccessOverviewDto dto = buildWebsiteAccessOverview(website, query);
            overviewDtoList.add(dto);
        }

        int totalSites = overviewDtoList.size();


        // 按 errorCount 是否为0 分区：正常站点 & 出错站点
        Map<Boolean, List<WebsiteAccessOverviewDto>> partitioned = overviewDtoList.stream()
                .collect(Collectors.partitioningBy(dto -> dto.getErrorCount() == 0));

        int normalSites = partitioned.getOrDefault(true, Collections.emptyList()).size();
        int errorSites = totalSites - normalSites;

        String errorRate = NumberUtils.calculateRatioPercentage(errorSites, totalSites, 2);

        WebsiteUpdateStatsDto statsDto = new WebsiteUpdateStatsDto();
        statsDto.setTotalSites(totalSites);
        statsDto.setNormalSites(normalSites);
        statsDto.setErrorSites(errorSites);
        statsDto.setErrorRate(errorRate);

        return statsDto;
    }

    @Override
    public PageResult<WebsiteAccessOverviewDto> pageWebsiteStats(WebsiteDeadLinkCheckRecordQuery query) {
        String newWebId = getNewWebId(query.getWebId());
        PageResult<Website> websitePageResult = websiteService.pageByWebIds(newWebId, query.getPage(), query.getLimit());
        List<WebsiteAccessOverviewDto> overviewDtoList = new ArrayList<>();

        for (Website website : websitePageResult.getList()) {
            WebsiteAccessOverviewDto dto = buildWebsiteAccessOverview(website, query);
            overviewDtoList.add(dto);
        }

        return new PageResult<>(overviewDtoList, websitePageResult.getTotal(), websitePageResult.getPageSize(), websitePageResult.getPage());
    }

    @SneakyThrows
    @Override
    public void export(WebsiteDeadLinkCheckRecordQuery query, LoginUserVO user, HttpServletResponse response) {
        try {
            if (query.getExportType() == 0) {
                log.info("用户{}导出死链详情记录", user.getUsername());
                List<Map<String, Object>> dataListResult = new ArrayList<>();
                List<WebsiteDeadLinkCheckRecord> dataList = baseMapper.selectList(getWrapper(query));
                List<WebsiteDeadLinkCheckRecordDto> dataListDto = converToDto(dataList, query);
                for (WebsiteDeadLinkCheckRecordDto record : dataListDto) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("id", record.getId());
                    map.put("deadLinkUrl", record.getLinkUrl());
                    map.put("type", record.getType());
                    map.put("httpCodeDec", record.getHttpCodeDesc());
                    map.put("webUrl", record.getWebUrl());
                    map.put("sourcePage", record.getSourcePage());
                    map.put("linkUrl", record.getLinkUrl());
                    map.put("checkCount", record.getCheckCount());
                    map.put("checkTime", DateUtil.formatDateTime(record.getCheckTime()));
                    dataListResult.add(map);
                }
                ExcelExportUtil.commonExport("死链详情记录", new String[]{"序号", "死链地址", "死链类型", "死链状态码", "来源网站", "来源页面","死链快照","检查详情","检查时间"},
                        new String[]{"id", "deadLinkUrl", "type", "httpCodeDec", "webUrl", "sourcePage","linkUrl","checkCount","checkTime"}, dataListResult, response);
            } else {
                log.info("用户{}导出死链统计记录", user.getUsername());
                List<Map<String, Object>> dataListResult = new ArrayList<>();
                List<Website> tmpList = websiteService.listByWebIds(query.getWebId(),query.getIds());
                List<WebsiteAccessOverviewDto> dataList = new ArrayList<>();
                for (Website website : tmpList) {
                    WebsiteAccessOverviewDto dto = buildWebsiteAccessOverview(website, query);
                    dataList.add(dto);
                }
                for (WebsiteAccessOverviewDto dto : dataList) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("id", dto.getId());
                    map.put("websiteName", dto.getWebName());
                    map.put("websiteIndexUrl", dto.getWebUrl());
                    map.put("totalCount", dto.getTotalCount());
                    map.put("errorCount", dto.getErrorCount());
                    dataListResult.add(map);
                }
                ExcelExportUtil.commonExport("死链统计记录", new String[]{"序号", "网站名称", "网站地址", "死链条数", "合并后死链条数"},
                        new String[]{"id", "websiteName", "websiteIndexUrl", "totalCount", "errorCount"}, dataListResult, response);
            }
        } catch (Exception e) {
            log.error("导出首页更新检查记录失败", e);
            throw new IOException("导出失败：" + e.getMessage());
        }
    }


    private WebsiteAccessOverviewDto buildWebsiteAccessOverview(Website website, WebsiteDeadLinkCheckRecordQuery query) {
        WebsiteAccessOverviewDto dto = new WebsiteAccessOverviewDto();
        dto.setId(website.getSiteId());
        dto.setWebUrl(website.getWebUrl());
        dto.setWebName(website.getWebName());

        Integer totalCount = baseMapper.countByFilters(
                String.valueOf(website.getSiteId()),
                query.getSourcePage(),
                query.getLinkUrl(),
                query.getHttpCode(),
                query.getBeginTime(),
                query.getEndTime()
        );
        totalCount = totalCount == null ? 0 : totalCount;

        Integer errorCount = baseMapper.countGroupRowsByWebIdAndDeadLinkUrl(
                String.valueOf(website.getSiteId()),
                query.getSourcePage(),
                query.getLinkUrl(),
                query.getHttpCode(),
                query.getBeginTime(),
                query.getEndTime()
        );
        errorCount = errorCount == null ? 0 : errorCount;
        String errorRate = NumberUtils.calculateRatioPercentage(errorCount, totalCount, 2);

        dto.setTotalCount(totalCount);
        dto.setErrorCount(errorCount);
        dto.setErrorRate(errorRate);

        return dto;
    }


    private List<WebsiteDeadLinkCheckRecordDto> converToDto(List<WebsiteDeadLinkCheckRecord> list, WebsiteDeadLinkCheckRecordQuery query) {
        ArrayList<WebsiteDeadLinkCheckRecordDto> dtoArrayList = new ArrayList<>();
        for (WebsiteDeadLinkCheckRecord websiteDeadLinkCheckRecord : list) {
            WebsiteDeadLinkCheckRecordDto dto = new WebsiteDeadLinkCheckRecordDto();
            BeanUtils.copyProperties(websiteDeadLinkCheckRecord, dto);
            Integer countNumber = baseMapper.countGroupedByWebIdAndSourcePageAndDeadLinkUrl(String.valueOf(websiteDeadLinkCheckRecord.getWebId()),
                    websiteDeadLinkCheckRecord.getSourcePage(),
                    websiteDeadLinkCheckRecord.getLinkUrl(),
                    query.getHttpCode(),
                    query.getBeginTime(), query.getEndTime());
            dto.setCheckCount(countNumber == null ? 0 : countNumber);
            dto.setHttpCodeDesc(dto.getHttpCodeDesc() == null ? "" : dto.getHttpCodeDesc().replace("HTTP ", ""));
            dtoArrayList.add(dto);
        }
        return dtoArrayList;
    }


    private QueryWrapper<WebsiteDeadLinkCheckRecord> getWrapper(WebsiteDeadLinkCheckRecordQuery query) {
        QueryWrapper<WebsiteDeadLinkCheckRecord> queryWrapper = Wrappers.query();
        List<Website> websiteList = getNewSiteList(query.getWebId());
        List<Integer> siteIds = websiteList.stream().map(Website::getSiteId).collect(Collectors.toList());
        if (siteIds.isEmpty()) {
            queryWrapper.eq("web_id", -1);
        }
        if (StringUtils.isNotBlank(query.getIds())) {
            queryWrapper.in("id", ListUtils.transferIdsToList(query.getIds()));
        }
        if (StringUtils.isNotBlank(query.getWebId())) {
            queryWrapper.in("web_id", siteIds);
        }
        if (StringUtils.isNotBlank(query.getBeginTime())) {
            queryWrapper.ge("check_time", query.getBeginTime());
        }
        if (StringUtils.isNotBlank(query.getEndTime())) {
            queryWrapper.le("check_time", query.getEndTime());
        }

        // 死链-模糊搜索
        if (StringUtils.isNotBlank(query.getLinkUrlLike())) {
            queryWrapper.like("link_url", query.getLinkUrlLike());
        }
        // 死链-准确搜索
        if (StringUtils.isNotBlank(query.getLinkUrl())) {
            queryWrapper.eq("link_url", query.getLinkUrl());
        }
        // 来源页面
        if (StringUtils.isNotBlank(query.getSourcePage())) {
            queryWrapper.eq("source_page", query.getSourcePage());
        }
        if (StringUtils.isNotBlank(query.getLinkType())) {
            queryWrapper.eq("type", query.getLinkType());
        }
        if (StringUtils.isNotBlank(query.getLatest())) {
            queryWrapper.eq("latest", query.getLatest());
        }
        // http状态码
        if (StringUtils.isNotBlank(query.getHttpCode())) {
            queryWrapper.eq("http_code", query.getHttpCode());
        }
        queryWrapper.orderByDesc("check_time");
        return queryWrapper;
    }

    private List<Website> getNewSiteList(String webId) {
        List<Website> websiteList = new ArrayList<>();
        if (StringUtils.isBlank(webId)) {
            websiteList = websiteService.listByStatus(1);
        } else {
            websiteList = websiteService.listByIds(ListUtils.transferIdsToList(webId));
        }
        return websiteList;
    }

    private String getNewWebId(String webId) {
        List<Website> websiteList = new ArrayList<>();
        if (StringUtils.isBlank(webId)) {
            websiteList = websiteService.listByStatus(1);
        } else {
            websiteList = websiteService.listByIds(ListUtils.transferIdsToList(webId));
        }
        String ids = websiteList.stream().map(website -> website.getSiteId().toString()).collect(Collectors.joining(","));
        return ids;
    }


}