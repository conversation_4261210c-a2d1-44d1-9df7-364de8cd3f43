package cn.dahe.service.impl;

import cn.dahe.check.html.HtmlMappingUtil;
import cn.dahe.dao.CheckContentDao;
import cn.dahe.entity.CheckContent;
import cn.dahe.service.CheckContentService;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class CheckContentServiceImpl extends ServiceImpl<CheckContentDao, CheckContent> implements CheckContentService {


    private String compressText(String htmlText) {
        if (StrUtil.isBlank(htmlText)) {
            return StrUtil.EMPTY;
        }
        return HtmlMappingUtil.compressHtml(htmlText);
    }

    @Override
    public void saveContent(Long checkId, String title, String content, String webCode) {
        this.removeById(checkId);
        CheckContent checkContent = new CheckContent();
        checkContent.setId(checkId);
        if (title != null) {
            checkContent.setCompressedTitle(compressText(title));
        }
        if (content != null) {
            checkContent.setCompressedContent(compressText(content));
        }
        if (webCode != null) {
            checkContent.setCompressedCode(compressText(webCode));
        }
        this.save(checkContent);
    }


    @Override
    public void updateContentByCheckResult(Long checkId, HtmlMappingUtil.HtmlMappingResult titleResult, HtmlMappingUtil.HtmlMappingResult contentResult, HtmlMappingUtil.HtmlMappingResult codeResult) {
        //  除了纯文本 ，原始文本可能在检查时发生变化，一并更新
        LambdaUpdateChainWrapper<CheckContent> lambdaUpdater = this.lambdaUpdate()
                .eq(CheckContent::getId, checkId);
        if (titleResult != null) {
            lambdaUpdater.set(CheckContent::getCleanedTitle, titleResult.getPlainText())
                    .set(CheckContent::getCompressedTitle, titleResult.getCompressedHtml());
        }
        if (contentResult != null) {
            lambdaUpdater.set(CheckContent::getCleanedContent, contentResult.getPlainText())
                    .set(CheckContent::getCompressedContent, contentResult.getCompressedHtml());
        }
        if (codeResult != null) {
            lambdaUpdater.set(CheckContent::getCleanedCode, codeResult.getPlainText())
                    .set(CheckContent::getCompressedCode, codeResult.getCompressedHtml());
        }
        lambdaUpdater.update();
    }


}
