package cn.dahe.service.impl;

import cn.dahe.common.advice.BusinessException;
import cn.dahe.dao.WebsiteGroupDao;
import cn.dahe.entity.Website;
import cn.dahe.entity.WebsiteGroup;
import cn.dahe.model.query.AppendWebsiteQuery;
import cn.dahe.model.request.WebsiteGroupRelateRequest;
import cn.dahe.model.vo.LoginUserVO;
import cn.dahe.model.vo.WebsiteGroupVO;
import cn.dahe.service.WebsiteGroupService;
import cn.dahe.service.WebsiteService;
import cn.hutool.core.util.StrUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;

/**
 * 网站分组Service实现类
 */
@Service
public class WebsiteGroupServiceImpl extends BaseServiceImpl<WebsiteGroupDao, WebsiteGroup> implements WebsiteGroupService {

    @Resource
    private WebsiteService websiteService;

    @PostConstruct
    private void postConstruct() {
        refreshGroupNameCache();
    }


    @Override
    public List<WebsiteGroupVO> listAvailableGroup() {
        return this.getBaseMapper().listAvailableGroup();
    }

    @Override
    public void saveByGroupName(String groupName) {
        groupName = StrUtil.trim(groupName);
        if (StrUtil.isBlank(groupName)) {
            return;
        }
        if(StrUtil.length(groupName) > 10){
            throw new BusinessException("分组名称长度不能超过10个字符");
        }
        Long count = this.lambdaQuery().eq(WebsiteGroup::getGroupName, groupName)
                .eq(WebsiteGroup::getIsDel, false)
                .count();
        if (count > 0) {
            throw new BusinessException("分组名称已存在");
        }
        this.save(new WebsiteGroup().setGroupName(groupName));
        refreshGroupNameCache();
    }

    @Override
    public void updateGroupName(Long id, String groupName) {
        groupName = StrUtil.trim(groupName);
        if (StrUtil.isBlank(groupName) || id == null) {
            throw new BusinessException("分组名称不能为空");
        }
        if(StrUtil.length(groupName) > 10){
            throw new BusinessException("分组名称长度不能超过10个字符");
        }
        WebsiteGroup nameGroup = this.lambdaQuery()
                .eq(WebsiteGroup::getGroupName, groupName)
                .eq(WebsiteGroup::getIsDel, false).one();
        if (nameGroup != null) {
            if (nameGroup.getId().equals(id)) {
                return;
            }
            throw new BusinessException("分组名称已存在");
        }
        this.lambdaUpdate()
                .eq(WebsiteGroup::getId, id)
                .set(WebsiteGroup::getGroupName, groupName)
                .update();
    }

    @Override
    public void relateByRequest(WebsiteGroupRelateRequest request) {
        Long websiteId = request.getWebsiteId();
        Long groupId = request.getGroupId();
        if (websiteId != null && groupId != null) {
            //  TODO 检测存在，检测冲突
            websiteService.lambdaUpdate()
                    .eq(Website::getId, websiteId)
                    .set(Website::getGroupId, groupId)
                    .update();
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Integer id, LoginUserVO user) {
        // 检查分组是否存在
        WebsiteGroup group = getById(id);
        if (group == null) {
            return;
        }
        // 检查分组下是否有网站
        Long websiteCount = websiteService.lambdaQuery()
                .eq(Website::getGroupId, id)
                .eq(Website::getIsDel, false)
                .count();
        if (websiteCount > 0) {
            throw new BusinessException(group.getGroupName() + " 分组下存在网站，无法删除");
        }
        removeById(id);
        refreshGroupNameCache();
    }

    @Override
    public void refreshGroupNameCache() {
        // 只缓存启用且未删除的分组
        List<WebsiteGroup> groups = this.lambdaQuery()
                .eq(WebsiteGroup::getIsDel, false).list();
        // 清空并更新缓存
        GROUP_NAME_CACHE.clear();
        groups.forEach(group -> GROUP_NAME_CACHE.put(group.getId(), group.getGroupName()));
    }


    @Override
    public List<WebsiteGroupVO> listSelectByQuery(AppendWebsiteQuery query) {
        return this.getBaseMapper().listSelectByQuery(query);
    }


}