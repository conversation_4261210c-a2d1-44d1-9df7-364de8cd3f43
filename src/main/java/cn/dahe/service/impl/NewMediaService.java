package cn.dahe.service.impl;

import cn.dahe.common.constants.RoleConstants;
import cn.dahe.common.constants.WarnPushConstants;
import cn.dahe.dao.NewMediaDailyStatisticDao;
import cn.dahe.dao.WarnPlanDao;
import cn.dahe.dao.WarnPlanPlatformDao;
import cn.dahe.dao.WarnPlanPushUserDao;
import cn.dahe.dao.WarnPushRecordDao;
import cn.dahe.dao.WarnUserDao;
import cn.dahe.dao.WebsiteDao;
import cn.dahe.entity.Article;
import cn.dahe.entity.NewMediaDailyStatistic;
import cn.dahe.entity.WarnPlan;
import cn.dahe.entity.WarnPlanPlatform;
import cn.dahe.entity.WarnPlanPushUser;
import cn.dahe.entity.WarnPushRecord;
import cn.dahe.entity.WarnUser;
import cn.dahe.enums.ProcessTypeEnum;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.WarnPushMsgDto;
import cn.dahe.model.vo.LoginUserVO;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.common.usermodel.HyperlinkType;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.ClientAnchor;
import org.apache.poi.ss.usermodel.Comment;
import org.apache.poi.ss.usermodel.CreationHelper;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.usermodel.Drawing;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Hyperlink;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Service
public class NewMediaService {
    final NewMediaDailyStatisticDao newMediaDailyStatisticDao;
    final WarnPlanPlatformDao warnPlanPlatformDao;
    final WarnPlanDao warnPlanDao;
    final WarnUserDao warnUserDao;
    final WarnPlanPushUserDao warnPlanPushUserDao;
    final WarnPushRecordDao warnPushRecordDao;
    final WebsiteDao websiteDao;
    final ObjectMapper objectMapper;
    final HttpServletResponse httpServletResponse;
    @Value("${anxun.base-url}")
    String warnBaseUrl;
    final RestTemplate restTemplate = new RestTemplate();
    final Lock lock = new ReentrantLock();
    final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    final Set<Integer> platformSet = Arrays.stream(ProcessTypeEnum.values())
                                           .filter(type -> type.getValue() > 0 && type.enabled)
                                           .map(ProcessTypeEnum::getValue)
                                           .collect(Collectors.toSet());

    @XxlJob("generateBlankNewMediaStatistic")
    void generateBlankNewMediaStatistic() {
        newMediaDailyStatisticDao.insertByWebsite();
    }

    @XxlJob("noUpdateNewMediaWarn")
    @SneakyThrows
    void noUpdateNewMediaWarn() {
        String now = dateTimeFormatter.format(LocalDateTime.now());
        for (var warnEntry : warnPlanPlatformDao.selectByPlatformGt(0)
                                                .stream()
                                                .collect(Collectors.groupingBy(WarnPlanPlatform::getWarnPlanId))
                                                .entrySet()) {
            WarnPlan plan = warnPlanDao.selectUsefulByIdAndUpdateWarnAndIsDeleted(warnEntry.getKey(), true, false);
            if (plan == null) continue;
            List<Integer> userIdList = warnPlanPushUserDao.listByWarnPlanId(warnEntry.getKey())
                                                          .stream()
                                                          .map(WarnPlanPushUser::getUserId)
                                                          .collect(Collectors.toList());
            if (userIdList.isEmpty()) continue;
            List<WarnUser> userList = warnUserDao.selectByIdInAndIsSubscribe(userIdList, 1);
            if (userList.isEmpty()) continue;
            for (var platformEntry : warnEntry.getValue().stream().collect(Collectors.groupingBy(WarnPlanPlatform::getPlatformType)).entrySet()) {
                Integer platform = platformEntry.getKey(), noUpdateDay = noUpdateDay(platform, plan);
                if (noUpdateDay == null || noUpdateDay <= 0) continue;
                Set<Long> newMediaIdSet = platformEntry.getValue().stream().map(WarnPlanPlatform::getWebsiteId).collect(Collectors.toSet());
                int size = newMediaIdSet.size();
                var resultList = statisticPage(null, null, newMediaIdSet, null, null, null, null, null, noUpdateDay, 1, size, null).getList();
                if (resultList.isEmpty()) continue;
                WarnPushRecord record = new WarnPushRecord()
                        .setSourcePlatform(platform)
                        .setWarnPlanId(warnEntry.getKey())
                        .setReceiverName(userList.stream().map(WarnUser::getUserName).collect(Collectors.joining(",")))
                        .setReceiverUserId(userList.stream().map(user -> String.valueOf(user.getId())).collect(Collectors.joining(",")))
                        .setWarnPushType(WarnPushConstants.METHOD_OF_SYSTEM)
                        .setWarnType(WarnPushConstants.PUSH_TYPE_UPDATE)
                        .setWarnLink(warnBaseUrl + "/mobile-alert-weibo-update.html?check=" + uuid())
                        .setData(objectMapper.writeValueAsString(new HashMap<String, Object>() {{
                            put("newMediaTotal", size);
                            put("noUpdateNewMediaTotal", resultList.size());
                            put("checkTime", now);
                            put("noUpdateDay", noUpdateDay);
                            put("resultList", resultList);
                        }}));
                warnPushRecordDao.insert(record);
                if (plan.getWechatPushEnable()) for (WarnUser user : userList)
                    sendWarn(new WarnPushMsgDto().setTime(now)
                                                 .setContent("有" + resultList.size() + "家" + ProcessTypeEnum.getByValue(platform).info + "未更新")
                                                 .setLink(record.getWarnLink()), user.getUserOpenId());
            }
        }
    }

    public void statistic(Article article) {
        generateBlankNewMediaStatistic();
        lock.lock();
        try {
            newMediaDailyStatisticDao.updateByArticle(article);
        } finally {
            lock.unlock();
        }
    }

    public PageResult<Map<String, Object>> statisticPage(Set<Integer> platformSet, Set<Integer> groupIdSet, Set<Long> newMediaIdSet,
                                                         LocalDate startDate, LocalDate endDate,
                                                         Boolean updated,
                                                         Integer tenantId, Integer sort, Integer noUpdateDay, int page, int limit,
                                                         LoginUserVO loginUser) {
        Integer userId = null;
        Boolean defaultRole = null;
        if (loginUser != null) {
            userId = loginUser.getUserId();
            defaultRole = loginUser.getRoles().contains(RoleConstants.defaultRole);
        }
        LocalDateTime startTime = startDate == null ? null : startDate.atStartOfDay();
        LocalDateTime endTime = endDate == null ? null : endDate.plusDays(1).atStartOfDay();
        try (Page<Map<String, Object>> mapPage = PageHelper.startPage(page, limit == 0 ? Integer.MAX_VALUE : limit)) {
            newMediaDailyStatisticDao.selectStatisticPage(platformSet, groupIdSet, newMediaIdSet,
                                                          startTime, endTime,
                                                          tenantId, userId, sort, noUpdateDay,
                                                          defaultRole, updated);
            return new PageResult<>(mapPage, mapPage.toPageInfo());
        }
    }

    public Map<String, Object> statistic(Set<Integer> platformSet, Set<Integer> groupIdSet, Set<Long> newMediaIdSet,
                                         LocalDate startDate, LocalDate endDate,
                                         int tenantId,
                                         LoginUserVO loginUser) {
        Integer userId = loginUser.getUserId();
        boolean defaultRole = loginUser.getRoles().contains(RoleConstants.defaultRole);
        LocalDateTime startTime = startDate == null ? null : startDate.atStartOfDay();
        LocalDateTime endTime = endDate == null ? null : endDate.plusDays(1).atStartOfDay();
        return newMediaDailyStatisticDao.selectStatistic(platformSet, groupIdSet, newMediaIdSet, startTime, endTime, tenantId, userId, defaultRole);
    }

    public PageResult<NewMediaDailyStatistic> statisticTotalPage(LocalDate startDate, LocalDate endDate, Long newMediaId, int page, int limit) {
        LocalDateTime startTime = startDate == null ? null : startDate.atStartOfDay();
        LocalDateTime endTime = endDate == null ? null : endDate.plusDays(1).atStartOfDay();
        try (Page<NewMediaDailyStatistic> statisticPage = PageHelper.startPage(page, limit == 0 ? Integer.MAX_VALUE : limit)) {
            newMediaDailyStatisticDao.selectStatisticTotalPage(startTime, endTime, newMediaId);
            return new PageResult<>(statisticPage, statisticPage.toPageInfo());
        }
    }

    @SneakyThrows
    public void statisticExport(Set<Integer> platformSet, Set<Integer> groupIdSet, Set<Long> newMediaIdSet,
                                LocalDate startDate, LocalDate endDate,
                                int tenantId,
                                LoginUserVO loginUser) {
        Map<String, Object> map = statistic(platformSet, groupIdSet, newMediaIdSet, startDate, endDate, tenantId, loginUser);
        var list = statisticPage(platformSet, groupIdSet, newMediaIdSet, startDate, endDate, null, tenantId, null, null, 1, 0, loginUser).getList();
        long newMediaTotal = (long) map.get("newMediaTotal"), updateNewMediaTotal = (long) map.get("updateNewMediaTotal");
        try (Workbook workbook = new XSSFWorkbook()) {
            CreationHelper helper = workbook.getCreationHelper();
            DataFormat format = helper.createDataFormat();
            Font boldFont = workbook.createFont();
            boldFont.setBold(true);
            Font redFont = workbook.createFont();
            redFont.setColor(IndexedColors.RED.getIndex());
            CellStyle headStyle = workbook.createCellStyle();
            headStyle.setFont(boldFont);
            headStyle.setAlignment(HorizontalAlignment.CENTER);
            headStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            CellStyle centerStyle = workbook.createCellStyle();
            centerStyle.setAlignment(HorizontalAlignment.CENTER);
            CellStyle redStyle = workbook.createCellStyle();
            redStyle.cloneStyleFrom(centerStyle);
            redStyle.setFont(redFont);
            CellStyle stripedStyle = workbook.createCellStyle();
            stripedStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            stripedStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            CellStyle dateTimeStyle = workbook.createCellStyle();
            dateTimeStyle.setDataFormat(format.getFormat("yyyy-MM-dd HH:mm:ss"));
            dateTimeStyle.setAlignment(HorizontalAlignment.CENTER);
            CellStyle dateStyle = workbook.createCellStyle();
            dateStyle.setDataFormat(format.getFormat("yyyy-MM-dd"));
            dateStyle.setAlignment(HorizontalAlignment.CENTER);
            CellStyle timeStyle = workbook.createCellStyle();
            timeStyle.setDataFormat(format.getFormat("HH:mm:ss"));
            timeStyle.setAlignment(HorizontalAlignment.CENTER);
            String sheetName = "新媒体概况";
            Sheet overviewSheet = workbook.createSheet(sheetName);
            Drawing<?> drawing = overviewSheet.createDrawingPatriarch();
            Row row = overviewSheet.createRow(0);
            cell(row, 0, "总体概况", headStyle);
            overviewSheet.addMergedRegion(new CellRangeAddress(0, 1, 0, 3));
            row = overviewSheet.createRow(2);
            cell(row, 0, "检测账号数", centerStyle);
            cell(row, 1, "更新账号数", centerStyle);
            cell(row, 2, "未更新账号数", centerStyle);
            cell(row, 3, "更新文章数", centerStyle);
            row = overviewSheet.createRow(3);
            cell(row, 0, newMediaTotal);
            cell(row, 1, updateNewMediaTotal);
            cell(row, 2, newMediaTotal - updateNewMediaTotal);
            cell(row, 3, map.get("articleTotal"));
            row = overviewSheet.createRow(5);
            cell(row, 0, "新媒体详情", headStyle);
            overviewSheet.addMergedRegion(new CellRangeAddress(5, 6, 0, 7));
            row = overviewSheet.createRow(7);
            cell(row, 0, "序号", centerStyle);
            cell(row, 1, "平台", centerStyle);
            cell(row, 2, "昵称", centerStyle);
            cell(row, 3, "分组", centerStyle);
            cell(row, 4, "更新天数", centerStyle);
            cell(row, 5, "连续未更新天数", centerStyle);
            cell(row, 6, "最后一次更新时间", centerStyle);
            cell(row, 7, "更新数", centerStyle);
            for (int i = 0; i < list.size(); i++) {
                Map<String, Object> newMedia = list.get(i);
                long id = (long) newMedia.get("id");
                String name = (String) newMedia.get("name"), platform = (String) newMedia.get("platform");
                Sheet detailSheet = workbook.createSheet(name + "（" + platform + "）");
                row = detailSheet.createRow(0);
                cell(row, 0, "日期", centerStyle);
                cell(row, 1, "是否更新", centerStyle);
                cell(row, 2, "更新文章数", centerStyle);
                cell(row, 3, "最后一次更新时间", centerStyle);
                cell(row, 5, "返回", headStyle, helper, HyperlinkType.DOCUMENT, "'" + sheetName + "'!F" + (i + 9));
                detailSheet.addMergedRegion(new CellRangeAddress(0, 1, 5, 6));
                List<NewMediaDailyStatistic> statisticList = statisticTotalPage(startDate, endDate, id, 1, 0).getList();
                for (int j = 0; j < statisticList.size(); j++) {
                    NewMediaDailyStatistic statistic = statisticList.get(j);
                    row = detailSheet.createRow(j + 1);
                    cell(row, 0, statistic.getCreateTime(), dateStyle);
                    Cell cell = row.createCell(1);
                    if (statistic.getNewMediaLastUpdateTime() == null) {
                        cell.setCellStyle(redStyle);
                        cell.setCellValue("否");
                    } else {
                        cell.setCellStyle(centerStyle);
                        cell.setCellValue("是");
                        cell(row, 3, statistic.getNewMediaLastUpdateTime(), timeStyle);
                    }
                    cell(row, 2, statistic.getArticleTotal());
                }
                autoWidth(detailSheet, 4);
                row = overviewSheet.createRow(i + 8);
                cell(row, 0, i + 1, i % 2 == 1 ? stripedStyle : null);
                cell(row, 1, platform);
                Cell cell = cell(row, 2, name, null, helper, HyperlinkType.URL, (String) newMedia.get("link"));
                ClientAnchor anchor = helper.createClientAnchor();
                anchor.setCol1(cell.getColumnIndex());
                anchor.setCol2(cell.getColumnIndex() + 1);
                anchor.setRow1(cell.getRowIndex());
                anchor.setRow2(cell.getRowIndex() + 1);
                Comment comment = drawing.createCellComment(anchor);
                comment.setString(helper.createRichTextString("id：" + id));
                cell.setCellComment(comment);
                cell(row, 3, newMedia.get("groupName"));
                cell(row, 4, newMedia.get("updateDay"));
                cell(row, 5, newMedia.get("noUpdateDay"), null, helper, HyperlinkType.DOCUMENT, "'" + name + "（" + platform + "）" + "'!A1");
                cell(row, 6, newMedia.get("lastUpdateTime"), centerStyle);
                cell(row, 7, newMedia.get("total"));
            }
            autoWidth(overviewSheet, 8);
            httpServletResponse.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            String name = URLEncoder.encode("新媒体检查.xlsx", StandardCharsets.UTF_8.name());
            httpServletResponse.setHeader("Content-Disposition", "attachment;filename*=utf-8''" + name);
            workbook.write(httpServletResponse.getOutputStream());
        }
    }

    public PageResult<Article> articlePage(Set<Integer> platformSet, Set<Integer> groupIdSet, Set<Long> newMediaIdSet,
                                           String startTime, String endTime, String keyword,
                                           int tenantId, Integer sort, int page, int limit,
                                           LoginUserVO loginUser) {
        Integer userId = loginUser.getUserId();
        boolean defaultRole = loginUser.getRoles().contains(RoleConstants.defaultRole);
        if (CollectionUtils.isEmpty(platformSet)) platformSet = this.platformSet;
        LocalDateTime start = toStartTime(startTime), end = toEndTime(endTime);
        try (Page<Article> articlePage = PageHelper.startPage(page, limit == 0 ? Integer.MAX_VALUE : limit)) {
            newMediaDailyStatisticDao.selectArticlePage(platformSet, groupIdSet, newMediaIdSet,
                                                        start, end,
                                                        keyword,
                                                        tenantId, userId, sort,
                                                        defaultRole);
            return new PageResult<>(articlePage, articlePage.toPageInfo());
        }
    }

    @SneakyThrows
    public void articleExport(Set<Integer> platformSet, Set<Integer> groupIdSet, Set<Long> newMediaIdSet,
                              String startTime, String endTime, String keyword,
                              int tenantId, int page, int limit,
                              LoginUserVO loginUser) {
        var list = articlePage(platformSet, groupIdSet, newMediaIdSet, startTime, endTime, keyword, tenantId, null, page, limit, loginUser).getList();
        try (Workbook workbook = new XSSFWorkbook()) {
            CreationHelper helper = workbook.getCreationHelper();
            Font boldFont = workbook.createFont();
            boldFont.setBold(true);
            CellStyle centerStyle = workbook.createCellStyle();
            centerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            centerStyle.setAlignment(HorizontalAlignment.CENTER);
            CellStyle boldStyle = workbook.createCellStyle();
            boldStyle.setFont(boldFont);
            CellStyle dateTimeStyle = workbook.createCellStyle();
            dateTimeStyle.cloneStyleFrom(centerStyle);
            dateTimeStyle.setDataFormat(helper.createDataFormat().getFormat("yyyy-MM-dd HH:mm:ss"));
            CellStyle wrapStyle = workbook.createCellStyle();
            wrapStyle.setWrapText(true);
            wrapStyle.setVerticalAlignment(VerticalAlignment.TOP);
            String sheetName = "新媒体内容列表";
            Sheet contentSheet = workbook.createSheet(sheetName);
            Row row = contentSheet.createRow(0);
            cell(row, 0, "内容", centerStyle);
            cell(row, 1, "平台", centerStyle);
            cell(row, 2, "来源", centerStyle);
            cell(row, 3, "内容发布时间", centerStyle);
            for (int i = 0; i < list.size(); i++) {
                Article article = list.get(i);
                row = contentSheet.createRow(i + 1);
                row.setHeightInPoints(64);
                cell(row, 0, article.getTitle(), wrapStyle, helper, HyperlinkType.URL, article.getArticleUrl());
                cell(row, 1, article.getPlatform());
                cell(row, 2, websiteDao.selectById(article.getWebsiteId()).getWebName());
                cell(row, 3, article.getPubTime(), dateTimeStyle);
            }
            autoWidth(contentSheet, 4);
            httpServletResponse.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            String name = URLEncoder.encode(sheetName + ".xlsx", StandardCharsets.UTF_8.name());
            httpServletResponse.setHeader("Content-Disposition", "attachment;filename*=utf-8''" + name);
            workbook.write(httpServletResponse.getOutputStream());
        }
    }

    public PageResult<Map<String, Object>> page(Set<Integer> platformSet, Set<Integer> groupIdSet, Set<Long> newMediaIdSet,
                                                String name, String link,
                                                int tenantId, Integer status, int page, int limit,
                                                LoginUserVO loginUser) {
        Integer userId = loginUser.getUserId();
        boolean defaultRole = loginUser.getRoles().contains(RoleConstants.defaultRole);
        if (CollectionUtils.isEmpty(platformSet)) platformSet = this.platformSet;
        try (Page<Map<String, Object>> mapPage = PageHelper.startPage(page, limit == 0 ? Integer.MAX_VALUE : limit)) {
            newMediaDailyStatisticDao.selectPage(platformSet, groupIdSet, newMediaIdSet, name, link, tenantId, userId, status, defaultRole);
            return new PageResult<>(mapPage, mapPage.toPageInfo());
        }
    }

    public JsonNode sendWarn(WarnPushMsgDto msg, String openId) {
        String link = "https://auth-center.dahe.cn/api/weixin/warn";
        JsonNode response = restTemplate.postForObject(link, new LinkedMultiValueMap<String, String>() {{
            add("time", msg.getTime());
            add("content", msg.getContent());
            add("link", msg.getLink());
            add("openId", openId);
            add("check", "<![CDATA[oNHrljuA5t,MJcgTx-K");
        }}, JsonNode.class);
        log.info("访问：{}，响应：{}", link, response);
        return response;
    }

    LocalDateTime toStartTime(String date) {
        if (date == null) return null;
        if (date.length() == 10) return LocalDate.parse(date, dateFormatter).atStartOfDay();
        if (date.length() == 19) return LocalDateTime.parse(date, dateTimeFormatter);
        return null;
    }

    LocalDateTime toEndTime(String date) {
        if (date == null) return null;
        if (date.length() == 10) return LocalDate.parse(date, dateFormatter).atTime(LocalTime.MAX);
        if (date.length() == 19) return LocalDateTime.parse(date, dateTimeFormatter);
        return null;
    }

    String uuid() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    Integer noUpdateDay(int platform, WarnPlan plan) {
        if (platform == ProcessTypeEnum.WeChat_Official.value) return plan.getWechatUnupdateDays();
        if (platform == ProcessTypeEnum.Weibo.value) return plan.getWeiboUnupdateDays();
        if (platform == ProcessTypeEnum.Toutiao.value) return plan.getToutiaoContinuousDays();
        if (platform == ProcessTypeEnum.Douyin.value) return plan.getDouyinContinuousDays();
        if (platform == ProcessTypeEnum.KuaiShou.value) return plan.getKuaishouContinuousDays();
        if (platform == ProcessTypeEnum.WeChat_Channel.value) return plan.getWxvideonumberContinuousDays();
        if (platform == ProcessTypeEnum.Xiaohongshu.value) return plan.getXiaohongshuContinuousDays();
        return null;
    }

    void autoWidth(Sheet sheet, int size) {
        DataFormatter formatter = new DataFormatter();
        for (int col = 0; col < size; col++) {
            int maxColumnWidth = 0;
            for (int num = 0; num <= sheet.getLastRowNum(); num++) {
                Row row = sheet.getRow(num);
                if (row == null) continue;
                Cell cell = row.getCell(col);
                if (cell == null) continue;
                String value = formatter.formatCellValue(cell);
                if (value == null) continue;
                int width = 0;
                for (char c : value.toCharArray()) width += c > 255 ? 2 : 1;
                if (width > maxColumnWidth) maxColumnWidth = width;
            }
            int width = (maxColumnWidth + 2) * 256, maxExcelWidth = 64 * 256;
            if (width > maxExcelWidth) width = maxExcelWidth;
            sheet.setColumnWidth(col, width);
        }
    }

    void cell(Row row, int num, Object value) {
        if (value == null) return;
        cell(row, num, value, null);
    }

    Cell cell(Row row, int num, Object value, CellStyle style) {
        Cell cell = row.createCell(num);
        if (value instanceof Integer) cell.setCellValue((int) value);
        else if (value instanceof Long) cell.setCellValue((long) value);
        else if (value instanceof BigDecimal) cell.setCellValue(((BigDecimal) value).longValue());
        else if (value instanceof Date) cell.setCellValue((Date) value);
        else if (value instanceof LocalDateTime) cell.setCellValue((LocalDateTime) value);
        else cell.setCellValue(value.toString());
        cell.setCellStyle(style);
        return cell;
    }

    Cell cell(Row row, int num, Object value, CellStyle style, CreationHelper helper, HyperlinkType type, String address) {
        Cell cell = cell(row, num, value, style);
        Hyperlink hyperlink = helper.createHyperlink(type);
        hyperlink.setAddress(address);
        cell.setHyperlink(hyperlink);
        return cell;
    }
}
