package cn.dahe.service.impl;

import cn.dahe.common.config.cas.CASUtil;
import cn.dahe.common.constants.RoleConstants;
import cn.dahe.common.constants.StatusConstants;
import cn.dahe.dao.TeamDao;
import cn.dahe.dao.TenantDao;
import cn.dahe.dao.UserDao;
import cn.dahe.dao.UserTenantDao;
import cn.dahe.dao.UserWebsiteDao;
import cn.dahe.dao.WebsiteDao;
import cn.dahe.entity.Role;
import cn.dahe.entity.User;
import cn.dahe.entity.UserTenant;
import cn.dahe.entity.UserWebsite;
import cn.dahe.enums.ProcessTypeEnum;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.Result;
import cn.dahe.model.dto.UserDto;
import cn.dahe.model.query.UserQuery;
import cn.dahe.model.vo.LoginUserVO;
import cn.dahe.service.RoleService;
import cn.dahe.service.UserService;
import cn.dahe.utils.SM4Util;
import cn.dahe.utils.StringUtils;
import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


/**
 *
 */
@Service("userService")
@AllArgsConstructor
public class UserServiceImpl extends BaseServiceImpl<UserDao, User> implements UserService {
    @Resource
    private TenantDao tenantDao;
    @Resource
    private TeamDao teamDao;
    @Resource
    final WebsiteDao websiteDao;
    @Resource
    private UserTenantDao userTenantDao;
    @Resource
    private UserWebsiteDao userWebsiteDao;
    @Resource
    private RoleService roleService;
    private final Set<Integer> newMediaSet = Arrays.stream(ProcessTypeEnum.values())
                                                   .filter(type -> type.getValue() > 0 && type.enabled)
                                                   .map(ProcessTypeEnum::getValue)
                                                   .collect(Collectors.toSet());

    public static final String REGEX_PWD = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&.+])[A-Za-z\\d@$!%*?&.+]{8,}$";

    @Override
    public PageResult<UserDto> page(UserQuery query, LoginUserVO loginUser) {
        IPage<User> dataPage = baseMapper.selectPage(new Page(query.getPage(), query.getLimit()), getWrapper(query, loginUser));
        return new PageResult(convertListToDto(dataPage.getRecords(), query.getTenantId()), dataPage);
    }


    @Override
    public PageResult<UserDto> pageRole(UserQuery query, String roleId) {
        PageInfo<User> objectPageInfo = PageHelper.startPage(query.getPage(), query.getLimit())
                                                  .doSelectPageInfo(() -> baseMapper.listByFilters("", roleId, Collections.emptyList()));
        return new PageResult<>(convertListToDto(objectPageInfo.getList(), query.getTenantId()),
                                objectPageInfo.getTotal(),
                                query.getLimit(),
                                query.getPage());
    }

    @Override
    public PageInfo<User> pageByUserNameAndRoleId(UserQuery query) {
        PageInfo<User> objectPageInfo = PageHelper.startPage(query.getPage(), query.getLimit())
                                                  .doSelectPageInfo(() -> baseMapper.listByRoleIdAndUserName(query.getName(), query.getRoleId()));
        return objectPageInfo;
    }


    @Override
    public List<User> listByCityIds(String cityIds) {
        if (cityIds == null || cityIds.isEmpty()) {
            return Collections.emptyList();
        }

        List<Long> cityIdList = Arrays.stream(cityIds.split(","))
                                      .map(Long::parseLong)
                                      .collect(Collectors.toList());

        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("city_id", cityIdList);

        return list(queryWrapper);
    }


    private QueryWrapper<User> getWrapper(UserQuery query, LoginUserVO loginUser) {
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(query.getCityId())) {
            queryWrapper.eq("city_id", query.getCityId());
        }
        if (!loginUser.getRoles().contains(RoleConstants.ADMIN_SUPER)) queryWrapper.eq("tenant_id", query.getTenantId());
        if (StringUtils.isNotBlank(query.getKeyword())) {
            queryWrapper.like("user_name", query.getKeyword());
        }

        return queryWrapper;
    }

    private UserDto convertUserToDto(User user, int tenantId) {
        UserDto userDto = new UserDto();
        if (user != null) {
            userDto.setUserId(user.getUserId());
            userDto.setUsername(user.getUserName());
//            userDto.setPhone(user.getPhone());
            userDto.setStatus(user.getStatus());
            userDto.setDepId(user.getDepId());
            userDto.setDepName(user.getDepName());
            userDto.setAccount(user.getAccount());
            List<Role> roles = roleService.listByUserId(user.getUserId());
            userDto.setListRole(roles);
            userDto.setTeam(teamDao.selectById(user.getTeamId()));
            boolean defaultRole = false;
            for (Role role : roles) {
                if (RoleConstants.ADMIN_SUPER.equals(role.getSn())) {
                    userDto.setTenantList(tenantDao.selectNotDelete());
                    break;
                } else if (RoleConstants.defaultRole.equals(role.getSn())) defaultRole = true;
            }
            if (userDto.getTenantList() == null) userDto.setTenantList(tenantDao.selectNotDeleteByUserId(user.getUserId()));
            Integer id = user.getUserId();
            userDto.setNewMediaList(websiteDao.selectNotDeleteByTenantIdAndUserAndProcessTypeIn(tenantId, id, defaultRole, newMediaSet));
        }
        return userDto;
    }

    private List<UserDto> convertListToDto(List<User> userList, int tenantId) {
        List<UserDto> list = new ArrayList<>();
        if (!userList.isEmpty()) {
            for (User user : userList) {
                UserDto userDto = convertUserToDto(user, tenantId);
                list.add(userDto);
            }
        }
        return list;
    }


    @Override
    public boolean updateSyncedUserInfo(LoginUserVO loginUser) {
        User user = this.getById(loginUser.getUserId());
        if (user == null) {
            user = new User();
        }
        user.setUserId(loginUser.getUserId());
        user.setUserName(loginUser.getUsername());
//        user.setPhone(loginUser.getPhone());
        return this.saveOrUpdate(user);
    }

    @Override
    public Integer getCityIdByUserId(Integer userId) {
        User user = this.getById(userId);
        //不存在直接城市ID设置为0
        Integer cityId = 0;
        if (user != null) {
            cityId = user.getCityId();
        }
        return cityId;
    }

    @Override
    public Boolean isProvincialOrSuperAdmin(Integer userId) {
        Set<String> strings = roleService.listSnByUserId(userId);
        return CASUtil.isAdmin(strings);
    }

    @Override
    public User getByPhone(String phone) {
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("account", phone);
        List<User> userList = baseMapper.selectList(queryWrapper);
        if (userList.size() > 0) {
            return userList.get(0);
        } else {
            return null;
        }
    }

    @Override
    public User getByAccount(String account) {
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("account", account);
        List<User> userList = baseMapper.selectList(queryWrapper);
        if (userList.size() > 0) {
            return userList.get(0);
        } else {
            return null;
        }
    }

    @Override
    public Result<String> save(User user, String roleIds, Set<Integer> tenantIdSet, Set<Integer> newMediaIdSet, LoginUserVO loginUser) {
        if (!StringUtils.hasText(roleIds)) roleIds = "15";
        String errorMsg = checkUser(user, roleIds);
        if (StringUtils.isNotBlank(errorMsg)) {
            return Result.error(errorMsg);
        }
        User byAccount = getByAccount(user.getAccount());
        if (byAccount != null) {
            return Result.error("手机号重复");
        }
        User entity = new User();
//        entity.setPhone(StringUtils.defaultIfBlank(user.getPhone(), ""));
        entity.setStatus(StatusConstants.COMMON_NORMAL);
        entity.setUserName(user.getUserName());
        entity.setCreateTime(new Date());
        entity.setUpdateTime(entity.getCreateTime());
        entity.setUpdateUserId(loginUser.getUserId());
        entity.setUpdateUserName(loginUser.getUsername());
        entity.setAccount(user.getAccount());
        entity.setPassword(SM4Util.encrypt(user.getPassword()));
        entity.setDepId(user.getDepId());
        entity.setDepName(user.getDepName());
        entity.setSeq(user.getSeq() == null ? 0 : user.getSeq());
        entity.setTeamId(user.getTeamId());
        if (!this.save(entity)) {
            return Result.error();
        }
        userNewMediaSet(entity, newMediaIdSet);
        if (loginUser.getRoles().contains(RoleConstants.ADMIN_SUPER)) {
            userTenantSet(entity, tenantIdSet);
            Result<String> stringResult = roleService.assignRole(String.valueOf(entity.getUserId()), roleIds);
            if (!stringResult.isSuccess()) {
                return Result.error();
            }
        }
        return Result.ok();
    }

    @Override
    public Result<String> update(User user, String roleIds, Set<Integer> tenantIdSet, Set<Integer> newMediaIdSet, LoginUserVO loginUser) {
        if (!StringUtils.hasText(roleIds)) roleIds = "15";
        User oldUser = this.getById(user.getUserId());
        if (oldUser == null) {
            return Result.error("该用户不存在，请重新选择");
        }
        user.setPassword(SM4Util.decrypt(oldUser.getPassword()));
        String errorMsg = checkUser(user, roleIds);
        if (StringUtils.isNotBlank(errorMsg)) {
            return Result.error(errorMsg);
        }
//        oldUser.setPhone(StringUtils.defaultIfBlank(user.getPhone(), ""));
        oldUser.setDepId(user.getDepId());
        oldUser.setDepName(user.getDepName());
        oldUser.setStatus(user.getStatus());
        oldUser.setUserName(user.getUserName());
        oldUser.setUpdateTime(new Date());
        oldUser.setUpdateUserId(loginUser.getUserId());
        oldUser.setUpdateUserName(loginUser.getUsername());
        oldUser.setSeq(user.getSeq() == null ? 0 : user.getSeq());
        oldUser.setTeamId(user.getTeamId() == null ? 0 : user.getTeamId());
        if (!this.updateById(oldUser)) {
            return Result.error();
        }
        userNewMediaSet(oldUser, newMediaIdSet);
        if (loginUser.getRoles().contains(RoleConstants.ADMIN_SUPER)) {
            userTenantSet(oldUser, tenantIdSet);
            Result<String> stringResult = roleService.assignRole(String.valueOf(oldUser.getUserId()), roleIds);
            if (!stringResult.isSuccess()) {
                return Result.error();
            }
        }
        return Result.ok();
    }


    @Override
    public Result<String> updateStatus(String userId, LoginUserVO loginUser) {
        User user = this.getById(userId);
        if (user == null) {
            return Result.error("该用户不存在，请重新选择");
        }
        user.setStatus(user.getStatus().equals(StatusConstants.COMMON_NORMAL) ? StatusConstants.COMMON_DISABLE : StatusConstants.COMMON_NORMAL);
        user.setUpdateTime(new Date());
        user.setUpdateUserId(loginUser.getUserId());
        user.setUpdateUserName(loginUser.getUsername());
        if (!updateById(user)) {
            return Result.error();
        }
        return Result.ok();
    }

    @Override
    public Result<String> updateMyPwd(String userId, String newPwd, String oldPwd) {
        String errorMsg = checkPwd(newPwd, oldPwd);
        if (StringUtils.isNotBlank(errorMsg)) {
            return Result.error(errorMsg);
        }
        User user = this.getById(userId);
        if (user == null) {
            return Result.error("用户不存在");
        }
        if (newPwd.equals(oldPwd)) {
            return Result.error("新老密码不能一致");
        }
        if (!oldPwd.equals(SM4Util.decrypt(user.getPassword()))) {
            return Result.error("原密码输入错误");
        }
        user.setPassword(SM4Util.encrypt(newPwd));
        if (!this.updateById(user)) {
            return Result.error();
        }
        return Result.ok();
    }

    @Override
    public Result<String> updateResetPwd(String userId) {
        User user = this.getById(userId);
        if (user == null) {
            return Result.error("该用户不存在，请重新选择");
        }
        String newPwd = "HvQ@et^ubc" + RandomUtil.randomString(6);
        user.setPassword(SM4Util.encrypt(newPwd));
        if (!this.updateById(user)) {
            return Result.error();
        }
        return Result.ok("重置之后的密码为" + newPwd);
    }

    @Override
    public Boolean isAdmin(Collection<String> userRoleSns) {
        boolean isAdmin = false;
        Set<String> adminRoles = RoleConstants.adminRoles;
        for (String role : userRoleSns) {
            if (adminRoles.contains(role)) {
                isAdmin = true;
                break;
            }
        }
        return isAdmin;
    }

    @Override
    public Boolean isAdminOrExpert(Collection<String> userRoleSns) {
        boolean isAdmin = false;
        Set<String> adminRoles = RoleConstants.adminRoles_and_expert;
        for (String role : userRoleSns) {
            if (adminRoles.contains(role)) {
                isAdmin = true;
                break;
            }
        }
        return isAdmin;
    }

    @Override
    public Boolean isSuperAdmin(Collection<String> userRoleSns) {
        boolean isAdmin = false;
        Set<String> adminRoles = new HashSet<>(Arrays.asList(RoleConstants.ADMIN_SUPER));
        for (String role : userRoleSns) {
            if (adminRoles.contains(role)) {
                isAdmin = true;
                break;
            }
        }
        return isAdmin;
    }


    private String checkPwd(String newPwd, String oldPwd) {
        if (StringUtils.isBlank(newPwd)) {
            return "新密码不能为空";
        }
        if (StringUtils.isBlank(oldPwd)) {
            return "原密码不能为空";
        }
        if (!newPwd.matches(REGEX_PWD)) {
            return "新密码不符合规则，长度不能少于8位，包含大小写字母、数字和特殊字符。";
        }

        return "";

    }

    private String checkUser(User user, String roleIds) {
        if (StringUtils.isBlank(user.getUserName())) {
            return "用户名不能为空";
        }
        if (StringUtils.isBlank(user.getAccount())) {
            return "登陆账户不能为空";
        }
        if (StringUtils.isBlank(user.getPassword())) {
            user.setPassword("anxun2025@dahe");
        }
//        Department department = departmentService.getById(user.getDepId());
//        if (department == null || !department.getStatus().equals(StatusConstants.COMMON_NORMAL)) {
//            return "请选择正确的单位";
//        }
//        user.setDepName(department.getName());
//        if (StringUtils.isNotBlank(user.getPhone()) || !StringUtils.isNumeric(user.getPhone())) {
//            return "手机号格式不符合要求";
//        }
//        User byPhone = getByPhone(user.getPhone());
//        if (byPhone != null && !byPhone.getUserId().equals(user.getUserId())) {
//            return "手机号已存在";
//        }
        if (StringUtils.isBlank(roleIds)) {
            return "请选择用户的角色";
        }
        return "";

    }

    void userTenantSet(User user, Set<Integer> tenantIdSet) {
        userTenantDao.deleteByUserId(user.getUserId());
        if (CollectionUtils.isNotEmpty(tenantIdSet))
            userTenantDao.insertList(tenantIdSet.stream()
                                                .map(tenantId -> new UserTenant().setUserId(user.getUserId()).setTenantId(tenantId))
                                                .collect(Collectors.toList()));
    }

    void userNewMediaSet(User user, Set<Integer> newMediaIdSet) {
        userWebsiteDao.deleteByUserIdAndProcessTypeIn(user.getUserId(), newMediaSet);
        if (CollectionUtils.isNotEmpty(newMediaIdSet))
            userWebsiteDao.insertBatch(newMediaIdSet.stream()
                                                    .map(newMediaId -> new UserWebsite().setUserId(user.getUserId()).setWebsiteId(newMediaId))
                                                    .collect(Collectors.toList()));
    }
}