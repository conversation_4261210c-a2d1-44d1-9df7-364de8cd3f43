package cn.dahe.service.impl;

import cn.dahe.dao.CheckErrorTypeDao;
import cn.dahe.entity.CheckErrorType;
import cn.dahe.service.CheckErrorTypeService;
import cn.dahe.model.vo.check.BaseErrorTypeVO;
import cn.dahe.model.vo.check.FirstLevelErrorTypeVO;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * 错误类型Service实现类
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Service
public class CheckErrorTypeServiceImpl extends BaseServiceImpl<CheckErrorTypeDao, CheckErrorType> implements CheckErrorTypeService {

    @PostConstruct
    private void postConstruct() {
        refreshErrorTypeNameCache();
        refreshTypeHierarchyCache();
    }

    @Override
    public List<FirstLevelErrorTypeVO> getTypeTree() {
        return baseMapper.getTypeTree();
    }

    @Override
    public List<CheckErrorType> getTypesByLevel(Integer level) {
        return this.lambdaQuery()
                .eq(CheckErrorType::getLevel, level)
                .orderByAsc(CheckErrorType::getSortOrder)
                .list();
    }

    @Override
    public List<CheckErrorType> getChildTypes(Long parentId) {
        return this.lambdaQuery()
                .eq(CheckErrorType::getParentId, parentId)
                .orderByAsc(CheckErrorType::getSortOrder)
                .list();
    }

    @Override
    public List<BaseErrorTypeVO> listTypeName() {
        return this.getBaseMapper().listTypeName();
    }

    /**
     * 刷新错误类型名称缓存
     */
    @Override
    public void refreshErrorTypeNameCache() {
        List<BaseErrorTypeVO> vos = this.listTypeName();
        TYPE_NAME_CACHE.clear();
        vos.forEach(vo -> TYPE_NAME_CACHE.put(vo.getId(), vo.getName()));
    }

    @Override
    public void refreshTypeHierarchyCache() {
        // 清空缓存
        TYPE_HIERARCHY_CACHE.clear();

        // 获取所有错误类型
        List<CheckErrorType> allTypes = this.lambdaQuery().orderByAsc(CheckErrorType::getLevel).list();

        // 构建ID到类型的映射，方便查找
        Map<Long, CheckErrorType> typeMap = new HashMap<>();
        allTypes.forEach(type -> typeMap.put(type.getId(), type));

        // 遍历所有类型，构建层级关系
        for (CheckErrorType type : allTypes) {
            Long[] hierarchy = new Long[3]; // [一级ID, 二级ID, 三级ID]
            Long typeId = type.getId();
            Long parentTypeId = type.getParentId();
            // 根据当前类型的level确定处理方式
            switch (type.getLevel()) {
                case 1:
                    // 一级分类
                    hierarchy[0] = typeId;
                    break;
                case 2:
                    // 二级分类
                    hierarchy[1] = typeId;
                    hierarchy[0] = parentTypeId;
                    break;
                case 3:
                    // 三级分类
                    hierarchy[2] = typeId;
                    hierarchy[1] = parentTypeId;
                    CheckErrorType parent = typeMap.get(parentTypeId);
                    if (parent != null) {
                        hierarchy[0] = parent.getParentId();
                    }
                    break;
                default:
                    continue;
            }

            // 将层级关系存入缓存
            TYPE_HIERARCHY_CACHE.put(typeId, hierarchy);
        }
    }
} 