package cn.dahe.service.impl;

import cn.dahe.dao.WebsiteAccessRecordDao;
import cn.dahe.dao.WebsiteDao;
import cn.dahe.entity.Website;
import cn.dahe.model.dto.WebsiteAccessOverviewDto;
import cn.dahe.model.dto.WebsiteUpdateStatsDto;
import cn.dahe.entity.WebsiteAccessRecord;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.query.WebsiteAccessRecordQuery;
import cn.dahe.model.vo.LoginUserVO;
import cn.dahe.service.WebsiteAccessRecordService;
import cn.dahe.utils.SpringUtils;
import cn.dahe.utils.excel.ExcelExportUtil;
import cn.dahe.utils.ListUtils;
import cn.dahe.utils.NumberUtils;
import cn.dahe.utils.StringUtils;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;


/**
 *
 */
@Slf4j
@Service
@AllArgsConstructor
public class WebsiteAccessRecordServiceImpl extends BaseServiceImpl<WebsiteAccessRecordDao, WebsiteAccessRecord> implements WebsiteAccessRecordService {

    @Override
    public PageResult<WebsiteAccessRecord> page(WebsiteAccessRecordQuery query) {
        IPage<WebsiteAccessRecord> dataPage = baseMapper.selectPage(new Page(query.getPage(), query.getLimit()), getWrapper(query));
        return new PageResult<>(dataPage.getRecords(), dataPage);
    }

    @Override
    public PageResult<WebsiteAccessOverviewDto> pageStats(WebsiteAccessRecordQuery query) {
        PageInfo<WebsiteAccessOverviewDto> objectPageInfo = PageHelper.startPage(query.getPage(), query.getLimit()).doSelectPageInfo(() ->
                baseMapper.listByFilters(ListUtils.transferIdsToList(query.getWebId()),
                        query.getGroupType(),
                        query.getBeginTime(), query.getEndTime()));
        return new PageResult<>(convertWebsiteAccessOverviewDto(objectPageInfo.getList()), objectPageInfo);
    }

    @Override
    public WebsiteUpdateStatsDto totalStats(WebsiteAccessRecordQuery query) {
        List<WebsiteAccessOverviewDto> overviewList = baseMapper.listByFilters(
                ListUtils.transferIdsToList(query.getWebId()),
                query.getGroupType(),
                query.getBeginTime(),
                query.getEndTime()
        );

        int totalSites = overviewList.size();

        // 按 errorCount 是否为0 分区：正常站点 & 出错站点
        Map<Boolean, List<WebsiteAccessOverviewDto>> partitioned = overviewList.stream()
                .collect(Collectors.partitioningBy(dto -> dto.getErrorCount() == 0));

        int normalSites = partitioned.getOrDefault(true, Collections.emptyList()).size();
        int errorSites = totalSites - normalSites;

        String errorRate = NumberUtils.calculateRatioPercentage(errorSites, totalSites, 2);

        WebsiteUpdateStatsDto statsDto = new WebsiteUpdateStatsDto();
        statsDto.setTotalSites(totalSites);
        statsDto.setNormalSites(normalSites);
        statsDto.setErrorSites(errorSites);
        statsDto.setErrorRate(errorRate);

        return statsDto;
    }

    @SneakyThrows
    @Override
    public void export(WebsiteAccessRecordQuery query, LoginUserVO user, HttpServletResponse response){
        try {
            if (query.getExportType() == 0) {
                log.info("用户{}导出连通性检查记录", user.getUsername());
                query.setWebIdList(ListUtils.transferIdsToList(query.getWebId()));
                List<Map<String, Object>> dataList = this.getBaseMapper().selectExportData(query);
                ExcelExportUtil.commonExport("连通性检查", new String[]{"序号", "网站名称", "网站地址", "访问次数", "访问异常次数", "访问异常占比"},
                        new String[]{"id", "webName", "webUrl", "totalCount", "errorCount", "errorRate"}, dataList, response);
            } else {
                log.info("用户{}导出连通性检查明细记录", user.getUsername());
                List<WebsiteAccessRecord> dataList = baseMapper.selectList(getWrapper(query));
                List<Map<String, Object>> dataListResult = new ArrayList<>();
                for (int i = 0; i < dataList.size(); i++) {
                    Map<String, Object> tmp = new HashMap<>();
                    tmp.put("webName", dataList.get(i).getWebName());
                    tmp.put("webUrl", dataList.get(i).getWebUrl());
                    tmp.put("accessTimeConsuming", dataList.get(i).getAccessTimeConsuming());
                    tmp.put("success", dataList.get(i).getSuccess() == 1 ? "是" : "否");
                    tmp.put("checkTime", DateUtil.formatDateTime(dataList.get(i).getCheckTime()));
                    dataListResult.add(tmp);
                }
                ExcelExportUtil.commonExport("连通性检查明细", new String[]{"序号","网站名称", "网站地址", "访问耗时(毫秒)", "访问结果", "访问时间"},
                        new String[]{"id","webName", "webUrl", "accessTimeConsuming", "success", "checkTime"}, dataListResult, response);
            }
        } catch (Exception e) {
            log.error("导出连通性检查记录失败", e);
            throw new IOException("导出失败：" + e.getMessage());
        }
    }


    private List<WebsiteAccessOverviewDto> convertWebsiteAccessOverviewDto(List<WebsiteAccessOverviewDto> list) {
        for (WebsiteAccessOverviewDto websiteAccessOverviewDto : list) {
            websiteAccessOverviewDto.setErrorRate(NumberUtils.calculateRatioPercentage(websiteAccessOverviewDto.getErrorCount(),
                    websiteAccessOverviewDto.getTotalCount(), 2));
        }
        return list;
    }


    private QueryWrapper<WebsiteAccessRecord> getWrapper(WebsiteAccessRecordQuery query) {
        QueryWrapper<WebsiteAccessRecord> queryWrapper = Wrappers.query();

        if (StringUtils.isNotBlank(query.getIds())) {
            queryWrapper.in("id", ListUtils.transferIdsToList(query.getIds()));
        }

        // 网站ID筛选
        if (StringUtils.isNotBlank(query.getWebId())) {
            List<Integer> websiteIds = ListUtils.transferIdsToIntegerList(query.getWebId());
            WebsiteDao websiteDao = SpringUtils.getBean(WebsiteDao.class);
            List<Integer> siteIds = websiteDao.selectSiteIdsByIds(websiteIds);
            queryWrapper.in("web_id", siteIds);
        }

        // 网站名称筛选
        if (StringUtils.isNotBlank(query.getWebName())) {
            queryWrapper.like("web_name", query.getWebName());
        }

        // 时间范围筛选
        if (StringUtils.isNotBlank(query.getBeginTime())) {
            queryWrapper.ge("check_time", query.getBeginTime());
        }
        if (StringUtils.isNotBlank(query.getEndTime())) {
            queryWrapper.le("check_time", query.getEndTime());
        }

        // 访问状态筛选
        if (query.getSuccess() != null) {
            queryWrapper.eq("success", query.getSuccess());
        }

        // 链接类型筛选
        if (query.getType() != null) {
            queryWrapper.eq("type", query.getType());
        }

        // HTTP状态码筛选
        if (query.getHttpCode() != null) {
            queryWrapper.eq("http_code", query.getHttpCode());
        }

        // 链接地址筛选
        if (StringUtils.isNotBlank(query.getLinkUrl())) {
            queryWrapper.like("link_url", query.getLinkUrl());
        }

        queryWrapper.orderByDesc("check_time");
        return queryWrapper;
    }


}