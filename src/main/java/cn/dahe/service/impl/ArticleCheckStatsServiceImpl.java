package cn.dahe.service.impl;

import cn.dahe.common.auth.SecurityUtils;
import cn.dahe.dao.ArticleCheckStatsDao;
import cn.dahe.model.query.ArticleCheckStatQuery;
import cn.dahe.model.query.ArticleCheckWordStatQuery;
import cn.dahe.model.vo.*;
import cn.dahe.service.ArticleCheckStatsService;
import cn.dahe.utils.excel.ExcelExportUtil;
import cn.dahe.utils.excel.FieldMapping;
import cn.dahe.utils.excel.SheetConfig;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * 网站文章错误统计服务实现类
 */
@Slf4j
@Service
public class ArticleCheckStatsServiceImpl implements ArticleCheckStatsService {
    @Resource
    private ArticleCheckStatsDao articleCheckStatsDao;

    @Override
    public IPage<WebsiteArticleCheckStatsVO> pageStats(ArticleCheckStatQuery query) {
        return articleCheckStatsDao.pageStats(Page.of(query.getPage(), query.getLimit()), query);
    }

    @Resource
    private HttpServletResponse httpServletResponse;

    @Override
    public void exportStats(ArticleCheckStatQuery query) {
        try {
            LoginUserVO user = SecurityUtils.getLoginUser();
            log.info("用户{}导出网站文章错误统计", user.getUserId());

            // 获取数据
            query.setPage(1);
            query.setLimit(10000); // 导出最多10000条
            IPage<WebsiteArticleCheckStatsVO> pageResult = this.pageStats(query);
            List<WebsiteArticleCheckStatsVO> dataList = pageResult.getRecords();

            // 创建字段映射
            List<FieldMapping<WebsiteArticleCheckStatsVO, ?>> fieldMappings = Arrays.asList(
                FieldMapping.of(WebsiteArticleCheckStatsVO::getGroupName, "分组名称"),
                FieldMapping.of(WebsiteArticleCheckStatsVO::getWebsiteName, "站点名称"),
                FieldMapping.of(WebsiteArticleCheckStatsVO::getProcessTypeName, "所属平台"),
                FieldMapping.of(WebsiteArticleCheckStatsVO::getArticleCount, "发布数"),
                FieldMapping.of(WebsiteArticleCheckStatsVO::getArticleLength, "文章字数"),
                FieldMapping.of(WebsiteArticleCheckStatsVO::getLv1ArticleCount, "总错误词数"),
                FieldMapping.of(WebsiteArticleCheckStatsVO::getLv2ArticleCount, "严重错误数"),
                FieldMapping.of(WebsiteArticleCheckStatsVO::getLv3ArticleCount, "疑错数")
            );

            // 创建Sheet配置
            SheetConfig<WebsiteArticleCheckStatsVO> sheetConfig = SheetConfig.of("错误统计", fieldMappings, dataList);

            // 导出Excel
            ExcelExportUtil.exportSingleSheetExcel(httpServletResponse, "站点错误统计", sheetConfig);

        } catch (Exception e) {
            log.error("导出网站文章错误统计失败", e);
            throw new RuntimeException("导出失败：" + e.getMessage());
        }
    }

    @Override
    public void exportWordStats(ArticleCheckWordStatQuery query) {
        try {
            LoginUserVO user = SecurityUtils.getLoginUser();
            log.info("用户{}导出错误词统计", user.getUserId());

            // 获取数据
            query.setPage(1);
            query.setLimit(10000); // 导出最多10000条
            IPage<WebsiteArticleCheckWordStatsVO> pageResult = this.pageWordStats(query);
            List<WebsiteArticleCheckWordStatsVO> dataList = pageResult.getRecords();

            // 创建字段映射
            List<FieldMapping<WebsiteArticleCheckWordStatsVO, ?>> fieldMappings = Arrays.asList(
                FieldMapping.of(WebsiteArticleCheckWordStatsVO::getErrorWord, "错误词"),
                FieldMapping.of(WebsiteArticleCheckWordStatsVO::getErrorLevelName, "错误级别"),
                FieldMapping.of(WebsiteArticleCheckWordStatsVO::getErrorTypeName, "错误类别"),
                FieldMapping.of(WebsiteArticleCheckWordStatsVO::getSuggestWord, "建议词"),
                FieldMapping.of(WebsiteArticleCheckWordStatsVO::getCheckResultCount, "出现次数"),
                FieldMapping.of(WebsiteArticleCheckWordStatsVO::getCount, "涉及站点数量"),
                FieldMapping.of(WebsiteArticleCheckWordStatsVO::getFilterStatusName, "过滤状态")
            );

            // 创建Sheet配置
            SheetConfig<WebsiteArticleCheckWordStatsVO> sheetConfig = SheetConfig.of("错误词统计", fieldMappings, dataList);

            // 导出Excel
            ExcelExportUtil.exportSingleSheetExcel(httpServletResponse, "错误词统计", sheetConfig);

        } catch (Exception e) {
            log.error("导出错误词统计失败", e);
            throw new RuntimeException("导出失败：" + e.getMessage());
        }
    }

    @Override
    public WebsiteArticleCheckTotalStatsVO queryTotalStats(ArticleCheckStatQuery query) {
        return articleCheckStatsDao.queryTotalStats(query);
    }

    @Override
    public WebsiteArticleCheckWordTotalStatsVO queryWordTotalStats(ArticleCheckWordStatQuery query) {
        return articleCheckStatsDao.queryWordTotalStats(query);
    }

    @Override
    public IPage<WebsiteArticleCheckWordStatsVO> pageWordStats(ArticleCheckWordStatQuery query) {
        return articleCheckStatsDao.pageWordStats(Page.of(query.getPage(), query.getLimit()), query);
    }


}