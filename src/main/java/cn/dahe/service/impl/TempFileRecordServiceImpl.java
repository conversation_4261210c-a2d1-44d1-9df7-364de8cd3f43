package cn.dahe.service.impl;

import cn.dahe.dao.TempFileRecordDao;
import cn.dahe.entity.TempFileRecord;
import cn.dahe.service.TempFileRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class TempFileRecordServiceImpl extends ServiceImpl<TempFileRecordDao, TempFileRecord> implements TempFileRecordService {
    
    @Override
    public void addTempFile(String filePath, Long businessId, String businessType) {
        TempFileRecord record = new TempFileRecord()
                .setFilePath(filePath)
                .setBusinessId(businessId)
                .setBusinessType(businessType)
                .setCreateTime(new Date())
                .setStatus(0);
        this.save(record);
    }
}
