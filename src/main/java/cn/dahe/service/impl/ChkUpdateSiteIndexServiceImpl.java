package cn.dahe.service.impl;

import cn.dahe.dao.*;
import cn.dahe.entity.*;
import cn.dahe.enums.ProcessTypeEnum;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.query.ChkUpdateSiteIndexQuery;
import cn.dahe.model.vo.ChkUpdateSiteIndexVO;
import cn.dahe.model.vo.LoginUserVO;
import cn.dahe.service.ArticleService;
import cn.dahe.service.ChkUpdateSiteIndexService;
import cn.dahe.service.WebsiteGroupService;
import cn.dahe.service.WebsiteService;
import cn.dahe.utils.excel.ExcelExportUtil;
import cn.dahe.utils.fan.FanCollectUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

/**
 * 首页更新检查Service实现类 - 基于t_article表
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@Service
public class ChkUpdateSiteIndexServiceImpl extends BaseServiceImpl<ChkUpdateSiteIndexDao, ChkUpdateSiteIndex> implements ChkUpdateSiteIndexService {

    @Resource
    private ChkUpdateSiteIndexDao chkUpdateSiteIndexDao;

    @Resource
    private ChkUpdateSiteIndexResultDao chkUpdateSiteIndexResultDao;

    @Resource
    private ArticleDao articleDao;

    @Resource
    private ArticleContentDao articleContentDao;

    @Resource
    private WebsiteService websiteService;

    @Resource
    private ChannelDao channelDao;

    @Resource
    private ArticleService articleService;

    // 定义锁对象
    private final Lock lock = new ReentrantLock();

    // ==================== 首页更新检查概览 ====================
    @Override
    public Map<String, Object> getOverviewStatistics(ChkUpdateSiteIndexQuery query) {
        Map<String, Object> result = new HashMap<>();
        long totalCount = StrUtil.isBlank(query.getWebsiteId()) ? websiteService.getWebsiteCount() : query.getWebsiteId().split(",").length;
        long updatedCount = chkUpdateSiteIndexDao.getUpdatedCount(query);
        result.put("totalSiteCount", totalCount);
        result.put("updatedSiteCount", updatedCount);
        result.put("notUpdatedSiteCount", totalCount - updatedCount);
        return result;
    }

    // ==================== 网站详情列表 ====================
    @Override
    public PageResult<ChkUpdateSiteIndexVO> page(ChkUpdateSiteIndexQuery query) {
        try {
            Page<Map<String, Object>> page = new Page<>(query.getPage(), query.getLimit());
            IPage<Map<String, Object>> result = baseMapper.selectPageWithExtInfo(
                    page,
                    query.getGroupId(),
                    query.getWebsiteId(),
                    StrUtil.isNotBlank(query.getBeginTime()) ? query.getBeginTime() : null,
                    StrUtil.isNotBlank(query.getEndTime()) ? query.getEndTime() : null);

            // 转换Map到VO
            List<ChkUpdateSiteIndexVO> voList = new ArrayList<>();
            for (Map<String, Object> map : result.getRecords()) {
                ChkUpdateSiteIndexVO vo = convertMapToVO(map);
                voList.add(vo);
            }
            return new PageResult<>(voList, result.getTotal(), (int) result.getSize(), (int) result.getCurrent());
        } catch (Exception e) {
            log.error("查询网站详情列表失败，返回兜底数据", e);
        }
        return new PageResult<>();
    }

    @Override
    public ChkUpdateSiteIndexVO get(Long id) {
        try {
            // 基于t_article表查询站点详情
            QueryWrapper<Article> wrapper = new QueryWrapper<>();
            wrapper.select("site_id", "site_name", "MAX(pub_time) as last_pub_time",
                            "COUNT(*) as article_count", "MAX(update_time) as last_update_time")
                    .eq("is_del", 0)
                    .eq("site_id", id)
                    .groupBy("site_id", "site_name");

            Map<String, Object> result = articleDao.selectMaps(wrapper).stream().findFirst().orElse(null);

            if (result == null) {
                // 返回兜底数据
                return createMockDetailData(id);
            }
            // 转换Map到VO
            return convertSiteMapToVO(result);
        } catch (Exception e) {
            log.error("获取网站详情失败，返回兜底数据", e);
            return createMockDetailData(id);
        }
    }

    @Override
    public Map<String, Object> syncHomepageUpdates() {
        Map<String, Object> result = new HashMap<>();
        int processedCount = 0;
        int savedCount = 0;
        int skippedCount = 0;
        int errorCount = 0;

        try {
            log.info("开始同步首页更新数据");

            // 获取所有启用的网站
            List<Website> websites = websiteService.listByStatus(1);
            if (websites.isEmpty()) {
                log.info("没有找到启用的网站，跳过同步");
                result.put("message", "没有找到启用的网站");
                return result;
            }

            log.info("找到{}个启用的网站，开始同步首页更新数据", websites.size());

            // 构建网站信息映射，便于后续查找
            Map<String, Website> urlToWebsiteMap = convertToMap(websites);

            // 构建请求参数：包含site_id和url的网站列表
            JSONArray sites = new JSONArray();
            for (Website website : websites) {
                if (website.getWebUrl() != null) {
                    JSONObject site = new JSONObject();
                    site.put("site_id", website.getSiteId());
                    site.put("url", website.getWebUrl());
                    sites.add(site);
                }
            }

            if (sites.isEmpty()) {
                log.info("没有有效的网站URL，跳过同步");
                result.put("message", "没有有效的网站URL");
                return result;
            }

            // 调用采集中心API获取首页更新情况
            JSONObject jsonObject = FanCollectUtil.getHomepageUpdates(sites);
            if (jsonObject == null) {
                log.error("获取首页更新情况失败：API返回null");
                result.put("message", "API返回null");
                result.put("success", false);
                return result;
            }

            if (!jsonObject.getBoolean("success")) {
                log.error("获取首页更新情况失败：{}", jsonObject.getString("message"));
                result.put("message", jsonObject.getString("message"));
                result.put("success", false);
                return result;
            }

            log.info("获取首页更新情况成功");
            JSONArray data = jsonObject.getJSONArray("data");
            if (data == null || data.isEmpty()) {
                log.info("首页更新数据为空");
                result.put("message", "首页更新数据为空");
                result.put("success", true);
                return result;
            }

            for (int i = 0; i < data.size(); i++) {
                try {
                    JSONObject item = data.getJSONObject(i);
                    processedCount++;

                    // 提取API返回的数据
                    Integer siteId = item.getInteger("site_id");
                    String url = item.getString("url");
                    String status = item.getString("status");
                    String lastCheck = item.getString("last_check");
                    lastCheck = offsetDateTime(lastCheck);
                    Integer latencyMs = item.getInteger("latency_ms");
                    String error = item.getString("error");

                    // 从本地网站映射中获取网站信息
                    Website website = urlToWebsiteMap.get(url);
                    if (website == null) {
                        log.warn("未找到URL对应的网站信息: {}", url);
                        errorCount++;
                        continue;
                    }

                    // 创建首页更新记录
                    ChkUpdateSiteIndex chkUpdateSiteIndex = new ChkUpdateSiteIndex();
                    chkUpdateSiteIndex.setSiteId(siteId != null ? siteId : website.getSiteId());
                    chkUpdateSiteIndex.setSiteName(website.getWebName());
                    chkUpdateSiteIndex.setSiteUrl(url);
                    chkUpdateSiteIndex.setStatus(status);
                    // 2025-08-15T08:19:56.692574+00:00 转换为 2025-08-15 08:19:56 再+8小时
                    chkUpdateSiteIndex.setLastCheck(DateUtil.parseDateTime(lastCheck));
                    chkUpdateSiteIndex.setLatencyMs(latencyMs);
                    chkUpdateSiteIndex.setError(error);
                    chkUpdateSiteIndex.setCreateTime(new Date());
                    chkUpdateSiteIndex.setIsDel(false);

                    // 设置兼容字段
                    chkUpdateSiteIndex.setGroupId(website.getGroupId());
                    chkUpdateSiteIndex.setGroupName(WebsiteGroupService.getGroupName(website.getGroupId()));

                    // 生成唯一签名用于去重
                    String signData = String.format("%d_%s_%s",
                            chkUpdateSiteIndex.getSiteId(),
                            url,
                            lastCheck != null ? lastCheck : "");
                    String sign = generateSign(signData);
                    chkUpdateSiteIndex.setSign(sign);

                    // 检查是否已存在相同签名的记录
                    QueryWrapper<ChkUpdateSiteIndex> checkWrapper = new QueryWrapper<>();
                    checkWrapper.eq("sign", sign);

                    if (chkUpdateSiteIndexDao.selectCount(checkWrapper) > 0) {
                        log.debug("跳过重复的首页更新检查记录: siteId={}, url={}", siteId, url);
                        skippedCount++;
                        continue;
                    }

                    // 保存记录
                    int saved = chkUpdateSiteIndexDao.insert(chkUpdateSiteIndex);
                    if (saved > 0) {
                        savedCount++;
                        log.debug("保存首页更新记录成功: siteId={}, url={}, status={}",
                                chkUpdateSiteIndex.getSiteId(), url, status);
                    } else {
                        log.warn("保存首页更新记录失败: siteId={}, url={}", siteId, url);
                        errorCount++;
                    }

                } catch (Exception e) {
                    log.error("处理第{}条首页更新数据时发生异常", i + 1, e);
                    errorCount++;
                }
            }

            log.info("首页更新数据同步完成: 处理{}条, 保存{}条, 跳过{}条, 错误{}条",
                    processedCount, savedCount, skippedCount, errorCount);

            result.put("success", true);
            result.put("message", "同步完成");

        } catch (Exception e) {
            log.error("首页更新数据同步失败", e);
            result.put("success", false);
            result.put("message", "同步失败: " + e.getMessage());
            errorCount++;
        }

        result.put("processedCount", processedCount);
        result.put("savedCount", savedCount);
        result.put("skippedCount", skippedCount);
        result.put("errorCount", errorCount);

        return result;
    }

    @Override
    public PageResult<ChkUpdateSiteIndex> pageDetail(ChkUpdateSiteIndexQuery query) {
        try {
            Page<ChkUpdateSiteIndex> page = new Page<>(query.getPage(), query.getLimit());
            // 基于t_article表查询详细记录
            QueryWrapper<Article> wrapper = new QueryWrapper<>();
            wrapper.eq("is_index", 1);
            wrapper.lt("pub_time", LocalDate.now());
            // 添加查询条件
            if (query.getSearchType() != null && query.getSearchType() == 1) {
                wrapper.eq("channel_id", query.getChannelId());
            } else if (StrUtil.isNotBlank(query.getWebsiteId())) {
                wrapper.eq("site_id", query.getWebsiteId());
            }
            if (StrUtil.isNotBlank(query.getBeginTime())) {
                wrapper.ge("pub_time", query.getBeginTime());
            }
            if (StrUtil.isNotBlank(query.getEndTime())) {
                wrapper.le("pub_time", query.getEndTime());
            }
            wrapper.orderByDesc("pub_time");
            Page<Article> columnPage = new Page<>(query.getPage(), query.getLimit());
            IPage<Article> columnResult = articleDao.selectPage(columnPage, wrapper);
            // 转换为ChkUpdateSiteIndex格式
            List<ChkUpdateSiteIndex> indexList = columnResult.getRecords().stream()
                    .map(this::convertColumnToIndex)
                    .collect(Collectors.toList());
            // 构造返回结果
            IPage<ChkUpdateSiteIndex> result = new Page<ChkUpdateSiteIndex>(query.getPage(), query.getLimit(), columnResult.getTotal());
            result.setRecords(indexList);

            return PageResult.page(result);
        } catch (Exception e) {
            log.error("查询详细记录列表失败，返回兜底数据", e);
        }
        return PageResult.page(new Page<>());
    }


    // ==================== 数据导出 ====================

    @Override
    public void export(ChkUpdateSiteIndexQuery query, LoginUserVO user, HttpServletResponse response) throws IOException {
        try {
            log.info("用户{}导出首页更新检查记录", user.getUserId());
            List<Map<String, Object>> dataList = chkUpdateSiteIndexDao.selectExportData(query);
            ExcelExportUtil.commonExport("首页更新检查记录", new String[]{"序号", "网站名称", "网站地址", "首页是否更新", "更新天数", "连续未更新天数", "最后一次更新时间"},
                    new String[]{"id", "websiteName", "websiteIndexUrl", "status", "updateDays", "continuousNotUpdateDays", "lastUpdateTime"}, dataList, response);
        } catch (Exception e) {
            log.error("导出首页更新检查记录失败", e);
            throw new IOException("导出失败：" + e.getMessage());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delUpdateSiteIndexData(List<Long> ids, List<ChkUpdateSiteIndexResult> updateSiteIndexResults) {
        // 加锁
        lock.lock();
        try {
            if (updateSiteIndexResults != null && !updateSiteIndexResults.isEmpty()) {
                chkUpdateSiteIndexResultDao.insertBatch(updateSiteIndexResults);
            }
            if (ids != null && ids.size() == 2) {
                chkUpdateSiteIndexDao.deleteSiteIndexTaskDeleteTodayData(ids.get(0), ids.get(1));
            }
        } finally {
            // 确保释放锁
            lock.unlock();
        }
    }

    @Override
    public List<Map<String, Object>> selectSiteIndexTaskInsertTodayData() {
        return chkUpdateSiteIndexDao.selectSiteIndexTaskInsertTodayData();
    }

    @Override
    public List<Long> getWaitDeleteSiteIndexIds() {
        return chkUpdateSiteIndexDao.selectWaitDeleteResultIds();
    }

    // ==================== 兜底数据创建方法 ====================

    /**
     * 创建概览统计兜底数据
     */
    private Map<String, Object> createMockOverviewStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalWebsiteCount", 100);  // 检测网站数
        statistics.put("updatedWebsiteCount", 37); // 更新网站
        statistics.put("notUpdatedWebsiteCount", 63); // 未更新网站
        return statistics;
    }

    /**
     * 创建分页数据兜底数据
     */
    private PageResult<ChkUpdateSiteIndexVO> createMockPageData(ChkUpdateSiteIndexQuery query) {
        List<ChkUpdateSiteIndexVO> mockList = new ArrayList<>();

        String[] websites = {"河南师范大学", "中原工学院", "河南理工大学"};
        String[] urls = {"https://www.henan.edu.cn/", "https://www.zut.edu.cn/", "https://www.hpu.edu.cn/"};
        Integer[] statusArray = {1, 0, 1}; // 1已更新，0未更新

        int limit = query.getLimit() != null ? query.getLimit() : 10;
        for (int i = 1; i <= Math.min(limit, 3); i++) {
            // 根据查询条件过滤mock数据
            boolean shouldInclude = true;

            // 按分组ID过滤（mock数据假设分组ID为1）
            if (StrUtil.isNotBlank(query.getGroupId())) {
                String[] groupIds = query.getGroupId().split(",");
                boolean groupIdMatch = false;
                for (String groupId : groupIds) {
                    if ("1".equals(groupId.trim())) {
                        groupIdMatch = true;
                        break;
                    }
                }
                if (!groupIdMatch) {
                    shouldInclude = false;
                }
            }

            // 按分组名称过滤
            if (StrUtil.isNotBlank(query.getGroupName())) {
                String[] groupNames = query.getGroupName().split(",");
                boolean groupNameMatch = false;
                for (String groupName : groupNames) {
                    if ("高校网站".contains(groupName.trim())) {
                        groupNameMatch = true;
                        break;
                    }
                }
                if (!groupNameMatch) {
                    shouldInclude = false;
                }
            }

            // 按网站ID过滤
            if (StrUtil.isNotBlank(query.getWebsiteId())) {
                String[] websiteIds = query.getWebsiteId().split(",");
                boolean websiteIdMatch = false;
                for (String websiteId : websiteIds) {
                    if (String.valueOf(i).equals(websiteId.trim())) {
                        websiteIdMatch = true;
                        break;
                    }
                }
                if (!websiteIdMatch) {
                    shouldInclude = false;
                }
            }

            // 按网站名称过滤
            if (StrUtil.isNotBlank(query.getWebsiteName())) {
                String[] websiteNames = query.getWebsiteName().split(",");
                boolean websiteNameMatch = false;
                for (String websiteName : websiteNames) {
                    if (websites[i - 1].contains(websiteName.trim())) {
                        websiteNameMatch = true;
                        break;
                    }
                }
                if (!websiteNameMatch) {
                    shouldInclude = false;
                }
            }

            // 按网站URL过滤
            if (StrUtil.isNotBlank(query.getWebsiteIndexUrl())) {
                String[] websiteUrls = query.getWebsiteIndexUrl().split(",");
                boolean urlMatch = false;
                for (String url : websiteUrls) {
                    if (urls[i - 1].contains(url.trim())) {
                        urlMatch = true;
                        break;
                    }
                }
                if (!urlMatch) {
                    shouldInclude = false;
                }
            }

            if (!shouldInclude) {
                continue;
            }

            ChkUpdateSiteIndexVO vo = new ChkUpdateSiteIndexVO();
            vo.setId((long) (i + 5)); // 从6开始，对应原型图
            vo.setGroupName("高校网站"); // 分组名称
            vo.setWebsiteName(websites[i - 1]); // 网站名称
            vo.setWebsiteIndexUrl(urls[i - 1]); // 网站地址
            vo.setStatus(statusArray[i - 1]); // 首页是否更新：0未更新，1已更新

            // 更新天数列表
            List<String> updateDays = new ArrayList<>();
            if (statusArray[i - 1] == 1) {
                updateDays.add("2025-07-30");
                updateDays.add("2025-07-29");
            }
            vo.setUpdateDays(updateDays);

            // 连续未更新天数列表
            List<String> continuousNotUpdateDays = new ArrayList<>();
            if (statusArray[i - 1] == 0) {
                continuousNotUpdateDays.add("2025-07-30");
                continuousNotUpdateDays.add("2025-07-29");
                continuousNotUpdateDays.add("2025-07-28");
            }
            vo.setContinuousNotUpdateDays(continuousNotUpdateDays);

            // 解析时间
            vo.setLastParseTime(new Date(System.currentTimeMillis() - i * 24 * 60 * 60 * 1000L));

            mockList.add(vo);
        }

        return new PageResult<>(mockList, (long) mockList.size(), limit, query.getPage() != null ? query.getPage() : 1);
    }

    /**
     * 创建详情兜底数据
     */
    private ChkUpdateSiteIndexVO createMockDetailData(Long id) {
        ChkUpdateSiteIndexVO vo = new ChkUpdateSiteIndexVO();
        vo.setId(id);
        vo.setGroupName("高校网站"); // 分组名称
        vo.setWebsiteName("河南师范大学"); // 网站名称
        vo.setWebsiteIndexUrl("https://www.henan.edu.cn/"); // 网站地址
        vo.setStatus(1); // 首页是否更新：1已更新

        // 更新天数列表
        List<String> updateDays = new ArrayList<>();
        updateDays.add("2025-07-30");
        updateDays.add("2025-07-29");
        vo.setUpdateDays(updateDays);

        // 连续未更新天数列表（已更新的网站为空）
        vo.setContinuousNotUpdateDays(new ArrayList<>());

        vo.setLastParseTime(new Date()); // 解析时间
        return vo;
    }

    /**
     * 将Map转换为VO对象
     */
    /**
     * 将Map转换为VO对象
     */
    private ChkUpdateSiteIndexVO convertMapToVO(Map<String, Object> map) {
        ChkUpdateSiteIndexVO vo = new ChkUpdateSiteIndexVO();

        vo.setId(map.get("id") != null ? Long.valueOf(map.get("id").toString()) : null);
        vo.setGroupName((String) map.get("groupName"));
        vo.setWebsiteName((String) map.get("websiteName"));
        vo.setChannelName((String) map.get("channelName"));
        vo.setWebsiteIndexUrl((String) map.get("websiteIndexUrl"));
        vo.setChannelUrl((String) map.get("channelUrl"));
        Integer processType = (Integer) map.get("processType");
        vo.setProcessType(processType);
        vo.setProcessTypeName(ProcessTypeEnum.getByValue(processType).getInfo());
        vo.setStatus(map.get("status") != null ? Integer.valueOf(map.get("status").toString()) : null);
        LocalDateTime localDateTime = (LocalDateTime) map.get("lastParseTime");
        if (localDateTime != null) {
            //TODO: 最后更新时间从新闻中获取
            List<Article> articles = articleService.listByIsIndex(1, vo.getId().toString());
            //如果存在就获取第一篇
            if (articles != null && articles.size() > 0) {
                vo.setLastParseTime(articles.get(0).getPubTime());
            }
        }
        // 处理字符串到List的转换
        String updateDaysStr = (String) map.get("updateDaysStr");
        if (StrUtil.isNotBlank(updateDaysStr)) {
            // 分割字符串为列表
            List<String> daysList = Arrays.asList(updateDaysStr.split(","));
            // 转换为可修改的列表（因为Arrays.asList返回的列表不可修改）
            List<String> mutableList = new ArrayList<>(daysList);
            // 降序排序
            Collections.sort(mutableList, Collections.reverseOrder());
            vo.setUpdateDays(mutableList);
        } else {
            vo.setUpdateDays(new ArrayList<>());
        }
        String continuousNotUpdateDaysStr = (String) map.get("continuousNotUpdateDaysStr");
        if (StrUtil.isNotBlank(continuousNotUpdateDaysStr)) {
            vo.setContinuousNotUpdateDays(Arrays.asList(continuousNotUpdateDaysStr.split(",")));
        } else {
            vo.setContinuousNotUpdateDays(new ArrayList<>());
        }

        return vo;
    }

    /**
     * 将站点Map转换为ChkUpdateSiteIndexVO
     */
    private ChkUpdateSiteIndexVO convertSiteMapToVO(Map<String, Object> map) {
        ChkUpdateSiteIndexVO vo = new ChkUpdateSiteIndexVO();

        vo.setId(Long.valueOf(map.get("site_id").toString()));
        vo.setWebsiteName((String) map.get("site_name"));
        vo.setGroupName("政府网站"); // 默认分组
        vo.setWebsiteIndexUrl("https://www.example.gov.cn"); // 默认首页地址

        // 判断是否更新（最近1天有发布内容）
        Object lastPubTime = map.get("last_pub_time");
        if (lastPubTime != null) {
            Date pubDate = convertToDate(lastPubTime);
            long daysDiff = (System.currentTimeMillis() - pubDate.getTime()) / (24 * 60 * 60 * 1000);
            vo.setStatus(daysDiff <= 1 ? 1 : 0);

            // 设置更新天数
            List<String> updateDays = new ArrayList<>();
            if (daysDiff <= 1) {
                updateDays.add(new SimpleDateFormat("yyyy-MM-dd").format(pubDate));
                updateDays.add(new SimpleDateFormat("yyyy-MM-dd").format(new Date(pubDate.getTime() - 24 * 60 * 60 * 1000)));
            }
            vo.setUpdateDays(updateDays);

            // 设置连续未更新天数
            List<String> continuousNotUpdateDays = new ArrayList<>();
            if (daysDiff > 1) {
                for (int i = 1; i <= Math.min(daysDiff, 7); i++) {
                    continuousNotUpdateDays.add(new SimpleDateFormat("yyyy-MM-dd")
                            .format(new Date(System.currentTimeMillis() - i * 24 * 60 * 60 * 1000)));
                }
            }
            vo.setContinuousNotUpdateDays(continuousNotUpdateDays);
        } else {
            vo.setStatus(0);
            vo.setUpdateDays(new ArrayList<>());
            vo.setContinuousNotUpdateDays(new ArrayList<>());
        }

        vo.setLastParseTime(convertToDate(map.get("last_update_time")));

        return vo;
    }

    /**
     * 将时间对象转换为Date类型
     * 处理LocalDateTime和Date类型的转换
     */
    private Date convertToDate(Object timeObj) {
        if (timeObj == null) {
            return null;
        }

        if (timeObj instanceof Date) {
            return (Date) timeObj;
        } else if (timeObj instanceof LocalDateTime) {
            LocalDateTime localDateTime = (LocalDateTime) timeObj;
            return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
        } else {
            // 如果是其他类型，返回null
            return null;
        }
    }

    /**
     * 生成数据唯一签名用于去重
     *
     * @param data 原始数据字符串
     * @return MD5签名
     */
    private String generateSign(String data) {
        try {
            java.security.MessageDigest md = java.security.MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(data.getBytes("UTF-8"));
            StringBuilder sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (Exception e) {
            log.error("生成签名失败", e);
            return String.valueOf(data.hashCode());
        }
    }

    // 处理单个URL并提取域名的方法
    private static String extractDomain(String url) {
        if (url == null) {
            return null;
        }
        // 处理http://开头的情况
        if (url.startsWith("http://")) {
            return url.substring("http://".length());
        }
        // 处理https://开头的情况
        else if (url.startsWith("https://")) {
            return url.substring("https://".length());
        }
        // 对于没有协议前缀的URL，直接返回
        return url;
    }

    // 转换方法
    public Map<String, Website> convertToMap(List<Website> websites) {
        return websites.stream()
                .filter(w -> w.getWebUrl() != null)
                // 使用处理单个URL的extractDomain方法
                .collect(Collectors.toMap(
                        el -> extractDomain(el.getWebUrl()),  // 注意方法名从extractDomains改为extractDomain
                        w -> w,
                        (existing, replacement) -> existing
                ));
    }

    private String offsetDateTime(String timeStr) {
        System.out.println("=== 方法1: OffsetDateTime ===");

        // 解析原始时间字符串
        OffsetDateTime originalTime = OffsetDateTime.parse(timeStr);
        System.out.println("原始时间: " + originalTime);

        // 加8小时
        OffsetDateTime newTime = originalTime.plusHours(8);

        // 格式化为目标格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String result = newTime.format(formatter);

        System.out.println("转换后: " + result);
        return result;
    }

    /**
     * 将Article转换为ChkUpdateSiteIndex
     */
    private ChkUpdateSiteIndex convertColumnToIndex(Article column) {
        ArticleContent articleContent = articleContentDao.selectById(column.getId());
        Channel channel = channelDao.selectById(column.getChannelId());
        ChkUpdateSiteIndex index = new ChkUpdateSiteIndex();

        index.setId(column.getId());
        index.setWebsiteId(Long.valueOf(channel.getSiteId()));
        index.setWebsiteName(channel.getSiteName());
        Website website = websiteService.getById(index.getWebsiteId());
        index.setWebsiteIndexUrl(website.getWebUrl()); // 默认首页地址
        index.setArticleTitle(articleContent.getTitle());
        index.setArticleContent(articleContent.getContent());
        index.setArticleUrl(column.getArticleUrl());
        index.setArticlePublishTime(column.getPubTime());
        index.setParseTime(column.getCollectTime());
        index.setCreateTime(column.getCreateTime());
        index.setIsDel(false);

        return index;
    }

    /**
     * 构建不包含时间条件的查询条件
     */
    private QueryWrapper<Article> buildNonTimeQueryWrapper(ChkUpdateSiteIndexQuery query) {
        QueryWrapper<Article> wrapper = new QueryWrapper<>();

        // 网站ID条件（支持逗号分隔的多个ID）
        if (StrUtil.isNotBlank(query.getWebsiteId())) {
            String[] siteIds = query.getWebsiteId().split(",");
            List<String> siteIdList = Arrays.stream(siteIds)
                    .map(String::trim)
                    .filter(StrUtil::isNotBlank)
                    .collect(Collectors.toList());
            if (!siteIdList.isEmpty()) {
                wrapper.in("site_id", siteIdList);
            }
        }

        // 分组ID条件（通过关联查询）
        if (StrUtil.isNotBlank(query.getGroupId())) {
            String[] groupIds = query.getGroupId().split(",");
            List<String> groupIdList = Arrays.stream(groupIds)
                    .map(String::trim)
                    .filter(StrUtil::isNotBlank)
                    .collect(Collectors.toList());
            if (!groupIdList.isEmpty()) {
                wrapper.exists("SELECT 1 FROM t_website w LEFT JOIN t_website_group wg ON w.group_id = wg.id " +
                                "WHERE w.site_id = t_article.site_id AND wg.id IN ({0})",
                        String.join(",", groupIdList));
            }
        }

        // 分组名称条件（通过关联查询）
        if (StrUtil.isNotBlank(query.getGroupName())) {
            String[] groupNames = query.getGroupName().split(",");
            List<String> groupNameList = Arrays.stream(groupNames)
                    .map(String::trim)
                    .filter(StrUtil::isNotBlank)
                    .collect(Collectors.toList());
            if (!groupNameList.isEmpty()) {
                wrapper.exists("SELECT 1 FROM t_website w LEFT JOIN t_website_group wg ON w.group_id = wg.id " +
                                "WHERE w.site_id = t_article.site_id AND wg.group_name IN ({0})",
                        groupNameList.stream().map(name -> "'" + name + "'").collect(Collectors.joining(",")));
            }
        }

        // 注意：这里不添加时间范围条件

        return wrapper;
    }

    /**
     * 构建基础查询条件（包含时间条件）
     */
    private QueryWrapper<Article> buildBaseQueryWrapper(ChkUpdateSiteIndexQuery query) {
        QueryWrapper<Article> wrapper = new QueryWrapper<>();

        // 网站ID条件（支持逗号分隔的多个ID）
        if (StrUtil.isNotBlank(query.getWebsiteId())) {
            String[] siteIds = query.getWebsiteId().split(",");
            List<String> siteIdList = Arrays.stream(siteIds)
                    .map(String::trim)
                    .filter(StrUtil::isNotBlank)
                    .collect(Collectors.toList());
            if (!siteIdList.isEmpty()) {
                wrapper.in("site_id", siteIdList);
            }
        }

        // 分组ID条件（通过关联查询）
        if (StrUtil.isNotBlank(query.getGroupId())) {
            String[] groupIds = query.getGroupId().split(",");
            List<String> groupIdList = Arrays.stream(groupIds)
                    .map(String::trim)
                    .filter(StrUtil::isNotBlank)
                    .collect(Collectors.toList());
            if (!groupIdList.isEmpty()) {
                wrapper.exists("SELECT 1 FROM t_website w LEFT JOIN t_website_group wg ON w.group_id = wg.id " +
                                "WHERE w.site_id = t_article.site_id AND wg.id IN ({0})",
                        String.join(",", groupIdList));
            }
        }

        // 分组名称条件（通过关联查询）
        if (StrUtil.isNotBlank(query.getGroupName())) {
            String[] groupNames = query.getGroupName().split(",");
            List<String> groupNameList = Arrays.stream(groupNames)
                    .map(String::trim)
                    .filter(StrUtil::isNotBlank)
                    .collect(Collectors.toList());
            if (!groupNameList.isEmpty()) {
                wrapper.exists("SELECT 1 FROM t_website w LEFT JOIN t_website_group wg ON w.group_id = wg.id " +
                                "WHERE w.site_id = t_article.site_id AND wg.group_name IN ({0})",
                        groupNameList.stream().map(name -> "'" + name + "'").collect(Collectors.joining(",")));
            }
        }

        // 时间范围条件
        if (StrUtil.isNotBlank(query.getBeginTime())) {
            wrapper.ge("pub_time", query.getBeginTime());
        }
        if (StrUtil.isNotBlank(query.getEndTime())) {
            wrapper.le("pub_time", query.getEndTime());
        }

        return wrapper;
    }

    /**
     * 统计检测状态分布
     */
    private Map<String, Long> getCheckStatusStatistics(QueryWrapper<Article> baseWrapper) {
        Map<String, Long> statusStats = new HashMap<>();

        try {
            // 统计正常状态的记录数
            QueryWrapper<Article> normalWrapper = baseWrapper.clone();
            normalWrapper.eq("is_del", 0).eq("check_status", 1);
            long normalCount = articleDao.selectCount(normalWrapper);

            // 统计异常状态的记录数
            QueryWrapper<Article> abnormalWrapper = baseWrapper.clone();
            abnormalWrapper.eq("is_del", 0).eq("check_status", 2);
            long abnormalCount = articleDao.selectCount(abnormalWrapper);

            // 统计不可访问状态的记录数
            QueryWrapper<Article> inaccessibleWrapper = baseWrapper.clone();
            inaccessibleWrapper.eq("is_del", 0).eq("check_status", 3);
            long inaccessibleCount = articleDao.selectCount(inaccessibleWrapper);

            // 统计未检测状态的记录数
            QueryWrapper<Article> uncheckWrapper = baseWrapper.clone();
            uncheckWrapper.eq("is_del", 0).and(w -> w.isNull("check_status").or().eq("check_status", 0));
            long uncheckCount = articleDao.selectCount(uncheckWrapper);

            statusStats.put("normalCount", normalCount);
            statusStats.put("abnormalCount", abnormalCount);
            statusStats.put("inaccessibleCount", inaccessibleCount);
            statusStats.put("uncheckCount", uncheckCount);

            // 计算总检测记录数
            long totalCheckCount = normalCount + abnormalCount + inaccessibleCount + uncheckCount;
            statusStats.put("totalCheckCount", totalCheckCount);

        } catch (Exception e) {
            log.error("统计检测状态分布失败", e);
            // 返回默认值
            statusStats.put("normalCount", 0L);
            statusStats.put("abnormalCount", 0L);
            statusStats.put("inaccessibleCount", 0L);
            statusStats.put("uncheckCount", 0L);
            statusStats.put("totalCheckCount", 0L);
        }

        return statusStats;
    }

}
