package cn.dahe.service.impl;

import cn.dahe.dao.WarnPlanDao;
import cn.dahe.dao.WarnPlanPlatformDao;
import cn.dahe.dao.WarnPlanPushUserDao;
import cn.dahe.dao.WarnUserDao;
import cn.dahe.entity.WarnPlan;
import cn.dahe.entity.WarnPlanPlatform;
import cn.dahe.entity.WarnPlanPushUser;
import cn.dahe.entity.WarnUser;
import cn.dahe.enums.WarnDetailRelateTypeEnum;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.WarnPlanDto;
import cn.dahe.model.query.WarnPlanQuery;
import cn.dahe.model.request.WarnPlanPlatformParam;
import cn.dahe.model.request.WarnPlanPushUserParam;
import cn.dahe.model.request.WarnPlanSaveRequest;
import cn.dahe.model.vo.WarnPlanVO;
import cn.dahe.service.WarnPlanPlatformService;
import cn.dahe.service.WarnPlanService;
import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class WarnPlanServiceImpl extends ServiceImpl<WarnPlanDao, WarnPlan> implements WarnPlanService {

    private final WarnPlanDao warnPlanDao;
    private final WarnPlanPlatformDao platformDao;
    private final WarnPlanPushUserDao pushUserDao;
    private final WarnUserDao warnUserDao;
    private final WarnPlanPushUserDao warnPlanPushUserDao;

    private final WarnPlanPlatformService warnPlanPlatformService;

    @Override
    public PageResult<WarnPlanVO> page(WarnPlanQuery query) {
        PageHelper.startPage(query.getPage(), query.getLimit());
        List<WarnPlanVO> list = warnPlanDao.list(query);
        PageInfo<WarnPlanVO> pageInfo = new PageInfo<>(list);
        return PageResult.page(pageInfo);
    }

    @Override
    public WarnPlanVO get(Integer id) {
        return warnPlanDao.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer create(WarnPlanSaveRequest warnPlanSaveRequest) {
        WarnPlanDto warnPlan = warnPlanSaveRequest.getWarnPlan();
        return create(BeanUtil.copyProperties(warnPlan, WarnPlan.class, "id"), warnPlanSaveRequest.getPlatforms(), warnPlanSaveRequest.getPushUsers());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer create(WarnPlan warnPlan, List<WarnPlanPlatformParam> platforms, List<WarnPlanPushUserParam> pushUsers) {
        Date now = new Date();
        warnPlan.setCreateTime(now);
        warnPlan.setUpdateTime(now);
        warnPlan.setIsDeleted(false);
        this.save(warnPlan);

        Integer planId = warnPlan.getId();

        warnPlanPlatformService.savePlatforms(planId, platforms);
        savePushUsers(planId, pushUsers);

        return planId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(WarnPlanSaveRequest warnPlanSaveRequest) {
        WarnPlanDto warnPlan = warnPlanSaveRequest.getWarnPlan();
        return update(BeanUtil.copyProperties(warnPlan, WarnPlan.class), warnPlanSaveRequest.getPlatforms(), warnPlanSaveRequest.getPushUsers());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(WarnPlan warnPlan, List<WarnPlanPlatformParam> platforms, List<WarnPlanPushUserParam> pushUsers) {
        Integer id = warnPlan.getId();
        if (id == null) {
            return false;
        }
        warnPlan.setId(id);
        warnPlan.setUpdateTime(new Date());
        this.updateById(warnPlan);

        platformDao.deleteByWarnPlanId(id);
        pushUserDao.deleteByWarnPlanId(id);

        warnPlanPlatformService.savePlatforms(id, platforms);
        savePushUsers(id, pushUsers);

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean delete(Integer id) {
        this.removeById(id);
        platformDao.deleteByWarnPlanId(id);
        pushUserDao.deleteByWarnPlanId(id);
        return true;
    }

    @Override
    public List<WarnPlan> listAll() {
        return warnPlanDao.listAll();
    }

    @Override
    public List<WarnPlanPushUser> getPushUserByWarnPlanId(Integer warnPlanId) {
        return warnPlanPushUserDao.listByWarnPlanId(warnPlanId);
    }

    @Override
    public List<Integer> getSiteIdsByWarnPlanId(Integer warnPlanId) {
        List<WarnPlanPlatform> warnPlanPlatforms = platformDao.listByWarnPlanId(warnPlanId);
        if (warnPlanPlatforms == null || warnPlanPlatforms.isEmpty()) {
            return Collections.emptyList();
        }
        List<Integer> siteIds = warnPlanPlatforms.stream().filter(el -> el.getPlatformType() == 0 && el.getWebsiteId() != null).map(el -> el.getWebsiteId().intValue()).collect(Collectors.toList());
        return siteIds;
    }

    @Override
    public List<Long> listAvailablePlanWithCondition(Long websiteId, WarnDetailRelateTypeEnum relateType) {
        return this.getBaseMapper().listAvailablePlanWithCondition(websiteId, relateType.getValue());
    }


    private void savePushUsers(Integer planId, List<WarnPlanPushUserParam> pushUsers) {
        if (pushUsers == null || pushUsers.isEmpty()) {
            return;
        }
        for (WarnPlanPushUserParam param : pushUsers) {
            if (param == null || param.getUserId() == null) {
                continue;
            }
            WarnPlanPushUser u = new WarnPlanPushUser();
            u.setWarnPlanId((long)planId);
            u.setUserId(param.getUserId());
            WarnUser warnUser = warnUserDao.selectById(param.getUserId());
            if (warnUser == null) {
                continue;
            }
            u.setUserName(warnUser.getUserName());
            u.setPushType(param.getPushType());
            u.setCreateTime(new Date());
            pushUserDao.insert(u);
        }
    }
}


