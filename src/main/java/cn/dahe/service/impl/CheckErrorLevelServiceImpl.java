package cn.dahe.service.impl;

import cn.dahe.dao.CheckErrorLevelDao;
import cn.dahe.entity.CheckErrorLevel;
import cn.dahe.service.CheckErrorLevelService;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * 内容错误等级Service实现类
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Service
public class CheckErrorLevelServiceImpl extends BaseServiceImpl<CheckErrorLevelDao, CheckErrorLevel> implements CheckErrorLevelService {

    @PostConstruct
    private void postConstruct() {
        refreshErrorLevelNameCache();
    }

    @Override
    public void refreshErrorLevelNameCache() {
        List<CheckErrorLevel> list = this.list();
        // 清空并更新缓存
        TYPE_LEVEL_CACHE.clear();
        list.forEach(item -> TYPE_LEVEL_CACHE.put(item.getId(), item.getLevelName()));
    }

}