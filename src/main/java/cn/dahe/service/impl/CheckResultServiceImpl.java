package cn.dahe.service.impl;

import cn.dahe.check.html.ErrorWordContext;
import cn.dahe.check.html.HtmlMappingUtil;
import cn.dahe.check.processor.ContentCheckProcessor;
import cn.dahe.dao.CheckResultDao;
import cn.dahe.entity.CheckResult;
import cn.dahe.entity.CheckWord;
import cn.dahe.enums.*;
import cn.dahe.model.dto.CheckApiExecuteDto;
import cn.dahe.model.dto.CheckResultDto;
import cn.dahe.model.query.CheckResultQuery;
import cn.dahe.model.vo.CheckResultVO;
import cn.dahe.model.vo.CheckVO;
import cn.dahe.model.vo.ErrorLevelCheckResultCountVO;
import cn.dahe.service.CheckResultService;
import cn.dahe.service.CheckWordService;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.nodes.Element;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 内容错误详情Service实现类
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Slf4j
@Service
public class CheckResultServiceImpl extends BaseServiceImpl<CheckResultDao, CheckResult> implements CheckResultService {

    @Resource
    private ContentCheckProcessor httpContentCheckProcessor;
    @Resource
    private CheckWordService checkWordService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveCheckResults(Long checkId, ArticleLocationEnum articleLocation, List<CheckResultDto> checkResultDtoList, HtmlMappingUtil.HtmlMappingResult mappingResult) {
        if (CollUtil.isEmpty(checkResultDtoList)) {
            return;
        }
        String checkPlainText = mappingResult.getPlainText();
        // 获取或创建分句列表（根据位置复用）
        List<HtmlMappingUtil.SentenceLocation> sentences = HtmlMappingUtil.splitSentences(checkPlainText);
        List<CheckResult> checkResults = CollUtil.newArrayList();
        Map<String, CheckWord> wordMap = checkWordService.batchGetOrCreateCheckWords(checkResultDtoList);
        for (CheckResultDto checkDto : checkResultDtoList) {
            CheckResult checkResult = new CheckResult();
            checkResult.setCheckId(checkId);
            checkResult.setArticleLocation(articleLocation.getValue());
            //  异步任务更新
            String errorWord = checkDto.getErrorWord();
            checkResult.setErrorWord(errorWord);
            checkResult.setSuggestWord(checkDto.getSuggestWord());
            checkResult.setErrorType(checkDto.getErrorType().getValue());
            checkResult.setErrorLevel(checkDto.getErrorLevel().getValue());
            checkResult.setPosition(checkDto.getPosition());
            checkResult.setWordId(wordMap.get(errorWord).getId());
            // 根据是标题还是正文，使用对应的映射关系更新HTML位置
            Integer position = checkDto.getPosition();
            checkResult.setPosition(position);
            HtmlMappingUtil.HtmlWordLocation location = HtmlMappingUtil.findInHtml(errorWord, position, mappingResult);
            if (location != null) {
                checkResult.setHtmlPosition(location.getStartPosition());
                checkResult.setHtmlErrorWord(location.getHtmlErrorWord());
            } else {
                //  找不到则跳过
                continue;
            }
            // 获取或创建分句列表（根据位置复用）
            ErrorWordContext errorWordContext = HtmlMappingUtil.getErrorWordContext(sentences, position, errorWord.length());
            if (errorWordContext != null) {
                checkResult.setContext(errorWordContext.getContext());
                checkResult.setContextPosition(errorWordContext.getRelativePosition());
            }
            checkResults.add(checkResult);
        }
        this.saveBatch(checkResults);
    }

    @Override
    public List<Long> confirmResultIdsFromArticle(Long articleId, List<Long> resultIds) {
        return this.getBaseMapper().listResultIdsFromArticle(articleId, resultIds);
    }

    private CheckResult makeCheckResult(Long checkId, ArticleLocationEnum articleLocation, Map<String, CheckWord> wordMap, CheckResultDto checkDto, HtmlMappingUtil.HtmlMappingResult result, List<HtmlMappingUtil.SentenceLocation> sentences) {
        CheckResult check = new CheckResult();
        check.setCheckId(checkId);
        check.setArticleLocation(articleLocation.getValue());

        String errorWord = checkDto.getErrorWord();
        CheckWord checkWord = wordMap.get(errorWord);
        check.setWordId(checkWord.getId());
        // 根据是标题还是正文，使用对应的映射关系更新HTML位置
        HtmlMappingUtil.HtmlWordLocation location;
        Integer position = checkDto.getPosition();
        check.setPosition(position);
        location = HtmlMappingUtil.findInHtml(errorWord, position, result);
        if (location != null) {
            check.setHtmlPosition(location.getStartPosition());
            check.setHtmlErrorWord(location.getHtmlErrorWord());
        } else {
            //  标记失败则跳过
            return null;
        }
        // 获取或创建分句列表（根据位置复用）
        ErrorWordContext errorWordContext = HtmlMappingUtil.getErrorWordContext(sentences, position, errorWord.length());
        if (errorWordContext != null) {
            check.setContext(errorWordContext.getContext());
            check.setContextPosition(errorWordContext.getRelativePosition());
        }
        return check;
    }

    @Override
    public boolean updateAuditStatus(List<Long> resultIds, AuditStatusEnum auditStatus) {
        return this.lambdaUpdate()
                .in(CheckResult::getId, resultIds)
                .set(CheckResult::getAuditStatus, auditStatus.getValue())
                .update();
    }

    @Override
    public List<CheckResultVO> listCheckResultByTaskAndCheckQuery(Long taskId, CheckResultQuery query) {
        //  默认只查正常结果
        query.setResultDisplayType(CheckResultDisplayTypeEnum.NORMAL.getValue());
        return this.getBaseMapper().listCheckResultByTaskId(taskId, query);
    }

    @Override
    public List<CheckResultVO> listSnapshotCheckResultByTask(Long taskId, CheckTaskRelationTypeEnum relationType) {
        //  默认只查正常结果
        CheckResultQuery query = new CheckResultQuery();
        if (relationType.equals(CheckTaskRelationTypeEnum.ATTACHMENT)) {
            query.setResultDisplayType(CheckResultDisplayTypeEnum.NORMAL.getValue());
        } else {
            query.setResultDisplayType(CheckResultDisplayTypeEnum.SNAPSHOT.getValue());
        }
        return this.getBaseMapper().listCheckResultByTaskId(taskId, query);
    }

    public List<CheckResultVO> getErrorObjsByTaskId(Long taskId) {
        CheckResultQuery query = new CheckResultQuery();
        query.setResultDisplayType(CheckResultDisplayTypeEnum.NORMAL.getValue());
        List<CheckResultVO> errorObjs = this.getBaseMapper().listCheckResultByTaskId(taskId, query);
        return errorObjs;
    }

    @Override
    public <T extends CheckVO, U extends CheckResultQuery> void markCheckContentWithHtml(T vo, U query, boolean doMark, boolean onlyMark) {
        Long taskId = vo.getTaskId();
        query.setResultDisplayType(CheckResultDisplayTypeEnum.NORMAL.getValue());
        List<CheckResultVO> errorObjs = this.getBaseMapper().listCheckResultByTaskId(taskId, query);
        vo.setErrorObjs(errorObjs);

        // 计算错误级别统计
        ErrorLevelCheckResultCountVO errorLevelCheckResultCountVO = calculateErrorLevelStats(errorObjs);
        vo.setErrorLevelCheckResultCountStats(errorLevelCheckResultCountVO);

        if (CollUtil.isNotEmpty(errorObjs) && doMark) {
            // 按位置分组错误
            Map<ArticleLocationEnum, List<CheckResultVO>> errorMap = errorObjs.stream()
                    .collect(Collectors.groupingBy(
                            e -> ArticleLocationEnum.getByValue(e.getArticleLocation())
                    ));

            // 处理标题
            String[] markedTitles = markErrorsInContent(
                    vo.getHtmlTitle(),
                    vo.getCleanedTitle(),
                    errorMap.getOrDefault(ArticleLocationEnum.TITLE, Collections.emptyList()),
                    ArticleLocationEnum.TITLE.getDesc()
            );
            vo.setMarkedHtmlTitle(markedTitles[0]);
            vo.setMarkedCleanTitle(markedTitles[1]);

            // 处理正文
            String[] markedContents = markErrorsInContent(
                    vo.getHtmlContent(),
                    vo.getCleanedContent(),
                    errorMap.getOrDefault(ArticleLocationEnum.CONTENT, Collections.emptyList()),
                    ArticleLocationEnum.CONTENT.getDesc()
            );
            vo.setMarkedHtmlContent(markedContents[0]);
            vo.setMarkedCleanContent(markedContents[1]);
        }
        if (onlyMark) {
            vo.setCleanedContent(null);
            vo.setCleanedTitle(null);
            vo.setHtmlContent(null);
            vo.setHtmlTitle(null);
            vo.setHtmlCode(null);
        }
    }

    @Override
    public <T extends CheckVO, U extends CheckResultQuery> void markCheckContentWithHtml(T vo, U query) {
        markCheckContentWithHtml(vo, query, true, false);
    }

    @Override
    public <T extends CheckVO, U extends CheckResultQuery> void markCheckContentWithHtmlCode(T vo, U query) {
        Long taskId = vo.getTaskId();
        query.setResultDisplayType(CheckResultDisplayTypeEnum.SNAPSHOT.getValue());
        List<CheckResultVO> errorObjs = this.getBaseMapper().listCheckResultByTaskId(taskId, query);
        vo.setErrorObjs(errorObjs);
        // 计算错误级别统计
        ErrorLevelCheckResultCountVO errorLevelCheckResultCountVO = calculateErrorLevelStats(errorObjs);
        vo.setErrorLevelCheckResultCountStats(errorLevelCheckResultCountVO);
    }


    @Override
    public void removeByCheckId(Long checkId) {
        this.lambdaUpdate().eq(CheckResult::getCheckId, checkId)
                .remove();
    }

    @Override
    public void clearHistoryCheckResult(Long checkId) {
        this.lambdaUpdate()
                .eq(CheckResult::getCheckId, checkId)
                .remove();
    }

    @Override
    public List<CheckResult> listByCheckId(Long checkId) {
        return this.lambdaQuery().eq(CheckResult::getCheckId, checkId)
                .in(CheckResult::getArticleLocation, ArticleLocationEnum.CONTENT.getValue(), ArticleLocationEnum.TITLE.getValue())
                .list();
    }


    @Override
    public CheckApiExecuteDto executeContentCheck(CheckStrategyEnum checkStrategy, String text) {
        ContentCheckProcessor processor = httpContentCheckProcessor;
        return processor.process(checkStrategy, text);
    }


    /**
     * 获取并标记错词的上下文
     *
     * @param sentences 已分好的句子列表
     * @return 包含原始上下文和标记后上下文的对象
     */
    public static HtmlMappingUtil.MarkedErrorContext getMarkedErrorContext(List<HtmlMappingUtil.SentenceLocation> sentences, CheckWord checkResult, Integer position) {
        String errorWord = checkResult.getErrorWord();
        // 1. 获取错词上下文
        ErrorWordContext errorContext = HtmlMappingUtil.getErrorWordContext(sentences, position, errorWord.length());
        if (errorContext == null) {
            return null;
        }
        // 2. 在上下文中标记错词
        StringBuilder context = new StringBuilder(errorContext.getContext());
        int relativePos = errorContext.getRelativePosition();


        return new HtmlMappingUtil.MarkedErrorContext(
                errorContext.getContext(),
                errorContext.getContextStart(),
                errorContext.getContextEnd()
        );
    }


    /**
     * 计算错误级别统计
     *
     * @param errorObjs 错误对象列表
     * @return ErrorLevelCheckCountVO 错误级别统计对象
     */
    private ErrorLevelCheckResultCountVO calculateErrorLevelStats(List<CheckResultVO> errorObjs) {
        ErrorLevelCheckResultCountVO stats = new ErrorLevelCheckResultCountVO();
        if (CollUtil.isEmpty(errorObjs)) {
            return stats;
        }

        stats.setCheckResultCount(errorObjs.size());

        // 使用Stream API按错误级别分组统计
        Map<Integer, Long> levelCounts = errorObjs.stream()
                .collect(Collectors.groupingBy(
                        CheckResultVO::getErrorLevel,
                        Collectors.counting()
                ));

        // 设置各级别错误数量
        stats.setLv1CheckResultCount(levelCounts.getOrDefault(1, 0L).intValue());
        stats.setLv2CheckResultCount(levelCounts.getOrDefault(2, 0L).intValue());
        stats.setLv3CheckResultCount(levelCounts.getOrDefault(3, 0L).intValue());
        stats.setLv4CheckResultCount(levelCounts.getOrDefault(4, 0L).intValue());
        stats.setLv5CheckResultCount(levelCounts.getOrDefault(5, 0L).intValue());

        return stats;
    }

    private String markWord(String wrongWord, CheckResultVO error, boolean isHtml) {
        Element errorWordSpan = new Element("span")
                .id(StrUtil.format("sensitive_check_{}", error.getResultId()))
                .attr("class", StrUtil.format("sensitive_lv{}_word", error.getErrorLevel()))
                .attr("data-check-id", error.getResultId().toString());
        Element errorRemarkSpan = new Element("span")
                .attr("class", StrUtil.format("sensitive_lv{}_remark", error.getErrorLevel()))

                .attr("data-check-id", error.getResultId().toString());
        String remarkText = StrUtil.format("({};{};{})",
                error.getErrorLevelName(),
                error.getErrorTypeName(),
                StrUtil.isBlankIfStr(error.getSuggestWord()) ? StrUtil.EMPTY : StrUtil.format("建议改为:{};", error.getSuggestWord())
        );
        String errorRemarkTag = errorRemarkSpan.text(remarkText).outerHtml();
        return (isHtml ? errorWordSpan.html(wrongWord).outerHtml() : errorWordSpan.text(wrongWord).outerHtml())
                + errorRemarkTag;
    }

    /**
     * 在内容中标记错误
     *
     * @param htmlContent   HTML内容
     * @param plainContent  纯文本内容
     * @param errors        错误列表
     * @param errorLocation 标题或者正文
     * @return 标记后的内容数组 [标记后的HTML内容, 标记后的纯文本内容]
     */
    public String[] markErrorsInContent(String htmlContent, String plainContent, List<CheckResultVO> errors, String errorLocation) {
        if (StringUtils.isBlank(htmlContent) || StringUtils.isBlank(plainContent) || CollUtil.isEmpty(errors) || StringUtils.isBlank(errorLocation)) {
            return new String[]{htmlContent, plainContent};
        }

        // 按照位置倒序排序，这样从后向前处理就不会影响前面的位置
        errors.sort((a, b) -> b.getPosition().compareTo(a.getPosition()));

        StringBuilder htmlResult = new StringBuilder(htmlContent);
        StringBuilder plainResult = new StringBuilder(plainContent);
        // 从错误总数开始倒序计数
        int totalErrors = errors.size();

        for (CheckResultVO error : errors) {
            // 处理HTML内容
            String htmlErrorWord = error.getHtmlErrorWord();
            Integer htmlPos = error.getHtmlPosition();
            // 找到错误词在HTML中的实际结束位置
            int htmlEndPos = htmlPos + htmlErrorWord.length();
            htmlResult.replace(htmlPos, htmlEndPos, markWord(htmlErrorWord, error, true));

            // 处理纯文本内容
            String errorWord = error.getErrorWord();
            Integer plainPos = error.getPosition();
            // 找到错误词在HTML中的实际结束位置
            int plainEndPos = plainPos + errorWord.length();
            plainResult.replace(plainPos, plainEndPos, markWord(errorWord, error, false));
        }

        return new String[]{htmlResult.toString(), plainResult.toString()};
    }


    public String markText(String content, List<CheckResultVO> errors, boolean isHtml, Function<CheckResultVO, Pair<String, Integer>> getMarkWord) {
        if (StringUtils.isEmpty(content) || CollUtil.isEmpty(errors)) {
            return content;
        }
        // 按照位置倒序排序，这样从后向前处理就不会影响前面的位置
        errors.sort((a, b) -> b.getPosition().compareTo(a.getPosition()));
        StringBuilder textResult = new StringBuilder(content);
        for (CheckResultVO error : errors) {
            Pair<String, Integer> pair = getMarkWord.apply(error);
            String word = pair.getKey();
            Integer pos = pair.getValue();
            int endPos = pos + word.length();
            textResult.replace(pos, endPos, markWord(word, error, isHtml));
        }
        return textResult.toString();
    }

    @Override
    public List<CheckResult> listUnrelatedWordResult() {
        return this.getBaseMapper().listUnrelatedWordResult();
    }

    @Override
    public void updateUnrelatedCheckWordResult() {
        this.getBaseMapper().updateUnrelatedCheckWordResult();
    }


}