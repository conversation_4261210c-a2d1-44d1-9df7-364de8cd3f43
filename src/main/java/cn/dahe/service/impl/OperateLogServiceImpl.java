package cn.dahe.service.impl;

import cn.dahe.dao.OperateLogDao;
import cn.dahe.model.dto.PageResult;
import cn.dahe.entity.OperateLog;
import cn.dahe.model.query.OperateLogQuery;
import cn.dahe.service.OperateLogService;
import cn.dahe.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 *
 */
@Service
@AllArgsConstructor
public class OperateLogServiceImpl extends BaseServiceImpl<OperateLogDao, OperateLog> implements OperateLogService {


    @Override
    public PageResult<OperateLog> page(OperateLogQuery query) {
        IPage<OperateLog> dataPage = baseMapper.selectPage(new Page(query.getPage(), query.getLimit()), getWrapper(query));
        return new PageResult<>(dataPage.getRecords(), dataPage);
    }

    @Override
    public Long countByRequestUrlAndTime(String beginTime, String endTime, String url) {
        OperateLogQuery operateLogQuery = new OperateLogQuery();
        operateLogQuery.setBeginTime(beginTime);
        operateLogQuery.setEndTime(endTime);
        operateLogQuery.setRequestUrl(url);
        QueryWrapper<OperateLog> wrapper = getWrapper(operateLogQuery);
        return count(wrapper);
    }


    private QueryWrapper<OperateLog> getWrapper(OperateLogQuery query) {
        QueryWrapper<OperateLog> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(query.getBeginTime())) {
            queryWrapper.ge("start_time", query.getBeginTime());
        }
        if (StringUtils.isNotBlank(query.getEndTime())) {
            queryWrapper.le("start_time", query.getEndTime());
        }
        if (StringUtils.isNotBlank(query.getRequestUrl())) {
            queryWrapper.like("request_url", query.getRequestUrl());
        }
        if (StringUtils.isNotBlank(query.getKeyword())) {
            queryWrapper.like("user_name", query.getKeyword());
        }
        queryWrapper.orderByDesc("start_time");
        return queryWrapper;
    }

}