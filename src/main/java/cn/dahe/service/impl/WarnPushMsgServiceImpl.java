package cn.dahe.service.impl;

import cn.dahe.dao.WarnPushMsgDao;
import cn.dahe.entity.WarnPushMsg;
import cn.dahe.service.WarnPushMsgService;
import cn.dahe.utils.DateUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class WarnPushMsgServiceImpl extends ServiceImpl<WarnPushMsgDao, WarnPushMsg> implements WarnPushMsgService {
    @Override
    public WarnPushMsg makeWarnPushMsg(Integer detailType, Long detailId, String type, Date time, String content, String link) {
        WarnPushMsg warnPushMsg = new WarnPushMsg();
        warnPushMsg.setDetailType(detailType);
        warnPushMsg.setDetailId(detailId);
        warnPushMsg.setType(type);
        warnPushMsg.setTime(DateUtil.formate(time));
        warnPushMsg.setContent(content);
        warnPushMsg.setLink(link);
        this.save(warnPushMsg);
        return warnPushMsg;
    }
}
