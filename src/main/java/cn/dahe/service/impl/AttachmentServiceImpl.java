package cn.dahe.service.impl;

import cn.dahe.check.exception.ContentGetException;
import cn.dahe.common.auth.SecurityUtils;
import cn.dahe.common.component.FileConverter;
import cn.dahe.dao.AttachmentDao;
import cn.dahe.entity.Article;
import cn.dahe.entity.Attachment;
import cn.dahe.model.dto.AttachmentContentDTO;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.WarnCheckDetailDto;
import cn.dahe.model.query.AttachmentCheckQuery;
import cn.dahe.model.vo.*;
import cn.dahe.service.AttachmentService;
import cn.dahe.service.CheckResultService;
import cn.dahe.service.TempFileRecordService;
import cn.dahe.utils.excel.ExcelExportUtil;
import cn.dahe.utils.excel.FieldMapping;
import cn.dahe.utils.excel.SheetConfig;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.http.HttpUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

/**
 * 附件检查Service实现类 - 对应原型图功能
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@Service
public class AttachmentServiceImpl extends BaseServiceImpl<AttachmentDao, Attachment> implements AttachmentService {
    @Resource
    private CheckResultService checkResultService;
    @Resource
    private TempFileRecordService tempFileRecordService;
    @Resource
    private FileConverter fileConverter;
    @Value("${anxun.attachment.download-path}")
    private String downloadPath;

    @Override
    public PageResult<AttachmentCheckVO> pageAttachmentAndCheckResults(AttachmentCheckQuery query) {
        Page<AttachmentCheckVO> page = Page.of(query.getPage(), query.getLimit());
        IPage<AttachmentCheckVO> result = this.getBaseMapper().listAttachmentInfo(page, query);
        // result.getRecords().forEach(vo -> checkResultService.markCheckContentWithHtml(vo, query));
        return PageResult.page(result);
    }

    @Override
    public AttachmentCheckVO getAttachmentAndCheckResults(Long attachmentId, AttachmentCheckQuery query, boolean doMark) {
        AttachmentCheckVO attachmentInfo = baseMapper.getAttachmentInfo(attachmentId);
        if (attachmentInfo == null) {
            return null;
        }
        checkResultService.markCheckContentWithHtml(attachmentInfo, query, doMark, false);
        //  为了和文章快照保持一致，将正文内容赋给htmlCode字段
        attachmentInfo.setHtmlCode(attachmentInfo.getHtmlContent());
        return attachmentInfo;
    }


    // ==================== 数据导出 ====================

    @Resource
    private HttpServletResponse httpServletResponse;

    @Value("${anxun.base-url}")
    private String baseUrl;


    @Override
    public void exportByQuery(AttachmentCheckQuery query) throws IOException {
        try {
            LoginUserVO user = SecurityUtils.getLoginUser();
            log.info("用户{}导出附件检查记录", user.getUserId());

            // 获取数据
            List<AttachmentCheckVO> dataList = this.getBaseMapper().listAttachmentInfo(query);
            // 创建字段映射
            List<FieldMapping<AttachmentCheckVO, ?>> fieldMappings1 = Arrays.asList(
                    FieldMapping.of(AttachmentCheckVO::getWebsiteName, "来源网站"),
                    FieldMapping.of(AttachmentCheckVO::getAttachmentName, "附件名称"),
                    FieldMapping.of(AttachmentCheckVO::getAttachmentUrl, "附件链接"),
                    FieldMapping.of(AttachmentCheckVO::getAttachmentType, "附件类型"),
                    FieldMapping.of(AttachmentCheckVO::getAttachmentSize, "附件大小"),
                    FieldMapping.of(attachmentCheckVO -> StrUtil.format("{}/#/snapshoot/{}/0", baseUrl, attachmentCheckVO.getTaskId()),"快照链接"),
                    FieldMapping.of(AttachmentCheckVO::getSourceName, "来源页面"),
                    FieldMapping.of(AttachmentCheckVO::getSourceUrl, "来源页面链接"),
                    FieldMapping.of(AttachmentCheckVO::getPubTime, "发布时间"),
                    FieldMapping.of(AttachmentCheckVO::getCheckTime, "检测时间")
            );
            // 创建Sheet配置
            SheetConfig<AttachmentCheckVO> sheetConfig1 = SheetConfig.of("附件检查记录", fieldMappings1, dataList);
            //  创建第二个sheet，检查结果
            List<AttachmentCheckResultVO> checkResults = CollUtil.newArrayList();
            for (AttachmentCheckVO vo : dataList) {
                List<CheckResultVO> checkResultVOS = checkResultService.listCheckResultByTaskAndCheckQuery(vo.getTaskId(), query);
                for (CheckResultVO checkResultVO : checkResultVOS) {
                    AttachmentCheckResultVO ao = new AttachmentCheckResultVO();
                    ao.setWebsiteName(vo.getWebsiteName());
                    ao.setProcessTypeName(vo.getProcessTypeName());
                    ao.setArticleTitle(vo.getAttachmentName());
                    ao.setArticleUrl(vo.getAttachmentUrl());
                    ao.setAttachmentType(vo.getAttachmentType());
                    ao.setErrorWord(checkResultVO.getErrorWord());
                    ao.setSuggestWord(checkResultVO.getSuggestWord());
                    ao.setContext(checkResultVO.getContext());
                    ao.setErrorLevelName(checkResultVO.getErrorLevelName());
                    ao.setErrorTypeName(checkResultVO.getErrorTypeName());
                    ao.setPubTime(vo.getPubTime());
                    ao.setParentArticleTitle(vo.getSourceName());
                    ao.setParentArticleUrl(vo.getSourceUrl());
                    checkResults.add(ao);
                }
            }
            // 创建字段映射
            List<FieldMapping<AttachmentCheckResultVO, ?>> fieldMappings2 = Arrays.asList(
                    FieldMapping.of(AttachmentCheckResultVO::getWebsiteName, "站点名称"),
                    FieldMapping.of(AttachmentCheckResultVO::getProcessTypeName, "平台类型"),
                    FieldMapping.of(AttachmentCheckResultVO::getArticleTitle, "附件名称"),
                    FieldMapping.of(AttachmentCheckResultVO::getAttachmentType, "附件类型"),
                    FieldMapping.of(AttachmentCheckResultVO::getErrorWord, "错误词"),
                    FieldMapping.of(AttachmentCheckResultVO::getSuggestWord, "建议词"),
                    FieldMapping.of(AttachmentCheckResultVO::getContext, ExcelExportUtil::safeStringWithoutHtml, "上下文"),
                    FieldMapping.of(AttachmentCheckResultVO::getErrorLevelName, "错误级别"),
                    FieldMapping.of(AttachmentCheckResultVO::getErrorTypeName, "错误类别"),
                    FieldMapping.of(AttachmentCheckResultVO::getParentArticleTitle, "来源页面"),
                    FieldMapping.of(AttachmentCheckResultVO::getParentArticleUrl, "来源页面链接"),
                    FieldMapping.of(AttachmentCheckResultVO::getPubTime, "发布时间"),
                    FieldMapping.of(AttachmentCheckResultVO::getArticleUrl, "附件链接")
            );
            // 创建Sheet配置
            SheetConfig<AttachmentCheckResultVO> sheetConfig2 = SheetConfig.of("附件检查结果记录", fieldMappings2, checkResults);

            // 导出Excel
            ExcelExportUtil.exportMultiSheetExcel(httpServletResponse, "附件检查记录", CollUtil.newArrayList(sheetConfig1, sheetConfig2));

        } catch (Exception e) {
            log.error("导出附件检查记录失败", e);
            throw new IOException("导出失败：" + e.getMessage());
        }
    }

    // private static String[] validFileType = new String[]{"pdf", "doc", "docx"};
    private static String[] validFileType = new String[]{
            "pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx", "txt", "wps"
    };

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveByPushData(Article article, List<String> fileLink) {
        // 1. 对输入链接列表去重
        HashSet<String> uniqueLinks = CollUtil.newHashSet(fileLink);
        Long articleId = article.getId();
        // 2. 一次性查询数据库中已存在的记录,从uniqueLinks中移除已存在的URL
        this.lambdaQuery()
                .eq(Attachment::getParentArticleId, articleId)
                .in(Attachment::getAttachmentUrl, uniqueLinks)
                .list()
                .stream()
                .map(Attachment::getAttachmentUrl)
                .forEach(uniqueLinks::remove);
        if (CollUtil.isEmpty(uniqueLinks)) {
            log.info("文章无待处理附件: articleId={}", articleId);
            return;
        }
        List<Attachment> attachmentList = new ArrayList<>();
        // 4. 只处理不存在的链接
        for (String link : uniqueLinks) {
            Attachment attachment = new Attachment();
            attachment.setWebsiteId(article.getWebsiteId());
            attachment.setProcessType(article.getProcessType());
            attachment.setParentArticleId(articleId);
            attachment.setAttachmentUrl(link);
            try {
                URL url = URLUtil.toUrlForHttp(link);
                //  获取文件名
                String fileName = getFileNameWithExtension(url.getPath());
                attachment.setAttachmentName(fileName);
                //  获取文件类型
                String fileType = getFileExtension(fileName);
                attachment.setAttachmentType(fileType);
                //  文件大小 TODO 这里不一定能获取 所以不能用来判断合法性
                long contentLength = URLUtil.getContentLength(url);
                attachment.setAttachmentSize(contentLength);
            } catch (Exception e) {
                //  无事发生
            }
            attachmentList.add(attachment);
            log.info("{}-文章保存附件信息：{}", articleId, attachment.getAttachmentName());
        }
        this.saveBatch(attachmentList);
    }


    @Override
    public AttachmentContentDTO getAndTransferAttachmentContent(Attachment attachment) {
        // 1. 下载文件
        File tempFile = null;
        try {
            tempFile = downloadAttachment(attachment);
            String fileName = tempFile.getName();
            // 5. 检测实际文件类型
            String actualFileType = detectFileType(tempFile);
            if (!StrUtil.equalsAny(actualFileType, validFileType)) {
                throw ContentGetException.fileTypeFail(StrUtil.format("文件名：{} 文件类型：{}", fileName, actualFileType));
            }
            // 2. 获取文件信息
            AttachmentContentDTO dto = new AttachmentContentDTO()
                    .setTempFilePath(tempFile.getAbsolutePath())
                    .setFileName(fileName)
                    .setFileSize(tempFile.length())
                    .setFileType(actualFileType);
            // 3. 转换文件
            String content = convertAttachmentToHtml(actualFileType, tempFile, attachment);
            dto.setContent(content);
            return dto;
        } finally {
            if (tempFile != null) {
                // 4. 记录临时文件
                recordTempFile(tempFile, attachment.getId());
            }
        }
    }

    /**
     * 下载附件
     *
     * @param attachment 附件信息
     * @return 临时文件
     * @throws RuntimeException 下载失败时抛出异常
     */
    private File downloadAttachment(Attachment attachment) {
        String url = attachment.getAttachmentUrl();
        try {
            // 创建临时目录
            String fileDir = StrUtil.format("{}-{}-{}",
                    attachment.getId(),
                    RandomUtil.randomString(6),
                    System.currentTimeMillis());
            File tempDir = FileUtil.mkdir(downloadPath + "/" + fileDir);
            // 下载文件
            log.info("开始下载附件：{}", url);
            return HttpUtil.downloadFileFromUrl(url, tempDir);
        } catch (Exception e) {
            log.error("附件下载失败：{}", url, e);
            throw ContentGetException.downloadFail(StrUtil.format("下载失败：{} \n {}", url, ExceptionUtil.stacktraceToString(e)));
        }
    }

    /**
     * 将附件转换为HTML
     *
     * @param file       文件
     * @param attachment 附件信息
     * @return HTML内容
     * @throws RuntimeException 转换失败时抛出异常
     */
    private String convertAttachmentToHtml(File file, Attachment attachment) {
        if (file == null || !file.exists()) {
            throw ContentGetException.parseFail("文件为空", "");
        }
        try {
            log.info("开始转换文件：{}", file.getAbsolutePath());
            return fileConverter.convertToHtml(file.getAbsolutePath());
        } catch (Exception e) {
            log.error("文件转换失败：{}", attachment.getAttachmentUrl(), e);
            throw ContentGetException.parseFail("文件转换失败", ExceptionUtil.stacktraceToString(e));
        }
    }

    private String convertAttachmentToHtml(String fileType, File file, Attachment attachment) {
        if ("pdf".equalsIgnoreCase(fileType)) {
            //  TODO
            return fileConverter.convertPdfToHtml(file.getAbsolutePath());
        }
        return convertAttachmentToHtml(file, attachment);
    }

    /**
     * 检测文件实际类型
     *
     * @param file 文件
     * @return 文件类型
     */
    private String detectFileType(File file) {
        // 这里可以使用更复杂的文件类型检测逻辑
        // 目前简单使用文件扩展名
        String fileName = file.getName();
        return getFileExtension(fileName);
    }


    /**
     * 记录临时文件
     *
     * @param tempFile     临时文件
     * @param attachmentId 附件ID
     */
    private void recordTempFile(File tempFile, Long attachmentId) {
        if (tempFile != null && tempFile.exists()) {
            try {
                tempFileRecordService.addTempFile(
                        tempFile.getAbsolutePath(),
                        attachmentId,
                        "attachment"
                );
            } catch (Exception e) {
                log.error("记录临时文件失败：{}", tempFile.getAbsolutePath(), e);
                // 不抛出异常，因为这不影响主要业务流程
            }
        }
    }

    @Override
    public void relateWithCheckTask(Long attachmentId, Long checkId) {
        this.lambdaUpdate()
                .eq(Attachment::getId, attachmentId)
                .set(Attachment::getCheckId, checkId)
                .update();
    }

    @Override
    public List<Attachment> listNoContentAttachment() {
        return this.getBaseMapper().listNoContentAttachment();
    }

    @Override
    public List<Attachment> listNoTaskAttachment() {
        return this.getBaseMapper().listNoTaskAttachment();
    }

    @Override
    public void updateByDto(Long attachmentId, AttachmentContentDTO contentDTO) {
        this.lambdaUpdate()
                .eq(Attachment::getId, attachmentId)
                .set(Attachment::getAttachmentName, contentDTO.getFileName())
                .set(Attachment::getAttachmentType, contentDTO.getFileType())
                .set(Attachment::getAttachmentSize, contentDTO.getFileSize())
                .update();
    }

    @Override
    public WarnCheckDetailDto getWarnAttachmentInfo(Long attachmentId) {
        return this.getBaseMapper().getWarnAttachmentInfo(attachmentId);
    }

    @Override
    public CheckSnapshotVO getSnapshotInfo(Long attachmentId) {
        return this.getBaseMapper().getSnapshotInfo(attachmentId);
    }

    private static String getFileNameWithExtension(String url) {
        String path = URLUtil.getPath(url);
        int lastDotIndex = path.lastIndexOf('/');
        if (lastDotIndex > 0 && lastDotIndex < path.length() - 1) {
            return path.substring(lastDotIndex + 1).toLowerCase();
        }
        return "";
    }

    private static String getFileExtension(String fileName) {
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
            return fileName.substring(lastDotIndex + 1).toLowerCase();
        }
        return StrUtil.EMPTY;
    }


}
