package cn.dahe.service.impl;

import cn.dahe.dao.ArticleDao;
import cn.dahe.dao.WebSiteIndexArticleCheckDao;
import cn.dahe.entity.Article;
import cn.dahe.entity.WebSiteIndexArticleCheck;
import cn.dahe.entity.Website;
import cn.dahe.entity.WebsiteGroup;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.query.WebSiteIndexCheckQuery;
import cn.dahe.model.vo.WebSiteIndexCheckDetailVO;
import cn.dahe.model.vo.WebSiteIndexCheckExportVO;
import cn.dahe.model.vo.WebSiteIndexCheckVO;
import cn.dahe.service.WebSiteIndexArticleCheckService;
import cn.dahe.service.WebsiteGroupService;
import cn.dahe.service.WebsiteService;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 站点下网站首页更新详情Service实现类
 *
 * <AUTHOR>
 * @date 2025-09-03
 */
@Slf4j
@Service
public class WebSiteIndexArticleCheckServiceImpl extends ServiceImpl<WebSiteIndexArticleCheckDao, WebSiteIndexArticleCheck> implements WebSiteIndexArticleCheckService {

    @Resource
    private WebSiteIndexArticleCheckDao WebSiteIndexArticleCheckDao;

    @Resource
    private ArticleDao articleDao;

    @Resource
    private WebsiteService websiteService;

    @Resource
    private WebsiteGroupService websiteGroupService;

    @Override
    public PageResult<WebSiteIndexCheckVO> pageByWebsiteId(Long websiteId, Integer page, Integer limit) {
        // 计算偏移量
        int offset = (page - 1) * limit;
        
        // 设置查询日期范围：从2025年8月1日开始到当前时间
        LocalDate startDate = LocalDate.of(2025, 8, 1);
        LocalDate endDate = LocalDate.now();
        
        // 查询总数
        Long total = WebSiteIndexArticleCheckDao.countByWebsiteIdAndDateRange(websiteId, startDate, endDate);
        
        // 查询数据
        List<WebSiteIndexArticleCheck> records = WebSiteIndexArticleCheckDao.selectByWebsiteIdAndDateRange(
                websiteId, startDate, endDate, offset, limit);
        
        // 转换为VO
        List<WebSiteIndexCheckVO> voList = records.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
        
        return new PageResult<>(voList, total, limit, page);
    }


    @Override
    public Integer generateIndexCheckData(Long websiteId) {
        log.info("开始生成站点{}的首页更新检查数据", websiteId);
        
        // 设置开始日期：2025年8月1日
        LocalDate startDate = LocalDate.of(2025, 8, 1);
        LocalDate endDate = LocalDate.now();
        
        int generatedCount = 0;
        
        // 遍历每一天
        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            // 检查是否已存在该日期的记录
            WebSiteIndexArticleCheck existingRecord = getByWebsiteIdAndDate(websiteId, currentDate);
            if (existingRecord == null) {
                // 生成新记录
                WebSiteIndexArticleCheck newRecord = generateDailyRecord(websiteId, currentDate);
                if (newRecord != null) {
                    save(newRecord);
                    generatedCount++;
                    log.debug("生成站点{}在{}的首页更新检查记录", websiteId, currentDate);
                }
            }
            currentDate = currentDate.plusDays(1);
        }
        
        log.info("站点{}的首页更新检查数据生成完成，共生成{}条记录", websiteId, generatedCount);
        return generatedCount;
    }

    @Override
    public Integer generateAllSitesIndexCheckData() {
        log.info("开始批量生成所有站点的首页更新检查数据");
        // 获取所有启用的站点
        List<Website> websites = websiteService.listByStatus(1);
        log.info("查询到{}个启用站点", websites.size());
        int totalGenerated = 0;
        for (Website website : websites) {
            try {
                int count = generateIndexCheckData(website.getId());
                totalGenerated += count;
            } catch (Exception e) {
                log.error("生成站点{}的首页更新检查数据失败", website.getId(), e);
            }
        }
        log.info("所有站点的首页更新检查数据生成完成，共生成{}条记录", totalGenerated);
        return totalGenerated;
    }

    @Override
    public Integer generateAllSitesYesterdayIndexCheckData() {
        log.info("开始批量生成所有站点的首页昨日更新检查数据");
        // 获取所有启用的站点
        List<Website> websites = websiteService.listByStatus(1);
        log.info("查询到{}个启用站点", websites.size());
        // 计算昨日日期
        LocalDate yesterday = LocalDate.now().minusDays(1);
        log.info("正在生成日期: {}", yesterday);
        int totalGenerated = 0;
        for (Website website : websites) {
            try {
                // 检查是否已存在该日期的记录
                //TODO: 暂时先这样
                WebSiteIndexArticleCheck existingRecord = getByWebsiteIdAndDate(website.getId(), yesterday);
                if (existingRecord != null){
                    WebSiteIndexArticleCheckDao.deleteById(existingRecord.getId());
                }
                WebSiteIndexArticleCheck newRecord = generateDailyRecord(website.getId(), yesterday);
                save(newRecord);
                totalGenerated++;
//                if (existingRecord == null) {
//                    // 生成昨日的检查记录
//                    WebSiteIndexArticleCheck newRecord = generateDailyRecord(website.getId(), yesterday);
//                    if (newRecord != null) {
//                        save(newRecord);
//                        totalGenerated++;
//                        log.debug("生成站点{}在{}的首页更新检查记录", website.getId(), yesterday);
//                    } else {
//                        log.warn("站点{}在{}的首页更新检查记录生成失败", website.getId(), yesterday);
//                    }
//                } else {
//                    log.debug("站点{}在{}的首页更新检查记录已存在，跳过", website.getId(), yesterday);
//                }
            } catch (Exception e) {
                log.error("生成站点{}的首页昨日更新检查数据失败", website.getId(), e);
            }
        }
        
        log.info("所有站点的首页昨日更新检查数据生成完成，共生成{}条记录", totalGenerated);
        return totalGenerated;
    }

    @Override
    public WebSiteIndexArticleCheck getByWebsiteIdAndDate(Long websiteId, LocalDate checkDate) {
        return WebSiteIndexArticleCheckDao.selectByWebsiteIdAndDate(websiteId, checkDate);
    }

    @Override
    public List<WebSiteIndexArticleCheck> listByWebsiteIdAndDateRange(Long websiteId, LocalDate startDate, LocalDate endDate) {
        return WebSiteIndexArticleCheckDao.selectByWebsiteIdAndDateRange(websiteId, startDate, endDate, 0, Integer.MAX_VALUE);
    }

    /**
     * 生成指定日期的首页更新检查记录
     *
     * @param websiteId 站点ID
     * @param checkDate 检查日期
     * @return 检查记录
     */
    private WebSiteIndexArticleCheck generateDailyRecord(Long websiteId, LocalDate checkDate) {
        try {
            // 查询该站点在指定日期的首页文章（isIndex=1）、且
            List<Article> indexArticles = articleDao.listByIsIndexAndPubTime(1, websiteId.toString());
            // 过滤出指定日期的文章
            List<Article> dailyArticles = indexArticles.stream()
                    .filter(article -> {
                        if (article.getPubTime() == null) {
                            return false;
                        }
                        LocalDate articleDate = article.getPubTime().toInstant()
                                .atZone(java.time.ZoneId.systemDefault())
                                .toLocalDate();
                        return articleDate.equals(checkDate);
                    })
                    .collect(Collectors.toList());
            
            // 创建检查记录
            WebSiteIndexArticleCheck record = new WebSiteIndexArticleCheck();
            record.setWebsiteId(websiteId);
            record.setUpdateCount(dailyArticles.size());
            //需要存入时分秒 LocalDate转换为Date
            // 转换为Date类型
            Date checkDateTime = Date.from(checkDate.atStartOfDay(java.time.ZoneId.systemDefault()).toInstant());
            record.setCheckTime(checkDateTime);
            record.setIsUpdate(dailyArticles.size() > 0 ? 1 : 0);
            
            // 设置最后更新时间
            if (!dailyArticles.isEmpty()) {
                // 按发布时间排序，获取最新的文章
                dailyArticles.sort((a1, a2) -> {
                    if (a1.getPubTime() == null || a2.getPubTime() == null) {
                        return 0;
                    }
                    return a2.getPubTime().compareTo(a1.getPubTime());
                });
                
                Article latestArticle = dailyArticles.get(0);
                if (latestArticle.getPubTime() != null) {
                    record.setLastUpdateTime(latestArticle.getPubTime());
                }
            }

            return record;
            
        } catch (Exception e) {
            log.error("生成站点{}在{}的首页更新检查记录失败", websiteId, checkDate, e);
            return null;
        }
    }

    /**
     * 将实体转换为VO
     *
     * @param entity 实体对象
     * @return VO对象
     */
    private WebSiteIndexCheckVO convertToVO(WebSiteIndexArticleCheck entity) {
        if (entity == null) {
            return null;
        }
        
        WebSiteIndexCheckVO vo = new WebSiteIndexCheckVO();
        vo.setId(entity.getId());
        vo.setWebsiteId(entity.getWebsiteId());
        vo.setUpdateCount(entity.getUpdateCount());
//        vo.setLastUpdateTime();
//        vo.setCheckTime(entity.getCheckTime());
        vo.setIsUpdate(entity.getIsUpdate());
        
        // 获取站点信息
        try {
            Website website = websiteService.getById(entity.getWebsiteId());
            if (website != null) {
                vo.setWebsiteName(website.getWebName());
                vo.setWebsiteUrl(website.getWebUrl());
            }
        } catch (Exception e) {
            log.warn("获取站点{}信息失败", entity.getWebsiteId(), e);
        }
        
        return vo;
    }

    @Override
    public PageResult<WebSiteIndexCheckDetailVO> pageByQuery(WebSiteIndexCheckQuery query) {
        // 计算偏移量
        int offset = (query.getPage() - 1) * query.getLimit();
        // 查询总数
        Long total = countByQuery(query);
        // 查询数据
        List<WebSiteIndexCheckDetailVO> voList = listByQuery(query);
        return new PageResult<>(voList, total, query.getLimit(), query.getPage());
    }
    @Override
    public List<WebSiteIndexCheckDetailVO> listByQuery(WebSiteIndexCheckQuery query) {
        // 查询所有processType为0的网站
        List<Website> websites = websiteService.listByStatusAndProcessTypeIn(1, Arrays.asList(0));
        List<WebSiteIndexCheckDetailVO> result = new ArrayList<>();
        for (Website website : websites) {
            // 应用查询条件过滤
            if (!shouldIncludeWebsite(website, query)) {
                continue;
            }
            WebSiteIndexCheckDetailVO vo = buildWebsiteIndexCheckDetailVO(website, query);
            if (vo != null) {
                result.add(vo);
            }
        }
        // 排序
        sortResult(result, query);
        return result;
    }

    @Override
    public byte[] exportToExcel(WebSiteIndexCheckQuery query) {
        List<WebSiteIndexCheckDetailVO> dataList = listByQuery(query);
        // 定义Excel列头
        String[] headers = {"序号", "网站名称", "网站地址", "分组", "首页是否更新", "更新天数", "连续未更新天数", "连续更新天数", "最后更新时间", "更新文章个数"};
        // 准备数据
        List<Object[]> data = new ArrayList<>();
        for (int i = 0; i < dataList.size(); i++) {
            WebSiteIndexCheckDetailVO vo = dataList.get(i);
            Object[] row = {
                i + 1,
                vo.getWebsiteName(),
                vo.getWebsiteUrl(),
                vo.getGroupName(),
                vo.getUpdateStatusDesc(),
                formatUpdateDays(vo.getCheckDays()),
                String.join(",", vo.getCheckNotUpdateDays()),
                String.join(",", vo.getCheckUpdateDays()),
                vo.getLastUpdateTime() != null ? formatDate(vo.getLastUpdateTime()) : "",
                vo.getUpdateCount()
            };
            data.add(row);
        }
        // 导出Excel - 使用POI直接生成Excel文件
        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("网站首页更新检查数据");
            
            // 创建表头
            Row headerRow = sheet.createRow(0);
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
            }
            
            // 填充数据
            for (int i = 0; i < data.size(); i++) {
                Row row = sheet.createRow(i + 1);
                Object[] rowData = data.get(i);
                for (int j = 0; j < rowData.length; j++) {
                    Cell cell = row.createCell(j);
                    cell.setCellValue(rowData[j] != null ? rowData[j].toString() : "");
                }
            }
            
            // 自动调整列宽
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }
            
            // 转换为字节数组
            try (java.io.ByteArrayOutputStream outputStream = new java.io.ByteArrayOutputStream()) {
                workbook.write(outputStream);
                return outputStream.toByteArray();
            }
        } catch (Exception e) {
            log.error("生成Excel文件失败", e);
            return new byte[0];
        }
    }

    @Override
    public void exportToExcelWithEasyExcel(WebSiteIndexCheckQuery query, HttpServletResponse response) throws IOException {
        try {
            log.info("开始使用EasyExcel导出网站首页更新检查数据");
            
            // 查询数据
            List<WebSiteIndexCheckDetailVO> dataList = listByQuery(query);
            log.info("查询到{}条数据", dataList.size());
            
            // 转换为导出VO
            List<WebSiteIndexCheckExportVO> exportDataList = new ArrayList<>();
            for (int i = 0; i < dataList.size(); i++) {
                WebSiteIndexCheckDetailVO vo = dataList.get(i);
                WebSiteIndexCheckExportVO exportVO = new WebSiteIndexCheckExportVO();
                exportVO.setSerialNumber(i + 1);
                exportVO.setWebsiteName(vo.getWebsiteName());
                exportVO.setWebsiteUrl(vo.getWebsiteUrl());
                exportVO.setGroupName(vo.getGroupName());
                exportVO.setUpdateStatusDesc(vo.getUpdateStatusDesc());
                exportVO.setUpdateDays(formatUpdateDays(vo.getCheckDays()));
                exportVO.setContinuousNotUpdateDays(String.join(",", vo.getCheckNotUpdateDays()));
                exportVO.setContinuousUpdateDays(String.join(",", vo.getCheckUpdateDays()));
                exportVO.setLastUpdateTime(vo.getLastUpdateTime() != null ? formatDate(vo.getLastUpdateTime()) : "");
                exportVO.setUpdateCount(vo.getUpdateCount());
                exportDataList.add(exportVO);
            }
            
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = java.net.URLEncoder.encode("网站首页更新检查数据_" + System.currentTimeMillis(), "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            
            // 使用EasyExcel导出
            EasyExcel.write(response.getOutputStream(), WebSiteIndexCheckExportVO.class)
                    .sheet("网站首页更新检查数据")
                    .doWrite(exportDataList);
            
            log.info("导出完成，共导出{}条数据", exportDataList.size());
            
        } catch (Exception e) {
            log.error("使用EasyExcel导出Excel失败", e);
            throw new IOException("导出失败：" + e.getMessage());
        }
    }

    /**
     * 统计查询条件下的记录总数
     */
    private Long countByQuery(WebSiteIndexCheckQuery query) {
        List<Website> websites = websiteService.listByStatusAndProcessTypeIn(1, Arrays.asList(0));
        return websites.stream()
                .filter(website -> shouldIncludeWebsite(website, query))
                .count();
    }

    /**
     * 判断网站是否应该包含在查询结果中
     */
    private boolean shouldIncludeWebsite(Website website, WebSiteIndexCheckQuery query) {
        // 按站点ID过滤
        if (query.getWebsiteIds() != null && !query.getWebsiteIds().trim().isEmpty()) {
            String[] websiteIds = query.getWebsiteIds().split(",");
            boolean websiteIdMatch = false;
            for (String websiteId : websiteIds) {
                if (String.valueOf(website.getId()).equals(websiteId.trim())) {
                    websiteIdMatch = true;
                    break;
                }
            }
            if (!websiteIdMatch) {
                return false;
            }
        }
        
        // 按站点名称过滤
        if (query.getWebsiteNames() != null && !query.getWebsiteNames().trim().isEmpty()) {
            String[] websiteNames = query.getWebsiteNames().split(",");
            boolean websiteNameMatch = false;
            for (String websiteName : websiteNames) {
                if (website.getWebName() != null && website.getWebName().contains(websiteName.trim())) {
                    websiteNameMatch = true;
                    break;
                }
            }
            if (!websiteNameMatch) {
                return false;
            }
        }
        
        // 按站点URL过滤
        if (query.getWebsiteUrls() != null && !query.getWebsiteUrls().trim().isEmpty()) {
            String[] websiteUrls = query.getWebsiteUrls().split(",");
            boolean urlMatch = false;
            for (String url : websiteUrls) {
                if (website.getWebUrl() != null && website.getWebUrl().contains(url.trim())) {
                    urlMatch = true;
                    break;
                }
            }
            if (!urlMatch) {
                return false;
            }
        }
        
        // 按分组过滤
        if (query.getGroupId() != null) {
            if (!query.getGroupId().equals(website.getGroupId())) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * 构建网站首页更新检查详情VO
     */
    private WebSiteIndexCheckDetailVO buildWebsiteIndexCheckDetailVO(Website website, WebSiteIndexCheckQuery query) {
        WebSiteIndexCheckDetailVO vo = new WebSiteIndexCheckDetailVO();
        vo.setId(website.getId());
        vo.setWebsiteId(website.getId());
        vo.setWebsiteName(website.getWebName());
        vo.setWebsiteUrl(website.getWebUrl());
        // 获取分组名称
        if (website.getGroupId() != null) {
            try {
                WebsiteGroup group = websiteGroupService.getById(website.getGroupId());
                if (group != null) {
                    vo.setGroupName(group.getGroupName());
                }
            } catch (Exception e) {
                log.warn("获取分组{}信息失败", website.getGroupId(), e);
                vo.setGroupName("未知分组");
            }
        } else {
            vo.setGroupName("未分组");
        }
        //  设置查询日期范围 从昨天开始的近30天
        LocalDate endDate = LocalDate.now().minusDays(1); // 昨天
        LocalDate startDate = endDate.minusDays(29);
        if (query.getSdate() != null && !query.getSdate().trim().isEmpty()) {
            try {
                startDate = LocalDate.parse(query.getSdate(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            } catch (Exception e) {
                log.warn("解析开始日期失败: {}", query.getSdate(), e);
            }
        }
        if (query.getEdate() != null && !query.getEdate().trim().isEmpty()) {
            try {
                endDate = LocalDate.parse(query.getEdate(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            } catch (Exception e) {
                log.warn("解析结束日期失败: {}", query.getEdate(), e);
            }
        }
        // 查询该网站在该日期范围内的检查记录
        List<WebSiteIndexArticleCheck> checkRecords = listByWebsiteIdAndDateRange(website.getId(), startDate, endDate);
        log.info("站点{}在{}到{}期间查询到{}条检查记录", website.getId(), startDate, endDate, checkRecords.size());
        
        // 调试：打印前几条记录
        if (!checkRecords.isEmpty()) {
            log.info("前3条记录: {}", checkRecords.stream().limit(3).map(r -> 
                String.format("ID=%d, checkTime=%s, isUpdate=%d, updateCount=%d", 
                    r.getId(), r.getCheckTime(), r.getIsUpdate(), r.getUpdateCount())
            ).collect(Collectors.joining("; ")));
        }
        // 构建更新天数列表
        List<WebSiteIndexCheckDetailVO.UpdateDayInfo> checkDays = new ArrayList<>();
        List<String> checkNotUpdateDays = new ArrayList<>();
        List<String> checkUpdateDays = new ArrayList<>();
        // 按日期排序检查记录，从最新到最旧
        List<WebSiteIndexArticleCheck> sortedRecords = checkRecords.stream()
                .filter(r -> r.getCheckTime() != null)
                .sorted(Comparator.comparing(WebSiteIndexArticleCheck::getCheckTime).reversed())
                .collect(Collectors.toList());
        
        // 遍历检查记录，构建更新天数列表
        for (WebSiteIndexArticleCheck record : sortedRecords) {
            LocalDate recordDate = record.getCheckTime().toInstant()
                    .atZone(java.time.ZoneId.systemDefault())
                    .toLocalDate();
            // 构建更新天数信息
            WebSiteIndexCheckDetailVO.UpdateDayInfo updateDay = new WebSiteIndexCheckDetailVO.UpdateDayInfo();
            updateDay.setUpdateDate(recordDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            updateDay.setIsUpdate(record.getIsUpdate());
            updateDay.setUpdateCount(record.getUpdateCount());
            checkDays.add(updateDay);
            if (record.getIsUpdate() == 1) {
                checkUpdateDays.add(recordDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            } else {
                checkNotUpdateDays.add(recordDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            }
        }
        // 设置首页是否更新（最新的检查记录状态）
        if (!sortedRecords.isEmpty()) {
            //最新的检查记录
            WebSiteIndexArticleCheck latestRecord = sortedRecords.get(0);
            vo.setIsUpdate(latestRecord.getIsUpdate());
            // 计算更新和未更新天数（从最新日期开始向前计算）
            int continuousUpdateCount = 0;
            int continuousNotUpdateCount = 0;

            // 按最新记录的状态，统计表头连续天数
            if (latestRecord.getIsUpdate() != null && latestRecord.getIsUpdate() == 1) {
                // 连续更新：统计表头连续为1的天数（正数）
                for (WebSiteIndexArticleCheck record : sortedRecords) {
                    if (record.getIsUpdate() != null && record.getIsUpdate() == 1) {
                        continuousUpdateCount++;
                    } else {
                        break;
                    }
                }
                // 未更新置0
                continuousNotUpdateCount = 0;
            } else {
                // 连续未更新：统计表头连续为0的天数（负数）
                int notUpdateRun = 0;
                for (WebSiteIndexArticleCheck record : sortedRecords) {
                    if (record.getIsUpdate() != null && record.getIsUpdate() == 0) {
                        notUpdateRun++;
                    } else {
                        break;
                    }
                }
                continuousNotUpdateCount = -notUpdateRun;
                // 更新置0
                continuousUpdateCount = 0;
            }

            vo.setContinuousNotUpdateCount(continuousNotUpdateCount);
            vo.setContinuousUpdateCount(continuousUpdateCount);
        } else {
            vo.setIsUpdate(0);
        }

        vo.setCheckDays(checkDays);
        vo.setCheckNotUpdateDays(checkNotUpdateDays);
        vo.setCheckUpdateDays(checkUpdateDays);
        
        // 设置最后更新时间和更新文章个数
        if (!checkRecords.isEmpty()) {
            WebSiteIndexArticleCheck latestRecord = checkRecords.stream()
                    .filter(r -> r.getLastUpdateTime() != null)
                    .max(Comparator.comparing(WebSiteIndexArticleCheck::getLastUpdateTime))
                    .orElse(null);
            
            if (latestRecord != null) {
                vo.setLastUpdateTime(latestRecord.getLastUpdateTime());
                vo.setUpdateCount(latestRecord.getUpdateCount());
            }
        }
        
        return vo;
    }

    /**
     * 排序结果
     */
    private void sortResult(List<WebSiteIndexCheckDetailVO> result, WebSiteIndexCheckQuery query) {
        if (query.getSortField() == null) {
            query.setSortField(0);
        }
        if (query.getSortDirection() == null) {
            query.setSortDirection(0);
        }
        
        result.sort((a, b) -> {
            int comparison = 0;
            
            switch (query.getSortField()) {
                case 0: // 检查时间
                    comparison = compareDates(a.getCheckTime(), b.getCheckTime());
                    break;
                case 1: // 最后更新时间
                    comparison = compareDates(a.getLastUpdateTime(), b.getLastUpdateTime());
                    break;
                case 2: // 更新文章个数
                    comparison = Integer.compare(a.getUpdateCount() != null ? a.getUpdateCount() : 0, 
                                               b.getUpdateCount() != null ? b.getUpdateCount() : 0);
                    break;
                default:
                    comparison = 0;
            }
            
            return query.getSortDirection() == 1 ? comparison : -comparison;
        });
    }

    /**
     * 比较日期
     */
    private int compareDates(Date date1, Date date2) {
        if (date1 == null && date2 == null) return 0;
        if (date1 == null) return -1;
        if (date2 == null) return 1;
        return date1.compareTo(date2);
    }

    /**
     * 格式化更新天数
     */
    private String formatUpdateDays(List<WebSiteIndexCheckDetailVO.UpdateDayInfo> updateDays) {
        if (updateDays == null || updateDays.isEmpty()) {
            return "";
        }
        return updateDays.stream()
                .map(WebSiteIndexCheckDetailVO.UpdateDayInfo::getUpdateDate)
                .collect(Collectors.joining(","));
    }

    /**
     * 格式化日期
     */
    private String formatDate(Date date) {
        if (date == null) return "";
        return new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(date);
    }
}
