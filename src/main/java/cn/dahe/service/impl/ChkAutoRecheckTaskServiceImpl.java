package cn.dahe.service.impl;

import cn.dahe.check.service.CheckService;
import cn.dahe.dao.ChkAutoRecheckTaskDao;
import cn.dahe.entity.Article;
import cn.dahe.entity.Channel;
import cn.dahe.entity.ChkAutoRecheckTask;
import cn.dahe.entity.Website;
import cn.dahe.model.dto.ChkAutoRecheckTaskDto;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.query.ChkAutoRecheckTaskQuery;
import cn.dahe.model.vo.ChkAutoRecheckTaskVO;
import cn.dahe.model.vo.LoginUserVO;
import cn.dahe.service.ArticleService;
import cn.dahe.service.ChannelService;
import cn.dahe.service.ChkAutoRecheckTaskService;
import cn.dahe.service.WebsiteService;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 复查任务Service
 *
 * <AUTHOR>
 * @date 2025-08-21
 */
@Slf4j
@Service
public class ChkAutoRecheckTaskServiceImpl extends ServiceImpl<ChkAutoRecheckTaskDao, ChkAutoRecheckTask> implements ChkAutoRecheckTaskService {

    @Resource
    private ArticleService articleService;
    @Resource
    private ChannelService channelService;
    @Resource
    private WebsiteService websiteService;
    @Resource
    private CheckService checkService;

    @Override
    public int add(ChkAutoRecheckTaskDto chkAutoRecheckTaskDto, LoginUserVO user) {
        ChkAutoRecheckTask chkAutoRecheckTask = BeanUtil.copyProperties(chkAutoRecheckTaskDto, ChkAutoRecheckTask.class);
        chkAutoRecheckTask.setCreateUserId(user.getUserId());
        chkAutoRecheckTask.setCreateUserName(user.getUsername());
        chkAutoRecheckTask.setCreateDepId(user.getDepId());
        chkAutoRecheckTask.setCreateDepName(user.getDepName());
        chkAutoRecheckTask.setIsDel(false);
        chkAutoRecheckTask.setCreateUserId(user.getUserId());
        chkAutoRecheckTask.setCreateUserName(user.getUsername());
        List<Long> queryArticleIds = chkAutoRecheckTaskDto.getArticleIds();
        if (chkAutoRecheckTaskDto.getFilterTitle() == 1 && CollUtil.isNotEmpty(queryArticleIds)) {
            List<Article> articles = articleService.listByIds(queryArticleIds);
            // 进行复查
            for (Article article : articles) {
                checkService.initCheckTaskForArticleReview(article);
            }
            if (!articles.isEmpty()) {
                List<Long> articleIds = CollUtil.newArrayList();
                List<String> articleTitles = CollUtil.newArrayList();
                List<String> articleUrls = CollUtil.newArrayList();
                Set<Long> siteId = CollUtil.newHashSet();
                Set<String> siteNames = CollUtil.newHashSet();
                articles.forEach(article -> {
                    articleIds.add(article.getId());
                    articleTitles.add(article.getTitle());
                    articleUrls.add(article.getArticleUrl());
                    //  TODO 这部分逻辑需要再更新，关于复查新媒体和网站的区分 目前只是为了不报错的处理
                    Long channelId = article.getChannelId();
                    Channel channel = channelService.getById(channelId);
                    siteId.add((long) channel.getSiteId());
                    siteNames.add(channel.getSiteName());
                });
                // 逗号分割id
                chkAutoRecheckTask.setArticleIds(CollUtil.join(articleIds, ","));
                // 逗号分割标题
                chkAutoRecheckTask.setArticleTitles(CollUtil.join(articleTitles, ","));
                // 逗号分割url
                chkAutoRecheckTask.setArticleUrls(CollUtil.join(articleUrls, ","));
                // 逗号分割id
                chkAutoRecheckTask.setSiteIds(CollUtil.join(siteId, ","));
                // 逗号分割名称
                chkAutoRecheckTask.setSiteNames(CollUtil.join(siteNames, ","));
            }
        } else {
            List<String> list = Arrays.asList(chkAutoRecheckTaskDto.getSiteIds().split(","));
            if (CollUtil.isNotEmpty(list)) {
                List<Website> websites = websiteService.listByIds(list);
                if (CollUtil.isNotEmpty(websites)) {
                    List<String> siteNames = websites.stream().map(Website::getWebName).collect(Collectors.toList());
                    chkAutoRecheckTask.setSiteNames(CollUtil.join(siteNames, ","));
                }
            }
        }
        return this.getBaseMapper().insert(chkAutoRecheckTask);
    }

    @Override
    public PageResult<ChkAutoRecheckTaskVO> page(ChkAutoRecheckTaskQuery query) {
        try {
            Page<ChkAutoRecheckTaskVO> page = new Page<>(query.getPage(), query.getLimit());
            IPage<ChkAutoRecheckTaskVO> result = this.getBaseMapper().selectPage(page, query);
            return PageResult.page(result);
        } catch (Exception e) {
            log.error("查询自动复查任务失败，返回兜底数据", e);
        }
        return new PageResult<>();
    }

    @Override
    public Map<String, Object> count(ChkAutoRecheckTaskQuery query) {
        Map<String, Object> statistics = this.getBaseMapper().getTaskStatistics(query);
        return statistics;
    }
}
