package cn.dahe.service.impl;

import cn.dahe.common.auth.SecurityUtils;
import cn.dahe.dao.ReprintSourceDao;
import cn.dahe.entity.ReprintSource;
import cn.dahe.model.query.ReprintSourceQuery;
import cn.dahe.model.vo.LoginUserVO;
import cn.dahe.model.vo.ReprintSourceVO;
import cn.dahe.service.ReprintSourceService;
import cn.dahe.utils.excel.ExcelExportUtil;
import cn.dahe.utils.excel.FieldMapping;
import cn.dahe.utils.excel.SheetConfig;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 站点转载信源关联表 服务实现类
 */
@Slf4j
@Service
public class ReprintSourceServiceImpl extends ServiceImpl<ReprintSourceDao, ReprintSource> implements ReprintSourceService {

    @Override
    public Page<ReprintSourceVO> pageList(ReprintSourceQuery query) {
        return this.getBaseMapper().pageList(Page.of(query.getPage(), query.getLimit()), query);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateFilterStatus(List<Long> ids, Boolean filterStatus) {
        if (CollectionUtils.isEmpty(ids) || filterStatus == null) {
            return;
        }
        this.lambdaUpdate()
                .in(ReprintSource::getId, ids)
                .set(ReprintSource::getFilterStatus, filterStatus)
                .set(ReprintSource::getUpdateTime, DateUtil.date())
                .set(filterStatus, ReprintSource::getFilterTime, DateUtil.date())
                .update();
    }
    @Resource
    private HttpServletResponse httpServletResponse;

    @Override
    public void exportByQuery(ReprintSourceQuery query) {
        try {
            LoginUserVO user = SecurityUtils.getLoginUser();
            log.info("用户{}导出转载信源记录", user.getUserId());
            // 获取数据
            query.setPage(1);
            query.setLimit(5000); // 导出最多5000条
            List<ReprintSourceVO> results = this.pageList(query).getRecords();

            // 创建字段映射
            List<FieldMapping<ReprintSourceVO, ?>> fieldMappings = Arrays.asList(
                    FieldMapping.of(ReprintSourceVO::getWebsiteName, "网站名称"),
                    FieldMapping.of(ReprintSourceVO::getReprintSource, "转载信源名称"),
                    FieldMapping.of(ReprintSourceVO::getFilterStatusName, "过滤状态"),
                    FieldMapping.of(ReprintSourceVO::getFilterTime, "过滤时间")
            );

            // 创建Sheet配置
            SheetConfig<ReprintSourceVO> sheetConfig = SheetConfig.of("转载信源导出记录", fieldMappings, results);

            // 导出Excel
            ExcelExportUtil.exportSingleSheetExcel(httpServletResponse,"转载信源导出记录", sheetConfig);

        } catch (Exception e) {
            log.error("导出内容检查记录失败", e);
            throw new RuntimeException("导出失败：" + e.getMessage());
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long getOrCreateByWebsiteId(Long websiteId, String reprintSourceName) {
        if (websiteId == null || StrUtil.isBlank(reprintSourceName)) {
            return null;
        }
        ReprintSource findSource = this.lambdaQuery()
                .eq(ReprintSource::getWebsiteId, websiteId)
                .eq(ReprintSource::getReprintSource, reprintSourceName).one();
        if (findSource != null) {
            return findSource.getId();
        }
        try {
            // 直接尝试插入，如果已存在会抛出唯一索引冲突异常
            ReprintSource source = new ReprintSource()
                    .setWebsiteId(websiteId)
                    .setReprintSource(reprintSourceName)
                    .setFilterStatus(false)
                    .setCreateTime(DateUtil.date());
            this.save(source);
            return source.getId();
        } catch (DuplicateKeyException e) {
            // 如果插入失败，说明记录已存在，直接查询返回
            return this.lambdaQuery()
                    .eq(ReprintSource::getWebsiteId, websiteId)
                    .eq(ReprintSource::getReprintSource, reprintSourceName)
                    .one()
                    .getId();
        }
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchCreate(List<Long> websiteIds, Collection<String> reprintSources) {
        if (CollectionUtils.isEmpty(websiteIds) || CollectionUtils.isEmpty(reprintSources)) {
            return;
        }
        // 过滤空的转载信源并去重
        reprintSources = reprintSources.stream().map(StrUtil::trim)
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(reprintSources)) {
            return;
        }
        List<ReprintSource> candidateList = CollUtil.newArrayList();
        for (Long websiteId : CollUtil.newHashSet(websiteIds)) {
            for (String source : reprintSources) {
                ReprintSource rs = new ReprintSource();
                rs.setWebsiteId(websiteId);
                rs.setReprintSource(source);
                candidateList.add(rs);
            }
        }
        List<ReprintSource> toInsert = this.getBaseMapper().findNonExistent(candidateList);
        if (CollUtil.isNotEmpty(toInsert)) {
            log.info("待插入的记录数：{}", toInsert.size());
            this.saveBatch(toInsert);
        }
    }


}
