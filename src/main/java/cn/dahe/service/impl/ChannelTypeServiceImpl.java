package cn.dahe.service.impl;

import cn.dahe.dao.ChannelDao;
import cn.dahe.entity.ChannelType;
import cn.dahe.dao.ChannelTypeDao;
import cn.dahe.model.dto.ChannelTypeQuery;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.Result;
import cn.dahe.service.ChannelTypeService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 栏目类型服务实现类
 * 
 * <AUTHOR>
 * @date 2025-09-16
 */
@Slf4j
@Service
public class ChannelTypeServiceImpl extends BaseServiceImpl<ChannelTypeDao, ChannelType>  implements ChannelTypeService {

    @Autowired
    private ChannelTypeDao channelTypeDao;
    
    @Autowired
    private ChannelDao channelDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> insertChannelType(ChannelType channelType) {
        try {
            // 校验参数
            Result<String> validateResult = validateChannelType(channelType, false);
            if (!validateResult.isSuccess()) {
                return validateResult;
            }
            
            // 校验名称唯一性
            Result<String> uniqueResult = validateNameUnique(channelType.getName(), channelType.getParentId(), null);
            if (!uniqueResult.isSuccess()) {
                return uniqueResult;
            }
            
            // 校验父级ID合法性
            Result<String> parentResult = validateParentId(channelType.getParentId());
            if (!parentResult.isSuccess()) {
                return parentResult;
            }
            
            // 执行插入
            int result = channelTypeDao.insertChannelType(channelType);
            if (result > 0) {
                log.info("新增栏目类型成功，ID: {}, 名称: {}", channelType.getId(), channelType.getName());
                return Result.ok("新增栏目类型成功");
            }
            return Result.error("新增栏目类型失败");
        } catch (Exception e) {
            log.error("新增栏目类型异常", e);
            return Result.error("新增栏目类型失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> updateChannelType(ChannelType channelType) {
        try {
            // 校验参数
            Result<String> validateResult = validateChannelType(channelType, true);
            if (!validateResult.isSuccess()) {
                return validateResult;
            }
            
            // 校验记录是否存在
            ChannelType existingChannelType = channelTypeDao.selectChannelTypeById(channelType.getId());
            if (existingChannelType == null) {
                return Result.error("栏目类型不存在");
            }
            
            // 校验名称唯一性
            Result<String> uniqueResult = validateNameUnique(channelType.getName(), channelType.getParentId(), channelType.getId());
            if (!uniqueResult.isSuccess()) {
                return uniqueResult;
            }
            
            // 校验父级ID合法性
            Result<String> parentResult = validateParentId(channelType.getParentId());
            if (!parentResult.isSuccess()) {
                return parentResult;
            }
            
            // 执行更新
            int result = channelTypeDao.updateChannelType(channelType);
            if (result > 0) {
                log.info("修改栏目类型成功，ID: {}, 名称: {}", channelType.getId(), channelType.getName());
                
                // 更新相关栏目的 updatePeriod 字段
                try {
                    int updateChannelCount = channelDao.updateChannelUpdatePeriodByTypeId(
                        channelType.getId(), 
                        channelType.getUpdatePeriod()
                    );
                    log.info("同步更新栏目更新期限成功，栏目类型ID: {}, 更新期限: {}, 影响栏目数量: {}", 
                        channelType.getId(), channelType.getUpdatePeriod(), updateChannelCount);
                } catch (Exception e) {
                    log.error("同步更新栏目更新期限失败，栏目类型ID: {}, 更新期限: {}", 
                        channelType.getId(), channelType.getUpdatePeriod(), e);
                    // 这里不抛出异常，因为栏目类型已经更新成功，只是同步更新栏目失败
                }
                
                return Result.ok("修改栏目类型成功");
            }
            return Result.error("修改栏目类型失败");
        } catch (Exception e) {
            log.error("修改栏目类型异常", e);
            return Result.error("修改栏目类型失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> deleteChannelTypeById(Long id) {
        try {
            if (id == null) {
                return Result.error("栏目类型ID不能为空");
            }
            
            // 校验记录是否存在
            ChannelType channelType = channelTypeDao.selectChannelTypeById(id);
            if (channelType == null) {
                return Result.error("栏目类型不存在");
            }
            
            // 检查是否有子级栏目类型
            List<ChannelType> children = channelTypeDao.listChannelTypesByParentId(id);
            if (!children.isEmpty()) {
                return Result.error("存在子级栏目类型，无法删除");
            }
            
            // 执行逻辑删除
            int result = channelTypeDao.deleteChannelTypeById(id);
            if (result > 0) {
                log.info("删除栏目类型成功，ID: {}, 名称: {}", id, channelType.getName());
                
                // 重置相关栏目的 channelTypeId 和 updatePeriod 字段
                try {
                    int resetChannelCount = channelDao.resetChannelTypeAndUpdatePeriodByTypeId(id);
                    log.info("重置相关栏目字段成功，栏目类型ID: {}, 影响栏目数量: {}", id, resetChannelCount);
                } catch (Exception e) {
                    log.error("重置相关栏目字段失败，栏目类型ID: {}", id, e);
                    // 这里不抛出异常，因为栏目类型已经删除成功，只是重置栏目字段失败
                }
                
                return Result.ok("删除栏目类型成功");
            }
            return Result.error("删除栏目类型失败");
        } catch (Exception e) {
            log.error("删除栏目类型异常", e);
            return Result.error("删除栏目类型失败：" + e.getMessage());
        }
    }

    @Override
    public Result<ChannelType> getChannelTypeById(Long id) {
        try {
            if (id == null) {
                return Result.error("栏目类型ID不能为空");
            }
            ChannelType channelType = channelTypeDao.selectChannelTypeById(id);
            if (channelType == null) {
                return Result.error("栏目类型不存在");
            }
            return Result.ok(channelType);
        } catch (Exception e) {
            log.error("查询栏目类型异常", e);
            return Result.error("查询栏目类型失败：" + e.getMessage());
        }
    }

    @Override
    public Result<PageResult<ChannelType>> listChannelTypesByPage(ChannelTypeQuery query) {
        try {
            if (query == null) {
                query = new ChannelTypeQuery();
            }
            
            // 创建分页对象
            Page<ChannelType> page = new Page<>(query.getPage(), query.getLimit());
            
            // 查询分页数据
            IPage<ChannelType> pageResult = channelTypeDao.listChannelTypesByPage(page, query);
            
            // 转换为PageResult
            PageResult<ChannelType> result = PageResult.page(pageResult);
            
            return Result.ok(result);
        } catch (Exception e) {
            log.error("分页查询栏目类型异常", e);
            return Result.error("分页查询栏目类型失败：" + e.getMessage());
        }
    }

    @Override
    public Result<List<ChannelType>> listChannelTypeTree() {
        try {
            // 查询所有栏目类型
            List<ChannelType> allChannelTypes = channelTypeDao.listChannelTypeTree();
            
            // 构建树形结构
            List<ChannelType> treeList = buildTree(allChannelTypes);
            return Result.ok(treeList);
        } catch (Exception e) {
            log.error("查询栏目类型树结构异常", e);
            return Result.error("查询栏目类型树结构失败：" + e.getMessage());
        }
    }

    /**
     * 校验栏目类型参数
     * 
     * @param channelType 栏目类型
     * @param isUpdate 是否为更新操作
     * @return 校验结果
     */
    private Result<String> validateChannelType(ChannelType channelType, boolean isUpdate) {
        if (channelType == null) {
            return Result.error("栏目类型信息不能为空");
        }
        
        if (isUpdate && channelType.getId() == null) {
            return Result.error("栏目类型ID不能为空");
        }
        
        if (!StringUtils.hasText(channelType.getName())) {
            return Result.error("栏目类型名称不能为空");
        }
        
        if (channelType.getName().length() > 50) {
            return Result.error("栏目类型名称长度不能超过50个字符");
        }
        
        return Result.ok();
    }

    /**
     * 校验名称唯一性
     * 
     * @param name 名称
     * @param parentId 父级ID
     * @param excludeId 排除的ID（修改时使用）
     * @return 校验结果
     */
    private Result<String> validateNameUnique(String name, Long parentId, Long excludeId) {
        int count = channelTypeDao.checkNameDuplicate(name, parentId, excludeId);
        if (count > 0) {
            return Result.error("栏目类型名称在同一层级下已存在");
        }
        return Result.ok();
    }

    /**
     * 校验父级ID合法性
     * 
     * @param parentId 父级ID
     * @return 校验结果
     */
    private Result<String> validateParentId(Long parentId) {
        if (parentId != null && parentId > 0) {
            int count = channelTypeDao.checkParentIdExists(parentId);
            if (count == 0) {
                return Result.error("父级栏目类型不存在");
            }
        }
        return Result.ok();
    }

    /**
     * 构建树形结构
     * 
     * @param allChannelTypes 所有栏目类型
     * @return 树形结构列表
     */
    private List<ChannelType> buildTree(List<ChannelType> allChannelTypes) {
        if (allChannelTypes == null || allChannelTypes.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 按父级ID分组
        Map<Long, List<ChannelType>> parentIdMap = allChannelTypes.stream()
                .collect(Collectors.groupingBy(item -> item.getParentId() == null ? 0L : item.getParentId()));
        
        // 构建树形结构
        List<ChannelType> rootNodes = parentIdMap.getOrDefault(0L, new ArrayList<>());
        for (ChannelType rootNode : rootNodes) {
            buildChildren(rootNode, parentIdMap);
        }
        
        return rootNodes;
    }

    /**
     * 递归构建子节点
     * 
     * @param parent 父节点
     * @param parentIdMap 父级ID映射
     */
    private void buildChildren(ChannelType parent, Map<Long, List<ChannelType>> parentIdMap) {
        List<ChannelType> children = parentIdMap.getOrDefault(parent.getId(), new ArrayList<>());
        parent.setChildren(children);
        
        for (ChannelType child : children) {
            buildChildren(child, parentIdMap);
        }
    }
}