package cn.dahe.service.impl;

import cn.dahe.common.constants.WarnPushConstants;
import cn.dahe.dao.WarnPushRecordDao;
import cn.dahe.entity.WarnPlanPushUser;
import cn.dahe.entity.WarnPushMsg;
import cn.dahe.entity.WarnPushRecord;
import cn.dahe.entity.Website;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.PushRecordDto;
import cn.dahe.model.query.WarnPushRecordQuery;
import cn.dahe.model.vo.WarnPushRecordVO;
import cn.dahe.service.WarnPlanPushUserService;
import cn.dahe.service.WarnPlanService;
import cn.dahe.service.WarnPushRecordService;
import cn.dahe.service.WebsiteService;
import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class WarnPushRecordServiceImpl extends BaseServiceImpl<WarnPushRecordDao, WarnPushRecord> implements WarnPushRecordService {

    @Override
    public PageResult<WarnPushRecordVO> page(WarnPushRecordQuery query) {
        PageHelper.startPage(query.getPage(), query.getLimit());
        List<WarnPushRecordVO> list = this.baseMapper.list(query);
        PageInfo<WarnPushRecordVO> pageInfo = new PageInfo<>(list);
        return PageResult.page(pageInfo);
    }

    @Override
    public Integer create(WarnPushRecord record) {
        this.baseMapper.insert(record);
        return record.getId();
    }

    @Override
    public void relateRecordsWithMsg(List<Long> recordIds, Long msgId) {
        if (CollUtil.isEmpty(recordIds)) {
            return;
        }
        this.lambdaUpdate()
                .in(WarnPushRecord::getId, recordIds)
                .set(WarnPushRecord::getMsgId, msgId)
                .update();
    }

    @Resource
    private WebsiteService websiteService;
    @Resource
    private WarnPlanService warnPlanService;
    @Resource
    private WarnPlanPushUserService warnPlanPushUserService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PushRecordDto createContentPushWithPlans(List<Long> planIds, WarnPushMsg warnPushMsg, Long websiteId) {
        if (CollUtil.isEmpty(planIds)) {
            return null;
        }
        Set<WarnPlanPushUser> warnUsers = CollUtil.newHashSet();
        List<WarnPushRecord> records = CollUtil.newArrayList();
        Website website = websiteService.getById(websiteId);
        for (Long planId : planIds) {
            WarnPushRecord warnPushRecord = new WarnPushRecord();
            warnPushRecord.setWarnPlanId(planId.intValue());
            warnPushRecord.setWarnSourceName(website.getWebName());
            warnPushRecord.setSourcePlatform(website.getProcessType());
            warnPushRecord.setWarnType(WarnPushConstants.PUSH_TYPE_CONTENT);
            warnPushRecord.setWarnPushType(WarnPushConstants.METHOD_OF_SYSTEM);
            warnPushRecord.setWarnLink(warnPushMsg.getLink());
            // warnPushRecord.setData(JSON.toJSONString(warnPushMsg));
            warnPushRecord.setMsgId(warnPushMsg.getId());
            List<WarnPlanPushUser> warnPlanPushUsers = warnPlanPushUserService.listUserByPlanId(planId);
            if (CollUtil.isNotEmpty(warnPlanPushUsers)) {
                //  TODO 这里是没有去重的，看用法了
                warnPushRecord.setReceiverName(warnPlanPushUsers.stream().map(WarnPlanPushUser::getUserName).collect(Collectors.joining(",")));
                warnPushRecord.setReceiverUserId(warnPlanPushUsers.stream().map(WarnPlanPushUser::getUserId).map(String::valueOf).collect(Collectors.joining(",")));
                warnUsers.addAll(warnPlanPushUsers);
            }
            records.add(warnPushRecord);
        }
        this.saveBatch(records);
        return new PushRecordDto().setRecords(records).setWarnUsers(warnUsers);
    }

    /**
     * 更新内容检查推送记录
     */
    @Override
    public void updateCheckPushRecords(List<Integer> recordIds, WarnPushMsg warnPushMsg) {
        String link = warnPushMsg.getLink();
        this.lambdaUpdate()
                .in(WarnPushRecord::getId, recordIds)
                .eq(WarnPushRecord::getWarnType, WarnPushConstants.PUSH_TYPE_CONTENT)
                .set(WarnPushRecord::getActualPushTime, new Date())
                .set(WarnPushRecord::getWarnLink, link)
                .update();
    }
}


