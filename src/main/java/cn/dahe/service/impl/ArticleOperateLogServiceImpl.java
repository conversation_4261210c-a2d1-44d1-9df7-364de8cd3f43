package cn.dahe.service.impl;

import cn.dahe.dao.ArticleOperateLogDao;
import cn.dahe.dao.OperateLogDao;
import cn.dahe.entity.ArticleOperateLog;
import cn.dahe.entity.OperateLog;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.query.OperateLogQuery;
import cn.dahe.service.ArticleOperateLogService;
import cn.dahe.service.OperateLogService;
import cn.dahe.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 *
 */
@Service
@AllArgsConstructor
public class ArticleOperateLogServiceImpl extends BaseServiceImpl<ArticleOperateLogDao, ArticleOperateLog> implements ArticleOperateLogService {


    @Override
    public PageResult<ArticleOperateLog> page(OperateLogQuery query) {
        IPage<ArticleOperateLog> dataPage = baseMapper.selectPage(new Page(query.getPage(), query.getLimit()), getWrapper(query));
        return new PageResult<>(dataPage.getRecords(), dataPage);
    }


    private QueryWrapper<ArticleOperateLog> getWrapper(OperateLogQuery query) {
        QueryWrapper<ArticleOperateLog> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(query.getBeginTime())) {
            queryWrapper.ge("start_time", query.getBeginTime());
        }
        if (StringUtils.isNotBlank(query.getEndTime())) {
            queryWrapper.le("start_time", query.getEndTime());
        }
        if (StringUtils.isNotBlank(query.getRequestUrl())) {
            queryWrapper.like("request_url", query.getRequestUrl());
        }
        if (StringUtils.isNotBlank(query.getKeyword())) {
            queryWrapper.like("user_name", query.getKeyword());
        }
        queryWrapper.orderByDesc("start_time");
        return queryWrapper;
    }

}