package cn.dahe.service.impl;

import cn.dahe.check.html.HtmlMappingUtil;
import cn.dahe.common.callback.TaskCallback;
import cn.dahe.common.redis.DistributedLockHandler;
import cn.dahe.dao.CheckTaskDao;
import cn.dahe.entity.CheckContent;
import cn.dahe.entity.CheckTask;
import cn.dahe.enums.*;
import cn.dahe.model.dto.CheckApiExecuteDto;
import cn.dahe.service.CheckContentService;
import cn.dahe.service.CheckResultService;
import cn.dahe.service.CheckTaskService;
import cn.dahe.service.WebsiteService;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 文章检查内容服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Slf4j
@Service
public class CheckTaskServiceImpl extends ServiceImpl<CheckTaskDao, CheckTask> implements CheckTaskService {

    @Resource
    private WebsiteService websiteService;
    @Resource
    private CheckContentService checkContentService;
    @Resource
    private CheckResultService checkResultService;


    private CheckTask initCreateTask() {
        CheckTask checkTask = new CheckTask();
        checkTask.setCheckStatus(CheckStatusEnum.UNCHECKED.getValue());
        checkTask.setResultStatus(CheckResultStatusEnum.UNKNOWN.getValue());
        checkTask.setContentStatus(CheckContentStatusEnum.UNKNOWN.getValue());
        checkTask.setCreateTime(new Date());
        return checkTask;
    }

    @Override
    public CheckTask initCheckTask(CheckTaskRelationTypeEnum relationType, Long relationId, CheckStrategyEnum checkStrategy) {
        CheckTask checkTask = initCreateTask();
        checkTask.setCheckStrategy(checkStrategy.getValue());
        checkTask.setRelationType(relationType.getValue());
        checkTask.setRelationId(relationId);
        this.save(checkTask);
        return checkTask;
    }


    @Override
    public void updateContentSuccess(Long checkId, String title, String content, String code) {
        this.lambdaUpdate().eq(CheckTask::getId, checkId)
                .set(CheckTask::getContentStatus, CheckContentStatusEnum.SAVE_SUCCESS.getValue())
                .set(CheckTask::getContentFailType, ContentFailTypeEnum.NONE.getValue())
                .set(CheckTask::getContentFailMsg, null)
                .set(CheckTask::getContentFailReason, null)
                .set(CheckTask::getContentLength, StrUtil.length(content))
                .update();
        checkContentService.saveContent(checkId, title, content, code);
    }

    @Override
    public void updateContentFail(Long checkId, Integer contentStatus, String failMsg, String failReason) {
        this.lambdaUpdate()
                .eq(CheckTask::getId, checkId)
                .set(CheckTask::getContentStatus, contentStatus)
                .set(CheckTask::getContentFailType, ContentFailTypeEnum.NONE.getValue())
                .set(CheckTask::getContentFailMsg, failMsg)
                .set(CheckTask::getContentFailReason, failReason)
                .update();
    }


    @Async("taskExecutor")
    @Override
    public void executeCheckTask(CheckTask checkTask, CheckStatusEnum checkStatusEnum, TaskCallback<CheckTask> callback) {
        Long checkId = checkTask.getId();
        String lockPrefix = checkStatusEnum.getLockPrefix();
        if (lockPrefix == null) {
            log.error("{} 非法检查状态 {} ，跳过执行", checkId, checkStatusEnum.name());
            return;
        }
        String lockKey = StrUtil.format("{}:{}", lockPrefix, checkId);

        distributedLockHandler.executeWithLock(lockKey, () -> {
            // 检查任务状态，避免重复执行
            CheckTask currentTask = this.getById(checkId);
            Long relationId = currentTask.getRelationId();
            Integer relationType = currentTask.getRelationType();
            if (!checkStatusEnum.getValue().equals(currentTask.getCheckStatus())) {
                log.info("检查任务 {} 状态不为 {}，跳过执行", checkId, checkStatusEnum.name());
                return;
            }
            try {
                CheckContent checkContent = checkContentService.getById(checkId);
                String compressedTitle = checkContent.getCompressedTitle();
                String compressedContent = checkContent.getCompressedContent();
                String compressedCode = checkContent.getCompressedCode();
                Integer checkStrategyValue = checkTask.getCheckStrategy();
                CheckStrategyEnum checkStrategy = CheckStrategyEnum.getByValue(checkStrategyValue);
                log.info("{}-{}-{} 检查开始", relationType, relationId, checkId);

                //  调用接口 并 定位
                CheckApiExecuteDto titleCheckResult = CheckApiExecuteDto.successWithNoResult();
                CheckApiExecuteDto contentCheckResult = CheckApiExecuteDto.successWithNoResult();
                HtmlMappingUtil.HtmlMappingResult titleMappingResult = null;
                HtmlMappingUtil.HtmlMappingResult contentMappingResult = null;
                HtmlMappingUtil.HtmlMappingResult codeMappingResult = null;
                if (StrUtil.isNotBlank(compressedTitle)) {
                    titleMappingResult = HtmlMappingUtil.extractTextFromHtml(compressedTitle);
                    String plainTitle = titleMappingResult.getPlainText();
                    if (StrUtil.isNotBlank(plainTitle)) {
                        titleCheckResult = checkResultService.executeContentCheck(checkStrategy, plainTitle);
                    }
                }
                if (!titleCheckResult.isCompleted()) {
                    this.updateCheckFail(checkTask, titleCheckResult, ArticleLocationEnum.TITLE);
                    return;
                }
                int contentLength = 0;
                if (StrUtil.isNotBlank(compressedContent)) {
                    contentMappingResult = HtmlMappingUtil.extractTextFromHtml(compressedContent);
                    String plainContent = contentMappingResult.getPlainText();
                    contentLength = plainContent.length();
                    if (StrUtil.isNotBlank(plainContent)) {
                        contentCheckResult = checkResultService.executeContentCheck(checkStrategy, plainContent);
                    }
                }
                if (!contentCheckResult.isCompleted()) {
                    this.updateCheckFail(checkTask, contentCheckResult, ArticleLocationEnum.CONTENT);
                    return;
                }
                boolean hasResults = titleCheckResult.hasResults() || contentCheckResult.hasResults();
                CheckApiExecuteDto codeCheckResult = CheckApiExecuteDto.successWithNoResult();
                if (hasResults) {
                    if (StrUtil.isNotBlank(compressedCode)) {
                        codeMappingResult = HtmlMappingUtil.extractTextFromHtml(compressedCode);
                        String plainCode = codeMappingResult.getPlainText();
                        if (StrUtil.isNotBlank(plainCode)) {
                            codeCheckResult = checkResultService.executeContentCheck(checkStrategy, plainCode);
                        }
                    }
                    if (!codeCheckResult.isCompleted()) {
                        this.updateCheckFail(checkTask, contentCheckResult, ArticleLocationEnum.WEB_CODE);
                        return;
                    }
                    //  保存检查结果
                    checkResultService.saveCheckResults(checkId, ArticleLocationEnum.TITLE, titleCheckResult.getCheckResults(), titleMappingResult);
                    checkResultService.saveCheckResults(checkId, ArticleLocationEnum.CONTENT, contentCheckResult.getCheckResults(), contentMappingResult);
                    checkResultService.saveCheckResults(checkId, ArticleLocationEnum.WEB_CODE, codeCheckResult.getCheckResults(), codeMappingResult);
                    //  更新检查内容
                    checkContentService.updateContentByCheckResult(checkId, titleMappingResult, contentMappingResult, codeMappingResult);
                    //  更新检查状态
                    this.updateCheckSuccess(checkTask, contentLength, hasResults);

                    log.info("检查任务-{}-有结果：{} {}", currentTask.getId(), titleCheckResult.getCheckResults(), contentCheckResult.getCheckResults());

                    // 执行回调
                    if (callback != null) {
                        callback.onComplete(checkTask);
                    }
                } else {
                    this.updateCheckSuccess(checkTask, contentLength, hasResults);
                }
            } catch (Exception e) {
                log.error("{}-{}-{} 执行失败", relationType, relationId, checkId, e);
                throw e;
            }
        });
    }


    @Override
    public List<CheckTask> listNoResultTaskWithCheckStatus(CheckStatusEnum checkStatus) {
        return this.lambdaQuery()
                .eq(CheckTask::getCheckStatus, checkStatus.getValue())
                .eq(CheckTask::getContentStatus, CheckContentStatusEnum.SAVE_SUCCESS.getValue())
                // .eq(CheckTask::getResultStatus, CheckResultStatusEnum.UNKNOWN.getValue())
                .list();
    }

    @Override
    public List<CheckTask> listNoContentReviewTask() {
        return this.lambdaQuery()
                .in(CheckTask::getContentStatus, CheckContentStatusEnum.UNKNOWN.getValue(), CheckContentStatusEnum.SAVE_FAIL_RETRY.getValue())
                .eq(CheckTask::getRelationType, CheckTaskRelationTypeEnum.ARTICLE_REVIEW.getValue())
                .list();
    }


    public void updateCheckFail(CheckTask checkTask, CheckApiExecuteDto checkResult, ArticleLocationEnum articleLocationEnum) {
        this.lambdaUpdate()
                .eq(CheckTask::getId, checkTask.getId()).set(CheckTask::getCheckTime, new Date())
                .set(CheckTask::getCheckStatus, CheckStatusEnum.FAILED_RETRY.getValue())
                .set(CheckTask::getCheckFailType, checkResult.getFailType())
                .set(CheckTask::getCheckFailMsg, StrUtil.format("{}: {}", articleLocationEnum.getDesc(), checkResult.getFailMessage()))
                .set(CheckTask::getCheckFailReason, checkResult.getFailDetail())
                .update();
        log.info("检查任务-{}-失败: {}", checkTask.getId(), checkResult.getFailDetail());
    }

    public void updateCheckSuccess(CheckTask checkTask, Integer contentLength, boolean hasResults) {
        LambdaUpdateChainWrapper<CheckTask> updateChainWrapper = this.lambdaUpdate()
                .eq(CheckTask::getId, checkTask.getId())
                .set(CheckTask::getContentLength, contentLength)
                .set(CheckTask::getCheckTime, new Date())
                .set(CheckTask::getCheckStatus, CheckStatusEnum.CHECKED.getValue())
                .set(CheckTask::getCheckFailType, CheckFailTypeEnum.NONE.getValue())
                .set(CheckTask::getCheckFailReason, StrUtil.EMPTY).set(CheckTask::getCheckFailMsg, StrUtil.EMPTY);
        updateChainWrapper.set(CheckTask::getResultStatus, (hasResults ? CheckResultStatusEnum.HAS_RESULT : CheckResultStatusEnum.NOT_HAS_RESULT).getValue());
        updateChainWrapper.update();
    }

    @Resource
    private DistributedLockHandler distributedLockHandler;


}