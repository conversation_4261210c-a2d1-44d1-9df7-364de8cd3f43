package cn.dahe.service.impl;

import cn.dahe.dao.ChannelDao;
import cn.dahe.dao.WebsiteDao;
import cn.dahe.entity.Channel;
import cn.dahe.entity.Website;
import cn.dahe.enums.ProcessTypeEnum;
import cn.dahe.model.vo.PlatformVO;
import cn.dahe.service.PlatformQueryService;
import cn.dahe.service.WebsiteService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class PlatformQueryServiceImpl implements PlatformQueryService {

    @Resource
    private ChannelDao channelDao;

    @Resource
    private WebsiteDao websiteDao;
    @Resource
    private WebsiteService websiteService;

    private static final List<Integer> PROCESS_TYPES = Arrays.asList(0, 1, 2, 3, 6335, 6429, 6452, 6460);

    @Override
    public Map<String, Object> queryPlatformsAndWebsites() {
        Map<String, Object> result = new LinkedHashMap<>();

        // 初始化所有平台类型的空列表（key 使用拼音缩写）
        for (ProcessTypeEnum type : ProcessTypeEnum.values()) {
            result.put(keyForProcessType(type.getValue()), Collections.emptyList());
        }
        // 查询网站（isDel=0 且 status=1）并转换为 PlatformVO
        List<Website> websites = websiteService.lambdaQuery()
                .eq(Website::getIsDel, false)
                .eq(Website::getStatus, 1).list();
        Map<Integer, List<PlatformVO>> grouped = websites.stream()
                .map(this::toPlatformVO)
                .collect(Collectors.groupingBy(PlatformVO::getProcessType));
        // 放入结果 Map（覆盖空列表），key 使用拼音缩写
        for (Map.Entry<Integer, List<PlatformVO>> entry : grouped.entrySet()) {
            result.put(keyForProcessType(entry.getKey()), entry.getValue());
        }

        return result;
    }

    private PlatformVO toPlatformVO(Channel c) {
        PlatformVO vo = new PlatformVO();
        vo.setId((long) c.getId());
        vo.setName(c.getName());
        vo.setProcessType(c.getProcessType());
        vo.setUrl(c.getUrl());
        return vo;
    }

    private PlatformVO toPlatformVO(Website w) {
        PlatformVO vo = new PlatformVO();
        vo.setId(w.getId());
        vo.setName(w.getWebName());
        vo.setUrl(w.getWebUrl());
        vo.setProcessType(w.getProcessType());
        return vo;
    }

    private String keyForProcessType(Integer processType) {
        if (processType == null) return "unknown";
        switch (processType) {
            case 0:
                return "Website";
            case 1:
                return "wx";    // 微信
            case 2:
                return "wb";    // 微博
            case 3:
                return "tt";    // 头条
            case 6335:
                return "dy"; // 抖音
            case 6429:
                return "ks"; // 快手
            case 6452:
                return "wxsp"; // 微信视频号
            case 6460:
                return "xhs"; // 小红书
            default:
                return String.valueOf(processType);
        }
    }
}


