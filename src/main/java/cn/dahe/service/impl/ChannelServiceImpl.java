package cn.dahe.service.impl;

import cn.dahe.common.constants.StatusConstants;
import cn.dahe.dao.ChannelDao;
import cn.dahe.dao.ChannelOperationLogDao;
import cn.dahe.dao.ChannelTypeDao;
import cn.dahe.entity.Channel;
import cn.dahe.entity.ChannelType;
import cn.dahe.entity.Website;
import cn.dahe.enums.ProcessTypeEnum;
import cn.dahe.model.dto.BatchUpdateChannelTypeDto;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.Result;
import cn.dahe.model.query.ChannelQuery;
import cn.dahe.model.query.WebSiteChannelCheckQuery;
import cn.dahe.model.vo.LoginUserVO;
import cn.dahe.service.ChannelService;
import cn.dahe.service.WebsiteService;
import cn.dahe.task.DataPullScheduleTask;
import cn.dahe.utils.ChannelOperationLogUtils;
import cn.dahe.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 栏目服务实现类
 */
@Slf4j
@Service
public class ChannelServiceImpl extends BaseServiceImpl<ChannelDao, Channel> implements ChannelService {

    @Resource
    private WebsiteService webSiteService;

    @Resource
    private ChannelDao channelDao;

    @Resource
    private ChannelTypeDao channelTypeDao;

    @Resource
    private ChannelOperationLogDao channelOperationLogDao;

    @Value("${spring.profiles.active}")
    private String env;

    @Override
    public PageResult<Channel> page(ChannelQuery query) {
        //oderby
        QueryWrapper<Channel> wrapper = getWrapper(query);
        //channel type
        if (query.getChannelTypeId() != null) {
            wrapper.eq("channel_type_id", query.getChannelTypeId());
        }
        wrapper.orderByDesc("create_time");
        IPage<Channel> dataPage = baseMapper.selectPage(new Page<>(query.getPage(), query.getLimit()), wrapper);
        return new PageResult<>(dataPage.getRecords(), dataPage);
    }

    @Override
    public PageResult<Channel> page(WebSiteChannelCheckQuery query) {
        int countChannelsByWebsiteAndProcessType = channelDao.getCountChannelsByWebsiteAndProcessType(query);
        List<Channel> channelsByWebsiteAndProcessType = channelDao.getChannelsByWebsiteAndProcessType(query);
        return new PageResult<>(channelsByWebsiteAndProcessType, countChannelsByWebsiteAndProcessType);
    }

    @Override
    public Result<String> save(Channel vo, LoginUserVO user) {
        if (StringUtils.isBlank(vo.getUrl())) {
            return Result.error("栏目链接不能为空");
        }
        Website webSite = webSiteService.getById(vo.getSiteId());
        if (webSite == null) {
            return Result.error("网站选择错误");
        }
        vo.setSiteName(webSite.getWebName());
        vo.setCreateTime(new Date());
        vo.setCreateUserId(user.getUserId());
        vo.setCreateUserName(user.getUsername());
        vo.setLastModifyTime(vo.getCreateTime());
        boolean save = this.save(vo);
        if (!save) {
            return Result.error();
        }
        return Result.ok();
    }

    @Override
    public Result<String> update(Channel vo, LoginUserVO user) {
        Channel oldChannel = this.getById(vo.getId());
        if (oldChannel == null) {
            return Result.error("栏目ID选择错误");
        }
        Website webSite = webSiteService.getById(vo.getSiteId());
        if (webSite == null) {
            return Result.error("网站选择错误");
        }
        vo.setSiteName(webSite.getWebName());
        vo.setCreateTime(oldChannel.getCreateTime());
        vo.setLastModifyTime(new Date());
        boolean saveOrUpdate = this.saveOrUpdate(vo);
        if (!saveOrUpdate) {
            return Result.error();
        }
        // 记录操作日志
        ChannelOperationLogUtils.addUpdateLog(vo.getId(), user.getUsername());
        return Result.ok();
    }

    @Override
    public Result<String> updateType(int id, Long channelTypeId, LoginUserVO user) {
        // 更改抓取状态
        Channel oldChannel = this.getById(id);
        if (oldChannel == null) {
            return Result.error("Id传递错误");
        }
        ChannelType channelType = channelTypeDao.selectById(channelTypeId);
        if (channelType == null) {
            return Result.error("栏目类型不存在");
        }
        oldChannel.setChannelTypeId(channelType.getId());
        oldChannel.setUpdatePeriod(channelType.getUpdatePeriod());
        boolean update = updateById(oldChannel);
        if (!update) {
            return Result.error();
        }
        ChannelOperationLogUtils.addUpdateLog(oldChannel.getId(), user.getUsername());
        return Result.ok();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> batchUpdateType(BatchUpdateChannelTypeDto batchDto, LoginUserVO user) {
        try {
            log.info("开始批量更新栏目类型，原始参数: channelIds={}, channelTypeId={}", 
                    batchDto.getChannelIds(), batchDto.getChannelTypeId());
            
            // 获取解析后的栏目ID列表
            List<Integer> channelIdList = batchDto.getChannelIdList();
            log.info("解析后的栏目ID列表: {}", channelIdList);
            
            if (channelIdList.isEmpty()) {
                return Result.error("栏目ID列表为空");
            }
            // 校验栏目类型是否存在
            ChannelType channelType = channelTypeDao.selectById(batchDto.getChannelTypeId());
            log.info("查询到的栏目类型: {}", channelType);
            
            if (channelType == null) {
                return Result.error("栏目类型不存在，ID: " + batchDto.getChannelTypeId());
            }

            // 校验栏目ID列表中的栏目是否存在
            List<Channel> existingChannels = this.listByIds(channelIdList);
            log.info("查询到的现有栏目数量: {}, 期望数量: {}", existingChannels.size(), channelIdList.size());
            
            if (existingChannels.size() != channelIdList.size()) {
                return Result.error("部分栏目ID不存在，请检查后重试");
            }

            // 批量更新栏目类型
            UpdateWrapper<Channel> updateWrapper = new UpdateWrapper<>();
            updateWrapper.in("id", channelIdList)
                    .set("channel_type_id", channelType.getId())
                    .set("update_period", channelType.getUpdatePeriod())
                    .set("last_modify_time", new Date());
            log.info("执行批量更新SQL，更新条件: id in {}, 设置字段: channel_type_id={}, update_period={}", 
                    channelIdList, channelType.getId(), channelType.getUpdatePeriod());
            // 记录操作日志
            ChannelOperationLogUtils.addUpdateTypeLog(channelIdList, user.getUsername(),  channelType.getUpdatePeriod());
            boolean updateResult = this.update(updateWrapper);
            log.info("批量更新执行结果: {}", updateResult);
            
            if (!updateResult) {
                return Result.error("批量更新栏目类型失败");
            }

            log.info("批量更新栏目类型成功，更新数量: {}, 栏目类型ID: {}, 操作人: {}", 
                    channelIdList.size(), batchDto.getChannelTypeId(), user.getUsername());
            
            return Result.ok("批量更新栏目类型成功，共更新 " + channelIdList.size() + " 个栏目");
        } catch (Exception e) {
            log.error("批量更新栏目类型异常", e);
            return Result.error("批量更新栏目类型失败：" + e.getMessage());
        }
    }

    @Override
    public Result<String> updateStatus(String id, LoginUserVO user) {
        // 更改抓取状态
        Channel oldChannel = this.getById(id);
        if (oldChannel == null) {
            return Result.error("Id传递错误");
        }
        //订阅，取消订阅生效
        List<Long> list = Arrays.asList((long)oldChannel.getId());
        if (oldChannel.getEnable() == StatusConstants.COMMON_NORMAL) {
            DataPullScheduleTask.pushLingCai(list, StatusConstants.COMMON_DISABLE, env);
        } else {
            DataPullScheduleTask.pushLingCai(list, StatusConstants.COMMON_NORMAL, env);
        }
        oldChannel.setEnable(oldChannel.getEnable() == StatusConstants.COMMON_NORMAL ? StatusConstants.COMMON_DISABLE : StatusConstants.COMMON_NORMAL);
        boolean update = updateById(oldChannel);
        if (!update) {
            return Result.error();
        }
        // 记录操作日志
        ChannelOperationLogUtils.addUpdateLog(oldChannel.getId(), user.getUsername(),"更新状态");
        return Result.ok();
    }

    @Override
    public Long getWebsiteChannelId(Long websiteId, ProcessTypeEnum processType) {
        Channel websiteChannel = this.lambdaQuery().eq(Channel::getSiteId, websiteId)
                .eq(Channel::getProcessType, processType.getValue())
                .one();
        return websiteChannel == null ? null : websiteChannel.getId().longValue();
    }

    @Override
    public Long getWebsiteChannelId(Long websiteId, Integer processType) {
        ProcessTypeEnum processTypeEnum = ProcessTypeEnum.getByValue(processType);
        return this.getWebsiteChannelId(websiteId, processTypeEnum);
    }

    @Override
    public List<Long> getWebsiteChannelIds(List<Long> websiteIds, ProcessTypeEnum processType) {
        return this.lambdaQuery().in(Channel::getSiteId, websiteIds)
                .eq(Channel::getProcessType, processType.getValue())
                .select(Channel::getId)
                .list()
                .stream()
                .map(channel -> channel.getId().longValue())
                .collect(Collectors.toList());
    }

    @Override
    public List<Long> getWebsiteChannelIds(List<Long> websiteIds) {
        return this.lambdaQuery().in(Channel::getSiteId, websiteIds)
                .select(Channel::getId)
                .list()
                .stream()
                .map(channel -> channel.getId().longValue())
                .collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveById(Channel newChannel) {
        channelDao.insertById(newChannel);
    }


    private QueryWrapper<Channel> getWrapper(ChannelQuery query) {
        QueryWrapper<Channel> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(query.getName())) {
            queryWrapper.like("name", query.getName());
        }
        if (StringUtils.isNotBlank(query.getUrl())) {
            queryWrapper.like("url", query.getUrl());
        }
        if (StringUtils.isNotBlank(query.getEnable())) {
            queryWrapper.eq("enable", query.getEnable());
        }
        if (StringUtils.isNotBlank(query.getSiteId())) {
            queryWrapper.eq("site_id", query.getSiteId());
        }
        if (query.getProcessTypeList() != null && !query.getProcessTypeList().isEmpty()) {
            queryWrapper.in("process_type", query.getProcessTypeList());
        }
        return queryWrapper;
    }
}
