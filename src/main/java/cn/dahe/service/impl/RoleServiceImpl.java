package cn.dahe.service.impl;

import cn.dahe.common.constants.StatusConstants;

import cn.dahe.dao.RoleDao;
import cn.dahe.dao.UserDao;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.Result;
import cn.dahe.entity.Role;
import cn.dahe.entity.UserRole;
import cn.dahe.model.query.RoleQuery;
import cn.dahe.service.*;
import cn.dahe.utils.StringUtils;
import cn.dahe.model.vo.LoginUserVO;
import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 *
 */
@Service
@AllArgsConstructor
public class RoleServiceImpl extends BaseServiceImpl<RoleDao, Role> implements RoleService {

    @Resource
    private PermissionRoleService permissionRoleService;

    @Resource
    private UserRoleService userRoleService;

    @Resource
    private UserDao userDao;


    @Override
    public PageResult<Role> page(RoleQuery query) {
        IPage<Role> dataPage = baseMapper.selectPage(new Page(query.getPage(), query.getLimit()), getWrapper(query));
        return new PageResult<>(dataPage.getRecords(), dataPage);
    }

    @Override
    public List<Role> listByUserId(Integer userId) {
        ArrayList<Role> list = new ArrayList<>();
        List<UserRole> userRoles = userRoleService.listByUserId(userId);
        if (!userRoles.isEmpty()) {
            List<Integer> collect = userRoles.stream().map(UserRole::getRoleId).collect(Collectors.toList());
            List<Role> roles = this.listByIds(collect);
            list.addAll(roles);
        }
        return list;
    }

    @Override
    public Set<String> listSnByUserId(Integer userId) {
        HashSet<String> snList = new HashSet<>();
        List<Role> roles = listByUserId(userId);
        if (!roles.isEmpty()) {
            List<String> collect = roles.stream().map(Role::getSn).collect(Collectors.toList());
            snList.addAll(collect);
        }
        return snList;
    }


    private QueryWrapper<Role> getWrapper(RoleQuery query) {
        QueryWrapper<Role> wrapper = Wrappers.query();
        if (StringUtils.isNotBlank(query.getKeyword())) {
            wrapper.like("name", query.getKeyword());
        }
        if (StringUtils.isNotBlank(query.getStatus())) {
            wrapper.eq("status", query.getStatus());
        }
        return wrapper;
    }

    @Override
    public Result<String> save(Role role, String permissionIds, LoginUserVO user) {
        if (StringUtils.isBlank(role.getName())) {
            return Result.error("请输入角色名称");
        }
        if (StringUtils.isBlank(permissionIds)) {
            return Result.error("请选择权限");
        }
        Role entity = new Role();
        entity.setStatus(StatusConstants.COMMON_NORMAL);
        entity.setName(role.getName());
        entity.setNote(StringUtils.defaultIfBlank(role.getNote(), ""));
        entity.setSn(getSn(role.getName(), null));
        this.save(entity);
        //更新中间表
        permissionRoleService.saveByRoleIdAndPermissionIds(entity.getId(), permissionIds);
        return Result.ok();
    }

    private String getSn(String name, Integer id) {
        String sn = StringUtils.getFirstLetter(name, "");
        QueryWrapper<Role> wrapper = Wrappers.query();
        wrapper.eq("sn", sn);
        wrapper.eq("status", StatusConstants.COMMON_NORMAL);
        if (id != null) {
            wrapper.ne("id", id);
        }
        List<Role> roleList = baseMapper.selectList(wrapper);
        if (roleList.isEmpty()) {
            return sn;
        }
        return sn + RandomUtil.randomNumbers(3);
    }


    @Override
    public Result<String> update(String id, String permissionIds, Role vo) {
        Role role = baseMapper.selectById(id);
        if (role == null) {
            return Result.error("请选择正确的数据");
        }
        if (StringUtils.isBlank(role.getName())) {
            return Result.error("请输入角色名称");
        }
        if (StringUtils.isBlank(permissionIds)) {
            return Result.error("请选择权限");
        }
        role.setName(vo.getName());
        role.setNote(StringUtils.defaultIfBlank(vo.getNote(), ""));
        role.setSn(getSn(vo.getName(), role.getId()));
        updateById(role);
        //删除
        permissionRoleService.removeByRoleId(role.getId());
        //更新中间表
        permissionRoleService.saveByRoleIdAndPermissionIds(role.getId(), permissionIds);
        return Result.ok();
    }

    @Override
    public Result<String> updateStatus(String id) {
        Role role = baseMapper.selectById(id);
        if (role == null) {
            return Result.error("请选择正确的数据");
        }
        role.setStatus(role.getStatus() == StatusConstants.COMMON_NORMAL ? StatusConstants.COMMON_DISABLE : StatusConstants.COMMON_NORMAL);
        boolean update = updateById(role);
        if (!update) {
            return Result.error();
        }
        return Result.ok();
    }

    @Override
    public Role getBySn(String sn) {
        QueryWrapper<Role> wrapper = Wrappers.query();
        wrapper.eq("sn", sn);
        return this.getOne(wrapper);
    }

    @Override
    public Result<String> assignRole(String userId, String roleIds) {
        if (StringUtils.isBlank(userId)) {
            return Result.error("用户ID不能为空");
        }

        if (StringUtils.isBlank(roleIds)) {
            return Result.error("角色ID不能为空");
        }
        userRoleService.removeByUserId(Integer.parseInt(userId));
        userRoleService.saveByUserIdAndRoleIds(Integer.parseInt(userId), roleIds);
        return Result.ok();
    }

    @Override
    public Result<String> removeByUserId(String userId, String roleId) {
        QueryWrapper<UserRole> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("role_id", roleId);
        queryWrapper.eq("user_id", userId);
        if (!userRoleService.remove(queryWrapper)) {
            return Result.error();
        }
        return Result.ok();
    }
}