package cn.dahe.service.impl;

import cn.dahe.dao.LoginLogDao;
import cn.dahe.model.dto.PageResult;
import cn.dahe.entity.LoginLog;
import cn.dahe.model.query.LoginLogQuery;
import cn.dahe.service.LoginLogService;
import cn.dahe.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * init
 *
 * <AUTHOR> @since 1.0.0 2023-01-31
 */
@Service
@AllArgsConstructor
public class LoginLogServiceImpl extends BaseServiceImpl<LoginLogDao, LoginLog> implements LoginLogService {


    @Override
    public PageResult<LoginLog> page(LoginLogQuery query) {
        IPage<LoginLog> dataPage = baseMapper.selectPage(new Page(query.getPage(), query.getLimit()), getWrapper(query));
        return new PageResult<>(dataPage.getRecords(), dataPage);
    }


    private QueryWrapper<LoginLog> getWrapper(LoginLogQuery query) {
        QueryWrapper<LoginLog> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(query.getBeginTime())) {
            queryWrapper.ge("login_time", query.getBeginTime());
        }
        if (StringUtils.isNotBlank(query.getEndTime())) {
            queryWrapper.le("login_time", query.getEndTime());
        }
        if (StringUtils.isNotBlank(query.getKeyword())) {
            queryWrapper.like("user_name", query.getKeyword());
        }
        queryWrapper.orderByDesc("login_time");
        return queryWrapper;
    }

}