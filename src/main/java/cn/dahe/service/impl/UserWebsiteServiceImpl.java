package cn.dahe.service.impl;

import cn.dahe.dao.UserWebsiteDao;
import cn.dahe.entity.UserWebsite;
import cn.dahe.service.UserWebsiteService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class UserWebsiteServiceImpl extends ServiceImpl<UserWebsiteDao, UserWebsite> implements UserWebsiteService {
    @Override
    public List<Long> listWebsiteIdsByUserId(Integer userId) {
        return this.getBaseMapper().listWebsiteIdsByUserId(userId);
    }
}
