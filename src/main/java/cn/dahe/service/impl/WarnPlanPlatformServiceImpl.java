package cn.dahe.service.impl;

import cn.dahe.dao.WarnPlanPlatformDao;
import cn.dahe.entity.WarnPlanPlatform;
import cn.dahe.entity.Website;
import cn.dahe.model.request.WarnPlanPlatformParam;
import cn.dahe.service.WarnPlanPlatformService;
import cn.dahe.service.WebsiteService;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class WarnPlanPlatformServiceImpl extends ServiceImpl<WarnPlanPlatformDao, WarnPlanPlatform> implements WarnPlanPlatformService {

    @Resource
    private WebsiteService websiteService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void savePlatforms(Integer planId, List<WarnPlanPlatformParam> platforms) {
        if (planId == null || CollUtil.isEmpty(platforms)) {
            return;
        }
        List<WarnPlanPlatform> list = platforms.stream().filter(Objects::nonNull)
                .filter(param -> param.getWebsiteId() != null)
                .map(param -> {
                    WarnPlanPlatform p = new WarnPlanPlatform();
                    p.setWarnPlanId(planId);
                    Integer websiteId = param.getWebsiteId();
                    Website website = websiteService.getById(websiteId);
                    if (website == null) {
                        return null;
                    }
                    p.setWebsiteId(websiteId.longValue());
                    p.setPlatformType(website.getProcessType());
                    p.setCreateTime(new Date());
                    return p;
                }).filter(Objects::nonNull).collect(Collectors.toList());
        this.saveBatch(list);
    }
}
