package cn.dahe.service.impl;

import cn.dahe.dao.ChannelOperationLogDao;
import cn.dahe.entity.ChannelOperationLog;
import cn.dahe.enums.ChannelOperationTypeEnum;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.query.ChannelOperationLogQuery;
import cn.dahe.model.vo.LoginUserVO;
import cn.dahe.service.ChannelOperationLogService;
import cn.dahe.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * 栏目操作日志服务层实现
 */
@Service
@AllArgsConstructor
public class ChannelOperationLogServiceImpl extends BaseServiceImpl<ChannelOperationLogDao, ChannelOperationLog> implements ChannelOperationLogService {

    @Override
    public PageResult<ChannelOperationLog> page(ChannelOperationLogQuery query) {
        IPage<ChannelOperationLog> dataPage = baseMapper.selectPage(new Page(query.getPage(), query.getLimit()), getWrapper(query));
        return new PageResult<>(dataPage.getRecords(), dataPage);
    }

    @Override
    public void save(ChannelOperationLog channelOperationLog, LoginUserVO user) {
        channelOperationLog.setCreateTime(new Date());
//        channelOperationLog.setDelStatus(0); // 设置为正常状态
        save(channelOperationLog);
    }

    @Override
    public void update(String id, ChannelOperationLog channelOperationLog) {
        channelOperationLog.setId(Integer.valueOf(id));
        updateById(channelOperationLog);
    }

    @Override
    public void updateStatus(String id) {
        ChannelOperationLog channelOperationLog = new ChannelOperationLog();
        channelOperationLog.setId(Integer.valueOf(id));
//        channelOperationLog.setDelStatus(1); // 设置为删除状态
        updateById(channelOperationLog);
    }

    @Override
    public void addLog(String username, LocalDateTime time, ChannelOperationTypeEnum type, String extraInfo) {
        addLog(null, username, time, type, extraInfo);
    }

    @Override
    public void addLog(Integer channelId, String username, LocalDateTime time, ChannelOperationTypeEnum type, String extraInfo) {
        ChannelOperationLog log = new ChannelOperationLog();
        log.setChannelId(channelId);
        log.setUserName(username);
        log.setCreateTime(new Date());
        log.setOptType(type.getType());
//        log.setDelStatus(0);
        
        // 拼装日志内容
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String timeStr = time.format(formatter);
        String content = String.format("%s在%s操作了%s", username, timeStr, type.getDesc());
        
        if (StringUtils.isNotBlank(extraInfo)) {
            content += "，" + extraInfo;
        }
        
        log.setContent(content);
        log.setResult("成功"); // 默认设置为成功
        
        save(log);
    }

    /**
     * 构建查询条件
     */
    private QueryWrapper<ChannelOperationLog> getWrapper(ChannelOperationLogQuery query) {
        QueryWrapper<ChannelOperationLog> queryWrapper = new QueryWrapper<>();
        
        // 只查询未删除的记录
//        queryWrapper.eq("del_status", 0);
        
        if (query.getChannelId() != null) {
            queryWrapper.eq("channel_id", query.getChannelId());
        }
        
        if (StringUtils.isNotBlank(query.getUserName())) {
            queryWrapper.like("user_name", query.getUserName());
        }
        
        if (query.getType() != null) {
            queryWrapper.eq("type", query.getType().getType());
        }
        
        if (StringUtils.isNotBlank(query.getBeginTime())) {
            queryWrapper.ge("create_time", query.getBeginTime());
        }
        
        if (StringUtils.isNotBlank(query.getEndTime())) {
            queryWrapper.le("create_time", query.getEndTime());
        }
        
        if (StringUtils.isNotBlank(query.getContent())) {
            queryWrapper.like("content", query.getContent());
        }
        
        if (StringUtils.isNotBlank(query.getKeyword())) {
            queryWrapper.and(wrapper -> wrapper
                .like("user_name", query.getKeyword())
                .or()
                .like("content", query.getKeyword())
            );
        }
        
        queryWrapper.orderByDesc("create_time");
        return queryWrapper;
    }
}