package cn.dahe.service.impl;

import cn.dahe.dao.WebsiteOutLinkCheckRecordDao;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.WebsiteAccessOverviewDto;
import cn.dahe.model.dto.WebsiteUpdateStatsDto;
import cn.dahe.entity.Website;
import cn.dahe.entity.WebsiteOutLinkCheckRecord;
import cn.dahe.model.query.WebsiteOutLinkCheckRecordQuery;
import cn.dahe.model.vo.LoginUserVO;
import cn.dahe.service.WebsiteOutLinkCheckRecordService;
import cn.dahe.service.WebsiteService;
import cn.dahe.utils.excel.ExcelExportUtil;
import cn.dahe.utils.ListUtils;
import cn.dahe.utils.NumberUtils;
import cn.dahe.utils.StringUtils;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;


/**
 *
 */
@Slf4j
@Service
@AllArgsConstructor
public class WebsiteOutLinkCheckRecordServiceImpl extends BaseServiceImpl<WebsiteOutLinkCheckRecordDao, WebsiteOutLinkCheckRecord> implements WebsiteOutLinkCheckRecordService {

    @Resource
    private WebsiteService websiteService;


    @Override
    public PageResult<WebsiteOutLinkCheckRecord> pageCheckRecordDetail(WebsiteOutLinkCheckRecordQuery query) {
        IPage<WebsiteOutLinkCheckRecord> dataPage = baseMapper.selectPage(new Page(query.getPage(), query.getLimit()), getWrapper(query));
        return new PageResult<>(dataPage.getRecords(), dataPage);
    }


    @Override
    public WebsiteUpdateStatsDto totalOverview(WebsiteOutLinkCheckRecordQuery query) {
        List<Website> websiteList = getNewSiteList(query.getWebId());
        List<WebsiteAccessOverviewDto> overviewDtoList = new ArrayList<>();
        for (Website website : websiteList) {
            WebsiteAccessOverviewDto dto = buildWebsiteAccessOverview(website, query);
            overviewDtoList.add(dto);
        }
        //检测网站数
        int totalSites = overviewDtoList.size();
        // 按 errorCount 是否为0 分区：正常站点 & 出错站点
        Map<Boolean, List<WebsiteAccessOverviewDto>> partitioned = overviewDtoList.stream()
                .collect(Collectors.partitioningBy(dto -> dto.getErrorCount() == 0));
        //外链正常网站
        int normalSites = partitioned.getOrDefault(true, Collections.emptyList()).size();
        //外链异常网站
        int errorSites = totalSites - normalSites;
        String errorRate = NumberUtils.calculateRatioPercentage(errorSites, totalSites, 2);
        WebsiteUpdateStatsDto statsDto = new WebsiteUpdateStatsDto();
        statsDto.setTotalSites(totalSites);
        statsDto.setNormalSites(normalSites);
        statsDto.setErrorSites(errorSites);
        statsDto.setErrorRate(errorRate);
        return statsDto;
    }

    @Override
    public PageResult<WebsiteAccessOverviewDto> pageWebsiteStats(WebsiteOutLinkCheckRecordQuery query) {
        String newWebId = getNewWebId(query.getWebId());
        PageResult<Website> websitePageResult = websiteService.pageByWebIds(newWebId, query.getPage(), query.getLimit());
        List<WebsiteAccessOverviewDto> overviewDtoList = new ArrayList<>();

        for (Website website : websitePageResult.getList()) {
            WebsiteAccessOverviewDto dto = buildWebsiteAccessOverview(website, query);
            overviewDtoList.add(dto);
        }

        return new PageResult<>(overviewDtoList, websitePageResult.getTotal(), websitePageResult.getPageSize(), websitePageResult.getPage());
    }

    @Override
    public boolean updateFilterByWebIdAndLinkUrl(int filter, int webId, String linkurl) {
        UpdateWrapper<WebsiteOutLinkCheckRecord> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("web_id", webId);
        updateWrapper.eq("link_url", linkurl);
        updateWrapper.set("filter", filter);
        return update(updateWrapper);
    }

    @SneakyThrows
    @Override
    public void export(WebsiteOutLinkCheckRecordQuery query, LoginUserVO user, HttpServletResponse response) {
        try {
            if (query.getExportType() == 0) {
                log.info("用户{}导出外链详情记录", user.getUsername());
                List<Map<String, Object>> dataListResult = new ArrayList<>();
                List<WebsiteOutLinkCheckRecord> dataList = baseMapper.selectList(getWrapper(query));
                //过滤勾选
                if (StrUtil.isNotBlank(query.getIds())) {
                    dataList = dataList.stream().filter(record -> query.getIds().contains(record.getId().toString())).collect(Collectors.toList());
                }
                for (WebsiteOutLinkCheckRecord record : dataList) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("linkUrl", record.getLinkUrl());
                    map.put("webName", record.getWebName());
                    map.put("sourcePage", record.getSourcePage());
                    map.put("snapshot", "传参调预览接口");
                    map.put("type", record.getType() == 0 ? "普通" : "附件");
                    map.put("checkTime", DateUtil.formatDateTime(record.getCheckTime()));
                    map.put("filter", record.getFilter());
                    dataListResult.add(map);
                }
                ExcelExportUtil.commonExport("外链详情记录", new String[]{"序号", "异常外链", "来源", "来源页面", "外链快照", "外链类型","检测时间","审计状态"},
                        new String[]{"id","linkUrl", "webName", "sourcePage", "snapshot", "type","checkTime","filter"}, dataListResult, response);
            } else {
                log.info("用户{}导出外链统计记录", user.getUsername());
                List<Map<String, Object>> dataListResult = new ArrayList<>();
                //相当于勾选过了
                List<Website> tmpList = websiteService.listByWebIds(query.getWebId(),query.getIds());
                List<WebsiteAccessOverviewDto> dataList = new ArrayList<>();
                for (Website website : tmpList) {
                    WebsiteAccessOverviewDto dto = buildWebsiteAccessOverview(website, query);
                    dataList.add(dto);
                }
                for (WebsiteAccessOverviewDto dto : dataList) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("id", dto.getId());
                    map.put("websiteName", dto.getWebName());
                    map.put("websiteIndexUrl", dto.getWebUrl());
                    map.put("errorCount", dto.getErrorCount());
                    dataListResult.add(map);
                }
                ExcelExportUtil.commonExport("外链统计记录", new String[]{"序号", "网站名称", "网站地址", "异常外链数"},
                        new String[]{"id", "websiteName", "websiteIndexUrl", "errorCount"}, dataListResult, response);
            }
        } catch (Exception e) {
            log.error("导出外链记录失败", e);
            throw new IOException("导出失败：" + e.getMessage());
        }
    }


    private WebsiteAccessOverviewDto buildWebsiteAccessOverview(Website website, WebsiteOutLinkCheckRecordQuery query) {
        WebsiteAccessOverviewDto dto = new WebsiteAccessOverviewDto();
        dto.setId(website.getSiteId());
        dto.setWebUrl(website.getWebUrl());
        dto.setWebName(website.getWebName());

        //总的死链数
        Integer totalCount = baseMapper.countByFilters(
                String.valueOf(website.getSiteId()),
                query.getSourcePage(),
                query.getDeadLinkUrl(),
                query.getHttpCode(),
                query.getBeginTime(),
                query.getEndTime()
        );
        totalCount = totalCount == null ? 0 : totalCount;

        // 总的死链数
        int errorCount = totalCount;

        String errorRate = NumberUtils.calculateRatioPercentage(errorCount, totalCount, 2);

        dto.setTotalCount(totalCount);
        dto.setErrorCount(errorCount);
        dto.setErrorRate(errorRate);

        return dto;
    }


    private QueryWrapper<WebsiteOutLinkCheckRecord> getWrapper(WebsiteOutLinkCheckRecordQuery query) {
        QueryWrapper<WebsiteOutLinkCheckRecord> queryWrapper = Wrappers.query();
        List<Website> websiteList = getNewSiteList(query.getWebId());
        List<Integer> websiteIdList = websiteList.stream().map(Website::getSiteId).collect(Collectors.toList());
        if (websiteIdList.isEmpty()) {
            queryWrapper.eq("web_id", -1);
        }
        if (StringUtils.isNotBlank(query.getWebId())) {
            queryWrapper.in("web_id", websiteIdList);
        }
        if (StringUtils.isNotBlank(query.getBeginTime())) {
            queryWrapper.ge("check_time", query.getBeginTime());
        }
        if (StringUtils.isNotBlank(query.getEndTime())) {
            queryWrapper.le("check_time", query.getEndTime());
        }
        if (StringUtils.isNotBlank(query.getHttpCode())) {
            queryWrapper.eq("http_code", query.getHttpCode());
        }
        // 死链-模糊搜索
        if (StringUtils.isNotBlank(query.getDeadLinkUrlLike())) {
            queryWrapper.like("dead_link_url", query.getDeadLinkUrlLike());
        }
        // 来源页面
        if (StringUtils.isNotBlank(query.getSourcePage())) {
            queryWrapper.eq("source_page", query.getSourcePage());
        }
        if (StringUtils.isNotBlank(query.getDeadLinkType())) {
            queryWrapper.eq("type", query.getDeadLinkType());
        }
        if (StringUtils.isNotBlank(query.getLatest())) {
            queryWrapper.eq("latest", query.getLatest());
        }
        if (StringUtils.isNotBlank(query.getFilter())) {
            queryWrapper.eq("filter", query.getFilter());
        }
        if (StringUtils.isNotBlank(query.getIds())) {
            queryWrapper.in("id", ListUtils.transferIdsToList(query.getIds()));
        }
        queryWrapper.orderByDesc("check_time");
        return queryWrapper;
    }

    private List<Website> getNewSiteList(String webId) {
        List<Website> websiteList = new ArrayList<>();
        if (StringUtils.isBlank(webId)) {
            websiteList = websiteService.listByStatus(1);
        } else {
            websiteList = websiteService.listByIds(ListUtils.transferIdsToList(webId));
        }
        return websiteList;
    }

    private String getNewWebId(String webId) {
        List<Website> websiteList = new ArrayList<>();
        if (StringUtils.isBlank(webId)) {
            websiteList = websiteService.listByStatus(1);
        } else {
            websiteList = websiteService.listByIds(ListUtils.transferIdsToList(webId));
        }
        String ids = websiteList.stream().map(website -> website.getSiteId().toString()).collect(Collectors.joining(","));
        return ids;
    }


}