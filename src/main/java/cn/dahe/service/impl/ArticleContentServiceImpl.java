package cn.dahe.service.impl;

import cn.dahe.dao.ArticleContentDao;
import cn.dahe.entity.ArticleContent;
import cn.dahe.model.request.LingcaiPushDataDto;
import cn.dahe.service.ArticleContentService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 文章原文服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Service
public class ArticleContentServiceImpl extends ServiceImpl<ArticleContentDao, ArticleContent> implements ArticleContentService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveByPushData(Long id, LingcaiPushDataDto pushData) {
        ArticleContent articleContent = new ArticleContent();
        articleContent.setId(id);
        //  提取标题
        articleContent.setTitle(pushData.getTitle());
        //  提取正文
        articleContent.setContent(pushData.getContent());
        //  源码
        articleContent.setWebCode(pushData.getSource_code());
        this.saveOrUpdate(articleContent);
    }
}