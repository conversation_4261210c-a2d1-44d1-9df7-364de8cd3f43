package cn.dahe.service.impl;

import cn.dahe.dao.*;
import cn.dahe.entity.*;
import cn.dahe.model.dto.ChkTransferReplayDto;
import cn.dahe.model.dto.ChkTransferTaskDto;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.query.ChkTransferTaskQuery;
import cn.dahe.model.vo.ChkTransferTaskVO;
import cn.dahe.model.vo.LoginUserVO;
import cn.dahe.service.ChkTransferTaskService;
import cn.dahe.utils.TimeUtil;
import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 整改任务Service
 * <AUTHOR>
 * @date 2025-08-21
 */
@Slf4j
@Service
public class ChkTransferTaskServiceImpl extends ServiceImpl<ChkTransferTaskDao, ChkTransferTask> implements ChkTransferTaskService {

    @Autowired
    private ArticleDao articleDao;

    @Autowired
    private CheckResultDao checkResultDao;

    @Autowired
    private UserDao userDao;

    @Autowired
    private WebsiteDao websiteDao;

    @Autowired
    private ChkTransferReplayDao chkTransferReplayDao;

    @Transactional
    @Override
    public boolean transferSend(ChkTransferTaskDto taskDto, LoginUserVO user) {
        if (taskDto == null) {
            return false;
        }
        // 设置创建时间和发送时间
        Date now = new Date();
        List<ChkTransferTask> taskListEntity = new ArrayList<>();
        ChkTransferTask el = new ChkTransferTask();
        if (taskDto.getWebsiteId() == null) {
            log.error("站点ID不能为空");
            throw new IllegalArgumentException("站点ID不能为空");
        }
        Website website = websiteDao.selectById(taskDto.getWebsiteId());
        if (website == null) {
            log.error("站点不存在");
            throw new IllegalArgumentException("站点不存在");
        }
        if (taskDto.getArticleId() == null) {
            log.error("文章ID不能为空");
            throw new IllegalArgumentException("文章ID不能为空");
        }
        Article article = articleDao.selectById(taskDto.getArticleId());
        if (article == null) {
            log.error("文章不存在");
            throw new IllegalArgumentException("文章不存在");
        }
        int lv1 = 0;
        int lv2 = 0;
        int lv3 = 0;
        int lv4 = 0;
        int lv5 = 0;
        if (taskDto.getSelectResultIds() != null) {
            String[] errorIds = taskDto.getSelectResultIds().split(",");
            for (String errorId : errorIds) {
                CheckResult checkResult = checkResultDao.selectById(Long.parseLong(errorId));
                if (checkResult != null) {
                    switch (checkResult.getErrorLevel()) {
                        case 1:
                            lv1++;
                            break;
                        case 2:
                            lv2++;
                            break;
                        case 3:
                            lv3++;
                            break;
                        case 4:
                            lv4++;
                            break;
                        case 5:
                            lv5++;
                            break;
                    }
                }
            }
        }
        el.setSeriousErrorCount(lv1);
        el.setCommonErrorCount(lv2);
        el.setMaybeErrorCount(lv3);
        el.setDiyErrorCount(lv4);
        el.setRiskTipCount(lv5);
        List<User> userList = userDao.getUserBySiteId(taskDto.getWebsiteId());
        if (userList == null || userList.isEmpty()) {
            log.error("站点未分配用户，无法下发整改");
            throw new IllegalArgumentException("站点未分配用户，无法下发整改");
        }
        String userIds = userList.stream()
                .map(e -> e.getUserId() + "")
                .collect(Collectors.joining(","));
        String userNames = userList.stream()
                .map(e -> e.getUserName() + "")
                .collect(Collectors.joining(","));
        String depIds = userList.stream()
                .map(e -> e.getDepId() + "")
                .collect(Collectors.joining(","));
        String depNames = userList.stream()
                .map(e -> e.getDepName() + "")
                .collect(Collectors.joining(","));
        el.setSiteId(website.getSiteId());
        el.setSiteName(website.getWebName());
        el.setSiteUrl(website.getWebUrl());
        el.setArticleId(article.getId());
        el.setArticleUrl(article.getArticleUrl());
        el.setArticleTitle(article.getTitle());
        el.setTransferUserId(userIds);
        el.setTransferUserName(userNames);
        el.setDepId(depIds);
        el.setDepName(depNames);
        el.setCreateTime(now);
        el.setIsDel(false);
        el.setSendTime(now);
        el.setCreateUserId(user.getUserId());
        el.setCreateUserName(user.getUsername());
        el.setSelectResultIds(taskDto.getSelectResultIds());
        taskListEntity.add(el);
        //更新文章整改状态
        article.setRectifyStatus(1);
        articleDao.updateById(article);

        int result = this.getBaseMapper().batchInsert(taskListEntity);
        return result > 0;
    }

    @Override
    public Map<String, Object> count(ChkTransferTaskQuery query) {
        // 计算本周和上周的时间范围
        Date now = new Date();
        Date currentWeekBegin = TimeUtil.getWeekBegin(now);
        Date nextWeekBegin = TimeUtil.getWeekBegin(DateUtils.addWeeks(now, 1));
        Date lastWeekBegin = TimeUtil.getWeekBegin(DateUtils.addWeeks(now, -1));

        //分别查询本周和上周
        Map<String, Object> statistics = new HashMap<>();
        Map<String, Object> thisWeek = this.getBaseMapper().getTaskStatisticsByTimeRange(query, currentWeekBegin, nextWeekBegin);
        Map<String, Object> lastWeek = this.getBaseMapper().getTaskStatisticsByTimeRange(query, lastWeekBegin, currentWeekBegin);
        Map<String, Object> allData = this.getBaseMapper().getTaskStatisticsByTimeRange(null, null, null);
        // 合并结果
        statistics.put("thisWeek", thisWeek);
        statistics.put("lastWeek", lastWeek);
        statistics.put("allData", allData);
        return statistics;
    }

    @Override
    public PageResult<ChkTransferTaskVO> page(ChkTransferTaskQuery query) {
        try {
            Page<ChkTransferTaskVO> page = new Page<>(query.getPage(), query.getLimit());
            IPage<ChkTransferTaskVO> result = this.getBaseMapper().selectPage(page, query);
            return PageResult.page(result);
        } catch (Exception e) {
            log.error("查询转办督办失败，返回兜底数据", e);
        }
        return new PageResult<>();
    }

    @Override
    public int add(ChkTransferReplayDto chkTransferReplayDto, LoginUserVO user) {
        if (chkTransferReplayDto != null) {
            ChkTransferReplay chkTransferReplay = new ChkTransferReplay();
            BeanUtil.copyProperties(chkTransferReplayDto, chkTransferReplay);
            chkTransferReplay.setCreateUserId(user.getUserId());
            chkTransferReplay.setCreateUserName(user.getUsername());
            //更新文章整改状态
            Long transferTaskId = chkTransferReplayDto.getTransferTaskId();
            ChkTransferTask chkTransferTask = this.getById(transferTaskId);
            Article article = articleDao.selectById(chkTransferTask.getArticleId());
            if (article == null) {
                log.error("整改文章不存在");
                throw new IllegalArgumentException("整改文章不存在");
            }
            article.setRectifyStatus(chkTransferReplayDto.getRectifyStatus());
            articleDao.updateById(article);
            ChkTransferReplay oneReplay = chkTransferReplayDao.selectOne(new LambdaQueryWrapper<ChkTransferReplay>().eq(ChkTransferReplay::getTransferTaskId, transferTaskId));
            if (oneReplay != null) {
                oneReplay.setTransferArticleUrl(chkTransferReplayDto.getTransferArticleUrl());
                oneReplay.setTransferResultImgUrl(chkTransferReplayDto.getTransferResultImgUrl());
                oneReplay.setTransferMsg(chkTransferReplayDto.getTransferMsg());
                oneReplay.setUpdateUserId(user.getUserId());
                oneReplay.setUpdateUserName(user.getUsername());
                return chkTransferReplayDao.updateById(oneReplay);
            }
            return chkTransferReplayDao.insert(chkTransferReplay);
        }
        return 0;
    }

    @Override
    public int update(ChkTransferReplayDto chkTransferReplayDto, LoginUserVO user) {
        if (chkTransferReplayDto.getTransferTaskId() != null) {
            ChkTransferReplay oneReplay = chkTransferReplayDao.selectOne(new LambdaQueryWrapper<ChkTransferReplay>().eq(ChkTransferReplay::getTransferTaskId, chkTransferReplayDto.getTransferTaskId()));
            if (oneReplay != null) {
                oneReplay.setTransferArticleUrl(chkTransferReplayDto.getTransferArticleUrl());
                oneReplay.setTransferResultImgUrl(chkTransferReplayDto.getTransferResultImgUrl());
                oneReplay.setTransferMsg(chkTransferReplayDto.getTransferMsg());
                oneReplay.setUpdateUserId(user.getUserId());
                oneReplay.setUpdateUserName(user.getUsername());
                return chkTransferReplayDao.updateById(oneReplay);
            }
        } else {
            log.error("整改回复不存在");
            throw new IllegalArgumentException("整改回复不存在");
        }
        return 0;
    }

    @Override
    public ChkTransferReplay getDetail(Long id) {
        ChkTransferReplay oneReplay = chkTransferReplayDao.selectOne(new LambdaQueryWrapper<ChkTransferReplay>().eq(ChkTransferReplay::getTransferTaskId, id));
        return oneReplay;
    }

}
