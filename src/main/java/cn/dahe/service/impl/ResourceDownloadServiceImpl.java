package cn.dahe.service.impl;


import cn.dahe.common.constants.StatusConstants;
import cn.dahe.dao.ResourceDownloadDao;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.Result;
import cn.dahe.entity.ResourceDownload;
import cn.dahe.model.query.ResourceDownloadQuery;
import cn.dahe.service.ResourceDownloadService;
import cn.dahe.utils.StringUtils;
import cn.dahe.model.vo.LoginUserVO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023-08-11
 */
@Service
public class ResourceDownloadServiceImpl extends BaseServiceImpl<ResourceDownloadDao, ResourceDownload> implements ResourceDownloadService {


    @Override
    public PageResult<ResourceDownload> page(ResourceDownloadQuery query) {
        IPage<ResourceDownload> dataPage = baseMapper.selectPage(new Page(query.getPage(), query.getLimit()), getWrapper(query));
        return new PageResult<>(dataPage.getRecords(), dataPage);
    }

    @Override
    public Result<String> save(ResourceDownload resourceDownload, LoginUserVO user) {
        if (StringUtils.isBlank(resourceDownload.getName())) {
            return Result.error("名称不能为空");
        }
        if (StringUtils.isBlank(resourceDownload.getAttachmentUrl())) {
            return Result.error("附件地址不能为空");
        }
        ResourceDownload entity = new ResourceDownload();
        entity.setAttachmentUrl(resourceDownload.getAttachmentUrl());
        entity.setName(resourceDownload.getName());
        entity.setCreateTime(new Date());
        entity.setCreateUserId(user.getUserId());
        entity.setCreateUserName(user.getUsername());
        entity.setStatus(StatusConstants.COMMON_NORMAL);
        if (!save(entity)) {
            return Result.error();
        }
        return Result.ok();
    }

    @Override
    public Result<String> updateStatus(String id, LoginUserVO user) {
        ResourceDownload resourceDownload = this.getById(id);
        if (resourceDownload == null) {
            return Result.error("该资源不存在，请重新选择");
        }
        resourceDownload.setStatus(resourceDownload.getStatus().equals(StatusConstants.COMMON_NORMAL) ? StatusConstants.COMMON_DISABLE : StatusConstants.COMMON_NORMAL);
        if (!updateById(resourceDownload)) {
            return Result.error();
        }
        return Result.ok();
    }


    private QueryWrapper<ResourceDownload> getWrapper(ResourceDownloadQuery query) {
        QueryWrapper<ResourceDownload> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", StatusConstants.COMMON_NORMAL);
        return queryWrapper;
    }
}
