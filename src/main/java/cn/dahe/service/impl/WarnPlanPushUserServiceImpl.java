package cn.dahe.service.impl;

import cn.dahe.dao.WarnPlanPushUserDao;
import cn.dahe.entity.WarnPlanPushUser;
import cn.dahe.service.WarnPlanPushUserService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class WarnPlanPushUserServiceImpl extends ServiceImpl<WarnPlanPushUserDao, WarnPlanPushUser> implements WarnPlanPushUserService {
    @Override
    public List<WarnPlanPushUser> listUserByPlanId(Long planId) {
        return this.lambdaQuery()
                .eq(WarnPlanPushUser::getWarnPlanId, planId)
                .list();
    }
}
