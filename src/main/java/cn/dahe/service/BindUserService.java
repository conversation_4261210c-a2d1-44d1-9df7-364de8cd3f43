package cn.dahe.service;

import cn.dahe.model.dto.Result;

/**
 * 绑定预警人员服务
 * <AUTHOR>
 */
public interface BindUserService {
    /**
     * 绑定预警人员
     * @param token 加密后的openId
     * @param phone 手机号
     * @param code 验证码
     * @return 返回绑定结果
     */
    Result<String> bind(String token,String userName, String phone, String code);

    /**
     * 发送短信验证码
     * @param phone 手机号
     * @return 返回发送结果
     */
    Result<String> sendSmsCode(String phone);


}
