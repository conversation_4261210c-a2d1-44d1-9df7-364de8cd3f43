package cn.dahe.service;

import cn.dahe.entity.ChkTransferReplay;
import cn.dahe.entity.ChkTransferTask;
import cn.dahe.model.dto.ChkTransferReplayDto;
import cn.dahe.model.dto.ChkTransferTaskDto;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.query.ChkTransferTaskQuery;
import cn.dahe.model.vo.ChkTransferTaskVO;
import cn.dahe.model.vo.LoginUserVO;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 整改任务Service
 * <AUTHOR>
 * @date 2025-08-21
 */
public interface ChkTransferTaskService extends BaseService<ChkTransferTask> {



    /**
     * 整改任务下发
     * @param chkTransferTaskDto
     * @return
     */
    boolean transferSend(@Valid ChkTransferTaskDto chkTransferTaskDto, LoginUserVO user);

    /**
     * 统计
     *
     * @param query
     * @return
     */
    Map<String, Object> count(ChkTransferTaskQuery query);

    /**
     * 分页查询
     *
     * @param query
     * @return
     */
    PageResult<ChkTransferTaskVO> page(ChkTransferTaskQuery query);

    /**
     * 新增整改回复
     *
     * @param chkTransferReplayDto
     * @return
     */
    int add(ChkTransferReplayDto chkTransferReplayDto, LoginUserVO user);

    /**
     * 修改整改回复
     *
     * @param chkTransferTaskDto
     * @return
     */
    int update(ChkTransferReplayDto chkTransferTaskDto, LoginUserVO user);

    /**
     * 获取整改回复详情
     *
     * @param id
     * @return
     */
    ChkTransferReplay getDetail(Long id);
}
