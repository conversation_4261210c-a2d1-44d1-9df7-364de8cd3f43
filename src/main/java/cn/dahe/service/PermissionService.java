package cn.dahe.service;

import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.Result;
import cn.dahe.model.dto.TreeNode;
import cn.dahe.entity.Permission;
import cn.dahe.model.query.PermissionQuery;
import cn.dahe.model.vo.LoginUserVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Collection;
import java.util.List;

public interface PermissionService extends IService<Permission> {
    /**
     * 分页查询
     *
     * @param query
     * @return
     */
    PageResult<Permission> page(PermissionQuery query);

    /**
     * 添加
     *
     * @param permission
     * @param user
     * @return
     */
    Result<String> save(Permission permission, LoginUserVO user);

    /**
     * 编辑
     *
     * @param id
     * @param permission
     * @return
     */
    Result<String> update(String id, Permission permission);

    /**
     * 修改状态
     *
     * @param id
     * @return
     */
    Result<String> updateStatus(String id);


    /**
     * 权限树
     *
     * @return
     */
    List<TreeNode> getTree();


    /**
     * 角色权限树
     *
     * @return
     */
    List<TreeNode> getRoleTree(Integer roleId);


    /**
     * 根据角色查找权限
     */
    List<Permission> listByRoleIds(Collection<Integer> roleIds);



    /**
     * 根据角色SN查找权限
     */
    List<Permission> listByRoleSns(Collection<String> roleSns);
}