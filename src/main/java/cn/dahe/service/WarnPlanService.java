package cn.dahe.service;

import cn.dahe.entity.WarnPlan;
import cn.dahe.entity.WarnPlanPushUser;
import cn.dahe.enums.WarnDetailRelateTypeEnum;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.query.WarnPlanQuery;
import cn.dahe.model.request.WarnPlanPlatformParam;
import cn.dahe.model.request.WarnPlanPushUserParam;
import cn.dahe.model.request.WarnPlanSaveRequest;
import cn.dahe.model.vo.WarnPlanVO;

import java.util.List;

public interface WarnPlanService {

    PageResult<WarnPlanVO> page(WarnPlanQuery query);

    WarnPlanVO get(Integer id);

    Integer create(WarnPlanSaveRequest warnPlanSaveRequest);

    Integer create(WarnPlan warnPlan, List<WarnPlanPlatformParam> platforms, List<WarnPlanPushUserParam> pushUsers);

    Boolean update(WarnPlanSaveRequest warnPlanSaveRequest);

    Boolean update(WarnPlan warnPlan, List<WarnPlanPlatformParam> platforms, List<WarnPlanPushUserParam> pushUsers);

    Boolean delete(Integer id);

    List<WarnPlan> listAll();

    List<WarnPlanPushUser> getPushUserByWarnPlanId(Integer warnPlanId);

    List<Integer> getSiteIdsByWarnPlanId(Integer warnPlanId);

    List<Long> listAvailablePlanWithCondition(Long websiteId, WarnDetailRelateTypeEnum relateType);
}


