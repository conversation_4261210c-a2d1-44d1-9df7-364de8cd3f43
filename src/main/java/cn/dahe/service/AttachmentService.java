package cn.dahe.service;

import cn.dahe.entity.Article;
import cn.dahe.entity.Attachment;
import cn.dahe.model.dto.AttachmentContentDTO;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.WarnCheckDetailDto;
import cn.dahe.model.query.AttachmentCheckQuery;
import cn.dahe.model.vo.AttachmentCheckVO;
import cn.dahe.model.vo.CheckSnapshotVO;

import java.io.IOException;
import java.util.List;

/**
 * 附件检查Service - 对应原型图功能
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface AttachmentService extends BaseService<Attachment> {

    // ==================== 附件检查记录 ====================

    /**
     * 分页查询附件检查记录
     *
     * @param query 查询参数
     * @return 分页结果
     */
    PageResult<AttachmentCheckVO> pageAttachmentAndCheckResults(AttachmentCheckQuery query);

    /**
     * 根据ID获取附件检查详情
     *
     * @param attachmentId ID
     * @return 详情
     */
    AttachmentCheckVO getAttachmentAndCheckResults(Long attachmentId, AttachmentCheckQuery query,boolean doMark);
    // ==================== 数据导出 ====================

    /**
     * 导出附件检查记录
     *
     * @param query 查询参数
     */
    void exportByQuery(AttachmentCheckQuery query) throws IOException;

    void saveByPushData(Article article, List<String> fileLink);

    /**
     * 获取并转换附件内容
     * 
     * @param attachment 附件信息
     * @return 附件内容DTO，包含转换后的HTML内容和文件信息
     * @throws RuntimeException 当文件下载或转换失败时抛出异常
     */
    AttachmentContentDTO getAndTransferAttachmentContent(Attachment attachment);

    void relateWithCheckTask(Long attachmentId, Long checkId);

    List<Attachment> listNoContentAttachment();
    List<Attachment> listNoTaskAttachment();

    void updateByDto(Long attachmentId,AttachmentContentDTO contentDTO);

    WarnCheckDetailDto getWarnAttachmentInfo(Long attachmentId);

    CheckSnapshotVO getSnapshotInfo(Long relationId);
}
