package cn.dahe.service;

import cn.dahe.entity.WarnUser;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.Result;
import cn.dahe.model.dto.WarnUserDTO;
import cn.dahe.model.query.WarnUserQuery;

public interface WarnUserService extends BaseService<WarnUser> {

    Integer create(WarnUser user);

    Result<Boolean> updateForSubscribe(String openId, Integer subscribeStatus);

    Boolean deleteById(Integer id);

    WarnUserDTO getByIdMasked(Integer id);

    PageResult<WarnUserDTO> pageMasked(WarnUserQuery query);

    /**
     * 修改预警接收人用户名
     * @param id 用户ID
     * @param userName 新用户名
     * @return 是否修改成功
     */
    Boolean updateUsername(Integer id, String userName);
}


