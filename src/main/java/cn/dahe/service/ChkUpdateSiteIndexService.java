package cn.dahe.service;

import cn.dahe.entity.ChkUpdateSiteIndex;
import cn.dahe.entity.ChkUpdateSiteIndexResult;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.query.ChkUpdateSiteIndexQuery;
import cn.dahe.model.vo.ChkUpdateSiteIndexVO;
import cn.dahe.model.vo.LoginUserVO;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 首页更新检查Service - 基于article表进行业务处理
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface ChkUpdateSiteIndexService extends BaseService<ChkUpdateSiteIndex> {

    // ==================== 首页更新检查概览 ====================

    /**
     * 获取首页更新检查概览统计
     * 包含：检测网站数、更新网站、未更新网站
     *
     * @param query 查询参数
     * @return 统计数据
     */
    Map<String, Object> getOverviewStatistics(ChkUpdateSiteIndexQuery query);

    // ==================== 首页更新检查记录 ====================

    /**
     * 分页查询首页更新检查记录
     *
     * @param query 查询参数
     * @return 分页结果
     */
    PageResult<ChkUpdateSiteIndexVO> page(ChkUpdateSiteIndexQuery query);

    /**
     * 根据ID获取首页更新检查详情
     *
     * @param id ID
     * @return 详情
     */
    ChkUpdateSiteIndexVO get(Long id);

    /**
     * 同步首页更新数据
     * 从采集中心获取首页更新情况并保存到数据库
     *
     * @return 同步结果统计
     */
    Map<String, Object> syncHomepageUpdates();

    /**
     * 分页查询chk_update_site_index表的详细记录
     *
     * @param query 查询参数
     * @return 分页结果
     */
    PageResult<ChkUpdateSiteIndex> pageDetail(ChkUpdateSiteIndexQuery query);


    // ==================== 数据导出 ====================

    /**
     * 导出检查记录
     *
     * @param query 查询参数
     * @param user 当前用户
     * @param response HTTP响应
     */
    void export(ChkUpdateSiteIndexQuery query, LoginUserVO user, HttpServletResponse response) throws IOException;

    void delUpdateSiteIndexData(List<Long> ids, List<ChkUpdateSiteIndexResult> updateSiteIndexResults);

    List<Map<String, Object>> selectSiteIndexTaskInsertTodayData();

    List<Long> getWaitDeleteSiteIndexIds();

}
