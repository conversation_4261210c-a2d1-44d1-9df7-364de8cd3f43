package cn.dahe.service;

import cn.dahe.entity.ReprintSource;
import cn.dahe.model.query.ReprintSourceQuery;
import cn.dahe.model.vo.ReprintSourceVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Collection;
import java.util.List;

/**
 * 站点转载信源关联表 服务接口
 */
public interface ReprintSourceService extends IService<ReprintSource> {
    
    /**
     * 分页查询转载信源列表 暂时只查站点关联
     *
     * @param query 查询条件
     * @return 转载信源列表
     */
    Page<ReprintSourceVO> pageList(ReprintSourceQuery query);

    /**
     * 批量添加或更新转载信源过滤
     *
     * @param websiteIds 站点ID集合
     * @param reprintSources 转载信源名称集合
     */
    void batchCreate(List<Long> websiteIds, Collection<String> reprintSources);

    /**
     * 批量更新过滤状态
     *
     * @param ids 记录ID集合
     * @param filterStatus 过滤状态
     */
    void batchUpdateFilterStatus(List<Long> ids, Boolean filterStatus);

    Long getOrCreateByWebsiteId(Long websiteId, String reprintSourceName);

    void exportByQuery(ReprintSourceQuery query);
}
