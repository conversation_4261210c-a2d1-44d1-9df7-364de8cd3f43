package cn.dahe.service;

import cn.dahe.entity.Channel;
import cn.dahe.entity.ChannelType;
import cn.dahe.model.dto.ChannelTypeQuery;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.Result;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 栏目类型服务接口
 * 
 * <AUTHOR>
 * @date 2025-09-16
 */
public interface ChannelTypeService  extends IService<ChannelType> {

    /**
     * 新增栏目类型
     * 
     * @param channelType 栏目类型信息
     * @return 操作结果
     */
    Result<String> insertChannelType(ChannelType channelType);

    /**
     * 修改栏目类型
     * 
     * @param channelType 栏目类型信息
     * @return 操作结果
     */
    Result<String> updateChannelType(ChannelType channelType);

    /**
     * 删除栏目类型
     * 
     * @param id 栏目类型ID
     * @return 操作结果
     */
    Result<String> deleteChannelTypeById(Long id);

    /**
     * 根据ID查询栏目类型
     * 
     * @param id 栏目类型ID
     * @return 栏目类型信息
     */
    Result<ChannelType> getChannelTypeById(Long id);

    /**
     * 分页查询栏目类型列表
     * 
     * @param query 查询条件
     * @return 分页结果
     */
    Result<PageResult<ChannelType>> listChannelTypesByPage(ChannelTypeQuery query);

    /**
     * 查询栏目类型树结构
     * 
     * @return 树形结构列表
     */
    Result<List<ChannelType>> listChannelTypeTree();
}