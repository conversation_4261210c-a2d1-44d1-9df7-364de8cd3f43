package cn.dahe.service;

import cn.dahe.entity.Channel;
import cn.dahe.enums.ProcessTypeEnum;
import cn.dahe.model.dto.BatchUpdateChannelTypeDto;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.Result;
import cn.dahe.model.query.ChannelQuery;
import cn.dahe.model.query.WebSiteChannelCheckQuery;
import cn.dahe.model.vo.LoginUserVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-08-11
 */
public interface ChannelService extends IService<Channel> {

    /**
     * 分页查询
     *
     * @param query
     * @return
     */
    PageResult<Channel> page(ChannelQuery query);


    PageResult<Channel> page(WebSiteChannelCheckQuery query);

    /**
     * 添加
     *
     * @param Channel
     * @param user
     * @return
     */
    Result<String> save(Channel Channel, LoginUserVO user);

    /**
     * 添加
     *
     * @param Channel
     * @param user
     * @return
     */
    Result<String> update(Channel Channel, LoginUserVO user);

    /**
     * 更新栏目类型
     *
     * @param id 栏目ID
     * @param channelType 栏目类型ID
     * @param user 当前用户
     * @return 更新结果
     */
    Result<String> updateType(int id, Long channelType, LoginUserVO user);

    /**
     * 批量更新栏目类型
     *
     * @param batchDto 批量更新参数
     * @param user 当前用户
     * @return 更新结果
     */
    Result<String> batchUpdateType(BatchUpdateChannelTypeDto batchDto, LoginUserVO user);

    /**
     * 禁用or启用
     *
     * @param id
     * @param user
     * @return
     */
    Result<String> updateStatus(String id, LoginUserVO user);

    /**
     * 获取网站栏目id
     *
     * @param websiteId
     * @param processType
     * @return
     */
    Long getWebsiteChannelId(Long websiteId, ProcessTypeEnum processType);
    /**
     * 获取网站栏目id
     *
     * @param websiteId
     * @param processType
     * @return
     */
    Long getWebsiteChannelId(Long websiteId, Integer processType);
    /**
     * 获取网站栏目id列表
     *
     * @param websiteIds
     * @param processType
     * @return
     */
    List<Long> getWebsiteChannelIds(List<Long> websiteIds, ProcessTypeEnum processType);
    List<Long> getWebsiteChannelIds(List<Long> websiteIds);

    void saveById(Channel newChannel);
}
