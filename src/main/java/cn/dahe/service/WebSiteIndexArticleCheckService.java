package cn.dahe.service;

import cn.dahe.entity.WebSiteIndexArticleCheck;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.query.WebSiteIndexCheckQuery;
import cn.dahe.model.vo.WebSiteIndexCheckDetailVO;
import cn.dahe.model.vo.WebSiteIndexCheckVO;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.util.List;

/**
 * 站点下网站首页更新详情Service接口
 *
 * <AUTHOR>
 * @date 2025-09-03
 */
public interface WebSiteIndexArticleCheckService extends IService<WebSiteIndexArticleCheck> {

    /**
     * 根据站点ID分页查询首页更新检查记录
     *
     * @param websiteId 站点ID
     * @param page 页码
     * @param limit 每页大小
     * @return 分页结果
     */
    PageResult<WebSiteIndexCheckVO> pageByWebsiteId(Long websiteId, Integer page, Integer limit);

    /**
     * 生成指定站点的首页更新检查数据
     * 从2025年8月1日开始到当前时间，每天生成一条记录
     *
     * @param websiteId 站点ID
     * @return 生成的记录数量
     */
    Integer generateIndexCheckData(Long websiteId);

    /**
     * 批量生成所有站点的首页更新检查数据
     *
     * @return 生成的记录数量
     */
    Integer generateAllSitesIndexCheckData();

    /**
     * 批量生成所有站点的首页昨日更新检查数据
     *
     * @return 生成的记录数量
     */
    Integer generateAllSitesYesterdayIndexCheckData();
    /**
     * 根据站点ID和日期查询检查记录
     *
     * @param websiteId 站点ID
     * @param checkDate 检查日期
     * @return 检查记录
     */
    WebSiteIndexArticleCheck getByWebsiteIdAndDate(Long websiteId, LocalDate checkDate);

    /**
     * 根据站点ID和日期范围查询检查记录
     *
     * @param websiteId 站点ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 检查记录列表
     */
    List<WebSiteIndexArticleCheck> listByWebsiteIdAndDateRange(Long websiteId, LocalDate startDate, LocalDate endDate);

    /**
     * 根据查询条件分页查询网站首页更新检查详情
     *
     * @param query 查询条件
     * @return 分页结果
     */
    PageResult<WebSiteIndexCheckDetailVO> pageByQuery(WebSiteIndexCheckQuery query);

    /**
     * 根据查询条件查询所有网站首页更新检查详情（用于导出）
     *
     * @param query 查询条件
     * @return 检查详情列表
     */
    List<WebSiteIndexCheckDetailVO> listByQuery(WebSiteIndexCheckQuery query);

    /**
     * 导出网站首页更新检查数据到Excel
     *
     * @param query 查询条件
     * @return Excel文件字节数组
     */
    byte[] exportToExcel(WebSiteIndexCheckQuery query);

    /**
     * 导出网站首页更新检查数据到Excel（使用EasyExcel）
     *
     * @param query 查询条件
     * @param response HTTP响应对象
     * @throws IOException 导出异常
     */
    void exportToExcelWithEasyExcel(WebSiteIndexCheckQuery query, HttpServletResponse response) throws IOException;



}
