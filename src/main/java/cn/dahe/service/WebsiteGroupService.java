package cn.dahe.service;

import cn.dahe.entity.WebsiteGroup;
import cn.dahe.model.query.AppendWebsiteQuery;
import cn.dahe.model.request.WebsiteGroupRelateRequest;
import cn.dahe.model.vo.LoginUserVO;
import cn.dahe.model.vo.WebsiteGroupVO;
import cn.dahe.utils.SpringUtils;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 网站分组Service
 */
public interface WebsiteGroupService extends IService<WebsiteGroup> {


    /**
     * 删除分组
     */
    void delete(Integer id, LoginUserVO user);

    /**
     * 刷新分组名称缓存
     */
    void refreshGroupNameCache();


    Map<Long, String> GROUP_NAME_CACHE = new ConcurrentHashMap<>();

    /**
     * 根据分组ID获取分组名称
     *
     * @param groupId 分组ID
     * @return 分组名称
     */
    static String getGroupName(Long groupId) {
        if (groupId == null) {
            return "未分组";
        }
        if (GROUP_NAME_CACHE.isEmpty()) {
            SpringUtils.getBean(WebsiteGroupService.class).refreshGroupNameCache();
        }
        return GROUP_NAME_CACHE.get(groupId);
    }

    List<WebsiteGroupVO> listSelectByQuery(AppendWebsiteQuery query);

    List<WebsiteGroupVO> listAvailableGroup();

    void saveByGroupName(String groupName);


    void relateByRequest(WebsiteGroupRelateRequest request);

    void updateGroupName(Long id,String groupName);
}