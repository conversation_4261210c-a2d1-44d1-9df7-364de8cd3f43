package cn.dahe.service;

import cn.dahe.entity.Article;
import cn.dahe.enums.AuditStatusEnum;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.Result;
import cn.dahe.model.dto.WarnCheckDetailDto;
import cn.dahe.model.query.AllSiteSearchQuery;
import cn.dahe.model.query.ArticleCheckQuery;
import cn.dahe.model.query.IndexArticleQuery;
import cn.dahe.model.request.LingcaiPushDataDto;
import cn.dahe.model.query.ChannelArticleQuery;
import cn.dahe.model.vo.*;
import com.baomidou.mybatisplus.core.metadata.IPage;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 采集文章Service
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
public interface ArticleService extends BaseService<Article> {


    void relateWithCheckTask(Long articleId, Long checkTaskId);

    /**
     * 分页查询文章检查结果
     *
     * @param query 查询参数
     * @return 分页结果
     */
    IPage<ArticleCheckVO> pageArticleAndCheckResults(ArticleCheckQuery query);

    WarnCheckDetailDto getWarnArticleInfo(Long articleId);

    ArticleCheckVO getArticleAndCheckResults(Long articleId, ArticleCheckQuery query);

    ArticleCheckVO getArticleAndSnapshotResults(Long articleId, ArticleCheckQuery query);

    /**
     * 更新文章审核状态
     *
     * @param articleIds  文章ID列表，以逗号分隔
     * @param auditStatus 审核状态
     * @return 操作结果
     */
    boolean updateAuditStatus(List<Long> articleIds, AuditStatusEnum auditStatus);

    /**
     * 处置
     *
     * @param articleIds
     * @param disposalStatus
     * @param remark
     * @param loginUserVO
     * @return
     */
    Result<String> updateDisposalStatus(String articleIds, int disposalStatus, String remark, LoginUserVO loginUserVO);

    Article saveByPushData(LingcaiPushDataDto pushData);

    void exportByQuery(ArticleCheckQuery query);

    /**
     * 全站搜索
     *
     * @param query
     * @return
     */
    IPage<ArticleAllSiteSearchVO> pageAllSiteSearch(AllSiteSearchQuery query);

    /**
     * 导出全站检索记录
     *
     * @param query
     * @param user
     * @param response
     * @throws IOException
     */
    void allSiteSearchExport(AllSiteSearchQuery query, LoginUserVO user, HttpServletResponse response) throws IOException;

    List<Article> listUncheckedArticle();

    /**
     * 根据栏目与条件分页查询文章列表并转为VO
     */
    IPage<ArticleVO> pageChannelArticles(ChannelArticleQuery query);

    List<Article> listUnrelatedReprintSourceArticles();

    void batchUpdateReprintSourceId(List<Long> articleIds, Long reprintSourceId);

    List<Article> listByWebsiteId(Long websiteId);

    List<Article> listByIsIndex(int i, String webSiteId);

    PageResult<Article> pageByWebsiteId(Long websiteId, Integer pageNum, Integer pageSize);

    /**
     * 根据查询条件分页查询首页文章列表
     *
     * @param query 查询条件
     * @return 分页结果
     */
    PageResult<Article> pageIndexArticles(IndexArticleQuery query);

    /**
     * 导出首页文章数据到Excel（使用EasyExcel）
     *
     * @param query 查询条件
     * @param response HTTP响应对象
     * @throws IOException 导出异常
     */
    void exportIndexArticlesToExcel(IndexArticleQuery query, HttpServletResponse response) throws IOException;

    List<Article> listNoContentArticle();

    CheckSnapshotVO getSnapshotInfo(Long relationId);
}