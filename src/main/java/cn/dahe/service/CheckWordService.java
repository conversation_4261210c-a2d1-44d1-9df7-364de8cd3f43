package cn.dahe.service;

import cn.dahe.entity.CheckWord;
import cn.dahe.model.dto.CheckResultDto;

import java.util.List;
import java.util.Map;

/**
 * 文章检查错误词库Service接口
 */
public interface CheckWordService extends BaseService<CheckWord> {

    /**
     * 更新过滤状态
     */
    boolean updateFilterStatus(Long wordId, Boolean filterStatus);

    Map<String, CheckWord> batchGetOrCreateCheckWords(List<CheckResultDto> checks);
}