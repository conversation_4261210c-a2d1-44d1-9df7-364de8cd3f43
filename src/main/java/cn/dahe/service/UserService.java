package cn.dahe.service;

import cn.dahe.entity.User;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.Result;
import cn.dahe.model.dto.UserDto;
import cn.dahe.model.query.UserQuery;
import cn.dahe.model.vo.LoginUserVO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;

import java.io.IOException;
import java.util.Collection;
import java.util.List;
import java.util.Set;


/**
 *
 */
public interface UserService extends IService<User> {


    /**
     * 分页查询
     *
     * @param query
     * @return
     */
    PageResult<UserDto> page(UserQuery query, LoginUserVO loginUser) throws IOException;

    /**
     * 分页查询
     *
     * @param query
     * @return
     */
    PageResult<UserDto> pageRole(UserQuery query, String roleId);


    /**
     * 分页查询
     *
     * @param query
     * @return
     */
    PageInfo<User> pageByUserNameAndRoleId(UserQuery query);


    /**
     * 查询某城市下的用户
     *
     * @return
     */
    List<User> listByCityIds(String cityIds);


    /**
     * 更新用户扩展表
     *
     * @param loginUser
     * @return
     */
    boolean updateSyncedUserInfo(LoginUserVO loginUser);


    /**
     * 通过用户id获取用户的城市id
     *
     * @param userId
     * @return
     */
    Integer getCityIdByUserId(Integer userId);

    /**
     * 判断用户是否为省级管理员或者超级管理员
     *
     * @param userId 用户ID
     * @return 如果用户是省级管理员或者超级管理员，则返回true；否则，返回false。
     */
    Boolean isProvincialOrSuperAdmin(Integer userId);


    /**
     * 根据手机号查询
     *
     * @param phone 手机号
     * @return
     */
    User getByPhone(String phone);


    /**
     * 根据账号查询
     *
     * @param account 账号
     * @return
     */
    User getByAccount(String account);

    /**
     * 添加
     *
     * @param user
     * @param roleIds
     * @param loginUser
     * @return
     */
    Result<String> save(User user, String roleIds, Set<Integer> tenantIdSet, Set<Integer> newMediaIdSet, LoginUserVO loginUser);

    /**
     * 编辑
     *
     * @param user
     * @param roleIds
     * @param loginUser
     * @return
     */
    Result<String> update(User user, String roleIds, Set<Integer> tenantIdSet, Set<Integer> newMediaIdSet, LoginUserVO loginUser);

    /**
     * 禁用or启用
     *
     * @param id
     * @param user
     * @return
     */
    Result<String> updateStatus(String id, LoginUserVO user);


    /**
     * 更新用户密码
     *
     * @param userId
     * @param newPwd
     * @param oldPwd
     * @return
     */
    Result<String> updateMyPwd(String userId, String newPwd, String oldPwd);

    /**
     * 重置密码
     *
     * @param userId
     * @return
     */
    Result<String> updateResetPwd(String userId);


    Boolean isAdmin(Collection<String> userRoleSns);

    Boolean isAdminOrExpert(Collection<String> userRoleSns);

    Boolean isSuperAdmin(Collection<String> userRoleSns);



}