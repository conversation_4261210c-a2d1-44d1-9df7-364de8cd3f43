package cn.dahe.service;

import cn.dahe.check.html.HtmlMappingUtil;
import cn.dahe.entity.CheckResult;
import cn.dahe.enums.ArticleLocationEnum;
import cn.dahe.enums.AuditStatusEnum;
import cn.dahe.enums.CheckStrategyEnum;
import cn.dahe.enums.CheckTaskRelationTypeEnum;
import cn.dahe.model.dto.CheckApiExecuteDto;
import cn.dahe.model.dto.CheckResultDto;
import cn.dahe.model.query.CheckResultQuery;
import cn.dahe.model.vo.CheckResultVO;
import cn.dahe.model.vo.CheckVO;
import cn.hutool.core.lang.Pair;

import java.util.List;
import java.util.function.Function;

/**
 * 文章错误检查Service
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
public interface CheckResultService extends BaseService<CheckResult> {

    CheckApiExecuteDto executeContentCheck(CheckStrategyEnum checkStrategy, String text);

    void saveCheckResults(Long checkId, ArticleLocationEnum articleLocation, List<CheckResultDto> checkResultDtoList, HtmlMappingUtil.HtmlMappingResult mappingResult);

    /**
     * 检查结果id是否来自对应文章，只保留对应文章的
     */
    List<Long> confirmResultIdsFromArticle(Long articleId, List<Long> resultIds);

    /**
     * 更新审核状态
     *
     * @param resultIds   检查ID列表
     * @param auditStatus 审核状态
     */
    boolean updateAuditStatus(List<Long> resultIds, AuditStatusEnum auditStatus);

    List<CheckResultVO> getErrorObjsByTaskId(Long taskId);

    List<CheckResultVO> listCheckResultByTaskAndCheckQuery(Long taskId, CheckResultQuery query);

    List<CheckResultVO> listSnapshotCheckResultByTask(Long taskId, CheckTaskRelationTypeEnum relationType);

    <T extends CheckVO, U extends CheckResultQuery> void markCheckContentWithHtml(T vo, U query);

    <T extends CheckVO, U extends CheckResultQuery> void markCheckContentWithHtml(T vo, U query, boolean doMark, boolean onlyMark);

    <T extends CheckVO, U extends CheckResultQuery> void markCheckContentWithHtmlCode(T vo, U query);

    void removeByCheckId(Long checkId);

    void clearHistoryCheckResult(Long id);

    List<CheckResult> listByCheckId(Long checkId);

    String markText(String content, List<CheckResultVO> errors, boolean isHtml,Function<CheckResultVO, Pair<String,Integer>> getMarkWord);

    List<CheckResult> listUnrelatedWordResult();

    void updateUnrelatedCheckWordResult();
}