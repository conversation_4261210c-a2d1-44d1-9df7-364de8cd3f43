package cn.dahe.service;

import cn.dahe.check.feign.model.request.SiteQueryRequest;
import cn.dahe.check.feign.model.response.SiteQueryResponse;
import cn.dahe.check.feign.service.CollectApiService;
import cn.dahe.common.advice.BusinessException;
import cn.dahe.common.constants.StatusConstants;
import cn.dahe.entity.Website;
import cn.dahe.model.vo.LoginUserVO;
import cn.dahe.model.vo.SearchWebsiteVO;
import cn.dahe.utils.fan.FanCollectUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AppendWebsiteService {
    @Resource
    private WebsiteService websiteService;

    public Website makeWebsite(Integer siteId, String name, LoginUserVO loginUser) {
        JSONArray siteInfo = FanCollectUtil.searchSitesByName(name);
        if (siteInfo == null || siteInfo.isEmpty()) {
            throw new BusinessException("该站点不存在");
        }
        //  跳过判断存在和唯一性
        Website website = new Website();
        website.setSiteId(siteId);
        website.setWebName(name);
        String webUrl = siteInfo.getString(1);
        website.setWebUrl(webUrl);
        website.setCreateTime(new Date());
        website.setCreateUserId(loginUser.getUserId());
        website.setCreateUserName(loginUser.getUsername());
        website.setStatus(StatusConstants.COMMON_NORMAL);
        return website;
    }

    @Resource
    private CollectApiService collectApiService;

    public List<SearchWebsiteVO> searchWebsiteWithStatus(String name) {
        if (StrUtil.isBlank(name)) {
            return CollUtil.newArrayList();
        }
        try {
            SiteQueryResponse response = collectApiService.siteQuery(new SiteQueryRequest().setName(name));
            Map<Integer, String> siteMap = response.getSite_id();
            if (response.getSuccess() && CollUtil.isNotEmpty(siteMap)) {
                Set<Integer> siteIds = siteMap.keySet();
                List<Website> websites = websiteService.listWebsiteBySiteIds(siteIds);
                Map<Integer, Website> websiteMap = websites.stream().collect(Collectors.toMap(Website::getSiteId, website -> website));
                return siteIds.stream().map(siteId -> {
                    SearchWebsiteVO vo = new SearchWebsiteVO();
                    String siteName = siteMap.get(siteId);
                    Website website = websiteMap.get(siteId);
                    vo.setId(siteId);
                    vo.setName(siteName);
                    vo.setStatus(website != null);
                    vo.setWebsiteId(website == null ? null : website.getId());
                    vo.setUrl(website == null ? null : website.getWebUrl());
                    return vo;
                }).collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.warn("查询站点失败");
        }
        return CollUtil.newArrayList();
    }
}
