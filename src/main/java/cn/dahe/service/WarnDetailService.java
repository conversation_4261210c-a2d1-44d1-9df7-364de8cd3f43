package cn.dahe.service;

import cn.dahe.dao.WarnDetailDao;
import cn.dahe.entity.WarnDetail;
import cn.dahe.enums.WarnDetailRelateTypeEnum;
import cn.dahe.model.dto.WarnCheckDetailDto;
import cn.dahe.utils.SmUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;

@Service
public class WarnDetailService extends ServiceImpl<WarnDetailDao, WarnDetail> {

    @PostConstruct
    private void postConstruct() {
        SmUtil.encrypt("初始化");
    }

    @Transactional(rollbackFor = Exception.class)
    public WarnDetail initWarnDetail(WarnDetailRelateTypeEnum relateTypeEnum, Long relateId, Object detail) {
        Integer relateType = relateTypeEnum.getValue();
        WarnDetail warnDetail = this.lambdaQuery().eq(WarnDetail::getRelateType, relateType)
                .eq(WarnDetail::getRelateId, relateId)
                .one();
        if (warnDetail == null) {
            warnDetail = new WarnDetail();
        }
        warnDetail.setRelateType(relateType);
        warnDetail.setRelateId(relateId);
        warnDetail.setContent(JSON.toJSONString(detail));
        saveOrUpdate(warnDetail);
        return warnDetail;
    }

    public WarnCheckDetailDto getDtoBySign(String sign) {
        try {
            Long id = WarnDetail.getIdFromSign(sign);
            WarnDetail detail = this.getById(id);
            return JSON.parseObject(detail.getContent(), WarnCheckDetailDto.class);
        } catch (Exception e) {
            return null;
        }
    }
}
