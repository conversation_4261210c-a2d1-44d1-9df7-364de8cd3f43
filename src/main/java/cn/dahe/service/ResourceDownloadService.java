package cn.dahe.service;

import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.Result;
import cn.dahe.entity.ResourceDownload;
import cn.dahe.model.query.ResourceDownloadQuery;
import cn.dahe.model.vo.LoginUserVO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <AUTHOR>
 * @date 2023-08-11
 */
public interface ResourceDownloadService extends IService<ResourceDownload> {

    /**
     * 分页查询
     *
     * @param query
     * @return
     */
    PageResult<ResourceDownload> page(ResourceDownloadQuery query);

    /**
     * 添加
     *
     * @param resourceDownload
     * @param user
     * @return
     */
    Result<String> save(ResourceDownload resourceDownload, LoginUserVO user);


    /**
     * 禁用or启用
     *
     * @param id
     * @param user
     * @return
     */
    Result<String> updateStatus(String id, LoginUserVO user);


}
