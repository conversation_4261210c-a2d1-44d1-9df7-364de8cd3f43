package cn.dahe.service;

import cn.dahe.entity.CheckErrorLevel;
import cn.dahe.utils.SpringUtils;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 内容错误等级Service
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
public interface CheckErrorLevelService extends BaseService<CheckErrorLevel> {

    void refreshErrorLevelNameCache();

    Map<Integer, String> TYPE_LEVEL_CACHE = new ConcurrentHashMap<>();

    static String getLevelName(Integer level) {
        if (TYPE_LEVEL_CACHE.isEmpty()) {
            SpringUtils.getBean(CheckErrorLevelService.class).refreshErrorLevelNameCache();
        }
        return TYPE_LEVEL_CACHE.get(level);
    }


} 