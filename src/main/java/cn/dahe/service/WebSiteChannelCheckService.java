package cn.dahe.service;

import cn.dahe.entity.WebSiteChannelCheck;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.query.WebSiteChannelCheckQuery;
import cn.dahe.model.vo.WebSiteChannelCheckDetailVO;
import cn.dahe.model.vo.WebSiteChannelCheckExportVO;
import cn.dahe.model.vo.WebSiteChannelStatisticsVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDate;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 网站栏目更新检查Service接口
 *
 * <AUTHOR>
 * @date 2025-09-09
 */
public interface WebSiteChannelCheckService extends IService<WebSiteChannelCheck> {

    /**
     * 生成栏目检查结果
     * 1、查询所有栏目(Channel) 状态enable为1且processType为0的
     * 2、从昨天开始往前推2天，总共3天（前天、前二天、昨天）
     * 3、遍历每天每个栏目下的文章(Article)发布情况
     * 4、将结果保存到WebSiteChannelCheck表中
     * 注意：checkTime为检查的具体日期，而非当前时间
     *
     * @return 生成的记录数量
     */
    Integer generateChannelCheckData();
    
    /**
     * 异步生成栏目检查数据（多线程优化版本）
     * 
     * @return 生成的记录数量
     */
    CompletableFuture<Integer> generateChannelCheckDataAsync();

    /**
     * 分页查询栏目检查结果
     * 查询channel联合WebSiteChannelCheck表
     *
     * @param query 查询条件
     * @return 分页结果
     */
    PageResult<WebSiteChannelCheckDetailVO> pageChannelCheckDetail(WebSiteChannelCheckQuery query);

    /**
     * 根据栏目ID和日期范围查询检查记录
     *
     * @param columnId  栏目ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 检查记录列表
     */
    List<WebSiteChannelCheck> listByColumnIdAndDateRange(Long columnId, LocalDate startDate, LocalDate endDate);
    
    /**
     * 查询栏目检查结果统计
     * 按站点统计栏目更新情况，与/page-detail 结果保持一致
     *
     * @param query 查询条件
     * @return 站点统计结果列表
     */
    List<WebSiteChannelStatisticsVO> getChannelCheckStatistics(WebSiteChannelCheckQuery query);
    
    /**
     * 导出栏目检查结果
     * 根据查询条件导出Excel文件
     *
     * @param query 查询条件
     * @return 导出数据列表
     */
    List<WebSiteChannelCheckExportVO> exportChannelCheckData(WebSiteChannelCheckQuery query);
}