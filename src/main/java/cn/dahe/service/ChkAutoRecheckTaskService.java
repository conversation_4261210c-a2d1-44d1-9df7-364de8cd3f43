package cn.dahe.service;

import cn.dahe.entity.ChkAutoRecheckTask;
import cn.dahe.model.dto.ChkAutoRecheckTaskDto;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.query.ChkAutoRecheckTaskQuery;
import cn.dahe.model.vo.ChkAutoRecheckTaskVO;
import cn.dahe.model.vo.LoginUserVO;

import java.util.Map;

/**
 * 复查任务Service
 * <AUTHOR>
 * @date 2025-08-21
 */
public interface ChkAutoRecheckTaskService extends BaseService<ChkAutoRecheckTask> {

    /**
     * 添加
     * @param chkAutoRecheckTaskDto
     * @param user
     */
    int add(ChkAutoRecheckTaskDto chkAutoRecheckTaskDto, LoginUserVO user);

    /**
     * 分页查询
     * @param query
     * @return
     */
    PageResult<ChkAutoRecheckTaskVO> page(ChkAutoRecheckTaskQuery query);

    /**
     * 统计
     * @param query
     * @return
     */
    Map<String,Object> count(ChkAutoRecheckTaskQuery query);
}
