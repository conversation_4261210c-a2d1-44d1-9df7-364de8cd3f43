package cn.dahe.service;

import cn.dahe.entity.ChannelOperationLog;
import cn.dahe.enums.ChannelOperationTypeEnum;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.query.ChannelOperationLogQuery;
import cn.dahe.model.vo.LoginUserVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDateTime;

/**
 * 栏目操作日志服务层
 */
public interface ChannelOperationLogService extends IService<ChannelOperationLog> {

    /**
     * 分页查询
     */
    PageResult<ChannelOperationLog> page(ChannelOperationLogQuery query);

    /**
     * 新增操作日志
     */
    void save(ChannelOperationLog channelOperationLog, LoginUserVO user);

    /**
     * 更新操作日志
     */
    void update(String id, ChannelOperationLog channelOperationLog);

    /**
     * 逻辑删除操作日志
     */
    void updateStatus(String id);

    /**
     * 便捷添加日志方法
     * @param username 用户名
     * @param time 操作时间
     * @param type 操作类型
     * @param extraInfo 补充信息
     */
    void addLog(String username, LocalDateTime time, ChannelOperationTypeEnum type, String extraInfo);

    /**
     * 便捷添加日志方法（带栏目ID）
     * @param channelId 栏目ID
     * @param username 用户名
     * @param time 操作时间
     * @param type 操作类型
     * @param extraInfo 补充信息
     */
    void addLog(Integer channelId, String username, LocalDateTime time, ChannelOperationTypeEnum type, String extraInfo);
}