package cn.dahe.service;

import cn.dahe.model.dto.WebsiteAccessOverviewDto;
import cn.dahe.model.dto.WebsiteUpdateStatsDto;
import cn.dahe.entity.WebsiteAccessRecord;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.query.ChkUpdateSiteIndexQuery;
import cn.dahe.model.query.WebsiteAccessRecordQuery;
import cn.dahe.model.vo.LoginUserVO;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 *
 */
public interface WebsiteAccessRecordService extends IService<WebsiteAccessRecord> {

    /**
     * 分页查询
     *
     * @param query
     * @return
     */
    PageResult<WebsiteAccessRecord> page(WebsiteAccessRecordQuery query);

    /**
     * 分页查询
     *
     * @param query
     * @return
     */
    PageResult<WebsiteAccessOverviewDto> pageStats(WebsiteAccessRecordQuery query);

    /**
     * 分页查询
     *
     * @param query
     * @return
     */
    WebsiteUpdateStatsDto totalStats(WebsiteAccessRecordQuery query);

    /**
     * 导出连通性检查记录
     *
     * @param query
     * @param user
     * @param response
     */
    void export(WebsiteAccessRecordQuery query, LoginUserVO user, HttpServletResponse response);
}