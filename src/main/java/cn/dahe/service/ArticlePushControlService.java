package cn.dahe.service;

import cn.dahe.dao.ArticlePushControlDao;
import cn.dahe.entity.ArticlePushControl;
import cn.dahe.model.request.LingcaiPushDataDto;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Date;

@Slf4j
@Service
public class ArticlePushControlService extends ServiceImpl<ArticlePushControlDao, ArticlePushControl> {

    @Async
    public void saveByPushData(LingcaiPushDataDto pushData) {
        if (pushData != null && pushData.getId() != null) {
            if(this.getById(pushData.getId()) != null){
                log.warn("文章重复推送：{}", pushData.getId());
                return;
            }
            ArticlePushControl articlePushControl = new ArticlePushControl();
            articlePushControl.setId(pushData.getId());
            articlePushControl.setUrl(pushData.getUrl());
            articlePushControl.setPushTime(new Date());
            save(articlePushControl);
        }
    }
}
