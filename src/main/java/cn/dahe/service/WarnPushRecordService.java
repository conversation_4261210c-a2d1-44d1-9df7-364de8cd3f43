package cn.dahe.service;

import cn.dahe.entity.WarnPushMsg;
import cn.dahe.entity.WarnPushRecord;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.PushRecordDto;
import cn.dahe.model.query.WarnPushRecordQuery;
import cn.dahe.model.vo.WarnPushRecordVO;

import java.util.List;

public interface WarnPushRecordService extends BaseService<WarnPushRecord> {

    PageResult<WarnPushRecordVO> page(WarnPushRecordQuery query);

    Integer create(WarnPushRecord record);

    void relateRecordsWithMsg(List<Long> recordIds, Long msgId);

    PushRecordDto createContentPushWithPlans(List<Long> planIds, WarnPushMsg warnPushMsg, Long websiteId);

    void updateCheckPushRecords(List<Integer> recordIds, WarnPushMsg warnPushMsg);
}


