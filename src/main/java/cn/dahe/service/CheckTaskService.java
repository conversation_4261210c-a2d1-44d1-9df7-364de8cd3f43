package cn.dahe.service;

import cn.dahe.common.callback.TaskCallback;
import cn.dahe.entity.CheckTask;
import cn.dahe.enums.CheckStatusEnum;
import cn.dahe.enums.CheckStrategyEnum;
import cn.dahe.enums.CheckTaskRelationTypeEnum;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 文章检查内容服务接口
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
public interface CheckTaskService extends IService<CheckTask> {


    CheckTask initCheckTask(CheckTaskRelationTypeEnum relationType, Long relationId, CheckStrategyEnum checkStrategy);

    void updateContentFail(Long checkId, Integer contentStatus, String failMsg, String failReason);

    void updateContentSuccess(Long checkId, String title, String content, String code);

    void executeCheckTask(CheckTask checkTask, CheckStatusEnum checkStatusEnum, TaskCallback<CheckTask> callback);

    List<CheckTask> listNoResultTaskWithCheckStatus(CheckStatusEnum checkStatus);

    List<CheckTask> listNoContentReviewTask();

}