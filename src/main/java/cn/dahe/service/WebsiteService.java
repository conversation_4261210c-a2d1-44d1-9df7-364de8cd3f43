package cn.dahe.service;

import cn.dahe.entity.User;
import cn.dahe.entity.Website;
import cn.dahe.enums.CheckStrategyEnum;
import cn.dahe.enums.ProcessTypeEnum;
import cn.dahe.model.dto.PageResult;
import cn.dahe.model.dto.Result;
import cn.dahe.model.query.AppendWebsiteQuery;
import cn.dahe.model.query.WebsiteQuery;
import cn.dahe.model.vo.LoginUserVO;
import cn.dahe.model.vo.WebsiteVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023-08-11
 */
public interface WebsiteService extends IService<Website> {


    PageResult<Website> page(WebsiteQuery query);


    PageResult<Website> pageByWebIds(String webIds, int page, int limit);

    List<Website> listByWebIds(String webIds, String ids);

    PageResult<Website> pageByWebIds(List<Long> websiteIds, int page, int limit);


    Result<String> save(Website WebSite, LoginUserVO user);

    Result<String> saveNewMedia(Website WebSite, LoginUserVO user);


    Result<String> update(Website WebSite, LoginUserVO user);


    Result<String> updateStatus(String id, LoginUserVO user);


    List<Website> listByStatus(int status);

    List<Website> listByStatusAndProcessTypeIn(int status, List<Integer> processTypeList);


    /**
     * 获取站点的检测策略
     *
     * @param websiteId 站点ID
     * @return 检测策略
     */
    CheckStrategyEnum getWebsiteCheckStrategy(Long websiteId);

    /**
     * 更新网站巡查精准度
     *
     * @param websiteIds    网站ID
     * @param checkStrategy 巡查精准度值
     */
    boolean updateCheckStrategy(List<Long> websiteIds, CheckStrategyEnum checkStrategy);

    /**
     * 更新网站ID
     *
     * @param oldId 原ID
     * @param newId 新ID
     * @return 是否更新成功
     */
    boolean updateId(int oldId, int newId, String newUrl);

    /**
     * 根据用户ID查找站点
     *
     * @param userId
     * @return
     */
    List<Website> listByUserId(Integer userId);

    boolean assignSite(String userId, String siteIds);

    Website getWebsiteByCondition(ProcessTypeEnum processTypeEnum, Integer siteId, Long channelId);

    long getWebsiteCount();

    PageResult<WebsiteVO> pageList(AppendWebsiteQuery query);

    List<WebsiteVO> listSelectByQuery(AppendWebsiteQuery query);

    List<Website> listBySiteIds(Set<Integer> siteIds);

    Website getWebsiteBySiteId(Integer id);

    List<Website> listWebsiteBySiteIds(Collection<Integer> siteIds);

    List<User> userList(int id, int tenantId, int option);
}
