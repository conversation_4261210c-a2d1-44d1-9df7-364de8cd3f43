package cn.dahe.service;

import cn.dahe.check.html.HtmlMappingUtil;
import cn.dahe.entity.CheckContent;
import com.baomidou.mybatisplus.extension.service.IService;

public interface CheckContentService extends IService<CheckContent> {

    void updateContentByCheckResult(Long checkId, HtmlMappingUtil.HtmlMappingResult titleResult, HtmlMappingUtil.HtmlMappingResult contentResult, HtmlMappingUtil.HtmlMappingResult codeResult);

    void saveContent(Long checkId, String title,String content,String webCode);
}
