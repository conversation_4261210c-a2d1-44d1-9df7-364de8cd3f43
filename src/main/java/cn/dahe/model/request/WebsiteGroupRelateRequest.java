package cn.dahe.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 站点分组
 */
@Data
public class WebsiteGroupRelateRequest implements Serializable {


    private static final long serialVersionUID = 1L;

    @Schema(description = "网站id")
    private Long websiteId;

    @Schema(description = "分组id")
    private Long groupId;



}
