package cn.dahe.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 接收采集中心推送的栏目更新数据DTO
 */
@Data
public class SiteColumnPushDataDto {
    /**
     * 新闻id
     */
    private Long id;

    /**
     * 链接
     */
    private String url;

    /**
     * 标题
     */
    private String title;

    /**
     * 焦点图链接（焦点图只有一个，可能为空）
     */
    private String focus_img;

    /**
     * 摘要（可能为空）
     */
    private String summary;

    /**
     * 正文
     */
    private String content;

    /**
     * 转载信源
     */
    @Schema(description = "转载信源")
    private String source;

    /**
     * 栏目名称
     */
    @Schema(description = "栏目名称")
    private String origin;

    /**
     * 省份id
     */
    private Integer province;

    /**
     * 分类
     */
    private String classify;

    /**
     * 站点分类
     * 1：中央电子报 2：中央媒体网站 3：部委网站 4：中央微信
     * 5：中央微博 6：中央客户端 7：中央头条号 11：地方网站 12：微信 13：微博
     * 14：电子报 15：论坛 16：商业网站 17：高校网站 18：客户端 19：头条号
     * 20：百家号 21：网易号 22：企鹅号 23：人民号 24：搜狐号 25：UC大鱼号
     * 26：一点资讯 27：抖音号 28：快手号 29：微信视频号 30：小红书 31：搜狐视频
     */
    private Integer site_classify;

    /**
     * 内容权重
     * 1：网站首页头条
     * 2：网站首页滚动
     * 3：时政、要闻、国际、国内、党建、领导、媒体报道
     * 4：社会、民生、法治、科技、军事、经济、文化、教育、房产、旅游、明星、娱乐、体育
     * 5：健康、养生、美食、特产、情感、亲子、星座、公益
     */
    private Integer boost;

    /**
     * 情感倾向值，表示属于积极类别的概率，取值0-100
     */
    private Integer sens;

    /**
     * 发布时间（格式如：2019-08-29 07:53:00）
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date pub_time;

    /**
     * 抓取时间（格式如：2019-08-29 07:53:00）
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date create_time;

    /**
     * 站点id
     */
    private Long site_id;

    /**
     * 栏目id
     */
    private Long column_id;


}