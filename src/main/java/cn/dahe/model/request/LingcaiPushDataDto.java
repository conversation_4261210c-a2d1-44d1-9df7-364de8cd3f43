package cn.dahe.model.request;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * 接收采集中心推送的栏目更新数据DTO
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class LingcaiPushDataDto extends SiteColumnPushDataDto {

    /**
     * 附件链接
     */
    private List<String> file_link;
    /**
     * 作者
     */
    private String editor;
    /**
     * 源码
     */
    private String source_code;

    /**
     * 是否是首页文章
     */
    private int isIndex;


}