package cn.dahe.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 文章推送数据体
 */
@Data
public class ArticleRequest implements Serializable {


    private static final long serialVersionUID = 1L;
    
    /**
     * 文章ID
     */
    @Schema(description = "文章ID")
    private Long articleId;
    /**
     * 网站ID
     */
    @Schema(description = "网站ID")
    private Long websiteId;
    /**
     * 栏目ID
     */
    @Schema(description = "栏目id")
    private Long columnId;
    /**
     * 文章URL
     */
    @Schema(description = "文章URL")
    private String articleUrl;
    /**
     * 快照链接
     */
    @Schema(description = "快照链接")
    private String snapshotUrl;
    /**
     * 发布时间
     */
    @Schema(description = "发布时间")
    private Date pubTime;
    /**
     * 作者
     */
    @Schema(description = "作者")
    private String author;
    /**
     * 转载信源
     */
    @Schema(description = "转载信源")
    private String reprintSource;
    /**
     * 提取的文章标题
     */
    @Schema(description = "提取的文章标题")
    private String title;
    /**
     * 正文指正文区域的源码 快照显示的样本
     */
    @Schema(description = "正文指正文区域的源码 快照显示的样本")
    private String content;

}
