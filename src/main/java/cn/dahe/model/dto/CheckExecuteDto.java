package cn.dahe.model.dto;

import cn.dahe.entity.CheckTask;
import cn.dahe.check.html.HtmlMappingUtil;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class CheckExecuteDto {

    private boolean continueCheck = true;

    private CheckTask checkTask;

    private HtmlMappingUtil.HtmlMappingResult titleResult;
    private HtmlMappingUtil.HtmlMappingResult contentResult;
}
