package cn.dahe.model.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 附件内容转换结果DTO
 */
@Data
@Accessors(chain = true)
public class AttachmentContentDTO {
    /**
     * 转换后的HTML内容
     */
    private String content;
    
    /**
     * 实际文件名
     */
    private String fileName;
    
    /**
     * 实际文件类型
     */
    private String fileType;
    
    /**
     * 实际文件大小（字节）
     */
    private Long fileSize;
    
    /**
     * 临时文件路径
     */
    private String tempFilePath;
}
