package cn.dahe.model.dto;

import cn.dahe.entity.Article;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.annotation.ColumnComment;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-07-15
 */
@Data
public class ArticleDto extends Article implements Serializable {

    @Column(name = "marked_title", type = MySqlTypeConstant.LONGTEXT)
    @ColumnComment("标记错误后的标题内容")
    private String markedTitle;

    @Column(name = "marked_content", type = MySqlTypeConstant.LONGTEXT)
    @ColumnComment("标记错误后的内容")
    private String markedContent;


    private List<CheckResultDto> checkResultDtoList = new ArrayList<>();


}
