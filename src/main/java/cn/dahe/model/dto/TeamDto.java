package cn.dahe.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Accessors(chain = true)
@Data
public class TeamDto {
    private Integer id;

    private String name;

    private String tenantId;

    private String remark;

    private Integer createUserId;

    private Integer lastUpdateUserId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastUpdateTime;

    private String createUserName;

    private String lastUpdateUserName;

    private String tenantName;
}
