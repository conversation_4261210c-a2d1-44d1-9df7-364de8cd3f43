package cn.dahe.model.dto;

import cn.dahe.enums.CheckErrorLevelEnum;
import cn.dahe.enums.CheckErrorTypeEnum;
import lombok.Data;

/**
 * 内容错误详情实体类
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
public class CheckResultDto {

    /**
     * 错词
     */
    private String errorWord;
    /**
     * 正词
     */
    private String suggestWord;
    /**
     * 错误类型名称
     */
    private CheckErrorTypeEnum errorType;

    private CheckErrorLevelEnum errorLevel;

    private Integer position;

} 