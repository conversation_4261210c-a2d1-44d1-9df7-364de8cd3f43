package cn.dahe.model.dto;


import cn.dahe.entity.Role;
import cn.dahe.entity.Team;
import cn.dahe.entity.Tenant;
import cn.dahe.entity.Website;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by wenjianing
 * Date 2018/1/17.
 * 用户表
 *
 * <AUTHOR>
 */
@Accessors(chain = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserDto implements Serializable {


    private int userId;

    /**
     * 用户手机号
     */
    private String phone = "";

    /**
     * 登录用户姓名
     */
    private String username = "";

    /**
     * 真实名称
     */
    private String trueName = "";

    /**
     * 城市ID
     */
    private int cityId;

    /**
     * 城市名称
     */
    private String cityName;


    /**
     * 状态
     */
    private Integer status;


    @Column(name = "account", comment = "登陆时的账号")
    private String account;


    @Column(name = "dep_id", defaultValue = "0")
    private Integer depId;

    /**
     * 单位名
     */
    @Column(name = "dep_name")
    private String depName;


    /**
     * 拥有的角色
     */
    private List<Role> listRole = new ArrayList<>();

    private Team team;

    private List<Tenant> tenantList;

    private List<Website> newMediaList;
}
