package cn.dahe.model.dto;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.pagehelper.PageInfo;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 分页工具类
 *
 * <AUTHOR> <EMAIL>
 */
@Data
@NoArgsConstructor
public class PageResult<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    // 总记录数
    private Long total;

    // 列表数据
    private List<T> list;

    /**
     * 总页数
     */
    private Integer totalPages;
    /**
     * 每页数量
     */
    private Integer pageSize;
    /**
     * 当前页数
     */
    private Integer page;

    /**
     * 分页
     *
     * @param list  列表数据
     * @param total 总记录数
     */
    public PageResult(List<T> list, long total) {
        this.list = list;
        this.total = total;
    }

    public static <T> PageResult<T> page(IPage<T> page) {
        return new PageResult<>(page.getRecords(), page);
    }

    public static <T> PageResult<T> page(PageInfo<T> page) {
        return new PageResult<>(page.getList(), page);
    }


    /**
     * 分页
     */
    public PageResult(Integer page, Integer pageSize) {
        this.pageSize = pageSize;
        this.page = page;
    }

    public PageResult(List<T> list, IPage page) {
        this.list = list;
        this.total = page.getTotal();
        this.pageSize = (int) page.getSize();
        this.page = (int) page.getCurrent();
        this.totalPages = (int) page.getPages();
    }

    public PageResult(List<T> list, PageInfo page) {
        this.list = list;
        this.total = page.getTotal();
        this.pageSize = page.getSize();
        this.page = page.getPageNum();
        this.totalPages = page.getPages();
    }


    public PageResult(List<T> list, Long total, Integer pageSize, Integer page) {
        this.list = list;
        this.total = total;
        this.pageSize = pageSize;
        this.page = page;
        this.totalPages = list == null ? 0 : (int) Math.ceil((double) total / pageSize);
    }
}