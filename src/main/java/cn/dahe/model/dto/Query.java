package cn.dahe.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 查询公共参数
 *
 * <AUTHOR> <EMAIL>
 */
@Data
public class Query {

    @JsonIgnore
    String code;
    @JsonIgnore
    String tableName;
    @JsonIgnore
    String attrType;
    @JsonIgnore
    String columnType;
    @JsonIgnore
    String connName;
    @JsonIgnore
    String dbType;
    @JsonIgnore
    String projectName;
    /**
     * 页码，页码最小值为 1
     */
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码最小值为 1")
    Integer page = 1;
    /**
     * 每页条数，每页条数最小值为 1
     */
    @NotNull(message = "每页条数不能为空")
    @Min(value = 1, message = "每页条数最小值为 1")
    Integer limit = 10;
    /**
     * 查询关键词
     */
    @JsonIgnore
    private String keyword = "";
}
