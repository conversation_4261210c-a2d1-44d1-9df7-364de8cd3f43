package cn.dahe.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 自动复查
 *
 * <AUTHOR>
 * @date 2025-08-21
 */
@Data
@Schema(name = "自动复查任务", description = "自动复查任务")
public class ChkAutoRecheckTaskDto {

    @Schema(description = "任务名称", example = "xxx")
    private String taskName;

    @Schema(description = "信源id集合", example = "1,2,3")
    private String siteIds;

    @Schema(description = "复查文章id集合", example = "xxx")
    private List<Long> articleIds;

    @Schema(description = "文章标题", example = "xxx")
    private String articleTitle;

    @Schema(description = "复查错误类型", example = "多个错误类型逗号分割")
    private String checkErrorType;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "复查开始时间", example = "2025-07-30 10:30:00")
    private Date checkBeginTime;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "复查结束时间", example = "2025-07-30 10:30:00")
    private Date checkEndTime;

    @Schema(description = "复查状态 0未开始 100进行中 200已完成 500任务失败", example = "xxx")
    private int status;
    @Schema(description = "复查标题过滤：0未开启，1开启", example = "xxx")
    private int filterTitle;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间开始", example = "2025-07-30 10:30:00")
    private Date beginCreateTime;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间结束", example = "2025-07-30 10:30:00")
    private Date endCreateTime;

}

