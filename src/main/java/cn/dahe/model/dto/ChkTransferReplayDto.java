package cn.dahe.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 转办督办回复记录
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Data
@Schema(name = "转办督办回复记录", description = "转办督办回复记录")
public class ChkTransferReplayDto {

    @Schema(description = "转办督办回复记录编号", example = "1")
    private Long id;

    @Schema(description = "转办督办任务Id", example = "1")
    private Long transferTaskId;

    @Schema(description = "整改结果截图", example = "xxx")
    private String transferResultImgUrl;

    @Schema(description = "整改链接URL", example = "xxx")
    private String transferArticleUrl;

    @Schema(description = "整改留言", example = "xxx")
    private String transferMsg;

    @Schema(description = "整改状态：1待整改 2已整改 3无需整改", example = "1")
    private Integer rectifyStatus;
}
