package cn.dahe.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/8/22 13:30
 */
@Data
@Schema(name = "整改下发", description = "整改下发")
public class ChkTransferTaskDto {

    @Schema(description = "整改下发任务的站点id", example = "1")
    private Integer websiteId;
    @Schema(description = "整改下发任务的文章id", example = "1")
    private Long articleId;
    @Schema(description = "整改下发任务的错误id", example = "1,2,3")
    private String selectResultIds;

}
