package cn.dahe.model.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 树
 *
 * <AUTHOR>
 * @time 2019/3/8
 */
@Data
public class TreeNode {

    /**
     * 节点id
     */
    private String id;
    /**
     * 父节点id
     */
    private String pid;
    /**
     * 节点内容
     */
    private String name;

    /**
     * 是否展开  默认true
     */
    private boolean spread = false;

    /**
     * 是否被选中  默认false
     */
    private boolean checked = false;

    /**
     * 是否禁用  默认false
     */
    private boolean disabled = false;


    //类型 1城市 2单位
    private int kindType = 1;


    /**
     * 子节点列表
     */
    private List<TreeNode> childrenNode = new ArrayList<>();

    public TreeNode() {
    }

    public TreeNode(String id, String pid, String name) {
        this.id = id;
        this.pid = pid;
        this.name = name;
    }

    // 构造函数和其他方法省略

//    public List<String> getAllNodeIds() {
//        List<String> allNodeIds = new ArrayList<>();
//        allNodeIds.add(this.id); // 添加当前节点的ID
//
//        for (TreeNode child : childrenNode) {
//            allNodeIds.addAll(child.getAllNodeIds()); // 递归获取子节点的ID并添加到列表中
//        }
//
//        return allNodeIds;
//    }


}
