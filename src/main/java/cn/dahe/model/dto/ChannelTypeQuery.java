package cn.dahe.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 栏目类型查询参数
 * 
 * <AUTHOR>
 * @date 2025-09-16
 */
@Data
@Accessors(chain = true)
@Schema(description = "栏目类型查询参数")
public class ChannelTypeQuery  extends Query{

    @Schema(description = "栏目类型名称", example = "微信")
    private String name;

    @Schema(description = "状态", example = "0")
    private Integer status;

    @Schema(description = "父级ID", example = "1")
    private Long parentId;
}