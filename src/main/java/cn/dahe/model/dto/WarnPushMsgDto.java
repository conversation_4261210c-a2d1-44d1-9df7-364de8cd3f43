package cn.dahe.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
@Schema(description = "预警推送信息数据结构")
public class WarnPushMsgDto implements Serializable {

    @Schema(description = "预警时间")
    private String time;
    /**
     * 字段不够 暂时预留
     */
    @Schema(description = "预警类型")
    private String type;
    /**
     * 长度不超过20字
     */
    @Schema(description = "预警内容")
    private String content;
    @Schema(description = "预警链接")
    private String link;
}
