package cn.dahe.model.dto;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025-07-17
 */
@Data
public class ArticleErrorStatsDto {
    /**
     * 网站ID
     */
    private int webId;

    /**
     * 网站名称
     */
    private String webSiteName;

    /**
     * 分组类型（比如按省、按行业等）
     */
    private int groupType;

    /**
     * 错误总数（词条行数）
     */
    private int totalErrorCount;

    /**
     * 严重错误词条数量，具体含义依上下文而定：
     * - 若按词条统计：表示 error_level = 1 的错误记录数；
     * - 若按文章去重：表示包含严重错误的文章数；
     * - 若按网站去重：表示包含严重错误的网站数；
     */
    private int severeErrorCount;

    /**
     * 一般错误词条数
     * - 若按词条统计：表示 error_level = 2 的错误记录数；
     * - 若按文章去重：表示包含严重错误的文章数；
     * - 若按网站去重：表示包含严重错误的网站数；
     */
    private int generalErrorCount;

    /**
     * 疑似错误词条数
     * - 若按词条统计：表示 error_level = 3 的错误记录数；
     * - 若按文章去重：表示包含严重错误的文章数；
     * - 若按网站去重：表示包含严重错误的网站数；
     */
    private int suspectedErrorCount;

    /**
     * 自定义词条数
     * - 若按词条统计：表示 error_level = 4 的错误记录数；
     * - 若按文章去重：表示包含严重错误的文章数；
     * - 若按网站去重：表示包含严重错误的网站数；
     */
    private int customWordCount;

    /**
     * 风险提示词条数
     * - 若按词条统计：表示 error_level = 5 的错误记录数；
     * - 若按文章去重：表示包含严重错误的文章数；
     * - 若按网站去重：表示包含严重错误的网站数；
     */
    private int riskTipCount;

    /**
     * 涉及文章数
     */
    private int articleCount;

    /**
     * 涉及文章的总字数。
     * 通常为对应文章的 word_count 字段求和。
     */
    private int totalWordCount;

    /**
     * 实际检测的网站数量（去重）。
     * 若按汇总统计，可反映数据覆盖面。
     */
    private int websiteCount;

    /**
     * 最早发布时间
     */
    private Date firstPubTime;
}
