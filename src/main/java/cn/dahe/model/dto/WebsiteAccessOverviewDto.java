package cn.dahe.model.dto;

import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.annotation.ColumnComment;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsAutoIncrement;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsKey;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025-07-08
 */
@Data
public class WebsiteAccessOverviewDto {

    private Integer no;
    private Integer id;
    private String webName;
    private String webUrl;

    /**
     * 总访问次数
     */
    private int totalCount;

    /**
     * 访问异常次数
     */
    private int errorCount;

    /**
     * 访问异常占比（如：0.1535 表示 15.35%）
     */
    private String errorRate;

}
