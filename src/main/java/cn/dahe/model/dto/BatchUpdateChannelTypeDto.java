package cn.dahe.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.util.StringUtils;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 批量更新栏目类型DTO
 * 
 * <AUTHOR>
 * @date 2025-09-16
 */
@Data
@Accessors(chain = true)
@Schema(description = "批量更新栏目类型参数")
public class BatchUpdateChannelTypeDto {

    @Schema(description = "栏目ID列表（逗号分隔的字符串）", example = "1,2,3", required = true)
    @NotBlank(message = "栏目ID列表不能为空")
    private String channelIds;

    @Schema(description = "栏目类型ID", example = "1", required = true)
    @NotNull(message = "栏目类型ID不能为空")
    private Long channelTypeId;

    /**
     * 获取解析后的栏目ID列表
     * 
     * @return 栏目ID列表
     */
    public List<Integer> getChannelIdList() {
        if (!StringUtils.hasText(channelIds)) {
            return Arrays.asList();
        }
        
        try {
            return Arrays.stream(channelIds.split(","))
                    .map(String::trim)
                    .filter(StringUtils::hasText)
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("栏目ID格式错误，请使用逗号分隔的数字格式，如：1,2,3");
        }
    }

    /**
     * 校验栏目ID列表是否有效
     * 
     * @return 校验结果
     */
    public boolean isValidChannelIds() {
        List<Integer> idList = getChannelIdList();
        return !idList.isEmpty() && idList.stream().allMatch(id -> id > 0);
    }
}
