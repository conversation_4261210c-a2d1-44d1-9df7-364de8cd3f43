package cn.dahe.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Accessors(chain = true)
@Data
public class TenantDto {
    private Integer id;

    private String name;

    private Integer status;

    private Integer userCount;

    private Integer siteCount;

    private Integer newMediaCount;

    private Integer groupCount;

    private Integer teamCount;

    private Integer warnPlanCount;

    private String remark;

    private Integer createUserId;

    private Integer lastUpdateUserId;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate expireDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastUpdateTime;

    private String createUserName;

    private String lastUpdateUserName;
}
