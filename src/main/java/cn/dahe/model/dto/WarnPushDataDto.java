package cn.dahe.model.dto;

import lombok.Data;
import java.util.Date;
import java.util.List;

@Data
public class WarnPushDataDto {

    /**
     * 站点名称
     */
    private String siteName;

    /**
     * 账号名称
     */
    private String accountName;

    /**
     * 检测时间
     */
    private String checkTime;


    /**
     * 0内容预警，1更新预警,2链接预警，
     */
    private int warnType;

    /**
     * 类型：1首页，2栏目，3连通，4死链，5外链
     */
    private int warnContentType;

    //更新检查
    private UpdateCheckDto updateCheckDto;
    @Data
    public static class UpdateCheckDto {
        /**
         * 检测站点数
         */
        private int totalSitesOrColumns;
        /**
         * 连续未更新站点数
         */
        private int continuousNotUpdateSitesOrColumns;
        /**
         * 检测详情
         */
        private List<UpdateCheckDetailDto> updateCheckDetailList;
    }
    @Data
    public static class UpdateCheckDetailDto {
        /**
         * 序号
         */
        private int no;
        /**
         * 名称
         */
        private String name;
        /**
         * 地址
         */
        private String url;
    }


    //连通性检查
    /**
     * 序号,访问时间,访问耗时/秒
     */
    private List<URLDto> connectList;

    @Data
    public static class URLDto {
        /**
         * 序号
         */
        private int no;
        /**
         * 访问时间
         */
        private Date accessTime;
        /**
         * 访问耗时/秒
         */
        private double accessTimeSecond;
        /**
         * 问题链接
         */
        private String errorLink;
        /**
         * 状态吗
         */
        private int statusCode;
        /**
         * 问题页面
         */
        private String errorPage;
    }

    @Data
    public static class IndexConnectDto extends URLDto {

    }

    @Data
    public static class DeadLinkDto extends URLDto {

    }

    @Data
    public static class OutsideLinkDto extends URLDto {

    }

}
