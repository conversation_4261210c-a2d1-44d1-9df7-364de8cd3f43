package cn.dahe.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
public class WarnPlanDto implements Serializable {

    @Schema(description = "主键ID")
    private Integer id;
    @Schema(description = "预警方案名称")
    private String schemeName;

    @Schema(description = "文章错误预警：0关闭，1开启")
    private Integer articleErrorWarn;

    @Schema(description = "附件错误预警：0关闭，1开启")
    private Integer attachmentErrorWarn;

    @Schema(description = "更新预警：0关闭，1开启")
    private Integer updateWarn;

    @Schema(description = "微信连续未更新天数设置")
    private Integer wechatUnupdateDays;

    @Schema(description = "微博连续未更新天数设置")
    private Integer weiboUnupdateDays;

    @Schema(description = "头条平台更新天数")
    private Integer toutiaoContinuousDays;

    @Schema(description = "抖音号更新天数")
    private Integer douyinContinuousDays;

    @Schema(description = "快手号更新天数")
    private Integer kuaishouContinuousDays;

    @Schema(description = "微信视频号更新天数")
    private Integer wxvideonumberContinuousDays;

    @Schema(description = "小红书更新天数")
    private Integer xiaohongshuContinuousDays;

    @Schema(description = "网站更新天数")
    private Integer websiteContinuousDays;

    @Schema(description = "首页更新天数设置")
    private Integer homePageUpdateDays;

    @Schema(description = "栏目分类，多个用逗号分隔")
    private String columnCategory;

    @Schema(description = "栏目信息天数设置")
    private Integer columnInfoDays;

    @Schema(description = "链接预警：0关闭，1开启")
    private Integer linkWarn;

    @Schema(description = "连通性预警：0关闭，1开启")
    private Integer connectivityWarn;

    @Schema(description = "异常访问次数阈值")
    private Integer abnormalAccessCount;

    @Schema(description = "死链预警：0关闭，1开启")
    private Integer deadLinkWarn;

    @Schema(description = "死链条数阈值")
    private Integer deadLinkCount;

    @Schema(description = "死链类型：全部、图片、附件、外部链接、文章、其他，多个用逗号分隔")
    private String deadLinkTypes;

    @Schema(description = "外链预警：0关闭，1开启")
    private Integer externalLinkWarn;

    @Schema(description = "外链条数阈值")
    private Integer externalLinkCount;

    @Schema(description = "微信推送开关：0-关闭 1-开启")
    private Integer wechatPushEnable;

    /**
     * 短信推送开关：0-关闭 1-开启
     */
    @Schema(description = "短信推送开关：0-关闭 1-开启")
    private Integer smsPushEnable;

}
