package cn.dahe.model.dto;

import cn.hutool.core.collection.CollUtil;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 内容校对结果DTO
 */
@Data
@Accessors(chain = true)
public class ContentCheckResultDto {
    /**
     * 是否校对成功
     */
    private boolean success;

    /**
     * 校对结果列表
     */
    private List<CheckResultDto> contentCheckResults;

    private List<CheckResultDto> titleCheckResults;

    public boolean hasResults(){
        return CollUtil.isNotEmpty(contentCheckResults) && CollUtil.isNotEmpty(titleCheckResults);
    }

    /**
     * 错误类型：1-本地异常，2-接口调用失败，3-接口响应失败
     */
    private Integer failType;

    /**
     * 错误信息
     */
    private String failMessage;

    /**
     * 错误详情
     */
    private String failDetail;

    /**
     * 接口响应时间（毫秒）
     */
    private Long responseTime;

    /**
     * 构建成功结果
     */
    public static ContentCheckResultDto success(List<CheckResultDto> titleCheckResults, List<CheckResultDto> contentCheckResults, Long responseTime) {
        return new ContentCheckResultDto()
                .setSuccess(true)
                .setTitleCheckResults(titleCheckResults)
                .setContentCheckResults(contentCheckResults)
                .setResponseTime(responseTime);
    }

    /**
     * 构建失败结果
     */
    public static ContentCheckResultDto fail(Integer errorType, String errorMessage, String errorDetail) {
        return new ContentCheckResultDto()
                .setSuccess(false)
                .setFailType(errorType)
                .setFailMessage(errorMessage)
                .setFailDetail(errorDetail);
    }

    /**
     * 本地异常
     */
    public static ContentCheckResultDto localHandleFail(String message, String detail) {
        return fail(1, message, detail);
    }

    /**
     * 接口返回超时
     */
    public static ContentCheckResultDto apiTimeoutFail(String message, String detail) {
        return fail(2, message, detail);
    }

    /**
     * 接口结果失败
     */
    public static ContentCheckResultDto apiReturnFail(String message, String detail) {
        return fail(3, message, detail);
    }
}