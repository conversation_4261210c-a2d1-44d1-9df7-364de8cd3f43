package cn.dahe.model.dto;

import cn.dahe.model.vo.CheckResultVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class WarnCheckDetailDto {

    private Long checkId;

    @Schema(description = "网站/账号名称")
    private String websiteName;

    @Schema(description = "分组名称")
    private String groupName;

    @Schema(description = "发布时间")
    private Date pubTime;

    @Schema(description = "检测时间")
    private Date checkTime;

    @Schema(description = "访问地址")
    private String url;

    @Schema(description = "上级地址（附件所在页面地址）")
    private String parentUrl;

    @Schema(description = "上级标题（附件所在页面标题）")
    private String parentTitle;

    @Schema(description = "错误列表")
    private List<CheckResultVO> errorObjs;

    @Schema(description = "标注html正文")
    private String markedContent;
    @Schema(description = "标注html标题")
    private String markedTitle;

}
