package cn.dahe.model.dto;

import cn.dahe.check.exception.CheckException;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 内容校对结果DTO
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
public class CheckApiExecuteDto {

    /**
     * 是否校对流程是否完成，已完成的不需要重试
     */
    private boolean completed = false;
    /**
     * 校对结果列表
     */
    private List<CheckResultDto> checkResults = null;

    public static CheckApiExecuteDto successWithNoResult() {
        return new CheckApiExecuteDto(true, null, null, null, null);
    }

    public static CheckApiExecuteDto success(List<CheckResultDto> results) {
        return new CheckApiExecuteDto(true, results, null, null, null);
    }

    public boolean hasResults() {
        return checkResults != null && !checkResults.isEmpty();
    }


    /**
     * 错误类型：1-本地异常，2-接口调用失败，3-接口响应失败
     */
    private Integer failType;

    /**
     * 错误简略信息
     */
    private String failMessage;

    /**
     * 错误详细信息
     */
    private String failDetail;

    public static CheckApiExecuteDto fail(Integer failType, String failMessage, String failDetail) {
        return new CheckApiExecuteDto(false, null, failType, failMessage, failDetail);
    }


    public static CheckApiExecuteDto responseStatusFail(String url, int statusCode) {
        return fail(2, "responseStatus:" + statusCode,
                StrUtil.format("接口：{} \n 状态码：{}", url, statusCode));
    }

    public static CheckApiExecuteDto responseResultFail(String url, Exception e) {
        return fail(2,
                "requestException:" + e.getMessage(),
                StrUtil.format("接口：{} \n 异常堆栈：{}", url, ExceptionUtil.stacktraceToString(e))
        );
    }

    public static CheckApiExecuteDto localExceptionFail(Exception e) {
        return fail(1,
                "exception:" + e.getMessage(),
                StrUtil.format("异常堆栈：{}", ExceptionUtil.stacktraceToString(e))
        );
    }

    public static CheckApiExecuteDto apiFail(String url, String message, String responseBody) {
        return fail(3,
                message,
                StrUtil.format("接口：{} \n 响应结果：{}", url, responseBody)
        );
    }


    public static CheckApiExecuteDto checkExceptionFail(CheckException e) {
        return fail(e.getFailType(), e.getMessage(), e.getErrorDetail());
    }
}