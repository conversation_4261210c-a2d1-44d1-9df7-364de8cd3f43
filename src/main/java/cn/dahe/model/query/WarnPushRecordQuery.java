package cn.dahe.model.query;

import cn.dahe.model.dto.Query;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
public class WarnPushRecordQuery extends Query {

    @Schema(description = "推送时间-开始")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date pushTimeStart;

    @Schema(description = "推送时间-结束")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date pushTimeEnd;

    @Schema(description = "来源平台Id", example = "1,2,3")
    private List<Integer> sourcePlatforms;

    @Schema(description = "预警信源名称", example = "xxx")
    private String warnSourceName = "";

    @Schema(description = "预警方案Id", example = "1,2,3")
    private List<Integer> warnPlanIds;

    @Schema(description = "接收人Id", example = "1,2,3")
    private List<Integer> receiverUserIds;

    @Schema(description = "预警类型", example = "1")
    private Integer warnType;

    @Schema(description = "预警推送类型", example = "1")
    private Integer warnPushType;
}


