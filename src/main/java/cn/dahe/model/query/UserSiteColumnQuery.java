package cn.dahe.model.query;

import cn.dahe.model.dto.Query;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户站点栏目查询参数
 *
 * <AUTHOR>
 * @date 2025-01-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "用户站点栏目查询参数")
public class UserSiteColumnQuery extends Query {

    @Schema(description = "用户id")
    private Integer userId;

    @Schema(description = "用户姓名")
    private String userName;

    @Schema(description = "站点id")
    private Integer siteId;

    @Schema(description = "站点名称")
    private String siteName;

    @Schema(description = "栏目id")
    private Integer columnId;

    @Schema(description = "栏目名称")
    private String columnName;

    @Schema(description = "创建开始时间")
    private String createBeginTime;

    @Schema(description = "创建结束时间")
    private String createEndTime;

    @Schema(description = "创建人姓名")
    private String createByUserName;
}
