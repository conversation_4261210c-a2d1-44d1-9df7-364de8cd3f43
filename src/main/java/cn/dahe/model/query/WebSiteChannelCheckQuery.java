package cn.dahe.model.query;

import cn.dahe.model.dto.Query;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 网站栏目更新检查查询参数
 *
 * <AUTHOR>
 * @date 2025-09-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "网站栏目更新检查查询参数")
public class WebSiteChannelCheckQuery extends Query {

    @Schema(description = "站点ID列表")
    private List<Long> websiteIds;

    @Schema(description = "栏目名称（模糊查询）")
    private String channelName;

    @Schema(description = "是否更新：0-未更新，1-已更新")
    private Integer isUpdate;

    @Schema(description = "检查开始时间")
    private Date beginTime;

    @Schema(description = "检查结束时间")
    private Date endTime;

    @Schema(description = "栏目类型ID")
    private Long channelTypeId;
}