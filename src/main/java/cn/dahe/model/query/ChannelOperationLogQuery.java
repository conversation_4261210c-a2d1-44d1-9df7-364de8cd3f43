package cn.dahe.model.query;

import cn.dahe.enums.ChannelOperationTypeEnum;
import cn.dahe.model.dto.Query;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 栏目操作日志查询参数
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ChannelOperationLogQuery extends Query {

    /**
     * 栏目ID
     */
    private Integer channelId;

    /**
     * 操作人
     */
    private String userName;

    /**
     * 操作类型
     */
    private ChannelOperationTypeEnum type;

    /**
     * 开始时间
     */
    private String beginTime = "";

    /**
     * 结束时间
     */
    private String endTime = "";

    /**
     * 操作内容
     */
    private String content;
}