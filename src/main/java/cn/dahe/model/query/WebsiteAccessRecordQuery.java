package cn.dahe.model.query;

import cn.dahe.model.dto.Query;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

/**
 * 网站访问记录查询参数
 *
 * <AUTHOR>
 * @date 2023-07-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "网站访问记录查询参数")
public class WebsiteAccessRecordQuery extends Query {

    @Schema(description = "分组类型")
    private String groupType = "";

    @Schema(description = "网站ID，多个用逗号分隔")
    private String webId = "";

    @Schema(description = "网站名称")
    private String webName;

    @Schema(description = "开始时间", example = "2025-01-01 00:00:00")
    private String beginTime = "";

    @Schema(description = "结束时间", example = "2025-01-31 23:59:59")
    private String endTime = "";

    @Schema(description = "访问状态：0失败，1成功")
    private Integer success;

    @Schema(description = "链接类型：0内链，1外链")
    private Integer type;

    @Schema(description = "HTTP状态码")
    private Integer httpCode;

    @Schema(description = "链接地址")
    private String linkUrl;

    @Schema(description = "导出类型：0主页面（默认），1子页面")
    private Integer exportType = 0;

    @Schema(description = "勾选记录Id，多个用逗号分隔")
    private String ids = "";

    @Schema(description = "网站ID集合")
    List<String> webIdList =  new ArrayList<>();
}
