package cn.dahe.model.query;

import cn.dahe.model.dto.Query;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 栏目更新检查查询参数 - 基于article表结构
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "栏目更新检查查询参数")
public class ChkUpdateSiteColumnQuery extends Query {

    // ==================== 基础查询字段 ====================

    @Schema(description = "类型(0:站点,1:分组)")
    private Integer type;

    @Schema(description = "站点Id的值，多值逗号分割")
    private String siteId;

    @Schema(description = "分组Id的值，多值逗号分割")
    private String groupId;

    @Schema(description = "处理类型")
    private Integer processType;

    @Schema(description = "栏目信息")
    private String keyword;

    @Schema(description = "检查时间，例如2025-08-16")
    private String xpathPubTime;

    // ===================== 待解决查询字段 =====================
    @Schema(description = "*检查结果：0全部，1正常，2即将逾期，3严重逾期")
    private Integer checkResult;

    @Schema(description = "栏目分类：1：中央电子报  2：中央媒体网站  3：部委网站  4：中央微信  5：中央微博  6：中央客户端  7：中央头条号     11：地方网站  12：微信  13：微博  14：电子报  15：论坛  16：商业网站  17：高校网站  18：客户端  19：头条号       20：百家号  21：网易号  22：企鹅号  23：人民号  24：搜狐号  25：UC大鱼号  26：一点资讯 27:抖音号 28:快手号 29:微信视频号\n" +
            "30：小红书  31：搜狐视频")
    private String siteClassify;

    @Schema(description = "-更新期限：手动输入数值或下拉框")
    private String pubBeginTime;
    private String pubEndTime;

    @Schema(description = "-未更新天数：手动输入数值或下拉框")
    private Integer notUpdateDays;

    @Schema(description = "*检测状态：0全部，1正常，2异常")
    private Integer detectStatus;

    @Schema(description = "勾选记录Id，多个用逗号分隔")
    private String ids = "";
}
