package cn.dahe.model.query;

import cn.dahe.model.dto.Query;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 全站搜索查询参数
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "全站搜索查询参数")
public class ChkAllSiteSearchQuery extends Query {

    // ==================== 原型图对应字段 ====================
    @Schema(description = "站点类型：1网站，2分组", example = "站点类型：1网站，2分组")
    private int siteType = 1;

    @Schema(description = "站点值", example = "站点值")
    private String siteValue = "";

    @Schema(description = "搜索关键词范围：标题，内容", example = "搜索关键词范围：标题，内容")
    private int keywordsContentType = 2;

    @Schema(description = "搜索内容", example = "搜索内容")
    private String keywords;

    @Schema(description = "筛选开始时间", example = "筛选开始时间")
    private String startTime = "";

    @Schema(description = "筛选结束时间", example = "筛选开始时间")
    private String endTime = "";

    @Schema(description = "排序值：asc升序，desc降序", example = "排序值：asc升序，desc降序")
    private String orderValue = "desc";

    @Schema(description = "过滤结果类型：1全部，2未过滤，3已过滤转载信源", example = "过滤结果类型：1全部，2未过滤，3已过滤转载信源")
    private int filterResultType = 1;

    @Schema(description = "内容类型：1全部，2文章，3附件", example = "内容类型：1全部，2文章，3附件")
    private int contentType = 1;

}
