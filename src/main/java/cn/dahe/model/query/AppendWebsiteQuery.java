package cn.dahe.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 网站信息查询
 */
@Data
@Accessors(chain = true)
public class AppendWebsiteQuery {

    @Schema(description = "页码", example = "1", type = "integer")
    private Integer page = 1;

    @Schema(description = "每页大小", example = "10", type = "integer")
    private Integer limit = 10;

    @Schema(description = "网站名称")
    private String name;

    private String url;

    @Schema(description = "状态：0：禁用 1：正常")
    private Integer status;

    @Schema(description = "类型：0：网站 1：新媒体")
    private Integer searchType;

    private List<Long> websiteIds;

    @Schema(description = "平台类型 0：网站  1：微信  2：微博  3：头条号  6335：抖音号  6429：快手号  6452：微信视频号  6460：小红书")
    private Integer processType;

    @Schema(description = "分组ID")
    private Long groupId;

    @Schema(description = "分组名称")
    private String groupName;

    @Schema(description = "是否显示禁用未删除的网站 默认不显示")
    private boolean showDisable = false;


}