package cn.dahe.model.query;

import cn.dahe.model.dto.Query;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 网站错误统计查询参数
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "网站错误统计查询参数")
public class WebsiteErrorStatisticsQuery extends Query {

    @Schema(description = "网站名称", example = "河南省人民政府")
    private String websiteName = "";

    @Schema(description = "分组名称", example = "政府网站")
    private String groupName = "";

    @Schema(description = "检查开始时间", example = "2025-07-01 00:00:00")
    private String checkStartTime = "";

    @Schema(description = "检查结束时间", example = "2025-07-31 23:59:59")
    private String checkEndTime = "";

    @Schema(description = "错误等级", example = "1,2,3")
    private String errorLevels = "";

    @Schema(description = "最小错误数", example = "1")
    private Integer minErrorCount;

    @Schema(description = "最大错误数", example = "100")
    private Integer maxErrorCount;

    @Schema(description = "检查状态", example = "正常")
    private String checkStatus = "";

    @Schema(description = "排序字段", example = "totalErrorCount")
    private String sortField = "totalErrorCount";

    @Schema(description = "排序方向", example = "desc")
    private String sortDirection = "desc";
}
