package cn.dahe.model.query;

import cn.dahe.model.dto.Query;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 网站首页更新检查查询参数
 *
 * <AUTHOR>
 * @date 2025-09-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "网站首页更新检查查询参数")
public class WebSiteIndexCheckQuery extends Query {

    @Schema(description = "站点ID，多个用逗号分隔")
    private String websiteIds;

    @Schema(description = "站点名称，多个用逗号分隔")
    private String websiteNames;

    @Schema(description = "站点URL，多个用逗号分隔")
    private String websiteUrls;

    @Schema(description = "分组ID")
    private Long groupId;

    @Schema(description = "开始日期，格式：yyyy-MM-dd")
    private String sdate;

    @Schema(description = "结束日期，格式：yyyy-MM-dd")
    private String edate;

    @Schema(description = "是否更新：0-未更新，1-已更新")
    private Integer isUpdate;

    @Schema(description = "排序字段：0-检查时间，1-最后更新时间，2-更新文章个数")
    private Integer sortField = 0;

    @Schema(description = "排序方向：0-降序，1-升序")
    private Integer sortDirection = 0;
}
