package cn.dahe.model.query;

import cn.dahe.model.dto.Query;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 首页文章查询参数
 *
 * <AUTHOR>
 * @date 2025-09-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class IndexArticleQuery extends Query {

    /**
     * 网站ID
     */
    private Long websiteId;

    /**
     * 开始时间
     */
    private Date beginTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 文章ID列表（用于批量导出）
     */
    private List<Long> articleIds;

    /**
     * 导出类型：0-按条件导出，1-按ID批量导出
     */
    private Integer exportType = 0;
}