package cn.dahe.model.query;

import cn.dahe.model.dto.Query;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * 首页更新主表查询参数
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@ToString(callSuper=true)
@EqualsAndHashCode(callSuper = false)
@Schema(description = "首页更新主表查询参数")
public class ChkUpdateSiteIndexQuery extends Query {

    @Schema(description = "分组id", example = "1,2,3")
    private String groupId = "";

    @Schema(description = "分组名称", example = "站1,站2,站3")
    private String groupName = "";

    @Schema(description = "网站id", example = "1,2,3")
    private String websiteId = "";

    @Schema(description = "网站名称", example = "名称1,名称2,名称3")
    private String websiteName = "";

    @JsonIgnore
    @Schema(description = "网站首页地址", example = "https://www.a1.cn,https://www.a2.cn,https://www.a3.cn")
    private String websiteIndexUrl = "";

    @Schema(description = "解析开始时间", example = "2025-07-01 00:00:00")
    private String beginTime = "";

    @Schema(description = "解析结束时间", example = "2025-07-31 23:59:59")
    private String endTime = "";

    @Schema(description = "勾选记录Id，多个用逗号分隔")
    private String ids = "";

    @Schema(description = "类型(0:站点,1:分组)")
    private Integer type;

    @Schema(description = "查询类型，0：网站，1：新媒体")
    private Integer searchType;

    @Schema(description = "解析类型：0：网站  1：微信  2：微博  3：头条号  6335：抖音号  6429：快手号  6452：微信视频号  6460：小红书")
    private List<Integer> processTypeList;

    @Schema(description = "站点分类,12：微信  13：微博  19：头条号  27：抖音号  28：快手号  29：微信视频号  30：小红书")
    private List<Integer> siteClassifyList;

    @Schema(description = "栏目id")
    private Long channelId;
}
