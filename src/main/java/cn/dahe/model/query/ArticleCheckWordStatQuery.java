package cn.dahe.model.query;

import cn.dahe.enums.SearchProcessTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-07-05
 */
@Data
public class ArticleCheckWordStatQuery {

    Integer page = 1;

    Integer limit = 10;

    @Schema(description = "关键词，匹配正词和错词", type = "string")
    private String keyword;

    @Schema(description = "网站ID列表", example = "6,7", type = "array", implementation = Long.class)
    private List<Long> websiteIds;

    @Schema(description = "栏目ID列表", example = "6,7", type = "array", implementation = Long.class)
    private List<Long> channelIds;

    @Schema(description = "错误等级", example = "1,2,3,4,5", type = "array", implementation = Integer.class)
    private List<Integer> errorLevels;

    /**
     * 发布开始时间
     */
    @Schema(description = "发布开始时间", example = "2025-07-16")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date pubBeginDate;
    /**
     * 发布结束时间
     */
    @Schema(description = "发布结束时间", example = "2025-07-17")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date pubEndDate;

    @Schema(description = "一级错误类型", example = "1,2,3", type = "array", implementation = Long.class)
    private List<Long> firstErrorTypes;

    @Schema(description = "二级错误类型", type = "array", implementation = Long.class)
    private List<Long> secondErrorTypes;

    @Schema(description = "三级错误类型", type = "array", implementation = Long.class)
    private List<Long> thirdErrorTypes;

    @Schema(description = "勘误过滤词", example = "0,1", type = "array", implementation = Integer.class)
    private List<Integer> filterStatuses;

    @Schema(description = "类型(0:站点,1:分组)")
    private Integer type;

    // @Schema(description = "查询类型，0：网站，1：新媒体")
    // private Integer searchType;

    @Schema(description = "搜索平台类型 目前暂定 0网站 1微信 2微博 3今日头条 4其他平台 和SearchProcessTypeEnum对齐", example = "")
    private Integer searchProcessType;

    private List<Integer> getProcessTypes() {
        SearchProcessTypeEnum type = SearchProcessTypeEnum.getByValue(searchProcessType);
        return type.getProcessTypeValues();
    }
}
