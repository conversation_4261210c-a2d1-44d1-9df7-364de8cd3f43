package cn.dahe.model.query;

import cn.dahe.model.dto.Query;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 仪表板查询参数
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "仪表板查询参数")
public class DashboardQuery extends Query {

    @Schema(description = "开始时间", example = "2025-07-01 00:00:00")
    private String startTime;

    @Schema(description = "结束时间", example = "2025-07-31 23:59:59")
    private String endTime;

    @Schema(description = "分组ID", example = "1")
    private Long groupId;

    @Schema(description = "分组名称", example = "政府网站")
    private String groupName;

    @Schema(description = "网站ID列表", example = "1,2,3")
    private String websiteIds;

    @Schema(description = "统计类型", example = "daily")
    private String statisticsType;

    @Schema(description = "是否包含历史数据", example = "true")
    private Boolean includeHistory = false;

    @Schema(description = "内容标题", example = "河南省政府")
    private String contentTitle;

    @Schema(description = "网站名称", example = "河南省人民政府")
    private String websiteName;

    @Schema(description = "检测状态", example = "正常")
    private String checkStatus;

    @Schema(description = "问题类型", example = "错别字")
    private String issueType;

    @Schema(description = "严重程度", example = "严重")
    private String severity;

    @Schema(description = "处理状态", example = "待处理")
    private String processStatus;

    /**
     * 获取分页偏移量
     */
    public Integer getOffset() {
        if (getPage() == null || getLimit() == null) {
            return 0;
        }
        return (getPage() - 1) * getLimit();
    }
}
