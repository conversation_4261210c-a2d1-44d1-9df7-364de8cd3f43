package cn.dahe.model.query;

import cn.dahe.model.dto.Query;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 转办督办
 * <AUTHOR>
 * @date 2025-08-21
 */
@Data
@Schema(name = "转办督办", description = "转办督办")
public class ChkTransferTaskQuery extends Query {

    @Schema(description = "信息筛选类型：0网站名称,1文章标题", example = "1")
    private Integer type;

    @Schema(description = "信息筛选", example = "1")
    private String keyword;

    @Schema(description = "整改状态 0未下发 1待整改 2已整改 3无需整改", example = "整改状态 0未下发 1待整改 2已整改 3无需整改")
    private Integer rectifyStatus;


    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "任务下发时间开始", example = "2025-07-30 10:30:00")
    private Date beginSendTime;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "任务下发时间结束", example = "2025-07-30 10:30:00")
    private Date endSendTime;

}
