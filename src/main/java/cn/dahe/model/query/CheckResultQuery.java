package cn.dahe.model.query;

import cn.dahe.enums.CheckResultDisplayTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 文章检查查询参数
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
@Schema(description = "检查结果查询参数")
public class CheckResultQuery {

    @Schema(description = "错误等级", example = "1,2,3,4,5", type = "array", implementation = Integer.class)
    private List<Integer> errorLevels;

    @Schema(description = "一级错误类型", example = "1,2,3", type = "array", implementation = Long.class)
    private List<Long> firstErrorTypes;

    @Schema(description = "二级错误类型", type = "array", implementation = Long.class)
    private List<Long> secondErrorTypes;

    @Schema(description = "三级错误类型", type = "array", implementation = Long.class)
    private List<Long> thirdErrorTypes;

    @Schema(description = "错误审核状态", example = "0,1,2", type = "array", implementation = Integer.class)
    private List<Integer> resultAuditStatuses;

    @Schema(description = "检查词ID", type = "integer")
    private Long wordId;

    @Schema(description = "过滤词状态", example = "0,1", type = "array", implementation = Integer.class)
    private List<Integer> filterStatuses;

    @Schema(description = "错误词", type = "string")
    private String errorWord;

    @Schema(description = "建议词", type = "string")
    private String suggestWord;

    @Schema(description = "是否只显示指定的错误类型（true: 只显示指定类型的错误列表，false: 显示文章的所有错误列表）", type = "boolean", defaultValue = "false")
    private Boolean onlyShowSpecifiedErrors = false;

    @Schema(description = "结果显示形式（0：传统标题正文，1：快照显示结果） 默认0", type = "integer")
    private Integer resultDisplayType = CheckResultDisplayTypeEnum.NORMAL.getValue();
} 