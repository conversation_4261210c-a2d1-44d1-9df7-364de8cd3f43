package cn.dahe.model.query;

import cn.dahe.model.dto.Query;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 全站搜索查询参数
 *
 * <AUTHOR>
 * @date 2025-08-18
 */

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "全站搜索查询参数")
public class AllSiteSearchQuery extends Query {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "类型(0:站点,1:分组)")
    private Integer type;

    @Schema(description = "站点Id的值，多值逗号分割")
    private String siteId;

    @Schema(description = "栏目ID,单个值")
    private Integer channelId;


    @Schema(description = "分组Id的值，多值逗号分割")
    private String groupId;

    @Schema(description = "搜索类型：标题或内容(0:标题 1:内容)", type = "int")
    private String keywordType;

    @Schema(description = "搜索值：标题或内容", type = "string")
    private String keyword;

    @Schema(description = "筛选起始时间", type = "string", format = "date-time")
    private String beginTime;

    @Schema(description = "筛选结束时间", type = "string", format = "date-time")
    private String endTime;

    @Schema(description = "排序列（ 0 发布时间 1 检测时间 ）", example = "0", type = "integer")
    private Integer sortField;

    @Schema(description = "排序顺序（0:降序 1:升序）", example = "0", type = "integer")
    private Integer sortDirection;

    @Schema(description = "勾选记录Id，多个用逗号分隔")
    private String ids = "";

    @Schema(description = "查询类型，0：网站，1：新媒体")
    private Integer searchType;
}