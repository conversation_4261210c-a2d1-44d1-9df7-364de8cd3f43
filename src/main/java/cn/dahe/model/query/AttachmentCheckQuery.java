package cn.dahe.model.query;

import cn.dahe.enums.ArticleSortTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 附件检查查询参数 - 对应原型图功能
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "附件检查查询参数")
public class AttachmentCheckQuery extends CheckResultQuery{

    @Schema(description = "附件ID列表", example = "1,2")
    private List<Long> attachmentIds;

    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码最小值为 1")
    Integer page = 1;
    @NotNull(message = "每页条数不能为空")
    @Min(value = 1, message = "每页条数最小值为 1")
    Integer limit = 10;


    @Schema(description = "站点ID列表", example = "6,7")
    private List<Long> websiteIds;

    /**
     * 发布开始时间
     */
    @Schema(description = "发布开始时间", example = "2025-07-16")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date pubBeginDate;
    /**
     * 发布结束时间
     */
    @Schema(description = "发布结束时间", example = "2025-07-17")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date pubEndDate;

    @Schema(description = "巡查精准度 0 查全 1 查准", example = "0,1", type = "array", implementation = Integer.class)
    private List<Integer> checkStrategies;


    @Schema(description = "排序列（ 0 发布时间 1 检测时间 ）", example = "0", type = "integer")
    private Integer sortField = 0;

    @Schema(description = "排序顺序（0:降序 1:升序）", example = "0", type = "integer")
    private Integer sortDirection = 0;




    /**
     * 不要删除，sql排序用
     */
    public ArticleSortTypeEnum getSortTypeEnum() {
        return ArticleSortTypeEnum.getByFieldAndDirection(sortField, sortDirection);
    }

}
