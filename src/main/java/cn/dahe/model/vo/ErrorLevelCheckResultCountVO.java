package cn.dahe.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 网站文章错误统计VO
 */
@Data
@Schema(description = "根据错误级别统计错误数量")
public class ErrorLevelCheckResultCountVO {

    /**
     * 错误总数
     */
    @Schema(description = "检测错误")
    private Integer checkResultCount = 0;

    /**
     * 一级错误数量
     */
    @Schema(description = "一级错误数量")
    private Integer lv1CheckResultCount = 0;

    /**
     * 二级错误数量
     */
    @Schema(description = "二级错误数量")
    private Integer lv2CheckResultCount = 0;

    /**
     * 三级错误数量
     */
    @Schema(description = "三级错误数量")
    private Integer lv3CheckResultCount = 0;

    /**
     * 四级错误数量
     */
    @Schema(description = "四级错误数量")
    private Integer lv4CheckResultCount = 0;

    /**
     * 五级错误数量
     */
    @Schema(description = "五级错误数量")
    private Integer lv5CheckResultCount = 0;
} 