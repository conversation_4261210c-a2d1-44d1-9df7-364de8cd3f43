package cn.dahe.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class SearchWebsiteVO {

    @Schema(description = "站点id")
    private Integer id;
    @Schema(description = "站点名称")
    private String name;
    @Schema(description = "录入状态 为true时不要录入")
    private Boolean status;
    @Schema(description = "已录入网站id")
    private Long websiteId;
    @Schema(description = "站点url")
    private String url;

}
