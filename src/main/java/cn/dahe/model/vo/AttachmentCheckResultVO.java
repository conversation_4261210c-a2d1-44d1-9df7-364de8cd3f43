package cn.dahe.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 文章检查结果VO
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "检查结果")
public class AttachmentCheckResultVO extends ArticleCheckResultVO{

    @Schema(description = "所在文章标题")
    private String parentArticleTitle;

    @Schema(description = "所在文章链接")
    private String parentArticleUrl;

    @Schema(description = "附件类型")
    private String attachmentType;

}