package cn.dahe.model.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 全站搜索结果
 */
@Data
@Schema(description = "全站搜索结果")
public class ArticleAllSiteSearchVO {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "标题")
    private String htmlTitle;

    @Schema(description = "内容")
    private String htmlContent;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @Schema(description = "发布时间", example = "2025-07-30")
    private Date pubTime;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间", example = "2025-07-30 10:30:00")
    private Date checkTime;

    @Schema(description = "转载信息")
    private String reprintSource;

    @Schema(description = "新闻地址")
    private String url;

    @Schema(description = "站点ID")
    private Long websiteId;

    @Schema(description = "站点名称")
    private String websiteName;

}