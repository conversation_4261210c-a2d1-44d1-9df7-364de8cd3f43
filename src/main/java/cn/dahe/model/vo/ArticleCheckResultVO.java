package cn.dahe.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 文章检查结果VO
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
@Schema(description = "检查结果")
public class ArticleCheckResultVO {

    @Schema(description = "网站/账号名称")
    private String websiteName;

    @Schema(description = "分组名称")
    private String groupName;


    @Schema(description = "文章审核状态：0未审核 1审核通过 2审核驳回", example = "0")
    private String auditStatusName;
    /**
     * 发布时间
     */
    @Schema(description = "发布时间")
    private Date pubTime;

    @Schema(description = "网址")
    private String articleUrl;

    private String articleTitle;

    @Schema(description = "快照网址")
    private String snapshotUrl;

    @Schema(description = "平台类型名称")
    public String processTypeName;

    private String errorWord;

    private String suggestWord;

    private String errorLevelName;

    private String errorTypeName;

    private String context;

}