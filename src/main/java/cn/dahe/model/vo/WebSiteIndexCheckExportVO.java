package cn.dahe.model.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.BooleanEnum;
import lombok.Data;

/**
 * 网站首页更新检查数据导出VO
 *
 * <AUTHOR>
 * @date 2025-09-03
 */
@Data
@HeadStyle(fillForegroundColor = 22, wrapped = BooleanEnum.TRUE)
@ContentStyle(wrapped = BooleanEnum.TRUE)
public class WebSiteIndexCheckExportVO {

    @ExcelProperty("序号")
    @ColumnWidth(8)
    private Integer serialNumber;

    @ExcelProperty("网站名称")
    @ColumnWidth(20)
    private String websiteName;

    @ExcelProperty("网站地址")
    @ColumnWidth(30)
    private String websiteUrl;

    @ExcelProperty("分组")
    @ColumnWidth(15)
    private String groupName;

    @ExcelProperty("首页是否更新")
    @ColumnWidth(15)
    private String updateStatusDesc;

    @ExcelProperty("更新天数")
    @ColumnWidth(20)
    private String updateDays;

    @ExcelProperty("连续未更新天数")
    @ColumnWidth(20)
    private String continuousNotUpdateDays;

    @ExcelProperty("连续更新天数")
    @ColumnWidth(20)
    private String continuousUpdateDays;

    @ExcelProperty("最后更新时间")
    @ColumnWidth(20)
    private String lastUpdateTime;

    @ExcelProperty("更新文章个数")
    @ColumnWidth(15)
    private Integer updateCount;
}