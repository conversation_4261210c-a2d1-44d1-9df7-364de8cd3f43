package cn.dahe.model.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gitee.sunchenbin.mybatis.actable.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024-02-24
 */
@Data
@TableName("t_department")
public class DepartmentVO {

    @IsKey
    @TableId
    @IsAutoIncrement
    @Column(name = "id")
    private Integer id;


    @Column("name")
    @ColumnComment("名称")
    private String name;


    private Integer seq;


    /**
     * 部门类型/类别
     * 1.报纸A
     * 2.广播电视B
     * 3.行业报、高校报、省直有关旗杆C
     * 4.省直有关期刊
     * 5.高校报
     * 6.新媒体D
     * 7.县级融媒体E
     * 8.中央、香港 F
     * <p>
     * 9.审批组
     */
    private String type;


    private String dictProjects;

    private String dictProjectsStr;


    /**
     * 更新人
     */
    private Integer updateUserId;


    /**
     * 更新人名称
     */
    private String updateUserName;


    /**
     * 添加时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;


    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


}
