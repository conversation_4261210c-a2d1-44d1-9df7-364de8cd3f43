package cn.dahe.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 网站栏目检查统计VO
 *
 * <AUTHOR>
 * @date 2025-09-10
 */
@Data
@Schema(description = "网站栏目检查统计VO")
public class WebSiteChannelStatisticsVO {

    @Schema(description = "站点ID")
    private Long websiteId;

    @Schema(description = "站点名称")
    private String websiteName;

    @Schema(description = "站点URL")
    private String websiteUrl;

    @Schema(description = "总栏目数")
    private Integer totalColumns;

    @Schema(description = "已更新栏目数")
    private Integer updatedColumns;

    @Schema(description = "未更新栏目数")
    private Integer notUpdatedColumns;

//    @Schema(description = "栏目更新率（百分比）")
//    private Double updateRate;

}