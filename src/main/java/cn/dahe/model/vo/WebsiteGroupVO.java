package cn.dahe.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 网站分组VO（包含网站列表）
 */
@Data
@Schema(name = "网站分组信息", description = "包含分组下的网站列表")
public class WebsiteGroupVO {

    @Schema(description = "分组ID")
    private Integer id;

    @Schema(description = "分组名称")
    private String groupName;

    @Schema(description = "网站列表")
    private List<WebsiteVO> websites;
} 