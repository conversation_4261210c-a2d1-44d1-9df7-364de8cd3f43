package cn.dahe.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 首页更新检查VO - 对应原型图字段
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@Schema(name = "首页更新检查视图对象", description = "网站首页更新检查信息视图")
public class ChkUpdateSiteColumnCountVO {

    @Schema(description = "栏目总数", example = "0")
    private Long totalColumnCount;
    @Schema(description = "采集正常数", example = "0")
    private Long normalColumnCount;
    @Schema(description = "采集异常数", example = "0")
    private Long collectAbnormalCount;
    @Schema(description = "不检测更新数", example = "0")
    private Long noCheckUpdateCount;

    @Schema(description = "正常检测结果总数", example = "0")
    private Long totalCheckResultCount;
    @Schema(description = "检测结果正常数", example = "0")
    private Long normalCheckCount;
    @Schema(description = "严重逾期数", example = "0")
    private Long seriousOverdueCount;
    @Schema(description = "即将逾期数", example = "0")
    private Long aboutToOverdueCount;
}
