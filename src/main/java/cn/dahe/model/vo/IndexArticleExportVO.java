package cn.dahe.model.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 首页文章导出VO
 *
 * <AUTHOR>
 * @date 2025-09-03
 */
@Data
@HeadStyle(fillForegroundColor = 22, wrapped = BooleanEnum.TRUE)
@ContentStyle(wrapped = BooleanEnum.TRUE)
public class IndexArticleExportVO {

    @ExcelProperty("标题")
    @ColumnWidth(30)
    private String title;

    @ExcelProperty("链接")
    @ColumnWidth(40)
    private String articleUrl;

    @ExcelProperty("发布时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ColumnWidth(20)
    private Date pubTime;

    @ExcelProperty("来源")
    @ColumnWidth(20)
    private String source;
}