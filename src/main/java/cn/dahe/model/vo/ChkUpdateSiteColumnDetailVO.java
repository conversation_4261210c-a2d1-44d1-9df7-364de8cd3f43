package cn.dahe.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 栏目更新检查详情 - 基于article表结构
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@Schema(name = "栏目更新检查详情", description = "栏目更新检查详情")
public class ChkUpdateSiteColumnDetailVO {

    @Schema(description = "编号", example = "1")
    private Long id;

    @Schema(description = "序号", example = "1")
    private Integer serialNumber;

    // ==================== 基础字段 ====================

    @Schema(description = "文章ID")
    private Integer articleId;

    @Schema(description = "文章链接")
    private String articleUrl;

    @Schema(description = "文章标题")
    private String articleTitle;

    @Schema(description = "焦点图链接")
    private String focusImgUrl;

    @Schema(description = "摘要")
    private String summary;

    @Schema(description = "正文")
    private String content;

    @Schema(description = "来源")
    private String source;

    @Schema(description = "来源")
    private String origin;

    @Schema(description = "省份ID")
    private Integer province;

    @Schema(description = "分类")
    private String classify;

    @Schema(description = "站点分类")
    private Integer siteClassify;

    @Schema(description = "内容权重")
    private Integer boost;

    @Schema(description = "情感倾向值")
    private Integer sens;

    @Schema(description = "发布时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date pubTime;

    @Schema(description = "站点ID")
    private Integer siteId;

    @Schema(description = "站点名称", example = "站点名称")
    private String siteName;

    @Schema(description = "栏目ID", example = "1")
    private Long columnId;

    @Schema(description = "更新时间", example = "2025-07-30 10:30:00")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @Schema(description = "检测状态(1:正常,2:异常,3:不可访问)", example = "1")
    private Integer checkStatus;

    @Schema(description = "栏目快照地址", example = "https://snapshot.example.com/column/123456")
    private String snapshotUrl;

    @Schema(description = "栏目快照签名", example = "def456ghi789")
    private String snapshotSignature;

    @Schema(description = "解析时间", example = "2025-07-30 10:30:00")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date parseTime;

    @Schema(description = "创建时间", example = "2025-07-30 10:30:00")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @Schema(description = "是否删除(0:否,1:是)", example = "0")
    private Integer isDel;

    // ==================== 扩展字段（用于显示） ====================

    @Schema(description = "检测状态描述", example = "正常")
    private String checkStatusDesc;

    @Schema(description = "连续不更新天数", example = "3")
    private Integer continuousNotUpdateDays;

    @Schema(description = "检测结果", example = "通过")
    private String checkResult;

    @Schema(description = "操作", example = "内容明细")
    private String operation;

    @Schema(description = "分组名称", example = "政府网站")
    private String groupName;

    // ==================== 栏目相关字段 ====================

    @Schema(description = "栏目名称", example = "新闻动态")
    private String columnName;

    @Schema(description = "栏目分类", example = "新闻类")
    private String columnCategory;

    @Schema(description = "栏目URL")
    private String columnUrl;
}
