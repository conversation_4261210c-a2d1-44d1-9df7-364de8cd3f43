package cn.dahe.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 网站文章错误词统计总计VO
 */
@Data
@Schema(description = "网站文章错误词统计总计")
public class WebsiteArticleCheckWordTotalStatsVO {

    /**
     * 错误词总数
     */
    @Schema(description = "错误词总数")
    private Integer checkWordCount;

    /**
     * 一级错误词数量
     */
    @Schema(description = "一级错误词数量")
    private Integer lv1CheckWordCount;

    /**
     * 二级错误词数量
     */
    @Schema(description = "二级错误词数量")
    private Integer lv2CheckWordCount;

    /**
     * 三级错误词数量
     */
    @Schema(description = "三级错误词数量")
    private Integer lv3CheckWordCount;

    /**
     * 四级错误词数量
     */
    @Schema(description = "四级错误词数量")
    private Integer lv4CheckWordCount;

    /**
     * 五级错误词数量
     */
    @Schema(description = "五级错误词数量")
    private Integer lv5CheckWordCount;
} 