package cn.dahe.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 仪表板统计数据VO
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@Schema(name = "仪表板统计数据", description = "系统首页统计信息")
public class DashboardStatisticsVO {

    @Schema(description = "白名单任务数", example = "156")
    private Long whitelistTaskCount;

    @Schema(description = "白天任务数", example = "89")
    private Long dayTaskCount;

    @Schema(description = "白天处理数", example = "67")
    private Long dayProcessedCount;

    @Schema(description = "未检测问题数", example = "23")
    private Long undetectedIssueCount;

    @Schema(description = "已检测问题数", example = "45")
    private Long detectedIssueCount;

    @Schema(description = "新增问题数", example = "12")
    private Long newIssueCount;

    @Schema(description = "解决已检测问题数", example = "33")
    private Long resolvedIssueCount;

    @Schema(description = "总网站数", example = "128")
    private Long totalWebsiteCount;

    @Schema(description = "活跃网站数", example = "95")
    private Long activeWebsiteCount;

    @Schema(description = "今日检查次数", example = "456")
    private Long todayCheckCount;

    @Schema(description = "今日发现问题数", example = "18")
    private Long todayIssueCount;
}
