package cn.dahe.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 用户站点栏目分配VO
 *
 * <AUTHOR>
 * @date 2025-01-11
 */
@Data
@Schema(description = "用户站点栏目分配VO")
public class UserSiteColumnVO {

    @Schema(description = "编号")
    private Integer id;

    @Schema(description = "用户id")
    private Integer userId;

    @Schema(description = "用户姓名")
    private String userName;

    @Schema(description = "站点id")
    private Integer siteId;

    @Schema(description = "站点名称")
    private String siteName;

    @Schema(description = "站点地址")
    private String siteUrl;

    @Schema(description = "栏目id")
    private Integer columnId;

    @Schema(description = "栏目名称")
    private String columnName;

    @Schema(description = "栏目地址")
    private String columnUrl;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "创建人Id")
    private String createByUserId;

    @Schema(description = "创建人姓名")
    private String createByUserName;

    @Schema(description = "修改时间")
    private Date updateTime;

    @Schema(description = "修改人Id")
    private String updateByUserId;

    @Schema(description = "修改人姓名")
    private String updateByUserName;
}

/**
 * 站点信息VO
 */
@Data
@Schema(description = "站点信息VO")
class SiteInfoVO {

    @Schema(description = "站点id")
    private Integer siteId;

    @Schema(description = "站点名称")
    private String siteName;

    @Schema(description = "站点地址")
    private String siteUrl;

    @Schema(description = "是否已分配")
    private Boolean assigned;
}

/**
 * 栏目信息VO
 */
@Data
@Schema(description = "栏目信息VO")
class ColumnInfoVO {

    @Schema(description = "栏目id")
    private Integer columnId;

    @Schema(description = "栏目名称")
    private String columnName;

    @Schema(description = "栏目地址")
    private String columnUrl;

    @Schema(description = "是否已分配")
    private Boolean assigned;
}

/**
 * 用户站点栏目分配请求VO
 */
@Data
@Schema(description = "用户站点栏目分配请求VO")
class UserSiteColumnAssignVO {

    @Schema(description = "用户id", required = true)
    private Integer userId;

    @Schema(description = "站点栏目分配列表", required = true)
    private List<SiteColumnAssignVO> assignments;
}

/**
 * 站点栏目分配VO
 */
@Data
@Schema(description = "站点栏目分配VO")
class SiteColumnAssignVO {

    @Schema(description = "站点id", required = true)
    private Integer siteId;

    @Schema(description = "站点名称", required = true)
    private String siteName;

    @Schema(description = "站点地址", required = true)
    private String siteUrl;

    @Schema(description = "栏目id列表", required = true)
    private List<Integer> columnIds;

    @Schema(description = "栏目信息列表", required = true)
    private List<ColumnAssignVO> columns;
}

/**
 * 栏目分配VO
 */
@Data
@Schema(description = "栏目分配VO")
class ColumnAssignVO {

    @Schema(description = "栏目id", required = true)
    private Integer columnId;

    @Schema(description = "栏目名称", required = true)
    private String columnName;

    @Schema(description = "栏目地址", required = true)
    private String columnUrl;
}
