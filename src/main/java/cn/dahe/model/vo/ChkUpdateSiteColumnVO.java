package cn.dahe.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.annotation.ColumnComment;
import com.gitee.sunchenbin.mybatis.actable.annotation.DefaultValue;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 栏目更新检查VO - 基于article表结构
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@Schema(name = "栏目更新检查视图对象", description = "网站栏目更新检查信息视图")
public class ChkUpdateSiteColumnVO {

    @Schema(description = "编号", example = "1")
    private Long id;

    @Schema(description = "序号", example = "1")
    private Integer serialNumber;

    @Schema(description = "站点ID")
    private int siteId;

    @Schema(description = "站点名称", example = "站点名称")
    private String siteName;

    @Schema(description = "分组ID")
    private Long groupId;

    @Schema(description = "分组名称", example = "分组名称")
    private String groupName;

    @Schema(description = "栏目ID", example = "1")
    private Long columnId;

    @Schema(description = "栏目名称", example = "栏目名称")
    private String columnName;

    @Schema(description = "处理类型", example = "1")
    private Integer processType;

    @Schema(description = "更新天数", example = "1")
    private Integer updateDays;

    @Schema(description = "更新天数详情", example = "2025-06-19,2025-06-20,2025-06-21")
    private String updateDaysDetail;

    @Schema(description = "连续不更新天数", example = "1")
    private Integer continuousNotUpdateDays;

    @Schema(description = "连续未更新天数详情", example = "2025-06-19,2025-06-20,2025-06-21")
    private String continuousNotUpdateDaysDetail;

    @Schema(description = "更新数量", example = "1")
    private Integer updateNum;

    @Schema(description = "检测结果：正常，即将逾期，严重逾期", example = "正常")
    private String checkResult;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间", example = "2025-07-30 10:30:00")
    private Date updateTime;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "上次更新时间", example = "2025-07-30 10:30:00")
    private Date lastUpdateTime;
}
