package cn.dahe.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 网站和栏目文章错误统计总计VO
 */
@Data
@Schema(description = "网站和栏目文章错误统计总计")
public class WebsiteArticleCheckTotalStatsVO {

    /**
     * 文章总数
     */
    @Schema(description = "文章总数")
    private Integer articleCount;

    /**
     * 文章总字数
     */
    @Schema(description = "文章总字数")
    private Integer articleLength;

    /**
     * 网站总数
     */
    @Schema(description = "网站/栏目总数")
    private Integer count;

    /**
     * 错误总数
     */
    @Schema(description = "错误总数")
    private Integer checkResultCount;

    /**
     * 一级错误网站/栏目数量
     */
    @Schema(description = "一级错误网站/栏目数量")
    private Integer lv1Count;

    /**
     * 二级错误网站/栏目数量
     */
    @Schema(description = "二级错误网站/栏目数量")
    private Integer lv2Count;

    /**
     * 三级错误网站/栏目数量
     */
    @Schema(description = "三级错误网站/栏目数量")
    private Integer lv3Count;

    /**
     * 四级错误网站/栏目数量
     */
    @Schema(description = "四级错误网站/栏目数量")
    private Integer lv4Count;

    /**
     * 五级错误网站/栏目数量
     */
    @Schema(description = "五级错误网站/栏目数量")
    private Integer lv5Count;

} 