package cn.dahe.model.vo;

import cn.dahe.enums.ProcessTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 网站信息VO
 */
@Data
@Schema(name = "网站信息", description = "网站基本信息")
public class WebsiteVO {

    @Schema(description = "ID")
    private Long id;

    private Long websiteId;

    private Long siteId;

    private Long channelId;

    @Schema(description = "网站状态：0：禁用 1：正常")
    private Integer status;

    @Schema(description = "网站名称")
    private String name;

    @Schema(description = "网站地址")
    private String url;

    @Schema(description = "分组ID")
    private Long groupId;

    @Schema(description = "分组名称")
    private String groupName;

    @Schema(description = "巡查精准度/策略")
    private Integer checkStrategy;

    @Schema(description = "平台类型")
    private Integer processType;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "添加时间")
    private Date createTime;

    public String getProcessTypeName() {
        return ProcessTypeEnum.getByValue(processType).getInfo();
    }

    public String getPlatformImgUrl() {
        return ProcessTypeEnum.getByValue(processType).getLogoPath();
    }
} 