package cn.dahe.model.vo;

import cn.dahe.enums.ProcessTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class CheckSnapshotVO {

    @Schema(description = "检测任务ID")
    private Long taskId;
    @Schema(description = "任务类型 0检查 1附件 2复查")
    private Integer relationType;
    @Schema(description = "网站/账号ID")
    public Long websiteId;
    @Schema(description = "网站/账号名称")
    private String websiteName;
    @Schema(description = "分组名称")
    private String groupName;
    @Schema(description = "网址")
    private String url;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "发布时间")
    private Date pubTime;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "检测时间")
    private Date checkTime;
    @Schema(description = "上级地址（附件所在页面地址）")
    private String parentUrl;
    @Schema(description = "上级标题（附件所在页面标题）")
    private String parentTitle;
    @Schema(description = "平台类型")
    private Integer processType;
    @Schema(description = "平台类型名称")
    public String getProcessTypeName() {
        return ProcessTypeEnum.getByValue(this.processType).getInfo();
    }
    @Schema(description = "错误列表")
    private List<CheckResultVO> errorObjs;
    // @Schema(description = "源码")
    // private String htmlCode;
    @Schema(description = "标题")
    private String title;
}
