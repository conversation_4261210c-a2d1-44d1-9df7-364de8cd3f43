package cn.dahe.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 首页更新检查VO - 对应原型图字段
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@Schema(name = "首页更新检查视图对象", description = "网站首页更新检查信息视图")
public class ChkUpdateSiteIndexVO {

    @Schema(description = "编号", example = "1")
    private Long id;

    @Schema(description = "分组", example = "政府网站")
    private String groupName;

    @Schema(description = "网站名称", example = "河南省人民政府")
    private String websiteName;

    @Schema(description = "网站首页地址", example = "https://www.henan.gov.cn")
    private String websiteIndexUrl;

    @Schema(description = "栏目名称", example = "河南省人民政府")
    private String channelName;

    @Schema(description = "栏目地址", example = "https://www.henan.gov.cn")
    private String channelUrl;

    @Schema(description = "0：网站  1：微信  2：微博  3：头条号  6335：抖音号  6429：快手号  6452：微信视频号  6460：小红书")
    private Integer processType;

    private String processTypeName;

    @Schema(description = "首页是否更新：0未更新，1已更新", example = "1")
    private Integer status;

    @Schema(description = "更新天数", example = "页面上展示的时候，需要展示出一列日期：2025-07-29,2025-07-30")
    private List<String> updateDays;

    @Schema(description = "连续未更新天数", example = "页面上展示的时候，需要展示出一列日期：2025-07-29,2025-07-30")
    private List<String> continuousNotUpdateDays;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "解析时间", example = "2025-07-30 10:30:00")
    private Date lastParseTime;

    //TODO 在每行记录点击查看详情，才分页展示chk_update_site_index中的列表
}
