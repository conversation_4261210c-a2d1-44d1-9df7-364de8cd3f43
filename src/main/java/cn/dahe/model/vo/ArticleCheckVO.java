package cn.dahe.model.vo;

import cn.dahe.enums.ProcessTypeEnum;
import cn.dahe.service.WebsiteGroupService;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 文章检查结果VO
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "文章检查结果")
public class ArticleCheckVO extends CheckVO {
    /**
     * 文章ID
     */
    @Schema(description = "文章ID")
    private Long articleId;

    @Schema(description = "网站/账号ID")
    public Long websiteId;

    @Schema(description = "网站/账号名称")
    private String websiteName;

    @Schema(description = "站点ID")
    private Long siteId;

    @Schema(description = "栏目ID")
    private Long columnId;

    @Schema(description = "栏目名称")
    private String columnName;

    @Schema(description = "分组ID")
    private Long groupId;

    @Schema(description = "分组名称")
    private String groupName;

    public String getGroupName() {
        return WebsiteGroupService.getGroupName(this.groupId);
    }


    @Schema(description = "文章审核状态：0未审核 1审核通过 2审核驳回", example = "0")
    private Integer auditStatus;
    /**
     * 发布时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "发布时间")
    private Date pubTime;

    @Schema(description = "摘要")
    private String summary;

    @Schema(description = "作者")
    private String author;

    @Schema(description = "网址")
    private String url;

    @Schema(description = "快照网址")
    private String snapshotUrl;

    @Schema(description = "转载信源")
    private String reprintSource;


    @Schema(description = "检查策略 0 查全 1 查准")
    private Integer checkStrategy;
    /**
     * 检查时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "检测时间")
    private Date checkTime;

    @Schema(description = "检测字数")
    private Integer articleLength;

    @Schema(description = "焦点图链接")
    private String focusImgUrl;

    public String getPlatformImgUrl(){
        return ProcessTypeEnum.getByValue(this.processType).getLogoPath();
    }

    @Schema(description = "平台类型")
    private Integer processType;

    @Schema(description = "平台类型名称")
    public String getProcessTypeName() {
        return ProcessTypeEnum.getByValue(this.processType).getInfo();
    }

    @Schema(description = "检测字数")
    public Integer getArticleLength() {
        if(articleLength != null && articleLength > 0){
            return articleLength;
        }
        return StrUtil.length(this.getCleanedContent());
    }


}