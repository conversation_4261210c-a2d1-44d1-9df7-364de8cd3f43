package cn.dahe.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 转办督办
 * <AUTHOR>
 * @date 2025-08-21
 */
@Data
@Schema(name = "转办督办", description = "转办督办")
public class ChkTransferTaskVO {

    @Schema(description = "编号", example = "1")
    private Long id;

    @Schema(description = "整改单位Id", example = "1")
    private String depId;

    @Schema(description = "整改单位名称", example = "xxx部门")
    private String depName;

    @Schema(description = "整改账号ID", example = "1")
    private String transferUserId;

    @Schema(description = "整改账号名称", example = "张三")
    private String transferUserName;

    @Schema(description = "网站ID", example = "1")
    private Integer siteId;

    @Schema(description = "网站名称", example = "网站名称")
    private String siteName;

    @Schema(description = "网站URL", example = "https://www.example.com")
    private String siteUrl;

    @Schema(description = "文章标题", example = "文章标题")
    private String articleTitle;

    @Schema(description = "文章ID", example = "123456")
    private String articleId;

    @Schema(description = "文章URL", example = "https://www.example.com/article/123")
    private String articleUrl;

    @Schema(description = "严重错误词数", example = "0")
    private Integer seriousErrorCount;

    @Schema(description = "一般错误词数", example = "0")
    private Integer commonErrorCount;

    @Schema(description = "疑错词数", example = "0")
    private Integer maybeErrorCount;

    @Schema(description = "自定义错误词数", example = "0")
    private Integer diyErrorCount;

    @Schema(description = "风险提示词数", example = "0")
    private Integer riskTipCount;

    @Schema(description = "选择的检测结果ID", example = "1,2,3")
    private String selectResultIds;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "任务下发时间", example = "2025-07-30 10:30:00")
    private Date sendTime;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间", example = "2025-07-30 10:30:00")
    private Date createTime;

    @Schema(description = "创建账号ID", example = "1")
    private Integer createUserId;

    @Schema(description = "创建账号名称", example = "管理员")
    private String createUserName;

    @Schema(description = "整改状态 0未下发 1待整改 2已整改 3无需整改", example = "1")
    private Integer rectifyStatus;

    @Schema(description = "处置状态 0未处置 1已约谈整改 2已上报线索 3已关停站点", example = "0")
    private Integer disposalStatus;

    @Schema(description = "审核状态 0未审核 1审核通过 2审核驳回", example = "0")
    private Integer auditStatus;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "文章发布时间", example = "2025-07-30 10:30:00")
    private Date pubTime;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "文章采集时间", example = "2025-07-30 10:30:00")
    private Date collectTime;

    @Schema(description = "整改结果id,查询时使用", example = "1")
    private Integer transferReplayId;

    @Schema(description = "检查结果id,查询时使用", example = "1")
    private Integer checkId;
}