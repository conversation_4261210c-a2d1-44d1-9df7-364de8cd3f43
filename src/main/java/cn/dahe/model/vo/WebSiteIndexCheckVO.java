package cn.dahe.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 网站首页更新检查VO
 *
 * <AUTHOR>
 * @date 2025-09-03
 */
@Data
@Schema(description = "网站首页更新检查信息")
public class WebSiteIndexCheckVO {

    @Schema(description = "记录ID")
    private Long id;

    @Schema(description = "站点ID")
    private Long websiteId;

    @Schema(description = "站点名称")
    private String websiteName;

    @Schema(description = "站点URL")
    private String websiteUrl;

    @Schema(description = "更新文章个数")
    private Integer updateCount;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "最后更新时间")
    private Date lastUpdateTime;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "检查时间")
    private Date checkTime;

    @Schema(description = "是否更新：0-未更新，1-已更新")
    private Integer isUpdate;

    @Schema(description = "检查日期（yyyy-MM-dd格式）")
    private String checkDate;

    @Schema(description = "更新状态描述")
    private String updateStatusDesc;

    /**
     * 获取更新状态描述
     */
    public String getUpdateStatusDesc() {
        if (isUpdate == null) {
            return "未知";
        }
        return isUpdate == 1 ? "已更新" : "未更新";
    }

    /**
     * 获取检查日期字符串
     */
    public String getCheckDate() {
        if (checkTime == null) {
            return "";
        }
        return new java.text.SimpleDateFormat("yyyy-MM-dd").format(checkTime);
    }
}
