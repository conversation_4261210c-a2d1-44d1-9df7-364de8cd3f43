package cn.dahe.model.vo.check;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 一级错误类型VO类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(name = "一级错误类型信息")
public class FirstLevelErrorTypeVO extends BaseErrorTypeVO {

    @Schema(description = "二级错误类型列表")
    private List<SecondLevelErrorTypeVO> children;
} 