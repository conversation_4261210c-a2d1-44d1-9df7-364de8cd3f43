package cn.dahe.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 网站首页更新检查详细VO
 *
 * <AUTHOR>
 * @date 2025-09-06
 */
@Data
@Schema(description = "网站栏目更新检查详细信息")
public class WebSiteChannelCheckDetailVO {

    @Schema(description = "记录ID")
    private Long id;

    @Schema(description = "站点ID")
    private Long websiteId;

    @Schema(description = "站点名称")
    private String websiteName;

    @Schema(description = "站点URL")
    private String websiteUrl;

    @Schema(description = "栏目ID")
    private Long columnId;

    @Schema(description = "栏目URL")
    private String columnUrl;

    @Schema(description = "栏目名称")
    private String columnName;

    @Schema(description = "是否更新：0-未更新，1-已更新")
    private Integer isUpdate;

    @Schema(description = "检查天数 存储每天的更新结果")
    private List<UpdateDayInfo> checkDays;

    @Schema(description = "未更新天数列表")
    private List<String> checkNotUpdateDays;

    @Schema(description = "更新天数列表")
    private List<String> checkUpdateDays;

    @Schema(description = "连续未更新天数")
    private Integer continuousNotUpdateDays;

    @Schema(description = "连续未更新天数")
    private Integer continuousNotUpdateCount;

    @Schema(description = "连续更新天数")
    private Integer continuousUpdateCount;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "最后更新时间")
    private Date lastUpdateTime;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "检查时间")
    private Date checkTime;

    @Schema(description = "更新文章个数")
    private Integer updateCount;

    @Schema(description = "更新状态描述")
    private String updateStatusDesc;

    /**
     * 栏目类型 与ChannelType的id关联
     */
    private Long channelTypeId;
    /**
     * 栏目类型名称
     */
    private String channelTypeName;

    /**
     * 更新期限 单位：天 与ChannelType的updatePeriod保持一致
     */
    private Integer updatePeriod;

    /**
     * 更新天数信息
     */
    @Data
    @Schema(description = "更新天数信息")
    public static class UpdateDayInfo {
        @Schema(description = "更新日期")
        private String updateDate;

        @Schema(description = "是否更新：0-未更新，1-已更新")
        private Integer isUpdate;

        @Schema(description = "更新文章个数")
        private Integer updateCount;
    }

    /**
     * 获取更新状态描述
     */
    public String getUpdateStatusDesc() {
        if (isUpdate == null) {
            return "未知";
        }
        return isUpdate == 1 ? "已更新" : "未更新";
    }
}
