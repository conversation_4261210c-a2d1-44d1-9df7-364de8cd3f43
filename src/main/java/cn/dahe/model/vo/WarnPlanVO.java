package cn.dahe.model.vo;

import cn.dahe.entity.WarnPlanPlatform;
import cn.dahe.entity.WarnPlanPushUser;
import cn.dahe.model.dto.WarnPlanDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WarnPlanVO extends WarnPlanDto {

    private List<WarnPlanPlatform> platforms;

    private List<WarnPlanPushUser> pushUsers;
}


