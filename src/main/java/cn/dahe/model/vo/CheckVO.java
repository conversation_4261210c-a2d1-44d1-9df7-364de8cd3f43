package cn.dahe.model.vo;

import cn.hutool.core.collection.CollUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 文章检查结果VO
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
public class CheckVO {

    @Schema(description = "任务ID")
    private Long taskId;

    @Schema(description = "错误列表")
    private List<CheckResultVO> errorObjs;

    @Schema(description = "错误数量")
    private Integer checkResultCount;

    public Integer getCheckResultCount() {
        return CollUtil.size(errorObjs);
    }

    @Schema(description = "根据错误级别统计错误数量")
    private ErrorLevelCheckResultCountVO errorLevelCheckResultCountStats;

    @Schema(description = "html标题")
    private String htmlTitle;

    @Schema(description = "html内容")
    private String htmlContent;

    @Schema(description = "html全文内容")
    private String htmlCode;

    @Schema(description = "标题")
    private String cleanedTitle;

    @Schema(description = "内容")
    private String cleanedContent;

    @Schema(description = "标记后的html标题")
    private String markedHtmlTitle;

    @Schema(description = "标记后的html内容")
    private String markedHtmlContent;

    /**
     * 文章标题
     */
    @Schema(description = "标记后的纯文本标题")
    private String markedCleanTitle;
    /**
     * 文章正文（已标记）
     */
    @Schema(description = "标记后的纯文本内容")
    private String markedCleanContent;

}