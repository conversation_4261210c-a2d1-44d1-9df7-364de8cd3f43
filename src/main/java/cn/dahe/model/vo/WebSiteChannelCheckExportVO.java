package cn.dahe.model.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

/**
 * 栏目检查结果导出VO
 * 
 * <AUTHOR>
 * @date 2025-09-10
 */
@Data
@ContentRowHeight(20)
@HeadRowHeight(25)
public class WebSiteChannelCheckExportVO {
    
    /**
     * 站点名称
     */
    @ExcelProperty(value = "站点名称", index = 0)
    @ColumnWidth(25)
    private String websiteName;
    
    /**
     * 站点地址
     */
    @ExcelProperty(value = "站点地址", index = 1)
    @ColumnWidth(40)
    private String websiteUrl;
    
    /**
     * 栏目名称
     */
    @ExcelProperty(value = "栏目名称", index = 2)
    @ColumnWidth(30)
    private String columnName;
    
    /**
     * 栏目地址
     */
    @ExcelProperty(value = "栏目地址", index = 3)
    @ColumnWidth(40)
    private String columnUrl;
    
    /**
     * 是否更新
     */
    @ExcelProperty(value = "是否更新", index = 4)
    @ColumnWidth(15)
    private String updateStatus;
    
    /**
     * 连续未更新天数
     */
    @ExcelProperty(value = "连续未更新天数", index = 5)
    @ColumnWidth(20)
    private Integer continuousNotUpdateDays;
}