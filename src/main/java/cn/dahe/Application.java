package cn.dahe;



import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

@ComponentScan({"com.gitee.sunchenbin.mybatis.actable.manager.*", "cn.dahe.*"})
@MapperScan({"com.gitee.sunchenbin.mybatis.actable.dao.*", "cn.dahe.dao"})
@SpringBootApplication(scanBasePackages = {"cn.dahe"})
public class Application {

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }

}
