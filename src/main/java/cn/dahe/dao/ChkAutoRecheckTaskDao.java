package cn.dahe.dao;

import cn.dahe.entity.ChkAutoRecheckTask;
import cn.dahe.model.query.ChkAutoRecheckTaskQuery;
import cn.dahe.model.vo.ChkAutoRecheckTaskVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;

import java.util.Map;

/**
 * 自动复查任务
 */
@Mapper
public interface ChkAutoRecheckTaskDao extends BaseMapper<ChkAutoRecheckTask> {

    /**
     * 分页查询
     * @param page
     * @param query
     * @return
     */
    IPage<ChkAutoRecheckTaskVO> selectPage(Page<ChkAutoRecheckTaskVO> page, ChkAutoRecheckTaskQuery query);

    /**
     * 获取任务统计信息
     * @param query
     * @return
     */
    Map<String, Object> getTaskStatistics(ChkAutoRecheckTaskQuery query);
}
