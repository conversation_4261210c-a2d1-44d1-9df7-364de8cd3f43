package cn.dahe.dao;

import cn.dahe.entity.Article;
import cn.dahe.model.dto.WarnCheckDetailDto;
import cn.dahe.model.query.AllSiteSearchQuery;
import cn.dahe.model.query.ArticleCheckQuery;
import cn.dahe.model.query.ChannelArticleQuery;
import cn.dahe.model.vo.ArticleAllSiteSearchVO;
import cn.dahe.model.vo.ArticleCheckVO;
import cn.dahe.model.vo.ArticleVO;
import cn.dahe.model.vo.CheckSnapshotVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 采集文章Dao
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Mapper
public interface ArticleDao extends BaseMapper<Article> {

    /**
     * 分页查询文章检查结果
     *
     * @param page  分页参数
     * @param query 查询参数
     * @return 分页结果
     */
    IPage<ArticleCheckVO> listArticleInfo(IPage<ArticleCheckVO> page, @Param("query") ArticleCheckQuery query);
    List<ArticleCheckVO> listArticleInfo(@Param("query") ArticleCheckQuery query);


    WarnCheckDetailDto getWarnArticleInfo(@Param("articleId") Long articleId);

    /**
     * 获取文章的错误列表
     *
     * @param articleId 文章ID
     * @param query     查询条件
     * @return 错误列表
     */
    ArticleCheckVO getArticleInfo(@Param("articleId") Long articleId, @Param("query") ArticleCheckQuery query);


    /**
     * 全站搜索
     *
     * @param page  分页参数
     * @param query 查询参数
     * @return 分页结果
     */
    IPage<ArticleAllSiteSearchVO> pageAllSiteSearch(IPage<ArticleAllSiteSearchVO> page, @Param("query") AllSiteSearchQuery query);

    /**
     * 导出全站检索记录
     */
    List<Map<String, Object>> allSiteSearchExport(@Param("query") AllSiteSearchQuery query);


    List<Article> listUncheckedArticle();

    /**
     * 根据栏目与条件分页查询文章基础信息
     */
    IPage<ArticleVO> pageChannelArticles(IPage<ArticleVO> page, @Param("query") ChannelArticleQuery query);

    List<Article> listUnrelatedReprintSourceArticles();

    default List<Article> selectByChannelIdAndPubDate(Integer channelId, LocalDate pubDate) {
        LocalDateTime startOfDay = pubDate.atStartOfDay();
        LambdaQueryWrapper<Article> wrapper = Wrappers.lambdaQuery(Article.class);
        wrapper.eq(Article::getChannelId, channelId);
        wrapper.ge(Article::getPubTime, startOfDay);
        wrapper.lt(Article::getPubTime, startOfDay.plusDays(1));
        wrapper.orderByDesc(Article::getPubTime);
        return selectList(wrapper);
    }

    List<Article> listByWebsiteId(Long websiteId);

    List<Article> listByIsIndex(int isIndex, String webSiteId);
    //昨天的文章
    List<Article> listByIsIndexAndPubTime(int isIndex, String webSiteId);

    List<Article> listNoContentArticle();

    CheckSnapshotVO getSnapshotInfo(@Param("articleId") Long articleId);
}