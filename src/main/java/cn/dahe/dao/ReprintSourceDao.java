package cn.dahe.dao;

import cn.dahe.entity.ReprintSource;
import cn.dahe.model.query.ReprintSourceQuery;
import cn.dahe.model.vo.ReprintSourceVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 站点转载信源关联表 Dao接口
 */
@Mapper
public interface ReprintSourceDao extends BaseMapper<ReprintSource> {

    /**
     * 分页查询转载信源列表
     *
     * @param page  分页参数
     * @param query 查询条件
     * @return 转载信源列表
     */
    Page<ReprintSourceVO> pageList(Page<ReprintSourceVO> page, @Param("query") ReprintSourceQuery query);

    List<ReprintSource> findNonExistent(@Param("list") List<ReprintSource> candidateList);
}
