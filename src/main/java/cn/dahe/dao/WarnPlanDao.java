package cn.dahe.dao;

import cn.dahe.entity.WarnPlan;
import cn.dahe.model.query.WarnPlanQuery;
import cn.dahe.model.vo.WarnPlanVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface WarnPlanDao extends BaseMapper<WarnPlan> {

    List<WarnPlanVO> list(@Param("query") WarnPlanQuery query);

    WarnPlanVO getById(@Param("id") Integer id);


    @Select("select * from t_warn_plan where is_deleted = 0")
    List<WarnPlan> listAll();

    /**
     * 获取可用的预警方案，主要是根据站点配置，以及不同预警需要的开关
     */
    List<Long> listAvailablePlanWithCondition(@Param("websiteId") Long websiteId, @Param("warnType") Integer warnType);

    default WarnPlan selectUsefulByIdAndUpdateWarnAndIsDeleted(int id, boolean updateWarn, boolean isDeleted) {
        LambdaQueryWrapper<WarnPlan> wrapper = Wrappers.lambdaQuery(WarnPlan.class);
        wrapper.eq(WarnPlan::getId, id);
        wrapper.eq(WarnPlan::getUpdateWarn, updateWarn);
        wrapper.eq(WarnPlan::getIsDeleted, isDeleted);
        wrapper.and(w -> w.eq(WarnPlan::getWechatPushEnable, true).or().eq(WarnPlan::getSmsPushEnable, true));
        return selectOne(wrapper);
    }
}
