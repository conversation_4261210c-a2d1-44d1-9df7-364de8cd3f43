package cn.dahe.dao;

import cn.dahe.model.query.ChkUpdateSiteColumnQuery;
import cn.dahe.model.vo.ChkUpdateSiteColumnCountVO;
import cn.dahe.model.vo.ChkUpdateSiteColumnDetailVO;
import cn.dahe.model.vo.ChkUpdateSiteColumnVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 栏目更新检查Dao - 对应原型图功能
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Mapper
public interface ChkUpdateSiteColumnDao {

    // ==================== 栏目更新检查概览 ====================

    /**
     * 获取栏目更新检查概览统计
     *
     * @param query 查询参数
     * @return 统计数据
     */
    ChkUpdateSiteColumnCountVO getOverviewStatistics(@Param("query") ChkUpdateSiteColumnQuery query);

    // ==================== 栏目更新检查记录 ====================

    /**
     * 分页查询栏目更新检查记录（包含扩展信息）
     *
     * @param page 分页参数
     * @param query 查询参数
     * @return 分页结果
     */
    IPage<ChkUpdateSiteColumnVO> selectPageWithExtInfo(
            Page<ChkUpdateSiteColumnVO> page,
            @Param("query") ChkUpdateSiteColumnQuery query
    );

    /**
     * 根据ID获取栏目更新检查详情
     *
     * @param id ID
     * @return 详情
     */
    IPage<ChkUpdateSiteColumnDetailVO> selectDetailById(Page<ChkUpdateSiteColumnDetailVO> page, @Param("id") Long id, @Param("updateTime") String updateTime);

    /**
     * 查询导出数据
     *
     * @param query 查询参数
     * @return 导出数据
     */
    List<Map<String, Object>> selectExportData(@Param("query") ChkUpdateSiteColumnQuery query);

    void deleteSiteColumnTaskDeleteTodayData(@Param("beginId") Long beginId, @Param("endId") Long endId);

    List<Map<String, Object>> selectSiteColumnTaskInsertTodayData();

    @Select("select Min(id) from chk_update_site_column_result where update_time = CURRENT_DATE()\n" +
            "UNION " +
            "select MAX(id) from chk_update_site_column_result where update_time = CURRENT_DATE()")
    List<Long> selectWaitDeleteResultIds();
}
