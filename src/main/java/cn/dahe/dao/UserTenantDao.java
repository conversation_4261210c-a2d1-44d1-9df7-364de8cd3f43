package cn.dahe.dao;

import cn.dahe.entity.UserTenant;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface UserTenantDao extends BaseMapper<UserTenant> {
    void insertList(List<UserTenant> list);

    default void deleteByUserId(int userId) {
        delete(Wrappers.lambdaQuery(UserTenant.class).eq(UserTenant::getUserId, userId));
    }
}
