package cn.dahe.dao;

import cn.dahe.entity.User;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 *
 */
@Mapper
public interface UserDao extends BaseMapper<User> {


    List<User> listByFilters(@Param("userName") String userName,
                             @Param("roleId") String roleId,
                             @Param("depIdList") List depIdList);


    List<User> listByRoleIdAndUserName(@Param("userName") String userName,
                                       @Param("roleId") String roleId);


    @Select("select * from t_user where user_id in(select user_id from t_user_site where site_id = #{siteId})")
    List<User> getUserBySiteId(Integer siteId);

    List<User> selectEnableByWebsiteIdAndTenantId(int websiteId, int tenantId, int option);
}