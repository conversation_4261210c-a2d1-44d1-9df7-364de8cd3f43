package cn.dahe.dao;

import cn.dahe.entity.WebSiteChannelCheck;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * 网站栏目更新检查DAO接口
 *
 * <AUTHOR>
 * @date 2025-09-09
 */
@Mapper
public interface WebSiteChannelCheckDao extends BaseMapper<WebSiteChannelCheck> {

    /**
     * 根据栏目ID和检查日期查询记录
     *
     * @param columnId  栏目ID
     * @param checkDate 检查日期
     * @return 检查记录
     */
    WebSiteChannelCheck selectByColumnIdAndDate(@Param("columnId") Long columnId, @Param("checkDate") LocalDate checkDate);

    /**
     * 根据栏目ID和日期范围查询检查记录
     *
     * @param columnId  栏目ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 检查记录列表
     */
    List<WebSiteChannelCheck> selectByColumnIdAndDateRange(
            @Param("columnId") Long columnId,
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate
    );
    
    /**
     * 批量根据栏目ID列表和日期范围查询检查记录
     *
     * @param columnIds 栏目ID列表
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 检查记录列表
     */
    List<WebSiteChannelCheck> selectByColumnIdsAndDateRange(
            @Param("columnIds") List<Long> columnIds,
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate
    );
}