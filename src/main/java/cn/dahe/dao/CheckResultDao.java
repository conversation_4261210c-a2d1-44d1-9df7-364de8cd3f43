package cn.dahe.dao;

import cn.dahe.entity.CheckResult;
import cn.dahe.model.query.CheckResultQuery;
import cn.dahe.model.vo.CheckResultVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 内容错误详情Dao
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Mapper
public interface CheckResultDao extends BaseMapper<CheckResult> {


    List<CheckResultVO> listCheckResultByTaskId(@Param("taskId") Long taskId, @Param("query") CheckResultQuery query);

    List<Long> listResultIdsFromArticle(@Param("articleId") Long articleId, @Param("resultIds") List<Long> resultIds);

    List<CheckResult> listUnrelatedWordResult();

    void updateUnrelatedCheckWordResult();
}