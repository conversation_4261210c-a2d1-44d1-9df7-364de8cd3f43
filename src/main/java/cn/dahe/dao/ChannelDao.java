package cn.dahe.dao;


import cn.dahe.entity.Channel;
import cn.dahe.model.query.ChkUpdateSiteIndexQuery;
import cn.dahe.model.query.WebSiteChannelCheckQuery;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ChannelDao extends BaseMapper<Channel> {
    int getCountChannelsByWebsiteAndProcessType(ChkUpdateSiteIndexQuery query);

    int getCountChannelsByWebsiteAndProcessType(WebSiteChannelCheckQuery query);

    List<Channel> getChannelsByWebsiteAndProcessType(WebSiteChannelCheckQuery query);
    
    /**
     * 根据栏目类型ID查询栏目列表
     * 
     * @param channelTypeId 栏目类型ID
     * @return 栏目列表
     */
    List<Channel> selectChannelsByChannelTypeId(@Param("channelTypeId") Long channelTypeId);
    
    /**
     * 批量更新栏目的更新期限
     * 
     * @param channelTypeId 栏目类型ID
     * @param updatePeriod 更新期限
     * @return 更新记录数
     */
    int updateChannelUpdatePeriodByTypeId(@Param("channelTypeId") Long channelTypeId, @Param("updatePeriod") Integer updatePeriod);
    
    /**
     * 批量重置栏目的栏目类型ID和更新期限
     * 
     * @param channelTypeId 栏目类型ID
     * @return 更新记录数
     */
    int resetChannelTypeAndUpdatePeriodByTypeId(@Param("channelTypeId") Long channelTypeId);

    /**
     * 根据栏目ID添加栏目信息
     * @return 栏目信息
     */
    int insertById(@Param("channel") Channel channel);
}
