package cn.dahe.dao;

import cn.dahe.entity.WarnUser;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

@Mapper
public interface WarnUserDao extends BaseMapper<WarnUser> {

//    @Insert("insert into t_warn_user(id, user_name, user_open_id, user_phone, subscribe_status, create_time) values(#{id}, #{userName}, #{userOpenId}, #{userPhone}, #{subscribeStatus}, #{createTime})")
//    int insert(WarnUser warnUser);

    List<WarnUser> list(@Param("query") cn.dahe.model.query.WarnUserQuery query);

    WarnUser getByOpenId(@Param("openId") String openId);

    WarnUser getByUserPhoneWarnUser(@Param("userPhone") String userPhone);

    default List<WarnUser> selectByIdInAndIsSubscribe(Collection<Integer> idCollection, int isSubscribe) {
        LambdaQueryWrapper<WarnUser> wrapper = Wrappers.lambdaQuery(WarnUser.class);
        wrapper.in(WarnUser::getId, idCollection);
        wrapper.eq(WarnUser::getIsSubscribe, isSubscribe);
        return selectList(wrapper);
    }
}
