package cn.dahe.dao;

import cn.dahe.entity.WarnPlanPlatform;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface WarnPlanPlatformDao extends BaseMapper<WarnPlanPlatform> {

    List<WarnPlanPlatform> listByWarnPlanId(@Param("warnPlanId") Integer warnPlanId);

    int deleteByWarnPlanId(@Param("warnPlanId") Integer warnPlanId);

    default List<WarnPlanPlatform> selectByPlatformGt(int platformGt) {
        LambdaQueryWrapper<WarnPlanPlatform> wrapper = Wrappers.lambdaQuery(WarnPlanPlatform.class);
        wrapper.gt(WarnPlanPlatform::getPlatformType, platformGt);
        return selectList(wrapper);
    }
}
