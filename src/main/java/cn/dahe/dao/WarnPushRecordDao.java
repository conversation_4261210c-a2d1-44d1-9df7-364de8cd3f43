package cn.dahe.dao;

import cn.dahe.entity.WarnPushRecord;
import cn.dahe.model.vo.WarnPushRecordVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface WarnPushRecordDao extends BaseMapper<WarnPushRecord> {

    List<WarnPushRecord> listByWarnPlanId(@Param("warnPlanId") Integer warnPlanId);

    List<WarnPushRecordVO> list(@Param("query") cn.dahe.model.query.WarnPushRecordQuery query);
}


