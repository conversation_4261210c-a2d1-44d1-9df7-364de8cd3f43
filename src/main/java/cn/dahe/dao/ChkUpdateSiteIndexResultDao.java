package cn.dahe.dao;

import cn.dahe.entity.ChkUpdateSiteIndexResult;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 首页更新检查结果
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Mapper
public interface ChkUpdateSiteIndexResultDao extends BaseMapper<ChkUpdateSiteIndexResult> {

    /**
     * 批量插入数据
     *
     * @param list 数据列表
     * @return 插入数量
     */
    Long insertBatch(@Param("list") List<ChkUpdateSiteIndexResult> list);
}
