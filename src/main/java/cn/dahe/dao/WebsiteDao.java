package cn.dahe.dao;

import cn.dahe.entity.Website;
import cn.dahe.model.query.AppendWebsiteQuery;
import cn.dahe.model.vo.WebsiteVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 网站信息Dao
 */
@Mapper
public interface WebsiteDao extends BaseMapper<Website> {


    /**
     * 更新网站ID
     *
     * @param oldId 原ID
     * @param newId 新ID
     * @return 更新记录数
     */
    @Update("UPDATE t_website SET site_id = #{newId}, web_url = #{newUrl} WHERE id = #{oldId}")
    int updateId(@Param("oldId") int oldId, @Param("newId") int newId, @Param("newUrl") String newUrl);

    @Select("select count(id) from t_website where is_del=0 and (process_type=0 or process_type is null) and status = 1")
    long getWebsiteCount();

    IPage<WebsiteVO> pageList(Page<WebsiteVO> page, @Param("query") AppendWebsiteQuery query);

    List<WebsiteVO> listSelectByQuery(@Param("query") AppendWebsiteQuery query);

    List<Website> selectBySiteIds(Set<Integer> siteIds);


    @Select("<script>" +
            "select site_id from t_website where is_del=0 and status = 1 " +
            "<if test='ids != null and ids.size() > 0'>" +
            "and id in " +
            "<foreach collection='ids' item='item' open='(' separator=',' close=')'>" +
            "#{item}" +
            "</foreach>" +
            "</if>" +
            "</script>")
    List<Integer> selectSiteIdsByIds(@Param("ids") List<Integer> ids);

    List<Website> selectEnableByTenantIdAndUserAndProcessTypeIn(int tenantId, int userId, boolean defaultRole, Set<Integer> platformSet);

    List<Website> selectNotDeleteByTenantIdAndUserAndProcessTypeIn(int tenantId, int userId, boolean defaultRole, Set<Integer> platformSet);
}