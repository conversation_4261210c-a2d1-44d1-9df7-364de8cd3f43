package cn.dahe.dao;

import cn.dahe.entity.ChkTransferTask;
import cn.dahe.model.query.ChkTransferTaskQuery;
import cn.dahe.model.vo.ChkTransferTaskVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 整改任务
 */
@Mapper
public interface ChkTransferTaskDao extends BaseMapper<ChkTransferTask> {

    /**
     * 根据时间范围获取任务统计
     */
    Map<String, Object> getTaskStatisticsByTimeRange(
            @Param("query") ChkTransferTaskQuery query,
            @Param("beginTime") Date beginTime,
            @Param("endTime") Date endTime
    );

    /**
     * 分页查询
     * @param page
     * @param query
     * @return
     */
    IPage<ChkTransferTaskVO> selectPage(Page<ChkTransferTaskVO> page, ChkTransferTaskQuery query);

    /**
     * 批量插入转办任务
     * @param taskList 任务列表
     * @return 插入成功的记录数
     */
    int batchInsert(@Param("list") List<ChkTransferTask> taskList);
}