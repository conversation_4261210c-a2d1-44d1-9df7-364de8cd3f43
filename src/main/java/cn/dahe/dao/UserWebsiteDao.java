package cn.dahe.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import cn.dahe.entity.UserWebsite;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Set;

/**
 *
 */
@Mapper
public interface UserWebsiteDao extends BaseMapper<UserWebsite> {


    List<Long> listWebsiteIdsByUserId(Integer userId);

    /**
     * 根据用户ID查找站点
     *
     * @param userId
     * @return
     */
    @Select("SELECT website_id FROM t_user_website WHERE user_id = #{userId}")
    List<Integer> listByUserId(Integer userId);

    /**
     * 批量插入
     *
     * @param userSites
     */
    @Insert("<script>" +
            "INSERT INTO t_user_website (user_id, website_id) VALUES " +
            "<foreach collection='list' item='item' separator=','>" +
            "(#{item.userId}, #{item.websiteId})" +
            "</foreach>" +
            "</script>")
    int insertBatch(List<UserWebsite> userSites);

    Set<Long> selectWebsiteIdByUserIdAndProcessTypeIn(int userId, Set<Integer> platformSet);

    void deleteByUserIdAndProcessTypeIn(int userId, Set<Integer> platformSet);
}