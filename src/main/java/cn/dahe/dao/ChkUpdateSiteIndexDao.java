package cn.dahe.dao;

import cn.dahe.entity.ChkUpdateSiteIndex;
import cn.dahe.model.query.ChkUpdateSiteIndexQuery;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-07-30
 */
@Mapper
public interface ChkUpdateSiteIndexDao extends BaseMapper<ChkUpdateSiteIndex> {

    /**
     * 获取首页更新统计结果
     */
    int getUpdatedCount(@Param("query") ChkUpdateSiteIndexQuery query);

    /**
     * 获取首页更新结果列表
     */
    IPage<Map<String, Object>>  selectPageWithExtInfo(
            Page<Map<String, Object>> page,
            @Param("groupId") String groupId,
            @Param("siteId") String websiteId,
            @Param("beginTime") String beginTime,
            @Param("endTime") String endTime
    );

    /**
     * 获取新媒体首页更新结果列表
     */
    IPage<Map<String, Object>>  selectPageWithExtInfo2(
            Page<Map<String, Object>> page,
            @Param("groupId") String groupId,
            @Param("siteId") String websiteId,
            @Param("beginTime") String beginTime,
            @Param("endTime") String endTime,
            @Param("processTypeList") List<Integer> processTypeList
    );

    /**
     * 导出首页更新检查记录
     */
    List<Map<String, Object>> selectExportData(@Param("query") ChkUpdateSiteIndexQuery query);

    void deleteSiteIndexTaskDeleteTodayData(@Param("beginId") Long beginId, @Param("endId") Long endId);

    List<Map<String, Object>> selectSiteIndexTaskInsertTodayData();

    @Select("select Min(id) from chk_update_site_index_result where update_time = CURRENT_DATE()\n" +
            "UNION " +
            "select MAX(id) from chk_update_site_index_result where update_time = CURRENT_DATE()")
    List<Long> selectWaitDeleteResultIds();
}
