package cn.dahe.dao;

import cn.dahe.entity.ChannelType;
import cn.dahe.model.dto.ChannelTypeQuery;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 栏目类型 Mapper 接口
 * 
 * <AUTHOR>
 * @date 2025-09-16
 */
@Mapper
public interface ChannelTypeDao extends BaseMapper<ChannelType> {

    /**
     * 新增栏目类型
     * 
     * @param channelType 栏目类型信息
     * @return 影响行数
     */
    int insertChannelType(ChannelType channelType);

    /**
     * 修改栏目类型
     * 
     * @param channelType 栏目类型信息
     * @return 影响行数
     */
    int updateChannelType(ChannelType channelType);

    /**
     * 逻辑删除栏目类型
     * 
     * @param id 栏目类型ID
     * @return 影响行数
     */
    int deleteChannelTypeById(Long id);

    /**
     * 根据ID查询栏目类型
     * 
     * @param id 栏目类型ID
     * @return 栏目类型信息
     */
    ChannelType selectChannelTypeById(Long id);

    /**
     * 校验名称在同一层级下是否重复
     * 
     * @param name 栏目类型名称
     * @param parentId 父级ID
     * @param id 当前记录ID（修改时排除自己）
     * @return 重复数量
     */
    int checkNameDuplicate(@Param("name") String name, @Param("parentId") Long parentId, @Param("id") Long id);

    /**
     * 校验父级ID是否存在
     * 
     * @param parentId 父级ID
     * @return 存在数量
     */
    int checkParentIdExists(Long parentId);

    /**
     * 分页查询栏目类型列表
     * 
     * @param page 分页对象
     * @param query 查询条件
     * @return 分页结果
     */
    IPage<ChannelType> listChannelTypesByPage(Page<ChannelType> page, @Param("query") ChannelTypeQuery query);

    /**
     * 分页查询总数
     * 
     * @param query 查询条件
     * @return 总数
     */
    Long countChannelTypesByPage(@Param("query") ChannelTypeQuery query);

    /**
     * 查询所有栏目类型（用于构建树结构）
     * 
     * @return 栏目类型列表
     */
    List<ChannelType> listChannelTypeTree();

    /**
     * 根据父级ID查询子级栏目类型
     * 
     * @param parentId 父级ID
     * @return 子级栏目类型列表
     */
    List<ChannelType> listChannelTypesByParentId(Long parentId);

    /**
     * 查询根节点栏目类型
     * 
     * @return 根节点栏目类型列表
     */
    List<ChannelType> listRootChannelTypes();

    /**
     * 批量查询栏目类型
     * 
     * @param ids ID列表
     * @return 栏目类型列表
     */
    List<ChannelType> listChannelTypesByIds(@Param("ids") List<Long> ids);

    /**
     * 查询某个节点的所有子节点ID
     * 
     * @param parentId 父级ID
     * @return 子节点ID列表
     */
    List<Long> listChildrenIds(Long parentId);
}