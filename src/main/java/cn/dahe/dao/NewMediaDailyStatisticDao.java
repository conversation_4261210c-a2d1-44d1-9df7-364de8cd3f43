package cn.dahe.dao;

import cn.dahe.entity.Article;
import cn.dahe.entity.NewMediaDailyStatistic;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Mapper
public interface NewMediaDailyStatisticDao extends BaseMapper<NewMediaDailyStatistic> {
    void insertByWebsite();

    void updateByArticle(Article article);

    List<Map<String, Object>> selectStatisticPage(Set<Integer> platformSet, Set<Integer> groupIdSet, Set<Long> newMediaIdSet,
                                                  LocalDateTime startTime, LocalDateTime endTime,
                                                  Integer tenantId, Integer userId, Integer sort, Integer noUpdateDay,
                                                  Boolean defaultRole, Boolean updated);

    Map<String, Object> selectStatistic(Set<Integer> platformSet, Set<Integer> groupIdSet, Set<Long> newMediaIdSet,
                                        LocalDateTime startTime, LocalDateTime endTime,
                                        int tenantId, int userId,
                                        boolean defaultRole);

    List<NewMediaDailyStatistic> selectStatisticTotalPage(LocalDateTime startTime, LocalDateTime endTime, Long newMediaId);

    List<Article> selectArticlePage(Set<Integer> platformSet, Set<Integer> groupIdSet, Set<Long> newMediaIdSet,
                                    LocalDateTime startTime, LocalDateTime endTime,
                                    String keyword,
                                    int tenantId, int userId, Integer sort,
                                    boolean defaultRole);

    List<Map<String, Object>> selectPage(Set<Integer> platformSet, Set<Integer> groupIdSet, Set<Long> newMediaIdSet,
                                         String name, String link,
                                         int tenantId, int userId, Integer status,
                                         boolean defaultRole);
}
