package cn.dahe.dao;

import cn.dahe.entity.WebSiteIndexArticleCheck;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * 站点下网站首页更新详情DAO接口
 *
 * <AUTHOR>
 * @date 2025-09-03
 */
@Mapper
public interface WebSiteIndexArticleCheckDao extends BaseMapper<WebSiteIndexArticleCheck> {

    /**
     * 根据站点ID和检查日期查询记录
     *
     * @param websiteId 站点ID
     * @param checkDate 检查日期
     * @return 检查记录
     */
    WebSiteIndexArticleCheck selectByWebsiteIdAndDate(@Param("websiteId") Long websiteId, @Param("checkDate") LocalDate checkDate);

    /**
     * 根据站点ID分页查询检查记录
     *
     * @param websiteId 站点ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 检查记录列表
     */
    List<WebSiteIndexArticleCheck> selectByWebsiteIdAndDateRange(
            @Param("websiteId") Long websiteId,
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate,
            @Param("offset") Integer offset,
            @Param("limit") Integer limit
    );

    /**
     * 根据站点ID和日期范围统计记录总数
     *
     * @param websiteId 站点ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 记录总数
     */
    Long countByWebsiteIdAndDateRange(
            @Param("websiteId") Long websiteId,
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate
    );
}
