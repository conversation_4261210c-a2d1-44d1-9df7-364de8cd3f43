package cn.dahe.dao;

import cn.dahe.model.dto.WebsiteAccessOverviewDto;
import cn.dahe.entity.WebsiteAccessRecord;
import cn.dahe.model.query.WebsiteAccessRecordQuery;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;


/**
 *
 */
@Mapper
public interface WebsiteAccessRecordDao extends BaseMapper<WebsiteAccessRecord> {


    List<WebsiteAccessOverviewDto> listByFilters(@Param(value = "webIdList") List<String> webIdList,
                                                 @Param(value = "groupType") String groupType,
                                                 @Param(value = "beginTime") String beginTime,
                                                 @Param(value = "endTime") String endTime);

    /**
     * 导出连通性检查记录
     *
     * @param query
     * @return
     */
    List<Map<String, Object>> selectExportData(@Param(value = "query") WebsiteAccessRecordQuery query);
}