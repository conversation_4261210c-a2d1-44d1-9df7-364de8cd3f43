package cn.dahe.dao;

import cn.dahe.entity.Team;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TeamDao extends BaseMapper<Team> {
    default Team selectOneByNameAndTenantId(String name, int tenantId) {
        return selectOne(Wrappers.lambdaQuery(Team.class).eq(Team::getName, name).eq(Team::getTenantId, tenantId).eq(Team::getDeleteTime, 0));
    }

    default Team selectOneByIdNotAndNameAndTenantId(int notId, String name, int tenantId) {
        return selectOne(Wrappers.lambdaQuery(Team.class)
                                 .ne(Team::getId, notId)
                                 .eq(Team::getName, name)
                                 .eq(Team::getTenantId, tenantId)
                                 .eq(Team::getDeleteTime, 0));
    }

    default void delete(int id, int userId) {
        Team team = new Team().setDeleteUserId(userId).setDeleteTime(System.currentTimeMillis());
        update(team, Wrappers.lambdaUpdate(Team.class).eq(Team::getId, id).eq(Team::getDeleteTime, 0));
    }

    default void update(Team team) {
        update(team, Wrappers.lambdaUpdate(Team.class).eq(Team::getId, team.getId()).eq(Team::getDeleteTime, 0));
    }

    List<Team> selectPage(String likeName, int tenantId);
}
