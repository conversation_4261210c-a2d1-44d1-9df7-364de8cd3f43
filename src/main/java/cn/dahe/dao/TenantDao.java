package cn.dahe.dao;

import cn.dahe.entity.Tenant;
import cn.dahe.enums.TenantStatus;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDate;
import java.util.List;

@Mapper
public interface TenantDao extends BaseMapper<Tenant> {
    default Tenant selectOneByName(String name) {
        return selectOne(Wrappers.lambdaQuery(Tenant.class).eq(Tenant::getName, name).ne(Tenant::getStatus, TenantStatus.delete.value));
    }

    default Tenant selectOneByIdNotAndName(int notId, String name) {
        return selectOne(Wrappers.lambdaQuery(Tenant.class)
                                 .ne(Tenant::getId, notId)
                                 .eq(Tenant::getName, name)
                                 .ne(Tenant::getStatus, TenantStatus.delete.value));
    }

    default void expire() {
        update(null, Wrappers.lambdaUpdate(Tenant.class)
                             .eq(Tenant::getStatus, TenantStatus.enable.value)
                             .lt(Tenant::getExpireDate, LocalDate.now())
                             .set(Tenant::getStatus, TenantStatus.expire.value));
    }

    default void delete(int id, int userId) {
        Tenant tenant = new Tenant().setStatus(TenantStatus.delete.value).setDeleteUserId(userId).setDeleteTime(System.currentTimeMillis());
        update(tenant, Wrappers.lambdaUpdate(Tenant.class).eq(Tenant::getId, id).ne(Tenant::getStatus, TenantStatus.delete.value));
    }

    default void update(Tenant tenant) {
        update(tenant, Wrappers.lambdaUpdate(Tenant.class).eq(Tenant::getId, tenant.getId()).ne(Tenant::getStatus, TenantStatus.delete.value));
    }

    default List<Tenant> selectNotDelete() {
        return selectList(Wrappers.lambdaQuery(Tenant.class).ne(Tenant::getStatus, TenantStatus.delete.value));
    }

    default List<Tenant> selectEnable() {
        return selectList(Wrappers.lambdaQuery(Tenant.class).eq(Tenant::getStatus, TenantStatus.enable.value));
    }

    List<Tenant> selectNotDeletePage(Integer status, String likeName);

    List<Tenant> selectEnableByUserId(int userId);

    List<Tenant> selectNotDeleteByUserId(int userId);
}
