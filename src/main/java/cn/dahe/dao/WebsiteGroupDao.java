package cn.dahe.dao;

import cn.dahe.entity.WebsiteGroup;
import cn.dahe.model.query.AppendWebsiteQuery;
import cn.dahe.model.vo.WebsiteGroupVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 网站分组Dao
 */
@Mapper
public interface WebsiteGroupDao extends BaseMapper<WebsiteGroup> {


    List<WebsiteGroupVO> listSelectByQuery(@Param("query") AppendWebsiteQuery query);

    List<WebsiteGroupVO> listAvailableGroup();
}