package cn.dahe.dao;

import cn.dahe.entity.ChkUpdateSiteColumnResult;
import cn.dahe.model.query.ChkUpdateSiteColumnQuery;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 栏目更新检查结果
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Mapper
public interface ChkUpdateSiteColumnResultDao extends BaseMapper<ChkUpdateSiteColumnResult> {

    /**
     * 批量插入数据
     *
     * @param list 数据列表
     * @return 插入数量
     */
    Long insertBatch(@Param("list") List<ChkUpdateSiteColumnResult> list);
}
